apiVersion: v2
appVersion: 0.0.0
dependencies:
  - name: common
    version: 2.x.x
    repository: oci://registry-1.docker.io/bitnamicharts
  - name: etcd
    version: 10.2.18
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: etcd.enabled
  - name: mongodb
    version: 16.0.3
    repository: oci://registry-1.docker.io/bitnamicharts
    condition: mongodb.enabled
  - name: casdoor-helm-charts
    repository: oci://registry-1.docker.io/casbin
    version: v1.724.0
    condition: casdoor-helm-charts.enabled
  - name: postgresql
    repository: oci://registry-1.docker.io/bitnamicharts
    version: 16.0.1
    condition: postgresql.enabled
name: bob
version: 0.0.0
