apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "bob.edgeserver.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: edgeserver
  {{- if or .Values.edgeserver.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.edgeserver.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.edgeserver.replicaCount }}
  {{- if .Values.edgeserver.updateStrategy }}
  strategy: {{- toYaml .Values.edgeserver.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.edgeserver.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: edgeserver
  template:
    metadata:
      {{- if .Values.edgeserver.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: edgeserver
    spec:
      {{- include "bob.edgeserver.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "bob.edgeserver.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.edgeserver.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.edgeserver.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.edgeserver.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.edgeserver.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.edgeserver.podAffinityPreset "component" "edgeserver" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.edgeserver.podAntiAffinityPreset "component" "edgeserver" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.edgeserver.nodeAffinityPreset.type "key" .Values.edgeserver.nodeAffinityPreset.key "values" .Values.edgeserver.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.edgeserver.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.edgeserver.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.edgeserver.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.edgeserver.priorityClassName }}
      priorityClassName: {{ .Values.edgeserver.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.edgeserver.schedulerName }}
      schedulerName: {{ .Values.edgeserver.schedulerName | quote }}
      {{- end }}
      {{- if .Values.edgeserver.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.edgeserver.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.edgeserver.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.edgeserver.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.edgeserver.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: false
      initContainers:
        {{- if .Values.edgeserver.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: edgeserver
          image: {{ template "bob.edgeserver.image" . }}
          imagePullPolicy: {{ .Values.edgeserver.image.pullPolicy }}
          {{- if .Values.edgeserver.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.edgeserver.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.edgeserver.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - edge
            - server
            - --listen=:{{ .Values.edgeserver.service.ports.http }}
            - --v={{ .Values.edgeserver.logLevel }}
            {{- if .Values.edgeserver.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.edgeserver.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- if .Values.edgeserver.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.edgeserver.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.edgeserver.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.edgeserver.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.edgeserver.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.edgeserver.resources }}
          resources: {{- toYaml .Values.edgeserver.resources | nindent 12 }}
          {{- else if ne .Values.edgeserver.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.edgeserver.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.edgeserver.service.ports.http }}
          {{- if .Values.edgeserver.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.edgeserver.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.edgeserver.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.edgeserver.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.edgeserver.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.edgeserver.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.edgeserver.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.edgeserver.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.edgeserver.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.edgeserver.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.edgeserver.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.edgeserver.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.edgeserver.sidecars "context" $) | nindent 8 }}
        {{- end }}