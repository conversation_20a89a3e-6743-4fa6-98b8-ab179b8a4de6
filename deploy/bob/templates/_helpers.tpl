{{/*
Return the proper image name
{{ include "common.images.image" ( dict "imageRoot" .Values.path.to.the.image "global" $) }}
*/}}
{{- define "common.images.image2" -}}
{{- $registryName := .imageRoot.registry -}}
{{- $repositoryName := .imageRoot.repository -}}
{{- $tag := .imageRoot.tag | toString -}}
{{- if or (not $tag) (eq $tag "latest") -}}
    {{- if hasPrefix "v" .root.Chart.AppVersion  -}}
        {{- $tag = printf "%s" .root.Chart.AppVersion | toString -}}
    {{- else -}}
        {{- $tag = printf "v%s" .root.Chart.AppVersion | toString -}}
    {{- end -}}
{{- end -}}
{{- if .global.imageRegistry }}
    {{- $registryName = .global.imageRegistry -}}
{{- end -}}
{{- if $registryName }}
    {{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- else -}}
    {{- printf "%s:%s" $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{- define "bob.apiserver.fullname" -}}
{{ template "common.names.fullname" . }}-apiserver
{{- end -}}

{{- define "bob.edgeserver.fullname" -}}
{{ template "common.names.fullname" . }}-edgeserver
{{- end -}}

{{- define "bob.controller.fullname" -}}
{{ template "common.names.fullname" . }}-controller
{{- end -}}

{{- define "bob.console.fullname" -}}
{{ template "common.names.fullname" . }}-console
{{- end -}}

{{- define "bob.common.fullname" -}}
{{ template "common.names.fullname" . }}
{{- end -}}

{{/*
Return the proper apiserver image name
*/}}
{{- define "bob.apiserver.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.apiserver.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "bob.edgeserver.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.edgeserver.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "bob.controller.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.controller.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "bob.console.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.console.image "root" . "global" .Values.global) }}
{{- end -}}

{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "bob.apiserver.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.apiserver.image) "context" $) -}}
{{- end -}}

{{- define "bob.edgeserver.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.edgeserver.image) "context" $) -}}
{{- end -}}
{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "bob.controller.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.controller.image) "context" $) -}}
{{- end -}}

{{- define "bob.console.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.console.image) "context" $) -}}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "bob.apiserver.serviceAccountName" -}}
{{- if .Values.apiserver.serviceAccount.create -}}
    {{ default (include "bob.apiserver.fullname" .) .Values.apiserver.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.apiserver.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "bob.edgeserver.serviceAccountName" -}}
{{- if .Values.edgeserver.serviceAccount.create -}}
    {{ default (include "bob.edgeserver.fullname" .) .Values.edgeserver.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.edgeserver.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{/*
Create the name of the service account to use
*/}}
{{- define "bob.controller.serviceAccountName" -}}
{{- if .Values.controller.serviceAccount.create -}}
    {{ default (include "bob.controller.fullname" .) .Values.controller.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.controller.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "bob.apiserver.address" -}}
http://{{- include "bob.apiserver.fullname" . -}}:{{- .Values.apiserver.service.ports.http -}}
{{- end -}}

{{- define "bob.mongodb.fullname" -}}
{{- include "common.names.dependency.fullname" (dict "chartName" "mongodb" "chartValues" .Values.mongodb "context" $) -}}
{{- end -}}

{{- define "bob.mongodb.host" -}}
{{- if .Values.mongodb.enabled -}}
  {{- printf "%s-headless" (include "bob.mongodb.fullname" .) -}}
{{- else if .Values.externalMongodb -}}
  {{- .Values.externalMongodb.host -}}
{{- end -}}
{{- end -}}

{{- define "bob.mongodb.port" -}}
{{- if .Values.mongodb.enabled -}}
    {{- .Values.mongodb.service.ports.mongodb -}}
{{- else if and .Values.externalMongodb.enabled .Values.externalMongodb.port -}}
    {{- .Values.externalMongodb.port -}}
{{- end -}}
{{- end -}}

{{- define "bob.mongodb.database" -}}
{{- if .Values.mongodb.enabled -}}
    {{- if .Values.mongodb.auth.databases -}}
        {{- first .Values.mongodb.auth.databases -}}
    {{- else -}}
        {{- /*keep empty,use default*/ -}}
    {{- end -}}
{{- else if and .Values.externalMongodb.enabled .Values.externalMongodb.database -}}
    {{- .Values.externalMongodb.database -}}
{{- end -}}
{{- end -}}

{{- define "bob.mongodb.username" -}}
{{- if .Values.mongodb.enabled -}}
    {{- if .Values.mongodb.auth.rootUser -}}
        {{- .Values.mongodb.auth.rootUser -}}
    {{- else -}}
        {{- /*keep empty,use default*/ -}}
    {{- end -}}
{{- else if .Values.externalMongodb.enabled -}}
    {{- .Values.externalMongodb.username -}}
{{- end -}}
{{- end -}}

{{- define "bob.mongodb.password" -}}
{{- if .Values.mongodb.enabled -}}
    {{ .Values.mongodb.auth.rootPassword }}
{{- else if .Values.externalMongodb.enabled -}}
    {{- .Values.externalMongodb.password -}}
{{- end -}}
{{- end -}}

{{- define "bob.mongodb.password.secret" -}}
{{- if .Values.mongodb.enabled -}}
    {{- if .Values.mongodb.auth.existingSecret -}}
        {{- .Values.mongodb.auth.existingSecret -}}
    {{- else -}}
        {{- include "bob.mongodb.fullname" . -}}
    {{- end -}}
{{- else if .Values.externalMongodb.enabled -}}
    {{- .Values.externalMongodb.existingSecret -}}
{{- end -}}
{{- end -}}

{{- define "bob.mongodb.password.secret.key" -}}
{{- if .Values.mongodb.enabled -}}
    {{- printf "%s" "mongodb-root-password" -}}
{{- else if and .Values.externalMongodb.enabled .Values.externalMongodb.existingSecret -}}
    {{- if .Values.externalMongodb.existingSecretPasswordKey -}}
        {{- .Values.externalMongodb.existingSecretPasswordKey -}}
    {{- else -}}
        {{- printf "%s" "mongodb-password" -}}
    {{- end -}}
{{- end -}}
{{- end -}}

{{- define "bob.casdoor.fullname" -}}
{{- include "common.names.dependency.fullname" (dict "chartName" "casdoor" "chartValues" (index .Values "casdoor-helm-charts") "context" $) -}}
{{- end -}}

{{- define "bob.casdoor.address" -}}
http://{{- include "bob.casdoor.fullname" . -}}:{{- (index .Values "casdoor-helm-charts" "service" "port") -}}
{{- end -}}

{{- define "bob.common.env" }}
{{- if .Values.registry.host }}
- name: OCI_HOST
  value: {{ .Values.registry.host | quote }}
{{- end -}}
{{- if .Values.registry.username }}
- name: OCI_USERNAME
  value: {{ .Values.registry.username | quote }}
{{- end -}}
{{- if .Values.registry.password }}
- name: OCI_PASSWORD
  value: {{ .Values.registry.password | quote }}
{{- end -}}
{{- if .Values.s3.provider }}
- name: OBJECTSTORAGE_PROVIDER
  value: {{ .Values.s3.provider | quote }}
{{- if .Values.s3.address }}
- name: OBJECTSTORAGE_ADDRESS
  value: {{ .Values.s3.address | quote }}
{{- end -}}
{{- if .Values.s3.accessKey }}
- name: OBJECTSTORAGE_ACCESSKEY
  value: {{ .Values.s3.accessKey | quote }}
{{- end -}}
{{- if .Values.s3.secretKey }}
- name: OBJECTSTORAGE_SECRETKEY
  value: {{ .Values.s3.secretKey | quote }}
{{- end -}}
{{- if .Values.s3.region }}
- name: OBJECTSTORAGE_REGION
  value: {{ .Values.s3.region | quote }}
{{- end -}}
{{- if .Values.s3.pathStyle }}
- name: OBJECTSTORAGE_PATHSTYLE
  value: {{ .Values.s3.pathStyle | quote }}
{{- end -}}
{{- if .Values.s3.insecureSkipVerify }}
- name: OBJECTSTORAGE_INSECURESKIPVERIFY
  value: {{ .Values.s3.insecureSkipVerify | quote }}
{{- end -}}
{{- if .Values.email.enabled }}
- name: NOTIFY_EMAIL_ENABLED
  value: {{ .Values.email.enabled | quote }}
{{- end -}}
{{- if .Values.email.address }}
- name: NOTIFY_EMAIL_ADDRESS
  value: {{ .Values.email.address | quote }}
{{- end -}}
{{- if .Values.email.userName }}
- name: NOTIFY_EMAIL_USERNAME
  value: {{ .Values.email.userName | quote }}
{{- end -}}
{{- if .Values.email.password }}
- name: NOTIFY_EMAIL_PASSWORD
  value: {{ .Values.email.password | quote }}
{{- end -}}
{{- if .Values.email.insecure }}
- name: NOTIFY_EMAIL_INSECURE
  value: {{ .Values.email.insecure | quote }}
{{- end -}}
{{- if .Values.email.from }}
- name: NOTIFY_EMAIL_FROM
  value: {{ .Values.email.from | quote }}
{{- end -}}
{{- if .Values.ctz }}
- name: CTZ
  value: {{ .Values.ctz | quote }}
{{- end -}}
{{- end -}}

{{- if (index .Values "casdoor-helm-charts" "enabled") }}
- name: CASDOOR_ADDRESS
  value: {{ include "bob.casdoor.address" . | quote }}
- name: CASDOOR_CLIENTID
  value: {{ (index .Values "casdoor-helm-charts" "clientId") | quote }}
- name: CASDOOR_CLIENTSECRET
  value: {{ (index .Values "casdoor-helm-charts" "clientSecret") | quote }}
{{- end -}}

{{- end -}}

{{- define "bob.mongodb.env" }}
{{- if .Values.mongodb.enabled -}}
- name: MONGODB_ADDRESS
  value: {{ printf "%s:%s" (include "bob.mongodb.host" .) (include "bob.mongodb.port" .) | quote }}
{{- if (include "bob.mongodb.username" .) }}
- name: MONGODB_USERNAME
  value: {{ include "bob.mongodb.username" . }}
{{- end }}
{{- if (include "bob.mongodb.database" .) }}
- name: MONGODB_DATABASE
  value: {{ include "bob.mongodb.database" . }}
{{- end }}
- name: MONGODB_PASSWORD
{{- if (include "bob.mongodb.password.secret" . ) }}
  valueFrom:
    secretKeyRef:
      name: {{ include "bob.mongodb.password.secret" . }}
      key: {{ include "bob.mongodb.password.secret.key" . }}
{{- else }}
  value: {{ include "bob.mongodb.password" . | quote }}
{{- end }}
{{- end }}
{{- end }}


{{- define "bob.etcd.fullname" -}}
{{ template "common.names.dependency.fullname" (dict "chartName" "etcd" "chartValues" .Values.etcd "context" $) }}
{{- end -}}

{{- define "bob.etcd.servers" -}}
{{- range $i := until (int .Values.etcd.replicaCount) }}
  {{- if $i }}
    {{- print "," -}}
  {{- end }}
    {{- if (index $.Values "etcd" "auth" "client" "secureTransport") -}}
    {{- printf "https://%s-%d.%s-headless.%s:%s" (include "bob.etcd.fullname" $) $i (include "bob.etcd.fullname" $) $.Release.Namespace (include "bob.etcd.port" $) -}}
    {{- else -}}
    {{- printf "http://%s-%d.%s-headless.%s:%s" (include "bob.etcd.fullname" $) $i (include "bob.etcd.fullname" $) $.Release.Namespace (include "bob.etcd.port" $) -}}
    {{- end -}}

{{- end -}}
{{- end -}}


{{- define "bob.etcd.port" -}}
{{- .Values.etcd.service.ports.client -}}
{{- end -}}

{{- define "bob.etcd.env" -}}
{{- if .Values.etcd.enabled -}}
- name: ETCD_SERVERS
  value: {{ include "bob.etcd.servers" . | quote }}
{{- if .Values.etcd.auth.rbac.create }} 
- name: ETCD_USERNAME
  value: {{ .Values.etcd.auth.rbac.username | default "root" | quote }}
- name: ETCD_PASSWORD
{{- if .Values.etcd.auth.rbac.rootPassword }}
  value: {{ .Values.etcd.auth.rbac.rootPassword | quote }}
{{- else }}
  valueFrom:
    secretKeyRef:
      name: {{ .Values.etcd.auth.existingSecret | default (include "bob.etcd.fullname" .) }}
      key: {{ .Values.etcd.auth.existingSecretPasswordKey | default "etcd-root-password" }}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{/*
Return true if cert-manager required annotations for TLS signed certificates are set in the Ingress annotations
Ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
*/}}
{{- define "bob.apiserver.ingress.certManagerRequest" -}}
{{ if or (hasKey . "cert-manager.io/cluster-issuer") (hasKey . "cert-manager.io/issuer") }}
    {{- true -}}
{{- end -}}
{{- end -}}

{{/*
Compile all warnings into a single message.
*/}}
{{- define "bob.apiserver.validateValues" -}}
{{- $messages := list -}}
{{- $messages := append $messages (include "bob.apiserver.validateValues.foo" .) -}}
{{- $messages := append $messages (include "bob.apiserver.validateValues.bar" .) -}}
{{- $messages := without $messages "" -}}
{{- $message := join "\n" $messages -}}

{{- if $message -}}
{{-   printf "\nVALUES VALIDATION:\n%s" $message -}}
{{- end -}}
{{- end -}}