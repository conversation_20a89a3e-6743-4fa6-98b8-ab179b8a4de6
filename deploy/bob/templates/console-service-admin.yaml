apiVersion: v1
kind: Service
metadata:
  name: {{ include "bob.console.fullname" . }}-admin
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: console-admin
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.console.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.console.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.console.service.type }}
  {{- if or (eq .Values.console.service.type "LoadBalancer") (eq .Values.console.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.console.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.console.service.type "LoadBalancer") (not (empty .Values.console.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.console.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.console.service.type "LoadBalancer") (not (empty .Values.console.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.console.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.console.service.ports.http }}
      protocol: TCP
      targetPort: http
      {{- if and (or (eq .Values.console.service.type "NodePort") (eq .Values.console.service.type "LoadBalancer")) (not (empty .Values.console.service.nodePorts.http)) }}
      nodePort: {{ .Values.console.service.nodePorts.http }}
      {{- end }}
    {{- if .Values.console.metrics.enabled }}
    - name: metrics
      port: {{ .Values.console.metrics.service.port }}
      targetPort: metrics
      protocol: TCP
    {{- end }}
    {{- if .Values.console.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.console.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: console-admin
