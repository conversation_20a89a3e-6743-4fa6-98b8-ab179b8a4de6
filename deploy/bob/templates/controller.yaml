apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "bob.controller.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: controller
  {{- if or .Values.controller.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.controller.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.controller.replicaCount }}
  {{- if .Values.controller.updateStrategy }}
  strategy: {{- toYaml .Values.controller.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.controller.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: controller
  template:
    metadata:
      {{- if .Values.controller.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.controller.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: controller
    spec:
      {{- include "bob.controller.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "bob.controller.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.controller.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.controller.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.controller.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.controller.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.controller.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.controller.podAffinityPreset "component" "controller" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.controller.podAntiAffinityPreset "component" "controller" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.controller.nodeAffinityPreset.type "key" .Values.controller.nodeAffinityPreset.key "values" .Values.controller.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.controller.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.controller.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.controller.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.controller.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.controller.priorityClassName }}
      priorityClassName: {{ .Values.controller.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.controller.schedulerName }}
      schedulerName: {{ .Values.controller.schedulerName | quote }}
      {{- end }}
      {{- if .Values.controller.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.controller.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.controller.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.controller.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.controller.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.controller.terminationGracePeriodSeconds }}
      {{- end }}
      initContainers:
        {{- if .Values.controller.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.controller.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: controller
          image: {{ template "bob.controller.image" . }}
          imagePullPolicy: {{ .Values.controller.image.pullPolicy }}
          {{- if .Values.controller.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.controller.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.controller.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.controller.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - bob
            - controller
            - --leaderelection={{ .Values.controller.leaderElect }}
            - --v={{ .Values.controller.logLevel }}
            {{- if .Values.controller.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.controller.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.controller.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.controller.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- include "bob.common.env" . | nindent 12 }}
            {{- include "bob.mongodb.env" . | nindent 12 }}
            {{- include "bob.etcd.env" . | nindent 12 }}
            {{- if .Values.controller.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.controller.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.controller.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.controller.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.controller.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.controller.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.controller.resources }}
          resources: {{- toYaml .Values.controller.resources | nindent 12 }}
          {{- else if ne .Values.controller.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.controller.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.controller.containerPorts.http }}
            {{- if .Values.controller.extraContainerPorts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.controller.extraContainerPorts "context" $) | nindent 12 }}
            {{- end }}
          {{- if .Values.controller.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.controller.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.controller.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.controller.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.controller.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.controller.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.controller.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.controller.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.controller.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.controller.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.controller.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.controller.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.controller.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.controller.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
          {{- if .Values.controller.extraVolumeMounts }}
          {{- include "common.tplvalues.render" (dict "value" .Values.controller.extraVolumeMounts "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.controller.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.controller.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if .Values.controller.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.controller.extraVolumes "context" $) | nindent 8 }}
        {{- end }}