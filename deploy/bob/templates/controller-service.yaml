apiVersion: v1
kind: Service
metadata:
  name: {{ include "bob.controller.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver
  {{- if or .Values.controller.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.controller.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.controller.service.type }}
  {{- if and .Values.controller.service.clusterIP (eq .Values.controller.service.type "ClusterIP") }}
  clusterIP: {{ .Values.controller.service.clusterIP }}
  {{- end }}
  {{- if .Values.controller.service.sessionAffinity }}
  sessionAffinity: {{ .Values.controller.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.controller.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.controller.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.controller.service.type "LoadBalancer") (eq .Values.controller.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.controller.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.controller.service.type "LoadBalancer") (not (empty .Values.controller.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.controller.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.controller.service.type "LoadBalancer") (not (empty .Values.controller.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.controller.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.controller.service.ports.http }}
      {{- if and (or (eq .Values.controller.service.type "NodePort") (eq .Values.controller.service.type "LoadBalancer")) (not (empty .Values.controller.service.nodePorts.https)) }}
      nodePort: {{ .Values.controller.service.nodePorts.http }}
      {{- else if eq .Values.controller.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.controller.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.controller.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.controller.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: apiserver