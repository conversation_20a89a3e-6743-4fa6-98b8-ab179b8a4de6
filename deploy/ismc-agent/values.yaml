global:
  imageRegistry: "registry.cn-hangzhou.aliyuncs.com"
  imageRepository: "xiaoshiai"
  imagePullSecrets: []

agent:
  image:
    registry: registry.cn-hangzhou.aliyuncs.com
    repository: xiaoshiai/ismc-agent
    tag: latest
    pullPolicy: ""
    pullSecrets: []
  logLevel: 1
  hostNetwork: false

  extraArgs: []
  nodeAffinityPreset: {}
  podSecurityContext: {}
  containerSecurityContext: {}
  livenessProbe:
    enabled: true
    initialDelaySeconds: 5
    periodSeconds: 30
  readinessProbe:
    enabled: true
    initialDelaySeconds: 10
    timeoutSeconds: 1
    periodSeconds: 30
    failureThreshold: 3
    successThreshold: 1
  startupProbe: {}
  resources:
    limits: {}
    requests: {}
  service:
    ports:
      http: 80
    nodePorts: {}
  serviceAccount:
    create: true
  rbac:
    create: true

monitoring:
  enabled: true

servicemonitor:
  enabled: false

ismc:
  server: ""
  deviceID: ""
  deviceToken: ""

kubernetes:
  enabled: true

vmware:
  address: ""
  username: ""
  password: ""

aliyun:
  accessKey: ""
  secretKey: ""

huawei:
  globalDomain: ""
  region: ""
  username: ""
  password: ""
  proxy: ""
  defaultProject: ""
