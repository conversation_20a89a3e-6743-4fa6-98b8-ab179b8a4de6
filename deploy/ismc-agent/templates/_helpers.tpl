{{- define "common.images.image2" -}}
{{- $registryName := .imageRoot.registry -}}
{{- $repositoryName := .imageRoot.repository -}}
{{- $tag := .imageRoot.tag | toString -}}
{{- if or (not $tag) (eq $tag "latest") -}}
    {{- if hasPrefix "v" .root.Chart.AppVersion  -}}
        {{- $tag = printf "%s" .root.Chart.AppVersion | toString -}}
    {{- else -}}
        {{- $tag = printf "v%s" .root.Chart.AppVersion | toString -}}
    {{- end -}}
{{- end -}}
{{- if .global.imageRegistry }}
    {{- $registryName = .global.imageRegistry -}}
{{- end -}}
{{- if $registryName }}
    {{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- else -}}
    {{- printf "%s:%s" $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{- define "ismc.agent.fullname" -}}
{{ template "common.names.fullname" . }}
{{- end -}}

{{- define "ismc.agent.image" -}}
{{ include "common.images.image2" (dict "imageRoot" .Values.agent.image "root" . "global" .Values.global) }}
{{- end -}}

{{- define "ismc.agent.imagePullSecrets" -}}
{{- include "common.images.renderPullSecrets" (dict "images" (list .Values.agent.image) "context" $) -}}
{{- end -}}

{{- define "ismc.agent.serviceAccountName" -}}
{{- if .Values.agent.serviceAccount.create -}}
    {{ default (include "ismc.agent.fullname" .) .Values.agent.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.agent.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{- define "ismc.agent.env" -}}
{{- if .Values.kubernetes.enabled }}
- name: KUBERNETES_ENABLED
  value: "true"
{{- end -}}
{{- if .Values.vmware.address }}
- name: CLOUD_TYPE
  value: vmware
{{- range $key, $value := .Values.vmware }}
- name: CLOUD_VMWARE_{{ $key | upper }}
  value: {{ $value }}
{{- end -}}
{{- else if .Values.aliyun.accessKey }}
- name: CLOUD_TYPE
  value: aliyun
{{- range $key, $value := .Values.aliyun }}
- name: CLOUD_ALIYUN_{{ $key | upper }}
  value: {{ $value }}
{{- end -}}
{{- end -}}
{{- if .Values.huawei.password  }}
- name: CLOUD_TYPE
  value: huawei
{{- range $key, $value := .Values.huawei }}
- name: CLOUD_HUAWEI_{{ $key | upper }}
  value: {{ $value }}
{{- end -}}
{{- end -}}
{{- end -}}

{{- define "ismc.agent.args" -}}
{{- if .Values.kubernetes.enabled }}
- --kubernetes-enabled
{{- end }}
{{- if .Values.vmware.address }}
- --cloud-type=vmware
{{- range $key, $value := .Values.vmware }}
- --cloud-vmware-{{ $key }}={{ $value }}
{{- end }}
{{- end }}
{{- if .Values.aliyun.accessKey }}
- --cloud-type=aliyun
{{- range $key, $value := .Values.aliyun }}
- --cloud-aliyun-{{ $key }}={{ $value }}
{{- end }}
{{- end }}
{{- if .Values.huawei.password  }}
- --cloud-type=huawei
{{- range $key, $value := .Values.huawei }}
- --cloud-huawei-{{ $key }}={{ $value }}
{{- end }}
{{- end }}
{{- end }}
