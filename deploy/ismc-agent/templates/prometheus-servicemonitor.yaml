{{- if .Values.servicemonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "ismc.agent.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels:
    app.kubernetes.io/component: agent
spec:
  endpoints:
    - interval: 2m
      path: /metrics
      port: http
      scheme: http
      scrapeTimeout: 1m
      tlsConfig:
        insecureSkipVerify: true
  namespaceSelector:
    matchNames:
    - {{ include "common.names.namespace" . | quote }}
  selector:
    matchLabels:
      app.kubernetes.io/component: agent
{{- end }}