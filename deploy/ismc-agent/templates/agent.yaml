apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ include "ismc.agent.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: agent
  {{- if or .Values.agent.deploymentAnnotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.agent.deploymentAnnotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.agent.replicaCount }}
  {{- if .Values.agent.updateStrategy }}
  strategy: {{- toYaml .Values.agent.updateStrategy | nindent 4 }}
  {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.agent.podLabels .Values.commonLabels) "context" .) }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 6 }}
      app.kubernetes.io/component: agent
  template:
    metadata:
      {{- if .Values.agent.podAnnotations }}
      annotations: {{- include "common.tplvalues.render" (dict "value" .Values.agent.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
      labels: {{- include "common.labels.standard" ( dict "customLabels" $podLabels "context" $ ) | nindent 8 }}
        app.kubernetes.io/component: agent
    spec:
      {{- include "ismc.agent.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ include "ismc.agent.serviceAccountName" . }}
      automountServiceAccountToken: {{ .Values.agent.serviceAccount.automountServiceAccountToken }}
      {{- if .Values.agent.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.agent.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.agent.affinity }}
      affinity: {{- include "common.tplvalues.render" ( dict "value" .Values.agent.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.agent.podAffinityPreset "component" "agent" "customLabels" $podLabels "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.agent.podAntiAffinityPreset "component" "agent" "customLabels" $podLabels "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.agent.nodeAffinityPreset.type "key" .Values.agent.nodeAffinityPreset.key "values" .Values.agent.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.agent.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" ( dict "value" .Values.agent.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.agent.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.agent.tolerations "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.agent.priorityClassName }}
      priorityClassName: {{ .Values.agent.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.agent.schedulerName }}
      schedulerName: {{ .Values.agent.schedulerName | quote }}
      {{- end }}
      {{- if .Values.agent.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.agent.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.agent.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.agent.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.agent.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.agent.terminationGracePeriodSeconds }}
      {{- end }}
      hostNetwork: {{ .Values.agent.hostNetwork }}
      initContainers:
        {{- if .Values.agent.initContainers }}
          {{- include "common.tplvalues.render" (dict "value" .Values.agent.initContainers "context" $) | nindent 8 }}
        {{- end }}
      containers:
        - name: agent
          image: {{ template "ismc.agent.image" . }}
          imagePullPolicy: {{ .Values.agent.image.pullPolicy }}
          {{- if .Values.agent.containerSecurityContext.enabled }}
          securityContext: {{- include "common.compatibility.renderSecurityContext" (dict "secContext" .Values.agent.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.agent.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.agent.command "context" $) | nindent 12 }}
           {{- else }}
          command:
            - ismc-agent
            - --listen=:{{ .Values.agent.service.ports.http }}
            - --v={{ .Values.agent.logLevel }}
            - --ismc-server={{ .Values.ismc.server }}
            - --ismc-deviceid={{ .Values.ismc.deviceID }}
            - --ismc-token={{ .Values.ismc.deviceToken }}
            {{- include "ismc.agent.args" .  | nindent 12 }}
            {{- if .Values.agent.extraArgs }}
            {{- include "common.tplvalues.render" (dict "value" .Values.agent.extraArgs "context" $) | nindent 12 }}
            {{- end }}
          {{- end }}
          {{- if .Values.agent.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.agent.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            {{- if .Values.agent.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.agent.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.agent.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.agent.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.agent.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.agent.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.agent.resources }}
          resources: {{- toYaml .Values.agent.resources | nindent 12 }}
          {{- else if ne .Values.agent.resourcesPreset "none" }}
          resources: {{- include "common.resources.preset" (dict "type" .Values.agent.resourcesPreset) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: {{ .Values.agent.service.ports.http }}
          {{- if .Values.agent.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.agent.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.agent.livenessProbe.enabled }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.agent.livenessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.agent.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.agent.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.agent.readinessProbe.enabled }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.agent.readinessProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.agent.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.agent.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.agent.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.agent.startupProbe "enabled") "context" $) | nindent 12 }}
            httpGet:
              path: /healthz
              port: http
          {{- end }}
          {{- if .Values.agent.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.agent.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
        {{- if .Values.agent.sidecars }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.agent.sidecars "context" $) | nindent 8 }}
        {{- end }}