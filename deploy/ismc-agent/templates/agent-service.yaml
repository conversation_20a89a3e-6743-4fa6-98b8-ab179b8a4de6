apiVersion: v1
kind: Service
metadata:
  name: {{ include "ismc.agent.fullname" . }}
  namespace: {{ include "common.names.namespace" . | quote }}
  labels: {{- include "common.labels.standard" ( dict "customLabels" .Values.commonLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: agent
  {{- if or .Values.agent.service.annotations .Values.commonAnnotations }}
  {{- $annotations := include "common.tplvalues.merge" (dict "values" (list .Values.agent.service.annotations .Values.commonAnnotations) "context" .) }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" $annotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.agent.service.type }}
  {{- if and .Values.agent.service.clusterIP (eq .Values.agent.service.type "ClusterIP") }}
  clusterIP: {{ .Values.agent.service.clusterIP }}
  {{- end }}
  {{- if .Values.agent.service.sessionAffinity }}
  sessionAffinity: {{ .Values.agent.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.agent.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.agent.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  {{- if or (eq .Values.agent.service.type "LoadBalancer") (eq .Values.agent.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.agent.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.agent.service.type "LoadBalancer") (not (empty .Values.agent.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.agent.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.agent.service.type "LoadBalancer") (not (empty .Values.agent.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.agent.service.loadBalancerIP }}
  {{- end }}
  ports:
    - name: http
      port: {{ .Values.agent.service.ports.http }}
      {{- if and (or (eq .Values.agent.service.type "NodePort") (eq .Values.agent.service.type "LoadBalancer")) (not (empty .Values.agent.service.nodePorts.http)) }}
      nodePort: {{ .Values.agent.service.nodePorts.http }}
      {{- else if eq .Values.agent.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.agent.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.agent.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  {{- $podLabels := include "common.tplvalues.merge" (dict "values" (list .Values.agent.podLabels .Values.commonLabels) "context" .) | fromYaml }}
  selector: {{- include "common.labels.matchLabels" ( dict "customLabels" $podLabels "context" $ ) | nindent 4 }}
    app.kubernetes.io/component: agent