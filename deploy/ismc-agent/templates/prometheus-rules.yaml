
{{- if .Values.monitoring.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: ismc-default-record-rule
  namespace: kubegems-monitoring
  labels:
    prometheusrule.kubegems.io/name: ismc-default-record-rule
spec:
  groups:
    - name: ismc
      rules:
        - record: pod_workload
          expr: |
            sum(label_replace(label_join(label_replace(kube_pod_owner{job="kube-state-metrics"},"owner_kind", "Deployment", "owner_kind", "ReplicaSet"), "workload",":","owner_kind","owner_name"), "workload","$1","workload","(Deployment:.+)-(.+)"))
            by(namespace, pod, owner_kind, workload)
        # helm 部署的应用会增加 label app.kubernetes.io/instance 通过这个 label 来区分不同的应用
        - record: ismc_pod_app
          expr: |
            sum(label_replace(kube_pod_labels, "instance","$1","label_app_kubernetes_io_instance","(.*)")) by(namespace, pod, instance) * on (namespace,pod) group_right(instance) pod_workload
        - record: ismc_pod_health
          expr: |
            sum(kube_pod_status_ready{condition="true"}) by(namespace, pod) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        # cpu
        - record: ismc_pod_cpu_used_cores
          expr: |
            sum(irate(container_cpu_usage_seconds_total{pod!="", image!="", container!=""}[5m]))by(pod, namespace, node) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_cpu_limit_cores
          expr: |
            sum(kube_pod_container_resource_limits{resource="cpu"})by(pod, namespace, node) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_cpu_usage_percent
          expr: |
            (ismc_pod_cpu_used_cores / ismc_pod_cpu_limit_cores) * 100
        # memory
        - record: ismc_pod_memory_used_bytes
          expr: |
            sum(container_memory_working_set_bytes{pod!="", image!="", container!=""})by(pod, namespace, node) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_memory_limit_bytes
          expr: |
            sum(kube_pod_container_resource_limits{resource="memory"})by(pod, namespace, node) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_memory_usage_percent
          expr: |
            (ismc_pod_memory_used_bytes / ismc_pod_memory_limit_bytes) * 100
        # network
        - record: ismc_pod_network_receive_bytes_per_5m
          expr: |
            sum(irate(container_network_receive_bytes_total{pod!="", image!=""}[5m]))by(pod, namespace, node) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_network_send_bytes_per_5m
          expr: |
            sum(irate(container_network_transmit_bytes_total{pod!="", image!=""}[5m]))by(pod, namespace, node) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        # performance
        - record: ismc_pod_calls_rate
          expr: |
            sum(rate(calls_total{}[1m]))by(pod, namespace, node,service_name) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_calls_errors_rate
          expr: |
            sum(rate(calls_total{status_code="STATUS_CODE_ERROR"}[5m])>0 / rate(calls_total{}[5m])>0) by (pod,namespace,service_name) * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        - record: ismc_pod_calls_latency_ms
          expr: |
            (sum(rate(latency_bucket{}[5m]))by(le,pod,namespace,service_name))   * on (namespace, pod) group_left(instance,workload) ismc_pod_app
        # storage
        - record: pvc_workload
          expr: |
            sum(label_replace(kube_persistentvolumeclaim_labels, "instance","$1","label_app_kubernetes_io_instance","(.*)")) by (namespace,persistentvolumeclaim,instance)
        - record: ismc_pvc_storage_used_bytes
          expr: |
            pvc_workload * on (namespace,persistentvolumeclaim) group_right (instance) kubelet_volume_stats_used_bytes
        - record: ismc_pvc_storage_total_bytes
          expr: |
            pvc_workload * on (namespace,persistentvolumeclaim) group_right (instance) kubelet_volume_stats_capacity_bytes
        - record: ismc_node_inode_total
          expr: |
            pvc_workload * on (namespace,persistentvolumeclaim) group_right (instance) kubelet_volume_stats_inodes
        - record: ismc_node_inode_used
          expr: |
            pvc_workload * on (namespace,persistentvolumeclaim) group_right (instance) kubelet_volume_stats_inodes_used
        
{{- end }}