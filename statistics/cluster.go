package statistics

import (
	"context"
	"net/http"

	corev1 "k8s.io/api/core/v1"
	storagev1 "k8s.io/api/storage/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	cloud "xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/resourcequota"
)

type ContainerClusterStatistics struct {
	ConfigMapCount   int `json:"configMapCount"`
	SecretCount      int `json:"secretCount"`
	ServiceCount     int `json:"serviceCount"`
	DeploymentCount  int `json:"deploymentCount"`
	StatefulSetCount int `json:"statefulSetCount"`
	DaemonSetCount   int `json:"daemonSetCount"`
	PodCount         int `json:"podCount"`
	JobsCount        int `json:"jobsCount"`
	NodeCount        int `json:"nodeCount"`
	CronJobsCount    int `json:"cronJobsCount"`
}

type VirtualMachineClusterStatistics struct {
	NodeCount           int `json:"nodeCount"`
	VirtualMachineCount int `json:"virtualMachineCount"`
	DiskCount           int `json:"diskCount"`
	NetworkClassCount   int `json:"networkClassCount"`
	ZoneCount           int `json:"zoneCount"`
	HostCount           int `json:"hostCount"`
}

func (a *API) ClusterStatistics(w http.ResponseWriter, r *http.Request) {
	a.OnClusterOrTenantScopedCluster(w, r, func(ctx context.Context, storage store.Store, ref store.ObjectReference) (any, error) {
		info, err := a.ClusterInfo.Get(ctx, ref)
		if err != nil {
			return nil, err
		}
		switch cluster.ClusterCategoryFrom(info.Type()) {
		case cluster.ClusterCategoryContainer:
			return a.CalcK8sClusterStatistics(ctx, info)
		case cluster.ClusterCategoryVirtualMachine:
			return a.CalcVirtualMachineClusterStatistics(ctx, info)
		default:
			return ContainerClusterStatistics{}, nil
		}
	})
}

func (a *API) GetClusterStatisticsResources(w http.ResponseWriter, r *http.Request) {
	a.OnClusterOrTenantScopedCluster(w, r, func(ctx context.Context, storage store.Store, ref store.ObjectReference) (any, error) {
		return a.getClusterStatisticsResources(ctx, ref)
	})
}

func (a *API) OnClusterOrTenantScopedCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, ref store.ObjectReference) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		cluster := api.Path(r, "cluster", "")
		ref := store.ObjectReference{Name: cluster}
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			ref.Scopes = append(ref.Scopes, base.ScopeTenant(tenant))
		}
		return fn(ctx, storage, ref)
	})
}

type ClusterStatisticsResources struct {
	Capacity base.ResourceList `json:"capacity"` // capacity = sum(node.status.capacity)
	Reserved base.ResourceList `json:"reserved"` // reserved = sum(node.status.capacity) where node.Labels[base.LabelPrivateNode] == base.LableValueTrue

	Allocatable     base.ResourceList `json:"allocatable"` // allocatable  = （capacity - reserved） * (1 + overSellingRate)
	Allocated       base.ResourceList `json:"allocated"`   // allocated = sum(resourceQuota.hard)
	Available       base.ResourceList `json:"available"`   // available = allocatable - allocated
	OverSellingRate float64           `json:"overSellingRate"`
}

func (a *API) getClusterStatisticsResources(ctx context.Context, ref store.ObjectReference) (*ClusterStatisticsResources, error) {
	c := &cluster.Cluster{}
	if err := a.Store.Scope(ref.Scopes...).Get(ctx, ref.Name, c); err != nil {
		return nil, err
	}
	overSellingRate := c.OverSellingRate

	info, err := a.ClusterInfo.Get(ctx, ref)
	if err != nil {
		return nil, err
	}

	clusterResources := &ClusterStatisticsResources{
		Capacity:        base.ResourceList{},
		Reserved:        base.ResourceList{},
		Allocated:       corev1.ResourceList{},
		OverSellingRate: overSellingRate,
	}

	// allocated resources under cluster
	resourceQuotas := &store.List[resourcequota.ResourceQuota]{}
	if err := a.Store.List(ctx, resourceQuotas,
		store.WithFieldRequirements(store.RequirementEqual("name", resourcequota.ClusterReferenceToQuotaName(ref))),
		store.WithSubScopes()); err != nil {
		return nil, err
	}
	for _, resourceQuota := range resourceQuotas.Items {
		if !resourceQuota.Cluster.Equals(ref) {
			continue
		}
		// only calc tenant level resource quota
		if len(resourceQuota.Scopes) == 1 && resourceQuota.Scopes[0].Resource == "tenants" {
			base.AddResourceList(clusterResources.Allocated, resourceQuota.Hard)
		}
		// calc organization level resource quota if tenant cluster
		if len(ref.Scopes) != 0 && len(resourceQuota.Scopes) == 2 &&
			resourceQuota.Scopes[0].Resource == "tenants" &&
			resourceQuota.Scopes[1].Resource == "organizations" {
			base.AddResourceList(clusterResources.Allocated, resourceQuota.Hard)
		}
	}

	switch cluster.ClusterCategoryFrom(info.Type()) {
	case cluster.ClusterCategoryContainer:
		kueclients, err := info.KubernetesConfig()
		if err != nil {
			return nil, err
		}
		cli := kueclients.Client
		nodelist := &corev1.NodeList{}
		if err := cli.List(ctx, nodelist); err != nil {
			return nil, err
		}
		for _, node := range nodelist.Items {
			if node.Labels != nil && node.Labels[base.LabelPrivateNode] == base.ValueTrue {
				base.AddResourceList(clusterResources.Reserved, node.Status.Capacity)
			}
			base.AddResourceList(clusterResources.Capacity, node.Status.Capacity)
		}

		// calc storage capacity
		storageClassList := &storagev1.StorageClassList{}
		if err := cli.List(ctx, storageClassList); err != nil {
			log.FromContext(ctx).Error(err, "failed to list storage classes")
		}
		for _, storageClass := range storageClassList.Items {
			if metadata := cluster.ResourceMetadataFrom(&storageClass); !metadata.Capacity.IsZero() {
				base.AddResourceList(clusterResources.Capacity, corev1.ResourceList{
					corev1.ResourceStorage: metadata.Capacity,
				})
			}
		}

	case cluster.ClusterCategoryVirtualMachine:
		provider, err := info.CloudProvider()
		if err != nil {
			return nil, err
		}
		hostslist, err := provider.ListHosts(ctx, cloud.ListHostOptions{})
		if err != nil {
			return nil, err
		}
		for _, zone := range hostslist.Items {
			for resourcename, quality := range zone.Status.Capacity {
				if corev1.ResourceName(resourcename) == corev1.ResourceStorage {
					continue
				}
				if val, ok := clusterResources.Capacity[corev1.ResourceName(resourcename)]; !ok {
					clusterResources.Capacity[corev1.ResourceName(resourcename)] = quality
				} else {
					val.Add(quality)
					clusterResources.Capacity[corev1.ResourceName(resourcename)] = val
				}
			}
		}
		diskclusterlist, err := provider.ListDiskClusters(ctx, cloud.ListDiskClusterOptions{})
		if err != nil {
			return nil, err
		}
		for _, diskcluster := range diskclusterlist.Items {
			if val, ok := clusterResources.Capacity[corev1.ResourceName("storage")]; !ok {
				clusterResources.Capacity[corev1.ResourceName("storage")] = diskcluster.Status.Total
			} else {
				val.Add(diskcluster.Status.Total)
				clusterResources.Capacity[corev1.ResourceName("storage")] = val
			}
		}

	}
	// allocatable = (capacity - reserved) * (1 + overSellingRate)
	clusterResources.Allocatable = clusterResources.Capacity.DeepCopy()
	base.SubResourceList(clusterResources.Allocatable, clusterResources.Reserved)
	if overSellingRate != 0 {
		base.ResourceListCollect(clusterResources.Allocatable, clusterResources.Allocatable, func(rn corev1.ResourceName, q1 *resource.Quantity, q2 resource.Quantity) {
			q1.Set(int64(float64(q1.Value()) * float64(1+overSellingRate)))
		})
	}
	// available = allocatable - allocated
	clusterResources.Available = clusterResources.Allocatable.DeepCopy()
	base.SubResourceList(clusterResources.Available, clusterResources.Allocated)

	return clusterResources, nil
}

func (a *API) CalcK8sClusterStatistics(ctx context.Context, info cluster.CloudInfo) (*ContainerClusterStatistics, error) {
	op, err := cluster.NewContainerOperation(info)
	if err != nil {
		return nil, err
	}
	stat := &ContainerClusterStatistics{}
	if err := op.Statistics(ctx, stat); err != nil {
		return nil, err
	}
	return stat, nil
}

func (a *API) CalcVirtualMachineClusterStatistics(ctx context.Context, info cluster.CloudInfo) (*VirtualMachineClusterStatistics, error) {
	provider, err := info.CloudProvider()
	if err != nil {
		return nil, err
	}
	systemstatics, err := provider.GetSystemStatistics(ctx)
	if err != nil {
		return nil, err
	}
	statistics := &VirtualMachineClusterStatistics{
		NodeCount:           systemstatics.ZoneCount,
		VirtualMachineCount: systemstatics.VirtualMachineCount,
		DiskCount:           systemstatics.DiskClusterCount,
		NetworkClassCount:   systemstatics.VirtualNetworkCount,
		ZoneCount:           systemstatics.ZoneCount,
		HostCount:           systemstatics.HostCount,
	}
	return statistics, nil
}

func (a *API) clusterStatisticsGroup() api.Group {
	return cluster.
		NewAllClusterGroup(api.NewGroup("/statistics").
			Route(
				api.GET("").
					Doc("Get cluster statistics").
					To(a.ClusterStatistics).
					Response(ContainerClusterStatistics{}),
				api.GET("/resources").
					Doc("Get cluster statistics resources").
					To(a.GetClusterStatisticsResources).
					Response(ClusterStatisticsResources{}),
			),
		)
}
