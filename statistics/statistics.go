package statistics

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
)

func NewAPI(base base.API, clusterinfo cluster.CloudInfoGetter, userinfo auth.UserProvider) *API {
	return &API{API: base, ClusterInfo: clusterinfo, UserProvider: userinfo}
}

type API struct {
	base.API
	UserProvider auth.UserProvider
	ClusterInfo  cluster.CloudInfoGetter
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Statistics").
		SubGroup(
			a.globalStatisticsGroup(),
			a.tenantStatisticsGroup(),
			a.clusterStatisticsGroup(),
			a.tenantOrganizationStatisticsGroup(),
		)
}
