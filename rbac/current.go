package rbac

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type UserRoleView struct {
	UserRole     `json:",inline"`
	Role         string `json:"role"`
	Tenant       string `json:"tenant"`
	Organization string `json:"organization"`
}

func (a *API) CurrentRoles(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		list := store.List[UserRole]{}
		options := []store.ListOption{
			store.WithSubScopes(),
			store.WithFieldRequirementsFromSet(map[string]string{"name": username}),
		}
		if err := storage.List(ctx, &list, options...); err != nil {
			return nil, err
		}

		items := make([]UserRoleView, 0, len(list.Items))
		for _, item := range list.Items {
			roleview := UserRoleView{UserRole: item}
			if len(item.Roles) != 0 {
				roleview.Role = item.Roles[0]
			}
			for _, scope := range item.Scopes {
				if scope.Resource == "tenants" {
					roleview.Tenant = scope.Name
				}
				if scope.Resource == "organizations" {
					roleview.Organization = scope.Name
				}
			}
			items = append(items, roleview)
		}
		items = a.removeInvalidRoles(ctx, items)
		return items, nil
	})
}

func (a *API) removeInvalidRoles(ctx context.Context, items []UserRoleView) []UserRoleView {
	var pos int
	for i, item := range items {
		if tenantname := item.Tenant; tenantname != "" {
			t := &store.Unstructured{}
			t.SetResource("tenants")
			if err := a.Store.Get(ctx, tenantname, t); err != nil {
				// remove invalid tenant
				continue
			}
			if orgname := item.Organization; orgname != "" {
				org := &store.Unstructured{}
				org.SetResource("organizations")
				if err := a.Store.Scope(base.ScopeTenant(tenantname)).Get(ctx, orgname, org); err != nil {
					// remove invalid organization
					continue
				}
			}
		}
		items[pos] = items[i]
		pos++
	}
	return items[:pos]
}

func (a *API) currentGroup() api.Group {
	return api.
		NewGroup("/current").
		Route(
			api.GET("/roles").
				To(a.CurrentRoles).
				Response(
					[]UserRole{}, "all roles",
				),
		)
}
