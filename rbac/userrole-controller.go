package rbac

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func NewUserRoleController(storage store.Store) (*controller.Controller, error) {
	rec := &UserRoleController{
		Client: storage,
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("userroles-controller"),
	)
	c := controller.
		NewController("userroles", br).
		Watch(controller.NewStoreSource(storage, &UserRole{}, rec.OnlyTenantScopeUserRole))
	return c, nil
}

func (c *UserRoleController) OnlyTenantScopeUserRole(kind store.WatchEventType, obj store.Object) bool {
	scopes := obj.GetScopes()
	return len(scopes) == 1 && scopes[0].Resource == "tenants"
}

type UserRoleController struct {
	Client store.Store
}

// Sync implements Reconciler.
func (c *UserRoleController) Sync(ctx context.Context, userrole *UserRole) (controller.Result, error) {
	return controller.Result{}, nil
}

// Remove implements Reconciler.
func (c *UserRoleController) Remove(ctx context.Context, userrole *UserRole) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, userrole, base.ScopeHandler[*UserRole]{
		OnTenant: c.onTenantUserRoleRemovedR,
	})
}

func (c *UserRoleController) onTenantUserRoleRemovedR(ctx context.Context, tenant string, userrole *UserRole) (controller.Result, error) {
	return controller.Result{}, c.onTenantUserRoleRemoved(ctx, tenant, userrole)
}

func (c *UserRoleController) onTenantUserRoleRemoved(ctx context.Context, tenant string, userrole *UserRole) error {
	// it tenant userrole is removed, we should remove the userrole from all the users in the tenant's organizations.
	organizationlist := store.List[UserRole]{}
	if err := c.Client.Scope(base.ScopeTenant(tenant)).List(ctx, &organizationlist, store.WithSubScopes()); err != nil {
		return err
	}
	log := log.FromContext(ctx)
	var errs []error
	for _, item := range organizationlist.Items {
		thistenant, org := base.TenantOrganizationFromScopes(item.Scopes...)
		if tenant != thistenant || org == "" || item.Name != userrole.Name {
			continue
		}
		log.Info("remove userrole from organization", "tenant", tenant, "org", org, "user", item.Name)
		if err := store.IgnoreNotFound(c.Client.Scope(item.Scopes...).Delete(ctx, &item)); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}
