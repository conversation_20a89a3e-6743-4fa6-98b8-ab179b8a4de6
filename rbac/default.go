package rbac

import (
	"context"

	"xiaoshiai.cn/common/store"
)

const (
	RoleAdmin  = "admin"
	RoleMember = "member"
)

var DefaultSystemRoles = []Role{
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleAdmin,
			Alias:       "systemadmin",
			Description: "Admin has full access to all resources",
		},
		Authorities: []Authority{{Actions: []string{"*"}, Resources: []string{"**"}}},
	},
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleMember,
			Alias:       "systemviewer",
			Description: "Viewer can read all resources",
		},
		Authorities: []Authority{
			{
				Actions: []string{"*"},
				Resources: []string{
					"ticket-modules", "ticket-modules:**",
					"tickets", "tickets:**",
				},
			},
			{
				Actions:   []string{"get", "list"},
				Resources: []string{"**"},
			},
			{
				Actions: []string{"*"},
				Resources: []string{
					"statistics",
					"users", "users:**",
					"tenants", "tenants:*", // allow manage tenant but tenant operations.
					"tenants:*:users", "tenants:*:users:*",
					"tenants:*:resourcequotas", "tenants:*:resourcequotas:*",
					"admin-products", "admin-products:**",
					"templategroups", "templategroups:**",
					"system-channels", "system-channels:**",
				},
			},
		},
	},
}

var DefaultTenantRoles = []Role{
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleAdmin,
			Alias:       "tenantadmin",
			Description: "Admin has full access to all tenant resources",
		},
		Authorities: []Authority{{Actions: []string{"*"}, Resources: []string{"**"}}},
	},
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleMember,
			Description: "Member can read organizations",
		},
		Authorities: []Authority{
			{
				Actions: []string{"*"},
				Resources: []string{
					"tickets", "tickets:*",
				},
			},
			{
				Actions: []string{"get", "list"},
				Resources: []string{
					"statistics",
					"organizations", "organizations:*",
					"alertchannels", "alertchannels:*", // organization use tenant's resources
					"users", "users:*", // allow organization add tenant's users to organization
					"wallet", "wallet:*", // organization can see how much money tenant has
					"resourcepools", "resourcepools:*",
					"resourcequotas", "resourcequotas:*",

					// allow see tenant's quota
					"clusters:*:resourcequotas", "clusters:*:resourcequotas:*",
					// application deployment load metadata
					"clusters:*:metadata", "clusters:*:metadata:*",
					"system-clustermetadata:**",
					"local-clustermetadata:**",
				},
			},
		},
	},
}

var DefaultOrganizationRoles = []Role{
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleAdmin,
			Alias:       "organizationadmin",
			Description: "Admin has full access to all resources under the organization",
		},
		Authorities: []Authority{
			{
				Actions: []string{"*"}, Resources: []string{"**"},
			},
		},
	},
	{
		ObjectMeta: store.ObjectMeta{
			Name:        RoleMember,
			Alias:       "organizationmember",
			Description: "Member can operation basic organization resources",
		},
		Authorities: []Authority{
			{
				Actions: []string{"*"},
				Resources: []string{
					"applications:**",
					"licenses:**",
					"objectstorages:**",
					"orders:**",
					"databases:**",
				},
			},
			{
				Actions: []string{"get", "list"},
				Resources: []string{
					"statistics",
					"resourcepools:**",
					"resourcequotas:**",
					"alertchannels:**",
					"users:**",
					"gateways:**",
					"system-clustermetadata:**",
					"local-clustermetadata:**",
					"credentials:**",
					"bills:**",
					"orderreports:**",
				},
			},
		},
	},
}

func InitRoles(ctx context.Context, s store.Store, role []Role, replace bool) error {
	for _, role := range role {
		// check if role exists
		if !replace {
			if err := store.CreateIfNotExists(ctx, s, &role); err != nil {
				return err
			}
		} else {
			exists := &Role{ObjectMeta: role.ObjectMeta}
			updatefunc := func() error {
				exists.Authorities = role.Authorities
				return nil
			}
			if err := store.CreateOrUpdate(ctx, s, exists, updatefunc); err != nil {
				return err
			}
		}
	}
	return nil
}
