package rbac

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/message/events"
)

func NewRBACAPIGroup(base base.API, users auth.UserProvider, recorder events.Recorder, scopePathVarNames ...base.ScopeVar) api.Group {
	return ScopedRbacAPI{
		API:               base,
		UserProvider:      users,
		recoreder:         recorder,
		ScopePathVarNames: scopePathVarNames,
	}.Group()
}

type ScopedRbacAPI struct {
	base.API
	ScopePathVarNames []base.ScopeVar
	UserProvider      auth.UserProvider
	recoreder         events.Recorder
}

func (a ScopedRbacAPI) Group() api.Group {
	prefix := ""
	for _, val := range a.ScopePathVarNames {
		prefix += "/" + val.Resource + "/{" + val.PathVarName + "}"
	}
	return api.
		NewGroup(prefix).
		SubGroup(
			a.rolesGroup(),
			// a.userGroupsGroup(),
			a.userRolesGroup(),
		)
}
