package rbac

import (
	"context"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func GetAttributesResource(a api.Attributes, resource string) string {
	for _, r := range a.Resources {
		if r.Resource == resource {
			return r.Name
		}
	}
	return ""
}

// IsTenantMember checks if the user is allowed to access the tenant
func IsTenantMember(storage store.Store) api.AuthorizerFunc {
	return func(ctx context.Context, user api.UserInfo, a api.Attributes) (api.Decision, string, error) {
		tenant := GetAttributesResource(a, "tenants")
		if tenant == "" {
			return api.DecisionNoOpinion, "", nil
		}
		userroles := store.List[UserRole]{}
		if err := storage.Scope(base.ScopeTenant(tenant)).List(ctx, &userroles); err != nil {
			return api.DecisionDeny, "", err
		}
		if len(userroles.Items) == 0 {
			return api.DecisionNoOpinion, "", nil
		}
		return api.DecisionAllow, "", nil
	}
}

func ListSystemAdmin(ctx context.Context, storage store.Store) ([]string, error) {
	list := store.List[UserRole]{}
	if err := storage.List(ctx, &list); err != nil {
		return nil, err
	}
	var userlist []string
	for _, rb := range list.Items {
		var role string
		if len(rb.Roles) > 0 {
			role = rb.Roles[0]
		}
		if role == "admin" {
			userlist = append(userlist, rb.Name)
		}
	}
	return userlist, nil
}

func ListTenantOrOrganizationAdmin(ctx context.Context, storage store.Store, scopes ...store.Scope) ([]string, error) {
	list := store.List[UserRole]{}
	userstore := storage.Scope(scopes...)
	if err := userstore.List(ctx, &list); err != nil {
		return nil, err
	}
	var userlist []string
	for _, rb := range list.Items {
		var role string
		if len(rb.Roles) > 0 {
			role = rb.Roles[0]
		}
		if role == "admin" {
			userlist = append(userlist, rb.Name)
		}
	}
	return userlist, nil
}
