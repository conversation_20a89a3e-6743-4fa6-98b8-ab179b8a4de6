package clusterresourcequota

import (
	"context"
	"os"

	"sigs.k8s.io/controller-runtime/pkg/client/config"
	ctrllog "sigs.k8s.io/controller-runtime/pkg/log"
	"sigs.k8s.io/controller-runtime/pkg/log/zap"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/manager/signals"
	"xiaoshiai.cn/core/scheme"
)

type Options struct {
	Listen string
}

func Run(ctx context.Context, options *Options) error {
	ctrllog.SetLogger(zap.New())
	log := ctrllog.Log.WithName("clusterresourcequota")

	restcfg, err := config.GetConfig()
	if err != nil {
		return err
	}
	mgr, err := manager.New(restcfg, manager.Options{
		Scheme:           scheme.Scheme,
		LeaderElectionID: "clusterresourcequota",
	})
	if err != nil {
		log.Error(err, "could not create manager")
		os.Exit(1)
	}
	resourceQuotaReconciler := &ClusterResourceQuotaReconciler{
		Client: mgr.GetClient(),
	}
	if err := resourceQuotaReconciler.Setup(mgr); err != nil {
		log.Error(err, "could not set up reconciler")
		return err
	}
	if err := mgr.Start(signals.SetupSignalHandler()); err != nil {
		log.Error(err, "could not start manager")
		return err
	}
	return nil
}
