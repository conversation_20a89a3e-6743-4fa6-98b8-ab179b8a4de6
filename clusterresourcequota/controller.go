package clusterresourcequota

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/apiserver/pkg/admission"
	quota "k8s.io/apiserver/pkg/quota/v1"
	"sigs.k8s.io/controller-runtime/pkg/builder"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/manager"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/log"
	quotav1 "xiaoshiai.cn/core/apis/quota/v1"
)

const (
	LabelClusterResourceQuota     = "clusterresourcequota." + common.GroupPrefix
	ClusterResourceQuotaFinalizer = "clusterresourcequota.finalizers." + common.GroupPrefix
)

func NewClusterResourceQuotaReconciler(client client.Client) *ClusterResourceQuotaReconciler {
	return &ClusterResourceQuotaReconciler{
		Client: client,
	}
}

// ClusterResourceQuotaReconciler is a simple ControllerManagedBy example implementation.
type ClusterResourceQuotaReconciler struct {
	registry   quota.Registry
	evaluators []SimpleEvaluator
	client.Client
}

func (a *ClusterResourceQuotaReconciler) Setup(mgr manager.Manager) error {
	return builder.ControllerManagedBy(mgr).
		For(&quotav1.ClusterResourceQuota{}).
		Watches(&corev1.Namespace{}, handler.EnqueueRequestsFromMapFunc(a.OnNamespaceChange)).
		Complete(a)
}

func (a *ClusterResourceQuotaReconciler) OnNamespaceChange(ctx context.Context, obj client.Object) []reconcile.Request {
	ns, ok := obj.(*v1.Namespace)
	if !ok {
		return []reconcile.Request{}
	}
	clusterresourcequotas := &quotav1.ClusterResourceQuotaList{}
	if err := a.Client.List(ctx, clusterresourcequotas); err != nil {
		return []reconcile.Request{}
	}
	requests := []reconcile.Request{}
	for _, clusterresourcequota := range clusterresourcequotas.Items {
		sel, err := metav1.LabelSelectorAsSelector(clusterresourcequota.Spec.Selector)
		if err != nil {
			continue
		}
		if sel.Matches(labels.Set(ns.Labels)) {
			requests = append(requests, reconcile.Request{
				NamespacedName: client.ObjectKeyFromObject(&clusterresourcequota),
			})
		}
	}
	return requests
}

func (e *ClusterResourceQuotaReconciler) Evaluate(a admission.Attributes) error {
	ctx := context.TODO()

	if a.GetOperation() != admission.Create {
		return nil
	}
	namespace := a.GetNamespace()
	if namespace == "" {
		return nil
	}
	obj := a.GetObject()

	newresources := corev1.ResourceList{}
	for _, evaluator := range e.evaluators {
		if !evaluator.MatchGroupResource(a.GetResource().GroupResource()) {
			continue
		}
		if evaluator.MatchGroupResource(a.GetResource().GroupResource()) {
			usage, err := evaluator.Usage(obj)
			if err != nil {
				return err
			}
			newresources = quota.Add(newresources, usage)
		}
	}
	// check if the new resource is within the cluster resource quota
	clusterresourcequotalist := &quotav1.ClusterResourceQuotaList{}
	if err := e.Client.List(ctx, clusterresourcequotalist); err != nil {
		return err
	}
	for _, clusterresourcequota := range clusterresourcequotalist.Items {
		for _, nsstatus := range clusterresourcequota.Status.Namespaces {
			if nsstatus.Name == namespace {
				//  chcek if the new resource is within the cluster resource quota
				newused := quota.Add(nsstatus.Used.DeepCopy(), newresources)
				// FIXME: this is not concurrency safe, if the resource created concurrently, it may exceed the quota
				// because the used resource is not real-time updated
				isvalid, exceededResources := quota.LessThanOrEqual(newused, clusterresourcequota.Spec.Hard)
				if !isvalid {
					return apierrors.NewForbidden(a.GetResource().GroupResource(), a.GetName(),
						fmt.Errorf("exceeded cluster resource quota: %v", exceededResources))
				}
				break
			}
		}
	}
	return nil
}

type SimpleEvaluator interface {
	MatchGroupResource(schema.GroupResource) bool
	Usage(item runtime.Object) (corev1.ResourceList, error)
	UsageStats(options quota.UsageStatsOptions) (quota.UsageStats, error)
}

func (a *ClusterResourceQuotaReconciler) Reconcile(ctx context.Context, req reconcile.Request) (reconcile.Result, error) {
	clusterresourcequota := &quotav1.ClusterResourceQuota{}
	if err := a.Client.Get(ctx, req.NamespacedName, clusterresourcequota); err != nil {
		if apierrors.IsNotFound(err) {
			return reconcile.Result{}, nil
		}
		return reconcile.Result{}, err
	}
	if clusterresourcequota.DeletionTimestamp != nil {
		return reconcile.Result{}, nil
	}
	if err := a.syncResourceQuota(ctx, clusterresourcequota); err != nil {
		return reconcile.Result{}, err
	}
	return reconcile.Result{}, nil
}

const ClusterResourceQuotaEmpty = "clusterresourcequota.empty"

func (rq *ClusterResourceQuotaReconciler) syncResourceQuota(ctx context.Context, clusterResourceQuota *quotav1.ClusterResourceQuota) (err error) {
	log := log.FromContext(ctx)
	namespacelist := &v1.NamespaceList{}
	if err := rq.Client.List(ctx, namespacelist); err != nil {
		return err
	}
	selector, err := metav1.LabelSelectorAsSelector(clusterResourceQuota.Spec.Selector)
	if err != nil {
		return err
	}
	matchedNamespaces := []string{}
	for _, ns := range namespacelist.Items {
		if selector.Matches(labels.Set(ns.Labels)) {
			matchedNamespaces = append(matchedNamespaces, ns.Name)
		}
	}

	totalUsage := v1.ResourceList{}
	namespaceUsage := []quotav1.NamespaceResourceQuota{}

	var errs []error
	for _, ns := range matchedNamespaces {
		resourceQuota := &corev1.ResourceQuota{
			ObjectMeta: metav1.ObjectMeta{
				Name:      GetNamespaceResouceQuotaName(clusterResourceQuota.Name),
				Namespace: ns,
			},
		}
		_, err := controllerutil.CreateOrUpdate(ctx, rq.Client, resourceQuota, func() error {
			if resourceQuota.Labels == nil {
				resourceQuota.Labels = map[string]string{}
			}
			resourceQuota.Labels[LabelClusterResourceQuota] = clusterResourceQuota.Name
			// set resource quota spec to cluster resource quota spec
			// single namespace resource quota should not larger than cluster resource quota
			resourceQuota.Spec = corev1.ResourceQuotaSpec{
				Hard:          clusterResourceQuota.Spec.Hard,
				Scopes:        clusterResourceQuota.Spec.Scopes,
				ScopeSelector: clusterResourceQuota.Spec.ScopeSelector,
			}
			// set owner reference to cluster resource quota so that resource quota will be deleted when cluster resource quota is deleted
			return controllerutil.SetOwnerReference(clusterResourceQuota, resourceQuota, rq.Client.Scheme())
		})
		if err != nil {
			log.Error(err, "failed to create or update resource quota", "namespace", ns)
			errs = append(errs, err)
			continue
		}
		totalUsage = quota.Add(totalUsage, resourceQuota.Status.Used)
		namespaceUsage = append(namespaceUsage, quotav1.NamespaceResourceQuota{Name: ns, Used: resourceQuota.Status.Used})
	}
	clusterResourceQuota.Status.Namespaces = namespaceUsage
	clusterResourceQuota.Status.Hard = clusterResourceQuota.Spec.Hard.DeepCopy()
	clusterResourceQuota.Status.Used = totalUsage.DeepCopy()
	return utilerrors.NewAggregate(errs)
}

func GetNamespaceResouceQuotaName(name string) string {
	return "clusterresourcequota." + name
}
