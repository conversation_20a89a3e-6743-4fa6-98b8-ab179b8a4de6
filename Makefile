
BUILD_DATE?=$(shell date -u +'%Y-%m-%dT%H:%M:%SZ')
GIT_VERSION?=$(shell git describe --tags --dirty --abbrev=0 2>/dev/null || git symbolic-ref --short HEAD)
GIT_COMMIT?=$(shell git rev-parse HEAD 2>/dev/null)
GIT_BRANCH?=$(shell git symbolic-ref --short HEAD 2>/dev/null)
VERSION?=$(shell echo "${GIT_VERSION}" | sed -e 's/^v//')

BIN_DIR?=bin
IMAGE_REGISTRY?=registry.cn-hangzhou.aliyuncs.com
IMAGE_REPOSITORY?=xiaoshiai
IMAGE_NAME?=$(shell basename $(CURDIR))

CHART_REGISTRY?=registry.xiaoshiai.cn
CHART_REPOSITORY?=charts
CHART_REGISTRY_USERNAME?=
CHART_REGISTRY_PASSWORD?=

LDFLAGS+=-w -s
LDFLAGS+=-X 'xiaoshiai.cn/common/version.gitVersion=${GIT_VERSION}'
LDFLAGS+=-X 'xiaoshiai.cn/common/version.gitCommit=${GIT_COMMIT}'
LDFLAGS+=-X 'xiaoshiai.cn/common/version.buildDate=${BUILD_DATE}'
BUILD_TARGET?=./cmd/...
##@ All
all: build ## build all

##@ General
help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make \033[36m<target>\033[0m\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s\033[0m\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

generate: generate-apis generate-openapi ## Generate all

generate-apis: ## Generate all
	hack/update-codegen.sh

generate-crd:
	${BIN_DIR}/controller-gen paths="./apis/quota/..." crd output:crd:dir=deploy/ismc-agent/crds

generate-openapi:
	openapi-gen --output-dir ./openapi --output-pkg openapi ./...

define build-binary
	@echo "Building ${1}-${2}";
	@mkdir -p ${BIN_DIR}/${1}-${2};
	GOOS=${1} GOARCH=$(2) CGO_ENABLED=0 go build -gcflags=all="-N -l" -ldflags="${LDFLAGS}" -o ${BIN_DIR}/${1}-${2} $(BUILD_TARGET)
endef
##@ Build
.PHONY: build
build: ## Build local binary.
	$(call build-binary,linux,amd64)
	$(call build-binary,linux,arm64)



.PHONY: release
release:release-image release-helm  ## Release all artifacts.

release-image: release-bob-image release-agent-image

FULL_IMAGE_NAME?=$(IMAGE_REGISTRY)/$(IMAGE_REPOSITORY)/$(IMAGE_NAME):$(GIT_VERSION)
BUILDX_PLATFORMS?=linux/amd64,linux/arm64
release-bob-image:
	docker buildx build --platform=${BUILDX_PLATFORMS} --push -t ${FULL_IMAGE_NAME} -f Dockerfile ${BIN_DIR}

AGENT_IMAGE_NAME?=$(IMAGE_REGISTRY)/$(IMAGE_REPOSITORY)/ismc-agent:$(GIT_VERSION)
release-agent-image:
	docker buildx build --platform=${BUILDX_PLATFORMS} --push -t ${AGENT_IMAGE_NAME} -f Dockerfile-ismc-agent ${BIN_DIR}

USE_CHARTMUSEUM?=true
HELM_REPO_USERNAME?=kubegems
HELM_REPO_PASSWORD?=
CHARTMUSEUM_ADDR?=https://${HELM_REPO_USERNAME}:${HELM_REPO_PASSWORD}@charts.kubegems.io
release-helm:
	helm dependency build deploy/bob
	helm package deploy/bob --destination=${BIN_DIR}/charts --version=${VERSION} --app-version=${GIT_VERSION}
ifeq ($(USE_CHARTMUSEUM),true)
	curl -f --data-binary "@${BIN_DIR}/charts/bob-${VERSION}.tgz" ${CHARTMUSEUM_ADDR}/bob/api/charts
else
	helm push ${BIN_DIR}/charts/bob-${VERSION}.tgz oci://${CHART_REGISTRY}/${CHART_REPOSITORY}
endif

	helm dependency build deploy/ismc-agent
	helm package deploy/ismc-agent --destination=${BIN_DIR}/charts --version=${VERSION} --app-version=${GIT_VERSION}
ifeq ($(USE_CHARTMUSEUM),true)
	curl -f --data-binary "@${BIN_DIR}/charts/ismc-agent-${VERSION}.tgz" ${CHARTMUSEUM_ADDR}/bob/api/charts
else
	helm push ${BIN_DIR}/charts/ismc-agent-${VERSION}.tgz oci://${CHART_REGISTRY}/${CHART_REPOSITORY}
endif

helm-login:
ifneq ($(USE_CHARTMUSEUM),true)
	helm registry login ${CHART_REGISTRY} --username ${CHART_REGISTRY_USERNAME} --password ${CHART_REGISTRY_PASSWORD}
endif

clean:
	rm -rf ${BIN_DIR}

install-tools:
	GOBIN=${PWD}/${BIN_DIR} go install k8s.io/code-generator/cmd/...@v0.31.1
	GOBIN=${PWD}/${BIN_DIR} go install k8s.io/kube-openapi/cmd/...@v0.0.0-20240228011516-70dd3763d340
	GOBIN=${PWD}/${BIN_DIR} go install sigs.k8s.io/controller-tools/cmd/...@v0.16.5
