package core

import (
	"bytes"
	"context"
	"fmt"
	"net/http"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/oci"
	"xiaoshiai.cn/common/pprof"
	mongodbqueue "xiaoshiai.cn/common/queue/mongodb"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/core/agent"
	"xiaoshiai.cn/core/announcement"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/application/database"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/audit"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/auth/provider/casdoor"
	"xiaoshiai.cn/core/authdevice"
	"xiaoshiai.cn/core/authn"
	"xiaoshiai.cn/core/authz"
	"xiaoshiai.cn/core/avatar"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/cluster/gateway"
	"xiaoshiai.cn/core/cluster/metadata"
	"xiaoshiai.cn/core/cluster/privatenode"
	"xiaoshiai.cn/core/cluster/resourcepool"
	"xiaoshiai.cn/core/global"
	"xiaoshiai.cn/core/integration"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/loadbalancer"
	"xiaoshiai.cn/core/market"
	"xiaoshiai.cn/core/message"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/observerability"
	"xiaoshiai.cn/core/observerability/dashboard"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/oss"
	"xiaoshiai.cn/core/pay"
	"xiaoshiai.cn/core/pay/analytics"
	"xiaoshiai.cn/core/pay/billing"
	"xiaoshiai.cn/core/pay/cost"
	"xiaoshiai.cn/core/pay/order"
	"xiaoshiai.cn/core/pay/promotion"
	"xiaoshiai.cn/core/rbac"
	"xiaoshiai.cn/core/resourcequota"
	"xiaoshiai.cn/core/setting"
	"xiaoshiai.cn/core/statistics"
	"xiaoshiai.cn/core/system/notifiy"
	"xiaoshiai.cn/core/system/ownerinject"
	"xiaoshiai.cn/core/tenant"
	"xiaoshiai.cn/core/ticketv2"
	"xiaoshiai.cn/core/user"
	"xiaoshiai.cn/core/wallet"
)

const MongodbDatabase = "bob"

type APIServerOptions struct {
	Listen         string                           `json:"listen,omitempty"`
	Global         *global.Options                  `json:"global,omitempty"`
	API            *APIOptions                      `json:"api,omitempty"`
	Etcd           *etcdcache.Options               `json:"etcd,omitempty"`
	Mongodb        *mongo.MongoDBOptions            `json:"mongodb,omitempty"`
	Authentication *authn.AuthenticationOptions     `json:"authentication,omitempty"`
	Authorization  *authz.AuthorizationOptions      `json:"authorization,omitempty"`
	Audit          *audit.AuditOptions              `json:"audit,omitempty"`
	OCI            *artifact.OCIOptions             `json:"oci,omitempty"`
	Device         *authdevice.UserDeviceOptions    `json:"device,omitempty"`
	UserVerify     *user.UserVerifyOptions          `json:"userVerify,omitempty"`
	Integration    *integration.IntergrationOptions `json:"integration,omitempty"`
	Casdoor        *casdoor.Options                 `json:"casdoor,omitempty"`
	AuthCache      *auth.CacheOptions               `json:"authCache,omitempty"`
	Edge           *agent.ServerOptions             `json:"edge,omitempty"`
	ObjectStorage  *oss.ObjectStorageOptions        `json:"objectStorage,omitempty"`
	Loadbalancer   *loadbalancer.Options            `json:"loadbalancer,omitempty"`
	Pay            *pay.Options                     `json:"pay,omitempty"`
}

func NewDefaultAPIServerOptions() *APIServerOptions {
	mongodb := mongo.NewDefaultMongoOptions(MongodbDatabase)
	return &APIServerOptions{
		Listen:         ":8080",
		Global:         global.NewDefaultOptions(),
		API:            DefaultAPIOptions(),
		Authentication: authn.DefaultAuthenticationOptions(),
		Authorization:  authz.DefaultAuthorizationOptions(),
		Audit:          audit.NewDefaultAuditOptions(),
		Etcd:           etcdcache.NewDefaultOptions(),
		Mongodb:        mongodb,
		OCI:            artifact.NewDefaultOCIOptions(),
		Device:         authdevice.NewDefaultUserDeviceOptions(),
		UserVerify:     user.NewUserVerifyOptions(),
		Integration:    integration.NewDefaultIntergrationOptions(),
		Casdoor:        casdoor.NewDefaultOptions(),
		Edge:           agent.NewDefaultServerOptions(),
		ObjectStorage:  oss.NewDefaultObjectStorageOptions(),
		Loadbalancer:   loadbalancer.NewDefaultOptions(),
		AuthCache:      auth.NewDefaultCacheOptions(),
		Pay:            pay.NewDefaultOptions(),
	}
}

var GarbageCollectorResources = []string{
	"clusters",
	"roles",
	"userroles",
	"licenses",
	"organizations",
	"applications",
	"applicationhistories",
	"resourcepools",
	"tenants",
	"resourcequotas",
	"privatenodes",
	"privatenodeclaims",
	"alertrules",
	"alertchannels",
	"objectstorages",
	"metricsdashboards",
	"loadbalancers",
}

func NewEtcdStore(ctx context.Context, options *etcdcache.Options) (store.Store, error) {
	return etcdcache.NewEtcdCacher(options, etcdcache.ResourceFieldsMap{
		"clusters":          {"type", "edge.deviceID", "published", "tenant"},
		"tenants":           {"enabled", "email", "phone"},
		"users":             {"email"},
		"products":          {"published", "category", "type", "subcategory"},
		"objectstorages":    {"tenant"},
		"resourcequotas":    {"tenant"},
		"licenserequests":   {"status.phase"},
		"licenses":          {"for", "status.useable"},
		"resourcepools":     {"cluster.name"},
		"skus":              {"enabled"},
		"applications":      {"status.product.category", "status.product.subcategory", "product.name"},
		"privatenodeclaims": {"cluster.name", "status.nodename"},
		"privatenodes":      {"model"},
		"metricsdashboards": {},
	})
}

func NewMongoStore(ctx context.Context, options *mongo.MongoDBOptions) (*mongo.MongoStorage, error) {
	return mongo.NewMongoStorage(ctx, mongo.GlobalObjectsScheme, options)
}

func RunAPIServer(ctx context.Context, options *APIServerOptions) error {
	// prepare dependencies
	deps, err := NewAPIServerDependencies(ctx, options)
	if err != nil {
		return err
	}
	api, err := NewAPI(ctx, deps, options)
	if err != nil {
		return err
	}
	cm, err := BuildAPIServerController(ctx, deps)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return api.Run(ctx)
	})
	eg.Go(func() error {
		return cm.Run(ctx)
	})
	eg.Go(func() error {
		return deps.AgentServer.Run(ctx)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

func BuildAPIServerController(ctx context.Context, deps *APIServerDependencies) (*ApiserverControllerManager, error) {
	cm := controller.NewControllerManager()

	// clusterinfos controller manager connection info to clusterinfos
	clusterinfos, err := cluster.NewClusterInfoController(ctx, deps.Store, deps.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	cm.AddController(clusterinfos)

	notifiermanager, err := notifiy.NewNotifierHolderController(deps.Store, deps.NofitierManager)
	if err != nil {
		return nil, err
	}
	cm.AddController(notifiermanager)

	osscontext, err := oss.NewObjectStorageProviderContextController(deps.Store, deps.ObjectProviderContext)
	if err != nil {
		return nil, err
	}
	cm.AddController(osscontext)

	return &ApiserverControllerManager{manager: cm, deps: deps}, nil
}

type ApiserverControllerManager struct {
	manager *controller.ControllerManager
	deps    *APIServerDependencies
}

func (c *ApiserverControllerManager) Run(ctx context.Context) error {
	if err := cluster.InitCloudInfoHolder(ctx, c.deps.Store, c.deps.CloudInfoHolder); err != nil {
		return err
	}
	return c.manager.Run(ctx)
}

type APIServerDependencies struct {
	Store                  store.Store
	MongoStore             *mongo.MongoStorage
	Avatar                 avatar.AvatarService
	Authn                  api.Authenticator
	Authz                  api.Authorizer
	AuditSink              api.AuditSink
	OCI                    *oci.OCIArtifacts
	CloudInfoHolder        cluster.CloudInfoHolder
	SelfTokenAuthenticator *authn.SelfTokenAuthenticator
	ArtifactService        *artifact.ArtifactService
	AuthProvider           auth.Provider
	AuthTokenAuthenticator *auth.SelfAuthAuthenticator
	NofitierManager        notifiy.NofitierManager
	SessionManager         user.SessionManager
	UserVerifier           user.UserVerifier
	Captcha                user.CaptchaProvider
	AgentServer            *agent.Server
	ObjectStorage          *oss.ObjectStorageService
	// OrderService           order.OrderService
	// WalletService          wallet.WalletService
	// TicketService          ticket.TicketService
	TicketV2Service ticketv2.BobTicketService
	// MessageService        send.SendMessage
	Recorder events.Recorder
	// OrderEventQueue       queue.Queue
	// SKUService            product.SKUService
	Pay                   *pay.PaySystem
	ObjectProviderContext *oss.ObjectProviderContext
	BuiltInDashboards     map[string]dashboard.DashboradConfiguration
	Loadbalancer          loadbalancer.Loadbalancer
}

func NewAPIServerDependencies(ctx context.Context, options *APIServerOptions) (*APIServerDependencies, error) {
	etcdstore, err := NewEtcdStore(ctx, options.Etcd)
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd store: %w", err)
	}
	mainstore := ownerinject.NewOwnerInjectStore(etcdstore, ownerinject.Options{
		InjectResources: GarbageCollectorResources,
	})

	mongostore, err := NewMongoStore(ctx, options.Mongodb)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongodb store: %w", err)
	}

	var authp auth.Provider
	casdoorauthp, err := casdoor.NewProvider(ctx, options.Casdoor)
	if err != nil {
		return nil, err
	}
	authp = auth.NewProviderWithCacheOptions(options.AuthCache, casdoorauthp)

	authntoken, err := auth.NewSelfTokenAuthenticator(authp)
	if err != nil {
		return nil, err
	}

	apikeyauthn := auth.NewAPIKeyAuthenticator(authp)

	// Deprecated: remove in the future
	selfauthn, err := authn.NewSelfTokenAuthenticator(ctx, mainstore, options.Authentication)
	if err != nil {
		return nil, err
	}
	usersesison, err := user.NewDefaultSessionManager(mainstore)
	if err != nil {
		return nil, err
	}
	sessionauthn := user.NewSessionAuthenticator(usersesison)

	// authntoken, selfauthn, sessionauthn
	authn, err := authn.BuildAuthn(ctx, mainstore,
		options.Authentication,
		[]api.TokenAuthenticator{
			authntoken,
			selfauthn,
			sessionauthn,
		},
		[]api.BasicAuthenticator{
			apikeyauthn,
		},
		[]api.Authenticator{},
	)
	if err != nil {
		return nil, err
	}
	authz, err := authz.BuildAuthz(ctx, mainstore, options.Authorization)
	if err != nil {
		return nil, err
	}
	auditsink, err := audit.BuildAuditSink(ctx, mongostore, options.Audit)
	if err != nil {
		return nil, err
	}

	if options.Device.EncryptionKey == "" {
		enckey, err := setting.GetOrCreateEncryptKey(ctx, mainstore)
		if err != nil {
			return nil, err
		}
		options.Device.EncryptionKey = enckey
	}

	oci, err := oci.NewOCIArtifacts([]oci.OCICredential{
		{
			Host:     options.OCI.Host,
			Username: options.OCI.Username,
			Password: options.OCI.Password,
		},
	})
	if err != nil {
		return nil, err
	}

	notifiyholder := notifiy.NewDefaultNotifierHolder()

	userverifier, err := user.NewUniversalUserVerifier(mainstore, notifiyholder, options.UserVerify)
	if err != nil {
		return nil, err
	}

	agentserver, err := agent.NewServer(ctx, mainstore, mongostore, options.Edge)
	if err != nil {
		return nil, err
	}

	var objectStorage *oss.ObjectStorageService
	if options.ObjectStorage.Enabled {
		osssvc, err := oss.NewObjectStorageService(ctx, mainstore, options.ObjectStorage)
		if err != nil {
			return nil, err
		}
		objectStorage = osssvc
	}

	cloudinfo := cluster.NewDefaultCloudInfoHolder(ctx, agentserver.Tunnel)

	artifactsvc, err := artifact.NewArtifactService(ctx, options.OCI)
	if err != nil {
		return nil, err
	}
	captcha, err := user.NewUniversalCaptcha(ctx)
	if err != nil {
		return nil, err
	}

	recorder := events.NewQueueRecorder(ctx, NewEventsQueue(mongostore), 64)

	// ticketService := ticket.NewTicketService(mongostore)

	ticketv2Service := ticketv2.NewBobTicketService(mongostore, mongostore.Database())

	paysystem, err := pay.New(ctx, mainstore, mongostore, recorder, artifactsvc, options.Pay)
	if err != nil {
		return nil, err
	}

	osscontext := oss.NewObjectProviderContext()

	dashboards, err := dashboard.NewBuiltInDashboards()
	if err != nil {
		log.FromContext(ctx).Error(err, "failed to load built-in dashboards")
	}

	loadbalancer, err := loadbalancer.NewDefaultLoadbalancer(options.Loadbalancer)
	if err != nil {
		return nil, err
	}

	return &APIServerDependencies{
		Store:                  mainstore,
		MongoStore:             mongostore,
		Avatar:                 avatar.NewLocalAvatarService(mainstore),
		Authn:                  authn,
		Authz:                  authz,
		AuditSink:              auditsink,
		OCI:                    oci,
		UserVerifier:           userverifier,
		SelfTokenAuthenticator: selfauthn,
		ArtifactService:        artifactsvc,
		AuthProvider:           authp,
		AuthTokenAuthenticator: authntoken,
		CloudInfoHolder:        cloudinfo,
		Captcha:                captcha,
		NofitierManager:        notifiyholder,
		SessionManager:         usersesison,
		AgentServer:            agentserver,
		ObjectStorage:          objectStorage,
		// WalletService:          walletservice,
		// TicketService:          ticketService,
		TicketV2Service:       ticketv2Service,
		Recorder:              recorder,
		ObjectProviderContext: osscontext,
		BuiltInDashboards:     dashboards,
		Loadbalancer:          loadbalancer,
		Pay:                   paysystem,
	}, nil
}

func NewOrderQueue(store *mongo.MongoStorage) *mongodbqueue.MongoQueue {
	return base.NewQueue(store, "order-queue")
}

func NewEventsQueue(store *mongo.MongoStorage) *mongodbqueue.MongoQueue {
	return base.NewQueue(store, "events-queue")
}

type APIOptions struct {
	Prefix string `json:"prefix,omitempty"`
}

func DefaultAPIOptions() *APIOptions {
	return &APIOptions{Prefix: "/v1"}
}

type API struct {
	options *APIServerOptions
	groups  []api.Group
}

func NewAPI(ctx context.Context, deps *APIServerDependencies, options *APIServerOptions) (*API, error) {
	allgroups := []api.Group{}
	// main api group
	maingroup, err := BuildMainAPIGroup(ctx, deps, options)
	if err != nil {
		return nil, err
	}
	allgroups = append(allgroups, maingroup)
	return &API{groups: allgroups, options: options}, nil
}

func BuildMainAPIGroup(ctx context.Context, deps *APIServerDependencies, options *APIServerOptions) (api.Group, error) {
	sink, finalauthn, finalauthz := deps.AuditSink, deps.Authn, deps.Authz
	apiprefix := options.API.Prefix

	baseapi := base.NewAPI(deps.Store)
	avatarapi := avatar.NewAPI(baseapi, deps.Avatar)
	marketapi := market.NewAPI(baseapi, deps.ArtifactService, deps.Pay.SKUService)
	tenantapi := tenant.NewAPI(baseapi, deps.Recorder)
	clusterapi := cluster.NewAPI(baseapi, deps.CloudInfoHolder)
	licenseapi := license.NewAPI(baseapi, deps.ArtifactService)
	statisticsapi := statistics.NewAPI(baseapi, deps.CloudInfoHolder, deps.AuthProvider)
	auditapi := audit.NewAPI(base.NewAPI(deps.MongoStore))
	settingapi := setting.NewAPI(baseapi)
	resourcequotaapi := resourcequota.NewAPI(baseapi, deps.CloudInfoHolder)
	ossapi := oss.NewAPI(baseapi, deps.ObjectStorage)
	rbacapi := rbac.NewAPI(baseapi, deps.AuthProvider, deps.Recorder)
	applicationapi := application.NewAPI(baseapi, deps.MongoStore, deps.CloudInfoHolder, deps.ArtifactService, deps.Loadbalancer, deps.Pay)
	organizationapi := organization.NewAPI(baseapi, deps.Recorder)
	observerabilityapi := observerability.NewAPI(baseapi, deps.MongoStore, deps.CloudInfoHolder, deps.BuiltInDashboards)
	integrationapi := integration.NewAPI(baseapi, deps.SessionManager, deps.AuthProvider, options.Integration)
	costapi := cost.NewAPI(baseapi, deps.MongoStore, deps.Pay.SKUService, deps.CloudInfoHolder)
	agentconnectapigroup := agent.NewConnectAPI(deps.AgentServer)
	orderapi := order.NewAPI(baseapi, deps.MongoStore, deps.Pay.OrderService, deps.Pay.SKUService, deps.Pay.OrderService.ResourceMetadata)
	billingapi := billing.NewAPI(deps.Pay.BillingService)
	discountapi := promotion.NewAPI(baseapi, deps.MongoStore)
	// ticketapi := ticket.NewAPI(baseapi, deps.TicketService)
	ticketv2api := ticketv2.NewAPI(baseapi, deps.TicketV2Service, deps.Recorder)
	messageapi := message.NewAPI(deps.MongoStore, deps.MongoStore.Database())
	walletapi := wallet.NewAPI(baseapi, deps.Pay.Wallet)
	announcementapi := announcement.New(deps.MongoStore, deps.Recorder)
	privatenodesapi := privatenode.NewAPI(baseapi, deps.CloudInfoHolder, deps.Pay.SKUService, deps.Pay.OrderService, deps.BuiltInDashboards)
	resourcepoolapi := resourcepool.NewAPI(baseapi, deps.CloudInfoHolder, deps.BuiltInDashboards)
	gatewayapi := gateway.NewAPI(baseapi, deps.MongoStore, deps.CloudInfoHolder, deps.BuiltInDashboards)
	clustermetadata := metadata.NewAPI(baseapi, deps.CloudInfoHolder)
	databseapi := database.NewAPI(baseapi, deps.MongoStore, deps.CloudInfoHolder, deps.ArtifactService, deps.Loadbalancer, deps.BuiltInDashboards, deps.Pay)
	reportapi := analytics.NewAPI(deps.MongoStore)

	var authngroup, usergroup api.Group
	if global.FeatureUseBuiltAuthProvider {
		userapi := user.NewAPI(baseapi, deps.Captcha, deps.UserVerifier, deps.SessionManager)
		authngroup, usergroup = userapi.PublicGroup(), userapi.Group()
	} else {
		authapi := auth.NewAPI(deps.AuthProvider)
		// perf: avoid unnecessary session injection
		sessionInject := auth.NewSessionFilter(deps.AuthProvider)
		authngroup, usergroup = authapi.PublicGroup().Filter(sessionInject), authapi.Group()
	}

	admissions := []api.AdmissionPlugin{
		license.NewApplicationLicenseAdminssion(deps.Store),
		resourcequota.NewResourceQuotaLicenseAdminssion(deps.Store),
	}

	return api.
		NewGroup(apiprefix).
		Filter(
			api.NewSimpleAuditFilter(sink, api.NewDefaultAuditOptions()),
			api.NewAttributeFilter(api.PrefixedAttributesExtractor(apiprefix)),
			ExtractCreationNameAttrFilter(),
		).
		// public group
		SubGroup(
			avatarapi.PublicGroup(),
			integrationapi.Group(),
			authngroup,
			agentconnectapigroup,
			marketapi.PublicGroup(),
			costapi.PublicGroup(),
		).
		// auth group
		SubGroup(
			api.NewGroup("").
				Filter(
					auth.NewSessionFilter(deps.AuthProvider),
					api.NewAuthenticateFilter(finalauthn, nil),
					api.NewAuthorizationFilter(finalauthz),
					api.NewAdminssionFilter(admissions...),
				).
				SubGroup(
					avatarapi.Group(),
					rbacapi.Group(),
					applicationapi.Group(),
					observerabilityapi.Group(),
					tenantapi.Group(),
					clusterapi.Group(),
					marketapi.Group(),
					settingapi.Group(),
					licenseapi.Group(),
					auditapi.Group(),
					ossapi.Group(),
					statisticsapi.Group(),
					resourcequotaapi.Group(),
					organizationapi.Group(),
					costapi.Group(),
					orderapi.Group(),
					billingapi.Group(),
					usergroup,
					// ticketapi.Group(),
					ticketv2api.Group(),
					messageapi.Group(),
					walletapi.Group(),
					announcementapi.Group(),
					privatenodesapi.Group(),
					resourcepoolapi.Group(),
					gatewayapi.Group(),
					clustermetadata.Group(),
					databseapi.Group(),
					reportapi.Group(),
					discountapi.Group(),
				),
		), nil
}

func ExtractCreationNameAttrFilter() api.Filter {
	return api.FilterFunc(func(w http.ResponseWriter, r *http.Request, next http.Handler) {
		if r.Method != http.MethodPost {
			next.ServeHTTP(w, r)
			return
		}
		if auditlog := api.AuditLogFromContext(r.Context()); auditlog != nil && len(auditlog.Request.Body) > 0 {
			attr := api.AttributesFromContext(r.Context())
			if len(attr.Resources) > 0 && attr.Resources[len(attr.Resources)-1].Name == "" {
				type meta struct {
					Name string `json:"name"`
				}
				var m meta
				_ = api.ReadContent(r.Header.Get("Content-Type"), r.Header.Get("Content-Encoding"), bytes.NewReader(auditlog.Request.Body), &m)
				if m.Name != "" {
					attr.Resources[len(attr.Resources)-1].Name = m.Name
				}
			}
		}
		next.ServeHTTP(w, r)
	})
}

func (a *API) Run(ctx context.Context) error {
	return api.New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			a.groups...,
		).
		Serve(ctx, a.options.Listen)
}
