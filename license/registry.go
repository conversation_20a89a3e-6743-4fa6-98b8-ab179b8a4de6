package license

import (
	"context"
	"time"

	"xiaoshiai.cn/core/artifact"
)

type RegistryPullSecret struct {
	Registry string `json:"registry,omitempty"`
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

type RegistryTokenIssuer interface {
	RegistryHost() string
	GenerateRegistryPullAccount(ctx context.Context, project, serial string, expires time.Time) (*RegistryPullSecret, error)
	RemoveRegistryAccount(ctx context.Context, project, serial string) error
	SetRegistryAccountEnabled(ctx context.Context, project, serial string, enabled bool) error
}

var _ RegistryTokenIssuer = ArtifactServiceAsRegistryTokenIssuer{}

type ArtifactServiceAsRegistryTokenIssuer struct {
	ArtifactService *artifact.ArtifactService
}

// RegistryHost implements RegistryTokenIssuer.
func (a ArtifactServiceAsRegistryTokenIssuer) RegistryHost() string {
	return a.ArtifactService.RegistryHost
}

// RemoveRegistryAccount implements RegistryTokenIssuer.
func (a ArtifactServiceAsRegistryTokenIssuer) RemoveRegistryAccount(ctx context.Context, project string, id string) error {
	return a.ArtifactService.RemoveRegistryAccount(ctx, project, "license-"+id)
}

func (a ArtifactServiceAsRegistryTokenIssuer) GenerateRegistryPullAccount(ctx context.Context, project, id string, expires time.Time) (*RegistryPullSecret, error) {
	options := artifact.RegistryAccountOptions{Pull: true, Expires: expires}
	token, err := a.ArtifactService.GenerateRegistryAccount(ctx, project, "license-"+id, options)
	if err != nil {
		return nil, err
	}
	return &RegistryPullSecret{Registry: a.ArtifactService.RegistryHost, Username: token.Username, Password: token.Password}, nil
}

func (a ArtifactServiceAsRegistryTokenIssuer) SetRegistryAccountEnabled(ctx context.Context, project, id string, enabled bool) error {
	return a.ArtifactService.EnableRegistryAccount(ctx, project, "license-"+id, enabled)
}
