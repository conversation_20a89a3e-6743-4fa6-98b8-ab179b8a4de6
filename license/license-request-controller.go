package license

import (
	"context"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
)

func NewLicenseRequestsController(storage store.Store, artifact *artifact.ArtifactService) (*controller.Controller, error) {
	rec := &LicenseRequestsController{
		Store:               storage,
		RegistryTokenIssuer: ArtifactServiceAsRegistryTokenIssuer{ArtifactService: artifact},
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("licenserequests", br).Watch(
		controller.NewStoreSource(storage, &LicenseRequest{}),
	)
	return c, nil
}

type LicenseRequestsController struct {
	Store               store.Store
	RegistryTokenIssuer RegistryTokenIssuer
}

func (c LicenseRequestsController) Sync(ctx context.Context, request *LicenseRequest) (controller.Result, error) {
	return base.UnwrapReQueueError(c.sync(ctx, request))
}

func (c LicenseRequestsController) sync(ctx context.Context, request *LicenseRequest) error {
	if request.Status.Phase == LicenseRequestPhaseDenied || request.Status.Phase == LicenseRequestPhaseApproved {
		return nil
	}
	if err := ApproveLicenseRequest(ctx, c.Store, request, c.RegistryTokenIssuer, false); err != nil {
		return err
	}
	if timeto := request.Status.OperationTime.Add(24 * time.Hour).Sub(time.Now()); timeto < 0 {
		if err := c.Store.Scope(request.Scopes...).Delete(ctx, request); err != nil {
			return err
		}
		return nil
	} else {
		return base.ReQueue(timeto)
	}
}

func (c LicenseRequestsController) Remove(ctx context.Context, request *LicenseRequest) (controller.Result, error) {
	return controller.Result{}, nil
}
