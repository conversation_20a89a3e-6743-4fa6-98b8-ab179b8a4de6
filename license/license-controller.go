package license

import (
	"context"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/message/events"
)

func NewTenantLicenseController(storage store.Store, recorder events.Recorder, info cluster.CloudInfoGetter) (*controller.Controller, error) {
	rec := &LicenseController{
		Client:   storage,
		Recorder: recorder,
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("license-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.
		NewController("licenses", br).
		Watch(
			controller.NewStoreSource(storage, &License{}),
		)
	return c, nil
}

type LicenseStatus struct {
	// Valid is true if the license is valid
	Valid bool `json:"valid,omitempty"`

	// Useable is true if the license is valid and not used
	Useable bool `json:"useable,omitempty"`

	// UsedBy is the reference to the object that uses the license
	UsedBy *store.ResourcedObjectReference `json:"usedBy,omitempty"`

	// Message is the message that describes the status of the license
	Message string `json:"message,omitempty"`

	// Expired is true if the license has expired
	Expired bool `json:"expired,omitempty"`
}

var _ controller.Reconciler[*License] = &LicenseController{}

type LicenseController struct {
	Client   store.Store
	Recorder events.Recorder
}

// Sync implements Reconciler.
func (l *LicenseController) Sync(ctx context.Context, license *License) (controller.Result, error) {
	if license.Status.UsedBy != nil {
		uns := &store.Unstructured{}
		uns.SetResource(license.Status.UsedBy.Resource)
		if err := l.Client.Scope(license.Status.UsedBy.Scopes...).Get(ctx, license.Status.UsedBy.Name, uns); err != nil {
			if errors.IsNotFound(err) {
				log.Info("used by object not found, removing reference", "license", license)
				license.Status.UsedBy = nil
			}
			log.Error(err, "failed to get used by object", "license", license)
		}
	}

	left := license.NotAfter.Time.Sub(time.Now())
	if left < 0 {
		license.Status.Expired = true
		license.Status.Message = "expired"
		license.Status.Valid = false
	} else {
		license.Status.Expired = false
		license.Status.Message = ""
		license.Status.Valid = true
	}

	// useable
	license.Status.Useable = (license.Status.UsedBy == nil) && license.Status.Valid
	return events.RecordRequeueOnExpires(ctx, l.Recorder, license, left), nil
}

// Remove implements Reconciler.
func (l *LicenseController) Remove(ctx context.Context, license *License) (controller.Result, error) {
	if license.Status.UsedBy == nil {
		return controller.Result{}, nil
	}
	log := log.FromContext(ctx)
	log.Info("waiting for the used by object to be deleted", "license", license)
	return controller.Result{Requeue: true, RequeueAfter: 30 * time.Second}, nil
}
