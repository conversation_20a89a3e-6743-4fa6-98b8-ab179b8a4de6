package license

import (
	"context"
	"crypto"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io"
	"math"
	"math/big"
	"strings"
	"time"
)

type SignLicenseData struct {
	Serial    big.Int        `json:"serial"`
	For       string         `json:"for"`
	Issuer    string         `json:"issuer"`
	Subject   string         `json:"subject"`
	Email     string         `json:"email"`
	NotBefore time.Time      `json:"notBefore"`
	NotAfter  time.Time      `json:"notAfter"`
	Content   map[string]any `json:"content"`
}

type SignLicenseOutput struct {
	Input SignLicenseData `json:"input"`
	Data  string          `json:"data"`
}

func SignLicense(ctx context.Context, input SignLicenseData, privateKey string, secret string) (*SignLicenseOutput, error) {
	data, err := encode(input, secret, privateKey)
	if err != nil {
		return nil, err
	}
	return &SignLicenseOutput{Input: input, Data: data}, nil
}

func NewSerial() big.Int {
	// returns a uniform random value in [0, max-1), then add 1 to serial to make it a uniform random value in [1, max).
	serial, err := rand.Int(rand.Reader, new(big.Int).SetInt64(math.MaxInt64-1))
	if err != nil {
		serial.SetUint64(uint64(time.Now().UnixNano()))
		return *serial
	}
	serial = new(big.Int).Add(serial, big.NewInt(1))
	return *serial
}

func encode(details any, secret string, privateKey string) (string, error) {
	jsonBytes, err := json.Marshal(details)
	if err != nil {
		return "", err
	}
	// read x509 private key
	block, _ := pem.Decode([]byte(privateKey))
	if block == nil {
		return "", fmt.Errorf("failed to parse PEM block containing the key")
	}
	privkey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return "", err
	}
	// signate the license
	hash := sha256.Sum256(jsonBytes)
	signature, err := rsa.SignPKCS1v15(rand.Reader, privkey, crypto.SHA256, hash[:])
	if err != nil {
		return "", err
	}
	// hex encode the signature
	licensestr := string(jsonBytes) + "." + base64.StdEncoding.EncodeToString(signature)
	// aes encrypt the license
	aeskey := sha256.Sum256([]byte(secret))
	// encrypt the license
	encrypted, err := EncryptAES([]byte(licensestr), aeskey[:])
	if err != nil {
		return "", err
	}
	// hex encode the license
	return base64.StdEncoding.EncodeToString(encrypted), nil
}

func DecodeLicense(ctx context.Context, raw string, publicKey string, secret string) (*SignLicenseData, error) {
	var data SignLicenseData
	if err := decode(raw, secret, publicKey, &data); err != nil {
		return nil, err
	}
	return &data, nil
}

func decode(raw string, secret string, publicKey string, into any) error {
	// hexdecode
	encrypted, err := base64.StdEncoding.DecodeString(raw)
	if err != nil {
		return err
	}
	// aes decrypt
	aeskey := sha256.Sum256([]byte(secret))
	decrypted, err := DecryptAES(encrypted, aeskey[:])
	if err != nil {
		return err
	}
	licensestr := string(decrypted)
	// validate the license
	i := strings.LastIndexAny(licensestr, ".")
	if i < 0 {
		return fmt.Errorf("invalid license")
	}
	licensecontent, signaturehex := licensestr[:i], licensestr[i+1:]
	pemblock, _ := pem.Decode([]byte(publicKey))
	if pemblock == nil {
		return fmt.Errorf("failed to parse PEM block containing the key")
	}
	pubcert, err := x509.ParseCertificate(pemblock.Bytes)
	if err != nil {
		return err
	}
	rsapubkey, ok := pubcert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return fmt.Errorf("invalid public key")
	}
	signature, err := base64.StdEncoding.DecodeString(signaturehex)
	if err != nil {
		return fmt.Errorf("invalid license")
	}
	sha256sum := sha256.Sum256([]byte(licensecontent))
	if err := rsa.VerifyPKCS1v15(rsapubkey, crypto.SHA256, sha256sum[:], signature); err != nil {
		return err
	}
	// decode the license
	if err := json.Unmarshal([]byte(licensecontent), into); err != nil {
		return err
	}
	return nil
}

// 使用AES-CFB模式进行加密
func EncryptAES(plainText, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	ciphertext := make([]byte, aes.BlockSize+len(plainText))
	iv := ciphertext[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}
	stream := cipher.NewCFBEncrypter(block, iv)
	stream.XORKeyStream(ciphertext[aes.BlockSize:], plainText)
	return ciphertext, nil
}

// 使用AES-CFB模式进行解密
func DecryptAES(ciphertext, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	if len(ciphertext) < aes.BlockSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	iv := ciphertext[:aes.BlockSize]
	ciphertext = ciphertext[aes.BlockSize:]
	stream := cipher.NewCFBDecrypter(block, iv)
	stream.XORKeyStream(ciphertext, ciphertext)
	return ciphertext, nil
}
