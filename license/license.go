package license

import (
	"context"
	"net/http"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
)

func NewAPI(base base.API, artifact *artifact.ArtifactService) *API {
	return &API{API: base, RegistryTokenIssuer: ArtifactServiceAsRegistryTokenIssuer{ArtifactService: artifact}}
}

type API struct {
	base.API
	RegistryTokenIssuer RegistryTokenIssuer
}

type License struct {
	store.ObjectMeta `json:",inline"`
	// For is the reference of the license
	For string `json:"for,omitempty"`
	// Serial is the license serial number
	Serial string `json:"serial,omitempty"`
	Issuer string `json:"issuer,omitempty"`
	// Subject is the license subject issue to
	Subject   string      `json:"subject,omitempty"`
	Email     string      `json:"email,omitempty"`
	NotBefore metav1.Time `json:"notBefore,omitempty"`
	NotAfter  metav1.Time `json:"notAfter,omitempty"`
	// Data is the encrypted license data
	Data string `json:"data,omitempty"`
	// Content is additional content of license details
	Content map[string]any `json:"content,omitempty"`
	// RegistrySecrets is the registry secrets
	RegistrySecrets []RegistryPullSecret `json:"registrySecrets,omitempty"`
	// Status license status
	Status LicenseStatus `json:"status,omitempty"`
}

func (a *API) ListLicenses(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		return base.GenericList(r, storage, &store.List[License]{}, store.WithSubScopes())
	})
}

func (a *API) DeleteLicense(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		ref := store.ObjectReference{}
		if err := api.Body(r, &ref); err != nil {
			return nil, err
		}
		if err := RevokeLicese(ctx, storage, ref, a.RegistryTokenIssuer); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) licenseAdminGroup() api.Group {
	return api.
		NewGroup("/licenses").
		Deprecated().
		Route(
			api.GET("").
				Doc("List all licenses").
				To(a.ListLicenses).
				Param(api.PageParams...).
				Response(store.List[License]{}),

			api.DELETE("").
				To(a.DeleteLicense).
				Param(
					api.BodyParam("license", store.ObjectReference{}),
				),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("License").
		SubGroup(
			a.licenseSignerGroup(),
			a.licenseRequestsGroup(),
			a.licenseAdminGroup(),
			a.tenantOrganizationLicenseGroup(),
		)
}
