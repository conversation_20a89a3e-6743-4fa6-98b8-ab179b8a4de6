package user

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/authdevice"
	"xiaoshiai.cn/core/authn"
)

type LoginMethodType string

const (
	LoginMethodTypePassword LoginMethodType = "Password"
	LoginMethodTypeOauth2   LoginMethodType = "OAuth2" // Third-party login
	LoginMethodTypeOTP      LoginMethodType = "OTP"    // One Time Password (OTP) / SMS / Email
	LoginMethodTypeWebAuthn LoginMethodType = "WebAuthn"
)

type LoginData struct {
	Type      LoginMethodType `json:"type" validate:"required"`
	Username  string          `json:"username"`
	RemeberMe bool            `json:"remeberMe"`
	//  Password is the password of the user
	Password PasswordData `json:"password"`
	// Auth2Code is the code from the oauth2 service
	// It is used to exchange the access token and verify the user
	Oauth2 Oauth2Data `json:"oauth2,omitempty"`
	// OTPCode is the one-time password
	OTP OTPData `json:"otp,omitempty"`
	// Captcha data
	Captcha CaptchaVerifyData `json:"captcha"`
}

type Oauth2Data struct {
	Provider string `json:"provider"`
	Code     string `json:"code"`
}

type OTPData struct {
	Provider string `json:"provider"`
	Code     string `json:"code"`
}

var (
	ErrInvalidUsernameOrPassword = liberrors.NewUnauthorized("Invalid account or password")
	ErrInvalidVerificationCode   = liberrors.NewUnauthorized("Invalid verification code")
	ErrSessionExpired            = liberrors.NewUnauthorized("Session expired")
)

var PhoneRegex = regexp.MustCompile(`^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$`)

func (a *API) Login(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		login := &LoginData{}
		if err := api.Body(r, login); err != nil {
			return nil, err
		}
		if auditlog := api.AuditLogFromContext(ctx); auditlog != nil {
			auditlog.Subject = login.Username
		}
		// captcha
		if err := a.Captcha.Verify(ctx, login.Captcha); err != nil {
			return nil, err
		}
		var loginuser *User
		switch login.Type {
		case LoginMethodTypePassword:
			user, matched, err := PasswordMatches(ctx, storage, login.Username, login.Password.Value)
			if err != nil {
				log.Error(err, "check password failed", "username", login.Username)
				return nil, ErrInvalidUsernameOrPassword
			}
			if !matched {
				return nil, ErrInvalidUsernameOrPassword
			}
			loginuser = user
		case LoginMethodTypeOTP:
			// find user from email or phone
			username := login.Username
			if strings.Contains(username, "@") {
				user, err := GetUserByEmail(ctx, storage, username)
				if err != nil {
					return nil, err
				}
				loginuser = user
			} else if PhoneRegex.MatchString(username) {
				user, err := GetUserByPhone(ctx, storage, username)
				if err != nil {
					return nil, err
				}
				loginuser = user
			} else {
				user, err := GetUserByUsername(ctx, storage, username)
				if err != nil {
					return nil, err
				}
				loginuser = user
			}
			if loginuser == nil {
				return nil, ErrInvalidUsernameOrPassword
			}
			ok, err := a.Verifier.Verify(ctx, loginuser.Name, login.Username, login.OTP.Code)
			if err != nil {
				log.Error(err, "verify otp failed", "username", login.Username)
				return nil, err
			}
			if !ok {
				return nil, ErrInvalidVerificationCode
			}
		default:
			return nil, liberrors.NewBadRequest(fmt.Sprintf("unsupported login type %s", login.Type))
		}
		if loginuser == nil {
			return nil, ErrInvalidUsernameOrPassword
		}
		// TODO: MFA

		tokenExpires := time.Duration(0)
		if !login.RemeberMe {
			tokenExpires = 24 * time.Hour
		}
		sessionopt := OpenSessionOptions{
			Device:  authdevice.UserDeviceFromContext(ctx),
			Expires: tokenExpires,
		}
		info := api.UserInfo{
			ID:            loginuser.UID,
			Name:          loginuser.Name,
			Email:         loginuser.Email,
			EmailVerified: loginuser.EmailVerified,
			Groups:        loginuser.Groups,
		}
		session, err := a.Session.OpenSession(ctx, info, sessionopt)
		if err != nil {
			return nil, err
		}
		tokenresp := SessionTokenResponse(session)
		return tokenresp, nil
	})
}

func GetUserByUsername(ctx context.Context, storage store.Store, username string) (*User, error) {
	user := &User{}
	if err := storage.Get(ctx, username, user); err != nil {
		return nil, err
	}
	return user, nil
}

func GetUserByEmail(ctx context.Context, storage store.Store, email string) (*User, error) {
	users := store.List[User]{}
	if err := storage.List(ctx, &users, store.WithFieldRequirements(store.RequirementEqual("email", email))); err != nil {
		return nil, err
	}
	if len(users.Items) == 0 {
		return nil, liberrors.NewNotFound("email", email)
	}
	return &users.Items[0], nil
}

func GetUserByPhone(ctx context.Context, storage store.Store, phone string) (*User, error) {
	users := store.List[User]{}
	if err := storage.List(ctx, &users, store.WithFieldRequirements(store.RequirementEqual("phone", phone))); err != nil {
		return nil, err
	}
	if len(users.Items) == 0 {
		return nil, liberrors.NewNotFound("phone", phone)
	}
	return &users.Items[0], nil
}

type TokenResponse struct {
	AcessToken   string `json:"access_token,omitempty"`
	ExpiresIn    int    `json:"expires_in,omitempty"`
	TokenType    string `json:"token_type,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	IDToken      string `json:"id_token,omitempty"`
}

func (a *API) SignOut(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		api.UnsetCookie(w, authn.SessionCookieKey)
		return nil, nil
	})
}

func (a *API) UserSignInSignOutGroup() api.Group {
	return api.
		NewGroup("").
		Tag("User").
		Route()
}
