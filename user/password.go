package user

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/user/htpassword"
)

type UserWithPassword struct {
	User     `json:",inline"`
	Password string `json:"password,omitempty"`
}

var _ store.ResourceName = &UserWithEncodedPassword{}

type UserWithEncodedPassword struct {
	User           `json:",inline"`
	EncodePassword string `json:"encodePassword,omitempty"`
}

// ResourceName implements store.ResourceName.
func (u *UserWithEncodedPassword) ResourceName() string {
	return "users"
}

func CreateUserWithPassword(ctx context.Context, storage store.Store, user User, password string) (*UserWithPassword, error) {
	if user.Name == "" {
		return nil, liberrors.NewBadRequest("User name is required")
	}
	hashed, err := PasswordHash(user.Name, password)
	if err != nil {
		return nil, err
	}
	up := &UserWithEncodedPassword{
		User:           user,
		EncodePassword: hashed,
	}
	if err := storage.Create(ctx, up); err != nil {
		return nil, err
	}
	ret := &UserWithPassword{
		User:     up.User,
		Password: password,
	}
	return ret, nil
}

func ReSetUserPassword(ctx context.Context, storage store.Store, user, password string) error {
	hashed, err := PasswordHash(user, password)
	if err != nil {
		return err
	}
	up := &UserWithEncodedPassword{}
	if err := storage.Get(ctx, user, up); err != nil {
		return err
	}
	up.EncodePassword = hashed
	return storage.Update(ctx, up)
}

func PasswordMatches(ctx context.Context, storage store.Store, user string, password string) (*User, bool, error) {
	// spcify the resource name
	up := &UserWithEncodedPassword{}
	if err := storage.Get(ctx, user, up); err != nil {
		return nil, false, err
	}
	ok, err := PasswordVerify(user, password, up.EncodePassword)
	if err != nil {
		return nil, false, err
	}
	if !ok {
		return nil, false, nil
	}
	return &up.User, true, nil
}

func MustPasswordHash(user, password string) string {
	hash, err := PasswordHash(user, password)
	if err != nil {
		panic(err)
	}
	return hash
}

func PasswordHash(user, password string) (string, error) {
	return htpassword.HTPasswdHash(user, password, htpassword.HashBCrypt)
}

func PasswordVerify(user, password, hash string) (bool, error) {
	return htpassword.HTPasswdVerify(user, password, hash)
}

type ResetPasswordRequest struct {
	Passowrd string `json:"password"`
}

func (a *API) ResetUserPassword(w http.ResponseWriter, r *http.Request) {
	a.onUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		req := &ResetPasswordRequest{}
		if r.ContentLength > 0 {
			if err := api.Body(r, req); err != nil {
				return nil, errors.NewBadRequest(err.Error())
			}
		} else {
			req.Passowrd = rand.RandomPassword(16)
		}
		if err := ReSetUserPassword(ctx, storage, username, req.Passowrd); err != nil {
			return nil, err
		}
		return req, nil
	})
}

type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword"`
	Password    string `json:"password"`
}

func (a *API) CurrentChangePassword(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		req := &ChangePasswordRequest{}
		if err := api.Body(r, req); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		_, ok, err := PasswordMatches(ctx, storage, username, req.OldPassword)
		if err != nil {
			return nil, errors.NewInternalError(err)
		}
		if !ok {
			return nil, errors.NewBadRequest("incorrect password")
		}
		if err := ReSetUserPassword(ctx, storage, username, req.Password); err != nil {
			return nil, err
		}
		return req, nil
	})
}
