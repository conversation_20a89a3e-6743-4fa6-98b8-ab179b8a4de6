package user

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type UserProfile struct {
	store.ObjectMeta `json:",inline"`
	Prerefences      map[string]string `json:"preferences,omitempty"`
	Email            string            `json:"email,omitempty"`
}

func (a *API) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	a.onUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		return base.GenericGet(r, storage, &UserProfile{}, username)
	})
}

func (a *API) UpdateUserProfile(w http.ResponseWriter, r *http.Request) {
	a.onUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		return base.GenericUpdate(r, storage, &UserProfile{}, username)
	})
}

func (a *API) userProfileGroup() api.Group {
	return api.
		NewGroup("/profile").
		Route(
			api.GET("").
				Doc("Get user profile").
				To(a.GetUserProfile),
			api.PUT("").
				Doc("Update user profile").
				To(a.UpdateUserProfile),
		)
}

func (a *API) CurrentGetUserProfile(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		profile := &UserProfile{}
		if err := storage.Get(ctx, username, profile); err != nil {
			if !errors.IsNotFound(err) {
				return nil, err
			}
			// in case of user exists but not profile, create a new profile
			profile = &UserProfile{ObjectMeta: store.ObjectMeta{Name: username}}
			if err := storage.Create(ctx, profile); err != nil {
				return nil, err
			}
		}
		return profile, nil
	})
}

func (a *API) CurrentUpdateUserProfile(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		return base.GenericUpdate(r, storage, &UserProfile{}, username)
	})
}
