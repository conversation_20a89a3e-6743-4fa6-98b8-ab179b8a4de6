package user

import "context"

func NewUniversalCaptcha(ctx context.Context) (*UniversalCaptcha, error) {
	providers := []CaptchaProvider{}

	return &UniversalCaptcha{Providers: providers}, nil
}

var _ CaptchaProvider = &UniversalCaptcha{}

type UniversalCaptcha struct {
	Providers []CaptchaProvider
}

// Get implements Provider.
func (u *UniversalCaptcha) Get(ctx context.Context, options GetCaptchaOptions) ([]CaptchaConfig, error) {
	return nil, nil
}

// Verify implements Provider.
func (u *UniversalCaptcha) Verify(ctx context.Context, data CaptchaVerifyData) error {
	return nil
}
