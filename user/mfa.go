package user

import (
	"context"
	"fmt"
	"net/http"
	"net/url"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

type MultiFactorAuthManager interface {
	// Get generates a new mfa token for the given action
	Generate(ctx context.Context, username string) (MfaBindConfig, error)
	// Get gets the mfa token for the given action
	Get(ctx context.Context, username string) (MfaBindConfig, error)
	// Verify verifies the mfa token
	Verify(ctx context.Context, username string, code string) error
}

type MfaConfig struct {
	Provider string `json:"provider"`
	Key      string `json:"key"`
}

type MfaVerifyData struct {
	Provider string `json:"provider"`
	Token    string `json:"token"`
}

type MfaBindConfig struct {
	Username string `json:"username,omitempty"`
	Secret   string `json:"secret,omitempty"`
	// RecoveryCodes allow the user to recover the MFA if the user lost the device
	RecoveryCodes []string `json:"recoveryCodes,omitempty"`
}

type InitMFAResponse struct {
	MfaBindConfig `json:",inline"`
	// URL is an otpauth URL for the MFA
	// https://github.com/google/google-authenticator/wiki/Key-Uri-Format
	// https://datatracker.ietf.org/doc/draft-linuxgemini-otpauth-uri/01/
	URL string `json:"url,omitempty"`
}

func (a *API) InitMFA(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storag store.Store, username string) (any, error) {
		user, err := GetUserByUsername(ctx, storag, username)
		if err != nil {
			return nil, err
		}
		if user.MFA.Enabled {
			return nil, errors.NewBadRequest("MFA is already enabled")
		}
		config, err := a.MFA.Generate(ctx, username)
		if err != nil {
			return nil, err
		}
		// https://github.com/google/google-authenticator/wiki/Key-Uri-Format
		issuer := "BOB"
		otpauth := url.URL{
			Scheme: "otpauth",
			// must be totp
			// https://tools.ietf.org/html/rfc6238
			Path:     fmt.Sprintf("totp/%s: %s", issuer, config.Username),
			RawQuery: url.Values{"secret": []string{config.Secret}, "issuer": []string{issuer}}.Encode(),
		}
		ret := InitMFAResponse{
			MfaBindConfig: config,
			URL:           otpauth.String(),
		}
		return ret, nil
	})
}

func (a *API) VerfiyMFA(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storag store.Store, username string) (any, error) {
		data := &MfaVerifyData{}
		if err := api.Body(r, data); err != nil {
			return nil, err
		}
		if err := a.MFA.Verify(ctx, username, data.Token); err != nil {
			return nil, err
		}
		return nil, nil
	})
}
