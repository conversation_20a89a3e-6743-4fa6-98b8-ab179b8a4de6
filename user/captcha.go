package user

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
)

type CaptchaProvider interface {
	// Get generates a new captcha token for the given action
	// it returns all available captcha configurations
	Get(ctx context.Context, options GetCaptchaOptions) ([]CaptchaConfig, error)
	Verify(ctx context.Context, data CaptchaVerifyData) error
}

type CaptchaConfig struct {
	Provider string `json:"provider"`
	Key      string `json:"key"`
}

type CaptchaVerifyData struct {
	Provider string `json:"provider"`
	Key      string `json:"key"`
	Token    string `json:"token"`
}

type GetCaptchaOptions struct {
	Action string `json:"action,omitempty"`
}

func (a API) Get(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		options := GetCaptchaOptions{
			Action: api.Query(r, "action", ""),
		}
		return a.Captcha.Get(r.Context(), options)
	})
}

func (a *API) CaptchaGroup() api.Group {
	return api.
		NewGroup("/captcha").
		Tag("Recaptcha")
}
