package htpassword

import (
	"crypto/sha1"
	"encoding/base64"
	"errors"
	"fmt"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

type HTHashAlgorithm string

const (
	HashBCrypt    HTHashAlgorithm = "bcrypt"
	HashSHA       HTHashAlgorithm = "sha"
	HashCrypt     HTHashAlgorithm = "crypt"
	HashPlainText HTHashAlgorithm = "plaintext"
)

func HTPasswdVerify(user, pass, htpasswd string) (bool, error) {
	htuser, hashed, algo, err := HtpasswdParse(htpasswd)
	if err != nil {
		return false, err
	}
	if user != htuser {
		return false, nil
	}
	switch algo {
	case HashBCrypt:
		return bcrypt.CompareHashAndPassword([]byte(hashed), []byte(pass)) == nil, nil
	case HashSHA:
		return hashed == hashSha1(user, pass), nil
	case HashPlainText:
		return hashed == pass, nil
	default:
		return false, fmt.Errorf("unknown algorithm %s", algo)
	}
}

// ParseHtpasswdString returns user, hashed password and algorithm
func HtpasswdParse(htpasswd string) (string, string, HTHashAlgorithm, error) {
	index := strings.Index(htpasswd, ":")
	if index == -1 {
		return "", "", "", errors.New("invalid htpassword")
	}
	user, hashed := htpasswd[:index], htpasswd[index+1:]
	if strings.HasPrefix(hashed, "$2y$") {
		return user, hashed[4:], HashBCrypt, nil
	}
	if strings.HasPrefix(hashed, "{SHA}") {
		return user, hashed[5:], HashSHA, nil
	}
	return user, hashed, HashPlainText, nil
}

func HTPasswdHash(user, pass string, algo HTHashAlgorithm) (string, error) {
	switch algo {
	case HashBCrypt:
		return hashBcrypt(user, pass)
	case HashSHA:
		return hashSha1(user, pass), nil
	case HashPlainText:
		return fmt.Sprintf("%s:%s", user, pass), nil
	}
	return "", fmt.Errorf("unknown algorithm %s", algo)
}

// SHA1
// "{SHA}" + Base64-encoded SHA-1 digest of the password. Insecure.
func hashSha1(user, password string) string {
	hasher := sha1.New()
	hasher.Write([]byte(password))
	hashed := hasher.Sum(nil)
	pass := base64.StdEncoding.EncodeToString(hashed)
	return fmt.Sprintf("%s:{SHA}%s", user, pass)
}

// bcrypt
// "$2y$" + the result of the crypt_blowfish algorithm. See the APR source file crypt_blowfish.c for the details of the algorithm.
func hashBcrypt(user, password string) (string, error) {
	passwordBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%s:$2y$%s", user, string(passwordBytes)), nil
}
