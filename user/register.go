package user

import (
	"context"
	"net/http"
	"time"

	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

// UserVerifier is the interface for user verification
// such as msm or email
type UserVerifier interface {
	// Send sends the verification code to the target
	Send(ctx context.Context, username, target string) error
	// Verify verifies the code
	Verify(ctx context.Context, username, target, code string) (bool, error)
	// Check checks if the user is verified
	Check(ctx context.Context, username, target string, ttl time.Duration) (bool, error)
}

type SendCodeRequest struct {
	// Username is the username for the OTP code
	Username string `json:"username"`
	// Type email or phone
	Type string `json:"type"`
	// Target is the target for the OTP code
	// The target could be the `email` or `phone number`
	Target string `json:"target"`
	// Action is the action for the OTP code request
	// The action could be `login`, `register`, `reset`
	Action string `json:"action"`
	// Captcha is the captcha for this request
	Captcha CaptchaVerifyData `json:"captcha"`
}

func (a *API) SendCode(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		req := &SendCodeRequest{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		// captcha
		if err := a.Captcha.Verify(ctx, req.Captcha); err != nil {
			return nil, err
		}
		// send email verify code
		if err := a.Verifier.Send(ctx, req.Username, req.Target); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

type RegisterData struct {
	Username    string `json:"username" validate:"required"`
	DisplayName string `json:"displayName"`
	//  Password is the password of the user
	Password PasswordData `json:"password"`
	// Email is the email of the user
	Email EmailData `json:"email"`
	// Phone is the phone number of the user
	Phone PhoneData `json:"phone"`
	// Agreement is the agreement of the user
	Agreement bool `json:"agreement"`
	// Captcha is the captcha for this request
	Captcha CaptchaVerifyData `json:"captcha"`
}

type PasswordAlgorithm string

const (
	PasswordAlgorithmPlainText PasswordAlgorithm = "PlainText"
	PasswordAlgorithmBCrypt    PasswordAlgorithm = "BCrypt"
)

type PasswordData struct {
	Value     string            `json:"value"`
	Algorithm PasswordAlgorithm `json:"algorithm"`
}

type EmailData struct {
	// Value is the email address
	Value string `json:"value" validate:"email"`
	// Code is the verification code of the email
	Code string `json:"code"`
}

type PhoneData struct {
	// Value is the phone number
	Value string `json:"value"`
	// Code is the verification code of the phone
	Code string `json:"code"`
}

func (a *API) Register(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		req := &RegisterData{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		if req.Password.Value == "" {
			return nil, liberrors.NewBadRequest("password is required")
		}
		if req.Email.Value == "" {
			return nil, liberrors.NewBadRequest("email is required")
		}
		if err := a.Captcha.Verify(ctx, req.Captcha); err != nil {
			return nil, err
		}
		existsVerify := UserIdentityField{
			Username: req.Username,
			Phone:    req.Phone.Value,
			Email:    req.Email.Value,
		}
		if err := VerifyUserExists(ctx, storage, existsVerify); err != nil {
			return nil, err
		}
		// verify email code
		ok, err := a.Verifier.Verify(ctx, req.Username, req.Email.Value, req.Email.Code)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, ErrInvalidVerificationCode
		}
		// create user
		u := User{
			ObjectMeta: store.ObjectMeta{
				Name: req.Username,
			},
			Email: req.Email.Value,
		}
		return CreateUserWithPassword(ctx, storage, u, req.Password.Value)
	})
}

type UserIdentityField struct {
	Username string `json:"username,omitempty"`
	Phone    string `json:"phone,omitempty"`
	Email    string `json:"email,omitempty"`
}

func VerifyUserExists(ctx context.Context, storage store.Store, verify UserIdentityField) error {
	if username := verify.Username; username != "" {
		if err := storage.Get(ctx, username, &User{}); err != nil {
			if liberrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		return liberrors.NewAlreadyExists("username", username)
	}
	if phone := verify.Phone; phone != "" {
		count, err := storage.Count(ctx, &User{}, store.WithCountFieldRequirementsFromSet(
			map[string]string{"phone": verify.Phone},
		))
		if err != nil {
			return err
		}
		if count > 0 {
			return liberrors.NewAlreadyExists("phone", phone)
		}
	}
	if verify.Email != "" {
		count, err := storage.Count(ctx, &User{}, store.WithCountFieldRequirementsFromSet(
			map[string]string{"email": verify.Email},
		))
		if err != nil {
			return err
		}
		if count > 0 {
			return liberrors.NewAlreadyExists("email", verify.Email)
		}
	}
	return nil
}
