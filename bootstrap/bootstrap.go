package bootstrap

import (
	"bytes"
	"context"
	"embed"
	"io"
	"reflect"

	"gopkg.in/yaml.v2"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/market"
	"xiaoshiai.cn/core/observerability"
	"xiaoshiai.cn/core/pay/cost"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/pay/promotion"
	"xiaoshiai.cn/core/rbac"
	"xiaoshiai.cn/core/user"
)

func InitStorage(ctx context.Context, store store.Store, mongo *mongo.MongoStorage, sku product.SKUService) error {
	if err := rbac.InitRoles(ctx, store, rbac.DefaultSystemRoles, true); err != nil {
		return err
	}
	if err := initUsers(ctx, store); err != nil {
		return err
	}
	if err := initUserRoles(ctx, store, []string{"admin"}); err != nil {
		return err
	}
	if err := initAlertRuleTemplates(ctx, store); err != nil {
		return err
	}
	if err := cost.Init(ctx, mongo, sku); err != nil {
		return err
	}
	if err := promotion.InitDiscounts(ctx, mongo); err != nil {
		return err
	}
	if err := market.InitBuiltInActegories(ctx, store); err != nil {
		return err
	}
	return nil
}

func initUsers(ctx context.Context, s store.Store) error {
	type UserWithPassword struct {
		user.User `json:",inline"`
		Password  string `json:"password"`
	}
	users := []UserWithPassword{
		{
			User:     user.User{ObjectMeta: store.ObjectMeta{Name: "admin", Description: "Administrator"}},
			Password: "admin",
		},
	}
	for _, item := range users {
		// check if user exists
		if err := s.Get(ctx, item.GetName(), &item.User); err != nil {
			if !liberrors.IsNotFound(err) {
				return err
			}
			if _, err := user.CreateUserWithPassword(ctx, s, item.User, item.Password); err != nil {
				return err
			}
		}
	}
	return nil
}

func initUserRoles(ctx context.Context, s store.Store, users []string) error {
	for _, user := range users {
		userRole := &rbac.UserRole{
			ObjectMeta: store.ObjectMeta{Name: user},
			Roles:      []string{rbac.RoleAdmin},
		}
		if err := store.CreateIfNotExists(ctx, s, userRole); err != nil {
			return err
		}
	}
	return nil
}

//go:embed template.yaml
var configFile embed.FS

func initAlertRuleTemplates(ctx context.Context, s store.Store) error {
	logger := log.FromContext(ctx)
	logger.Info("initAlertRuleTemplates", "templateFileName", "template.yaml")
	data, err := configFile.ReadFile("template.yaml")
	if err != nil {
		return err
	}
	ts, err := parseYAML(bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	logger.Info("initAlertRuleTemplates start parse")
	for _, templateGroup := range *ts {
		group := templateGroup.GroupName
		storage := s
		if err := storage.Get(ctx, group, &observerability.AlertRuleTemplateGroup{}); err != nil {
			if !liberrors.IsNotFound(err) {
				return err
			}
			// create templateGroup
			if err := storage.Create(ctx, &observerability.AlertRuleTemplateGroup{
				ObjectMeta: store.ObjectMeta{
					Name: group,
				},
				ShowName: group,
			}); err != nil {
				return err
			}
		}
		for _, templateGroupResource := range templateGroup.Resources {
			storage := storage.Scope(store.Scope{Resource: "alertruletemplategroups", Name: group})
			resourceGroup := templateGroupResource.ResourceName
			if err := storage.Get(ctx, resourceGroup, &observerability.AlertRuleTemplateResourceGroup{}); err != nil {
				if !liberrors.IsNotFound(err) {
					return err
				}
				// create templateGroupResource
				if err := storage.Create(ctx, &observerability.AlertRuleTemplateResourceGroup{
					ObjectMeta: store.ObjectMeta{
						Name: resourceGroup,
					},
					ShowName: resourceGroup,
				}); err != nil {
					return err
				}
			}
			for _, ele := range templateGroupResource.Templates {
				storage := storage.Scope(store.Scope{Resource: "alertruletemplateresourcegroups", Name: resourceGroup})
				alertRuleTemplate := &observerability.AlertRuleTemplate{}
				if err := storage.Get(ctx, ele.TemplateName, alertRuleTemplate); err != nil {
					if !liberrors.IsNotFound(err) {
						return err
					}
					// create ruletemplate
					if err := storage.Create(ctx, &observerability.AlertRuleTemplate{
						ObjectMeta: store.ObjectMeta{
							Name: ele.TemplateName,
						},
						Template: ele.Template,
					}); err != nil {
						return err
					}
					continue
				}
				// update
				if reflect.DeepEqual(alertRuleTemplate.Template, ele.Template) {
					continue
				}
				alertRuleTemplate.Template = ele.Template
				if err := storage.Update(ctx, alertRuleTemplate); err != nil {
					return err
				}
			}
		}

	}

	return nil
}

type templates []template

type template struct {
	GroupName string             `yaml:"groupName"`
	Resources []templateResource `yaml:"resources"`
}

type templateResource struct {
	ResourceName string                `yaml:"resourceName"`
	Templates    []templateRuleWrapper `yaml:"templates"`
}

type templateRuleWrapper struct {
	TemplateName string                           `yaml:"templateName"`
	Template     *observerability.PromqlGenerator `yaml:"template"`
}

func parseYAML(r io.Reader) (*templates, error) {
	var ts templates
	decoder := yaml.NewDecoder(r)
	if err := decoder.Decode(&ts); err != nil {
		return nil, err
	}
	return &ts, nil
}
