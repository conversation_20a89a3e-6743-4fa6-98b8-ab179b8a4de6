- groupName: Cluster
  resources:
    - resourceName: Kubernetes
      templates:
        - templateName: ClusterCertExpirationRemainTime
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Cluster Certs Expiration Time (second)
            templateType: cluster
            expr: gems_agent_cluster_component_cert_expiration_remain_seconds
            resource: component
            unit: duration-s
            labels:
              - component
        - templateName: ClusterCPUUsagePercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Cluster CPU Usage Ratio (%)
            templateType: cluster
            expr: (1 - avg(irate(node_cpu_seconds_total{mode="idle"}[5m]))) * 100
            resource: node
            unit: percent-0-100
            labels:
              - node
        - templateName: ClusterMemoryUsagePercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Cluster Memory Usage Ratio (%)
            templateType: cluster
            expr: (1- sum(node_memory_MemAvailable_bytes) / sum(node_memory_MemTotal_bytes))* 100
            resource: node
            unit: percent-0-100
            labels:
              - node
        - templateName: ClusterNamespaceCPUUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Namespace CPU Usage (1000m=1Core)
            templateType: cluster
            expr: gems_namespace_cpu_usage_cores
            resource: environment
            unit: short
            labels:
              - tenant
              - namespace
        - templateName: ClusterNamespaceMemoryUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Namespace Memory Usage (bytes)
            templateType: cluster
            expr: gems_namespace_memory_usage_bytes
            resource: environment
            unit: bytes-B
            labels:
              - tenant
              - namespace
        - templateName: ClusterNamespaceNetworkInBPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Namespace Network Received (bytes/s)
            templateType: cluster
            expr: gems_namespace_network_receive_bps
            resource: environment
            unit: bytes/sec-B/s
            labels:
              - tenant
              - namespace
        - templateName: ClusterNamespaceNetworkOutBPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Namespace Network Sent (bytes/s)
            templateType: cluster
            expr: gems_namespace_network_send_bps
            resource: environment
            unit: bytes/sec-B/s
            labels:
              - tenant
              - namespace
        - templateName: ClusterNamespaceVolumeUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Namespace PersistentVolume Usage (bytes)
            templateType: cluster
            expr: gems_namespace_pvc_usage_bytes
            resource: persistentvolumeclaim
            unit: bytes-B
            labels:
              - tenant
              - namespace
        - templateName: NodeCPUTotal
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node CPU Total (1000m=1Core)
            templateType: cluster
            expr: gems_node_cpu_total_cores
            resource: node
            unit: short
            labels:
              - node
        - templateName: NodeCPUUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node CPU Total Usage (1000m=1Core)
            templateType: cluster
            expr: gems_node_cpu_usage_cores
            resource: node
            unit: short
            labels:
              - node
        - templateName: NodeCPUUsagePercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node CPU Usage Ratio (%)
            templateType: cluster
            expr: gems_node_cpu_usage_percent
            resource: node
            unit: percent-0-100
            labels:
              - node
        - templateName: NodeDiskReadBPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Read (bytes/s)
            templateType: cluster
            expr: gems_node_disk_read_bps
            resource: node
            unit: bytes/sec-B/s
            labels:
              - node
        - templateName: NodeDiskReadIOPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Read OPS
            templateType: cluster
            expr: gems_node_disk_read_iops
            resource: node
            unit: ""
            labels:
              - node
        - templateName: NodeDiskTotal
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Total Size (bytes)
            templateType: cluster
            expr: gems_node_disk_total_bytes
            resource: node
            unit: bytes-B
            labels:
              - node
              - device
        - templateName: NodeDiskUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Usage  (bytes)
            templateType: cluster
            expr: gems_node_disk_usage_bytes
            resource: node
            unit: bytes-B
            labels:
              - node
              - device
        - templateName: NodeDiskUsagePercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Usage Ratio (%)
            templateType: cluster
            expr: gems_node_disk_usage_percent
            resource: node
            unit: percent-0-100
            labels:
              - node
              - device
        - templateName: NodeDiskWriteBPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Write (bytes/s)
            templateType: cluster
            expr: gems_node_disk_write_bps
            resource: node
            unit: bytes/sec-B/s
            labels:
              - node
        - templateName: NodeDiskWriteIOPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Disk Write OPS
            templateType: cluster
            expr: gems_node_disk_write_iops
            resource: node
            unit: ""
            labels:
              - node
        - templateName: NodeLoad1
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Load 1
            templateType: cluster
            expr: gems_node_load1
            resource: node
            unit: ""
            labels:
              - node
        - templateName: NodeLoad5
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Load 5
            templateType: cluster
            expr: gems_node_load5
            resource: node
            unit: ""
            labels:
              - node
        - templateName: NodeLoad15
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Load 15
            templateType: cluster
            expr: gems_node_load15
            resource: node
            unit: ""
            labels:
              - node
        - templateName: NodeMemoryTotal
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Memory Total Size (bytes)
            templateType: cluster
            expr: gems_node_memory_total_bytes
            resource: node
            unit: bytes-B
            labels:
              - node
        - templateName: NodeMemoryUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Memory Usage (bytes)
            templateType: cluster
            expr: gems_node_memory_usage_bytes
            resource: node
            unit: bytes-B
            labels:
              - node
        - templateName: NodeMemoryUsagePercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Memory Usage Ratio (%)
            templateType: cluster
            expr: gems_node_memory_usage_percent
            resource: node
            unit: percent-0-100
            labels:
              - node
        - templateName: NodeNetworkInBPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Network Received (bytes/s)
            templateType: cluster
            expr: gems_node_network_receive_bps
            resource: node
            unit: bytes/sec-B/s
            labels:
              - node
        - templateName: NodeNetworkInErrPercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Network Receive ErrorPack Ratio (%)
            templateType: cluster
            expr: gems_node_network_receive_errs_percent
            resource: node
            unit: percent-0-100
            labels:
              - node
              - instance
              - device
        - templateName: NodeNetworkOutBPS
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Network Sent (bytes/s)
            templateType: cluster
            expr: gems_node_network_send_bps
            resource: node
            unit: bytes/sec-B/s
            labels:
              - node
        - templateName: NodeNetworkOutErrPercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Network Sent ErrorPack Ratio (%)
            templateType: cluster
            expr: gems_node_network_send_errs_percent
            resource: node
            unit: percent-0-100
            labels:
              - node
              - instance
              - device
        - templateName: NodeRunningPodCount
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Running Pods
            templateType: cluster
            expr: gems_node_running_pod_count
            resource: node
            unit: ""
            labels:
              - node
        - templateName: NodeRunningPodPercent
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Running Pod Usage Ratio (%)
            templateType: cluster
            expr: gems_node_running_pod_percent
            resource: node
            unit: percent-0-100
            labels:
              - node
        - templateName: NodeOffline
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Offline
            templateType: cluster
            expr: kube_node_status_condition{condition="Ready",status="true"}
            resource: node
            unit: ""
            labels:
              - node
              - condition
              - status
        - templateName: NodeStatusCondition
          template:
            templateGroup: Cluster
            templateGroupResource: Kubernetes
            templateShowName: Node Kubelet Status Condition
            templateType: cluster
            expr: kube_node_status_condition
            resource: node
            unit: ""
            labels:
              - node
              - condition
              - status
    - resourceName: Huawei
      templates:
        - templateName: HuaweiHostCPUUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Huawei
            templateShowName: Huawei Host Allocated CPU Percentage
            templateType: cluster
            resource: host
            expr: ismc_host_cpu_used_percentage
            unit: percent-0-100
            labels:
              - host
        - templateName: HuaweiHostMemoryUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Huawei
            templateShowName: Huawei Host Allocated Memory Percentage
            templateType: cluster
            resource: host
            expr: ismc_host_memory_used_percentage
            unit: percent-0-100
            labels:
              - host
        - templateName: HuaweiHostDiskUsage
          template:
            templateGroup: Cluster
            templateGroupResource: Huawei
            templateShowName: Huawei Host Allocated Disk Percentage
            templateType: cluster
            resource: host
            expr: ismc_host_disk_used_percentage
            unit: percent-0-100
            labels:
              - host
    - resourceName: VMware
      templates:
        - templateName: VMwareHostMemoryUsage
          template:
            templateGroup: Cluster
            templateGroupResource: VMware
            templateShowName: ESXI Host Memory Usage
            templateType: cluster
            resource: hostsystem
            expr: ismc_host_memory_used_percentage
            unit: percent-0-100
            labels:
              - host
              - namespace
              - service
        - templateName: VMwareHostCPUUsage
          template:
            templateGroup: Cluster
            templateGroupResource: VMware
            templateShowName: ESXI Host CPU Usage
            templateType: cluster
            resource: hostsystem
            expr: ismc_host_cpu_used_percentage
            unit: percent-0-100
            labels:
              - host
              - namespace
              - service
- groupName: Middleware
  resources:
    - resourceName: Mysql
      templates:
        - templateName: MysqlCommandTop10
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Command Top10
            templateType: application
            resource: command
            expr: topk(10, rate(mysql_global_status_commands_total{#}[5m])>0)
            unit: ""
            labels:
              - service
              - command
              - namespace
        - templateName: MysqlConnections
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Connections
            templateType: application
            resource: service
            expr: mysql_global_status_max_used_connections{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlInnodbBufferSize
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: InnoDB Buffer Pool (bytes)
            templateType: application
            resource: service
            expr: mysql_global_variables_innodb_buffer_pool_size{#}
            unit: bytes-B
            labels:
              - service
              - namespace
        - templateName: MysqlOpenFiles
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Open Files
            templateType: application
            resource: service
            expr: mysql_global_status_innodb_num_open_files{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlQPS
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: QPS
            templateType: application
            resource: service
            expr: rate(mysql_global_status_queries{#}[5m])
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlQuestions
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Questions Rate (questions/s)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_questions{#}[5m])
            unit: ""
            labels:
              - service
        - templateName: MysqlReceivedBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Network Received (bytes/s)
            templateType: application
            resource: service
            expr: irate(mysql_global_status_bytes_received{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlSentBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Network Sent (bytes/s)
            templateType: application
            resource: service
            expr: irate(mysql_global_status_bytes_sent{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlSlowQuery
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Slow Queries Rate (queries/min)
            templateType: application
            resource: service
            expr: idelta(mysql_global_status_slow_queries{#}[1m])
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSlowQueryEnabled
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: MySQL Slow Query Enabled
            templateType: application
            resource: service
            expr: mysql_global_variables_slow_query_log{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlState
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Up
            templateType: application
            resource: service
            expr: mysql_up{@}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTPS
          template:
            templateGroup: Middlewares
            templateGroupResource: Mysql
            templateShowName: TPS
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_commands_total{command=~"insert|update|delete",#}[5m])) without (command)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTabelLockWaited
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Table Locks Waited 5 Minutes
            templateType: application
            resource: service
            expr: sum(increase(mysql_global_status_table_locks_waited{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTableOpenCacheHitRatio
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Table Open Cache Hit Ratio (%)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_table_open_cache_hits{#}[5m]) / ( rate(mysql_global_status_table_open_cache_hits{#}[5m]) + rate(mysql_global_status_table_open_cache_misses{#}[5m]))
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: MysqlTableSize
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Table Size (bytes)
            templateType: application
            resource: service
            expr: sum by (schema,service,namespace) (mysql_info_schema_table_size{#})
            unit: bytes-B
            labels:
              - service
              - namespace
        - templateName: MysqlThreads
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Threads (by state)
            templateType: application
            resource: service
            expr: mysql_info_schema_threads{#}
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MysqlTmpTables
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Tmp Table Created Rate (tables/s)
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_created_tmp_tables{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTotalRows
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Table Rows
            templateType: application
            resource: service
            expr: sum(mysql_info_schema_table_rows{#}) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlRunningThread
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Running Threads
            templateType: application
            resource: service
            expr: mysql_global_status_threads_running{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlInnodbLogWait
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: InnoDB Log Waits
            templateType: application
            resource: service
            expr: mysql_global_status_innodb_log_waits{#}
            unit: ""
            labels:
              - namespace
              - service
        - templateName: MysqlInnodbDataWrite
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: InnoDB Data Write Rate (bytes/s)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_innodb_data_writes{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlInnodbDataRead
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: InnoDB Data Read Rate (bytes/s)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_innodb_data_read{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlErrorConnections
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Error Connections Per Minute (by type)
            templateType: application
            resource: service
            expr: delta(mysql_global_status_connection_errors_total{#}[1m])
            unit: ""
            labels:
              - namespace
              - service
              - error
        - templateName: MysqlHandler
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Handler Rate (second)
            templateType: application
            resource: handler
            expr: rate(mysql_global_status_handlers_total{handler=~"commit|rollback|savepoint.*|prepare",#}[5m])
            unit: short
            labels:
              - service
              - namespace
              - handler
        - templateName: MysqlSortRows
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Sort Rows Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_rows{#}[5m])) by (service,namespace)
            unit: short
            labels:
              - service
              - namespace
        - templateName: MysqlSortRange
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Sort Range Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_range{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSortMergePass
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Sort Merge Passes Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_merge_passes{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - namespace
              - service
        - templateName: MysqlSortScan
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Sort Scan Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_scan{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectFullJoin
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Select Full Join Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_full_join{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectRangeJoin
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Select Full Range Join Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_full_range_join{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectRange
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Select Range Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_range{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectScan
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Select Scan Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_scan{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlBufferPoolPage
          template:
            templateGroup: Middleware
            templateGroupResource: Mysql
            templateShowName: Buffer Pool Pages Ratio (%)
            templateType: application
            resource: service
            expr: (sum by (namespace,service)(mysql_global_status_buffer_pool_pages{#}) - sum by (namespace,service)(mysql_global_status_buffer_pool_pages{state="free",#})) / sum(mysql_global_status_buffer_pool_pages{#}) by (namespace,service)
            unit: percent-0.0-1.0
            labels:
              - namespace
              - service
    - resourceName: Redis
      templates:
        - templateName: RedisClients
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Connected Clients
            templateType: application
            resource: service
            expr: redis_connected_clients{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisCommandsTop5
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Commands Top5
            templateType: application
            resource: cmd
            expr: topk(5,irate(redis_commands_total{#}[5m]))
            unit: ""
            labels:
              - service
              - cmd
              - namespace
        - templateName: RedisCpuUsage
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: CPU Usage (1000m=1Core)
            templateType: application
            resource: service
            expr: irate(redis_cpu_user_seconds_total{#}[5m]) + irate(redis_cpu_sys_seconds_total{#}[5m])
            unit: short
            labels:
              - service
              - namespace
        - templateName: RedisKeys
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: DB Keys
            templateType: application
            resource: db
            expr: sum (redis_db_keys{#}) by (db,namespace,service)
            unit: ""
            labels:
              - service
              - db
              - namespace
        - templateName: RedisKeysEvicted
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Evicted Keys Total
            templateType: application
            resource: service
            expr: redis_evicted_keys_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisKeysExpiring
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Expireing Keys
            templateType: application
            resource: service
            expr: redis_db_keys_expiring{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisKeysHitRate
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Keys Hit Ratio (%)
            templateType: application
            resource: service
            expr: redis_keyspace_hits_total{#} / (redis_keyspace_hits_total{#} + redis_keyspace_misses_total{#})
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: RedisKeysHits
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Keys Hits Total
            templateType: application
            resource: service
            expr: redis_keyspace_hits_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisKeysMissed
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Key Misses Total
            templateType: application
            resource: service
            expr: redis_keyspace_misses_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisMemoryUsedBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Memory Used Bytes
            templateType: application
            resource: service
            expr: redis_memory_used_bytes{#}
            unit: bytes-B
            labels:
              - service
              - namespace
        - templateName: RedisNoExpiringKeyRate
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Not-Expiring Keys Ratio (%)
            templateType: application
            resource: service
            expr: 1 - sum(redis_db_keys_expiring{#}) by (namespace, service) / sum(redis_db_keys{#}) by (namespace,service)
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: RedisOPS
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Commands Processed (op/s)
            templateType: application
            resource: service
            expr: irate(redis_commands_processed_total{#}[5m])
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisReceivedByted
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Network Received (bytes/s)
            templateType: application
            resource: service
            expr: irate(redis_net_input_bytes_total{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: RedisRejectedConnectionsTotal
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Rejected Connections Total
            templateType: application
            resource: service
            expr: redis_rejected_connections_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisSentBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Network Sent (bytes/s)
            templateType: application
            resource: service
            expr: irate(redis_net_output_bytes_total{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: RedisState
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Up
            templateType: application
            resource: service
            expr: redis_up{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisRssMemoryUsedBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Redis
            templateShowName: Redis Memory Used Bytes
            templateType: application
            resource: service
            expr: redis_memory_used_rss_bytes{#}
            unit: bytes-B
            labels:
              - service
              - namespace
    - resourceName: MongoDB
      templates:
        - templateName: MongdbState
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Up
            templateType: application
            resource: service
            expr: mongodb_up{@}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongdoResponseTime
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Response Time (us)
            templateType: application
            resource: type
            expr: rate(mongodb_mongod_op_latencies_latency_total{#}[5m]) / rate(mongodb_mongod_op_latencies_ops_total{#}[5m])
            unit: duration-us
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbAsserts
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Asserts Total
            templateType: application
            resource: type
            expr: rate(mongodb_asserts_total{#}[5m])
            unit: ""
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbCacheBytes
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache Size (bytes)
            templateType: application
            resource: type
            expr: mongodb_mongod_wiredtiger_cache_bytes{#}
            unit: bytes-B
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbConnections
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Connections
            templateType: application
            resource: state
            expr: mongodb_connections{#}
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MongodbCursor
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Cursor Open
            templateType: application
            resource: state
            expr: mongodb_mongod_metrics_cursor_open{#}
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MongodbDataSize
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: DB Size (bytes)
            templateType: application
            resource: db
            expr: mongodb_mongod_db_data_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - db
              - namespace
        - templateName: MongodbDocument
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Document Operations (op/s)
            templateType: application
            resource: service
            expr: irate(mongodb_mongod_metrics_document_total{#}[5m])
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MongodbGlobalLock
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Global Locks
            templateType: application
            resource: service
            expr: mongodb_mongod_global_lock_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbIndexSize
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Index Size (bytes)
            templateType: application
            resource: db
            expr: mongodb_mongod_db_index_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - db
              - namespace
        - templateName: MongodbLockQueue
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Lock Queue
            templateType: application
            resource: type
            expr: mongodb_mongod_global_lock_current_queue{#}
            unit: ""
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbMemory
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Memory Usage (M bytes)
            templateType: application
            resource: service
            expr: mongodb_memory{#}
            unit: bytes-MB
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbObjects
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Objects Total
            templateType: application
            resource: db
            expr: mongodb_mongod_db_objects_total{#}
            unit: ""
            labels:
              - service
              - db
              - namespace
        - templateName: MongodbOplogSize
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Oplog Size (bytes)
            templateType: application
            resource: service
            expr: mongodb_mongod_replset_oplog_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbPageFaults
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Page Faults Total
            templateType: application
            resource: service
            expr: mongodb_extra_info_page_faults_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbQPS
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: QPS
            templateType: application
            resource: service
            expr: sum(rate(mongodb_op_counters_total{type=~"query|getmore",#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbReceivedBytes
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Network Received (bytes/s)
            templateType: application
            resource: service
            expr: irate(mongodb_network_bytes_total{state="in_bytes",#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MongodbSentBytes
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Network Sent (bytes/s)
            templateType: application
            resource: service
            expr: irate(mongodb_network_bytes_total{state="out_bytes",#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MongodbTPS
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: TPS
            templateType: application
            resource: service
            expr: sum(rate(mongodb_op_counters_total{type=~"insert|update|delete",#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbWiredtigerCacheRate
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache Ratio (%)
            templateType: application
            resource: service
            expr:
              sum by (namespace,service) (mongodb_mongod_wiredtiger_cache_bytes{type="total",#})
              / sum by (namespace,service)(mongodb_mongod_wiredtiger_cache_bytes_total{#})
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: MongodbWiredtigerCacheBytes
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache I/O (bytes/s)
            templateType: application
            resource: service
            expr: rate(mongodb_mongod_wiredtiger_cache_bytes_total{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
              - type
        - templateName: MongodbWiredtigerDirtyCache
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Dirty Cache Rate
            templateType: application
            resource: service
            expr:
              100 * sum by (namespace,service) (mongodb_mongod_wiredtiger_cache_pages{type="dirty",#})
              / sum by (namespace,service) (mongodb_mongod_wiredtiger_cache_pages{type="total",#})
            unit: percent-0-100
            labels:
              - namespace
              - service
        - templateName: MongodbWiredtigerCacheEvicted
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache Evicted Rate
            templateType: application
            resource: service
            expr: rate(mongodb_mongod_wiredtiger_cache_evicted_total{#}[5m])
            unit: ""
            labels:
              - namesace
              - service
              - type
        - templateName: MongodbReceivedBytesV2
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: ReceivedBytes v2
            templateType: application
            resource: service
            expr: irate(mongodb_ss_network_bytesIn{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MongodbSentBytesV2
          template:
            templateGroup: Middleware
            templateGroupResource: MongoDB
            templateShowName: SentBytes v2
            templateType: application
            resource: service
            expr: rate(mongodb_ss_network_bytesOut{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
    - resourceName: Postgres
      templates:
        - templateName: PgXactRollback
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Xact Rollback
            templateType: application
            resource: service
            expr: irate(pg_stat_database_xact_rollback{#}[5m])
            unit: ""
            labels:
              - datname
              - namespace
              - service
        - templateName: PgActivifyCount
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Active Sessions
            templateType: application
            resource: service
            expr: pg_stat_activity_count{state="active",#}
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgCacheHitRate
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Cache Hit Ratio (%)
            templateType: application
            resource: service
            expr: pg_stat_database_blks_hit{#} / (pg_stat_database_blks_read{#} + pg_stat_database_blks_hit{#})
            unit: percent-0.0-1.0
            labels:
              - datname
              - service
              - namespace
        - templateName: PgConflicts
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Conflicts
            templateType: application
            resource: service
            expr: pg_stat_database_conflicts{#}
            unit: ""
            labels:
              - namespace
              - datname
              - service
        - templateName: PgDeadlocks
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Deadlocks
            templateType: application
            resource: service
            expr: pg_stat_database_deadlocks{#}
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgDeleteData
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Delete Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_deleted{#}[5m])
            unit: bytes-B
            labels:
              - namespace
              - datname
              - service
        - templateName: PgEffectiveCacheSizeBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Effective Cache (bytes)
            templateType: application
            resource: service
            expr: pg_settings_effective_cache_size_bytes{#}
            unit: bytes-B
            labels:
              - namespace
              - service
        - templateName: PgFetchData
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: QPS
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_fetched{#}[5m])
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgIdleSessions
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Idle Sessions
            templateType: application
            resource: service
            expr: pg_stat_activity_count{state=~"idle|idle in transaction|idle in transaction (aborted)",#}
            unit: ""
            labels:
              - namespace
              - datname
              - service
              - state
        - templateName: PgInsertedRate
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Insert Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_inserted{#}[5m])
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgMaxConnections
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Max Connections
            templateType: application
            resource: service
            expr: pg_settings_max_connections{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: PgReturnRate
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Return Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_returned{#}[5m])
            unit: ""
            labels:
              - namespace
              - datname
              - service
        - templateName: PgSharedBuffersBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Shared Buffers (bytes)
            templateType: application
            resource: service
            expr: pg_settings_shared_buffers_bytes{#}
            unit: bytes-B
            labels:
              - namespace
              - service
        - templateName: PgState
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Up
            templateType: application
            resource: service
            expr: pg_static{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: PgCheckpointSyncTime
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Checkpoint Sync Time (ms)
            templateType: application
            resource: service
            expr: irate(pg_stat_bgwriter_checkpoint_sync_time{#}[5m])
            unit: duration-ms
            labels:
              - namespace
              - service
        - templateName: PgTmpBytes
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Temp File (bytes)
            templateType: application
            resource: service
            expr: irate(pg_stat_database_temp_bytes{#}[5m])
            unit: bytes-B
            labels:
              - namespace
              - service
              - datname
        - templateName: PgUpdatedRate
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Update Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_updated{#}[5m])
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgCheckpointWriteTime
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Checkpoint Write Time (ms)
            templateType: application
            resource: service
            expr: irate(pg_stat_bgwriter_checkpoint_write_time{#}[5m])
            unit: duration-ms
            labels:
              - service
              - namespace
        - templateName: PgXactCommit
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: TPS
            templateType: application
            resource: service
            expr: irate(pg_stat_database_xact_commit{#}[5m])
            unit: ""
            labels:
              - datname
              - service
              - datname
        - templateName: PgDBSize
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Database Size (bytes)
            templateType: application
            resource: service
            expr: pg_database_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - namespace
              - datname
        - templateName: PgLocksCount
          template:
            templateGroup: Middleware
            templateGroupResource: Postgres
            templateShowName: Locks Count
            templateType: application
            resource: service
            expr: pg_locks_count{#}
            unit: ""
            labels:
              - namespace
              - service
              - datname
              - mode
- groupName: Database
  resources:
    - resourceName: Mysql
      templates:
        - templateName: MysqlCommandTop10
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Command Top10
            templateType: application
            resource: command
            expr: topk(10, rate(mysql_global_status_commands_total{#}[5m])>0)
            unit: ""
            labels:
              - service
              - command
              - namespace
        - templateName: MysqlConnections
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Connections
            templateType: application
            resource: service
            expr: mysql_global_status_max_used_connections{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlInnodbBufferSize
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: InnoDB Buffer Pool (bytes)
            templateType: application
            resource: service
            expr: mysql_global_variables_innodb_buffer_pool_size{#}
            unit: bytes-B
            labels:
              - service
              - namespace
        - templateName: MysqlOpenFiles
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Open Files
            templateType: application
            resource: service
            expr: mysql_global_status_innodb_num_open_files{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlQPS
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: QPS
            templateType: application
            resource: service
            expr: rate(mysql_global_status_queries{#}[5m])
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlQuestions
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Questions Rate (questions/s)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_questions{#}[5m])
            unit: ""
            labels:
              - service
        - templateName: MysqlReceivedBytes
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Network Received (bytes/s)
            templateType: application
            resource: service
            expr: irate(mysql_global_status_bytes_received{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlSentBytes
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Network Sent (bytes/s)
            templateType: application
            resource: service
            expr: irate(mysql_global_status_bytes_sent{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlSlowQuery
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Slow Queries Rate (queries/min)
            templateType: application
            resource: service
            expr: idelta(mysql_global_status_slow_queries{#}[1m])
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSlowQueryEnabled
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: MySQL Slow Query Enabled
            templateType: application
            resource: service
            expr: mysql_global_variables_slow_query_log{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlState
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Up
            templateType: application
            resource: service
            expr: mysql_up{@}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTPS
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: TPS
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_commands_total{command=~"insert|update|delete",#}[5m])) without (command)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTabelLockWaited
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Table Locks Waited 5 Minutes
            templateType: application
            resource: service
            expr: sum(increase(mysql_global_status_table_locks_waited{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTableOpenCacheHitRatio
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Table Open Cache Hit Ratio (%)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_table_open_cache_hits{#}[5m]) / ( rate(mysql_global_status_table_open_cache_hits{#}[5m]) + rate(mysql_global_status_table_open_cache_misses{#}[5m]))
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: MysqlTableSize
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Table Size (bytes)
            templateType: application
            resource: service
            expr: sum by (schema,service,namespace) (mysql_info_schema_table_size{#})
            unit: bytes-B
            labels:
              - service
              - namespace
        - templateName: MysqlThreads
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Threads (by state)
            templateType: application
            resource: service
            expr: mysql_info_schema_threads{#}
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MysqlTmpTables
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Tmp Table Created Rate (tables/s)
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_created_tmp_tables{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlTotalRows
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Table Rows
            templateType: application
            resource: service
            expr: sum(mysql_info_schema_table_rows{#}) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlRunningThread
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Running Threads
            templateType: application
            resource: service
            expr: mysql_global_status_threads_running{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlInnodbLogWait
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: InnoDB Log Waits
            templateType: application
            resource: service
            expr: mysql_global_status_innodb_log_waits{#}
            unit: ""
            labels:
              - namespace
              - service
        - templateName: MysqlInnodbDataWrite
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: InnoDB Data Write Rate (bytes/s)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_innodb_data_writes{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlInnodbDataRead
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: InnoDB Data Read Rate (bytes/s)
            templateType: application
            resource: service
            expr: rate(mysql_global_status_innodb_data_read{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MysqlErrorConnections
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Error Connections Per Minute (by type)
            templateType: application
            resource: service
            expr: delta(mysql_global_status_connection_errors_total{#}[1m])
            unit: ""
            labels:
              - namespace
              - service
              - error
        - templateName: MysqlHandler
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Handler Rate (second)
            templateType: application
            resource: handler
            expr: rate(mysql_global_status_handlers_total{handler=~"commit|rollback|savepoint.*|prepare",#}[5m])
            unit: short
            labels:
              - service
              - namespace
              - handler
        - templateName: MysqlSortRows
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Sort Rows Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_rows{#}[5m])) by (service,namespace)
            unit: short
            labels:
              - service
              - namespace
        - templateName: MysqlSortRange
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Sort Range Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_range{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSortMergePass
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Sort Merge Passes Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_merge_passes{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - namespace
              - service
        - templateName: MysqlSortScan
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Sort Scan Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_sort_scan{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectFullJoin
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Select Full Join Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_full_join{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectRangeJoin
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Select Full Range Join Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_full_range_join{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectRange
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Select Range Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_range{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlSelectScan
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Select Scan Rate
            templateType: application
            resource: service
            expr: sum(rate(mysql_global_status_select_scan{#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MysqlBufferPoolPage
          template:
            templateGroup: Database
            templateGroupResource: Mysql
            templateShowName: Buffer Pool Pages Ratio (%)
            templateType: application
            resource: service
            expr: (sum by (namespace,service)(mysql_global_status_buffer_pool_pages{#}) - sum by (namespace,service)(mysql_global_status_buffer_pool_pages{state="free",#})) / sum(mysql_global_status_buffer_pool_pages{#}) by (namespace,service)
            unit: percent-0.0-1.0
            labels:
              - namespace
              - service
    - resourceName: Redis
      templates:
        - templateName: RedisClients
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Connected Clients
            templateType: application
            resource: service
            expr: redis_connected_clients{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisCommandsTop5
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Commands Top5
            templateType: application
            resource: cmd
            expr: topk(5,irate(redis_commands_total{#}[5m]))
            unit: ""
            labels:
              - service
              - cmd
              - namespace
        - templateName: RedisCpuUsage
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: CPU Usage (1000m=1Core)
            templateType: application
            resource: service
            expr: irate(redis_cpu_user_seconds_total{#}[5m]) + irate(redis_cpu_sys_seconds_total{#}[5m])
            unit: short
            labels:
              - service
              - namespace
        - templateName: RedisKeys
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: DB Keys
            templateType: application
            resource: db
            expr: sum (redis_db_keys{#}) by (db,namespace,service)
            unit: ""
            labels:
              - service
              - db
              - namespace
        - templateName: RedisKeysEvicted
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Evicted Keys Total
            templateType: application
            resource: service
            expr: redis_evicted_keys_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisKeysExpiring
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Expireing Keys
            templateType: application
            resource: service
            expr: redis_db_keys_expiring{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisKeysHitRate
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Keys Hit Ratio (%)
            templateType: application
            resource: service
            expr: redis_keyspace_hits_total{#} / (redis_keyspace_hits_total{#} + redis_keyspace_misses_total{#})
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: RedisKeysHits
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Keys Hits Total
            templateType: application
            resource: service
            expr: redis_keyspace_hits_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisKeysMissed
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Key Misses Total
            templateType: application
            resource: service
            expr: redis_keyspace_misses_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisMemoryUsedBytes
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Memory Used (bytes)
            templateType: application
            resource: service
            expr: redis_memory_used_bytes{#}
            unit: bytes-B
            labels:
              - service
              - namespace
        - templateName: RedisNoExpiringKeyRate
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Not-Expiring Keys Ratio (%)
            templateType: application
            resource: service
            expr: 1 - sum(redis_db_keys_expiring{#}) by (namespace, service) / sum(redis_db_keys{#}) by (namespace,service)
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: RedisOPS
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Commands Processed (op/s)
            templateType: application
            resource: service
            expr: irate(redis_commands_processed_total{#}[5m])
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisReceivedByted
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Network Received (bytes/s)
            templateType: application
            resource: service
            expr: irate(redis_net_input_bytes_total{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: RedisRejectedConnectionsTotal
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Rejected Connections Total
            templateType: application
            resource: service
            expr: redis_rejected_connections_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisSentBytes
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Network Sent (bytes/s)
            templateType: application
            resource: service
            expr: irate(redis_net_output_bytes_total{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: RedisState
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Up
            templateType: application
            resource: service
            expr: redis_up{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: RedisRssMemoryUsedBytes
          template:
            templateGroup: Database
            templateGroupResource: Redis
            templateShowName: Redis Memory Used Bytes
            templateType: application
            resource: service
            expr: redis_memory_used_rss_bytes{#}
            unit: bytes-B
            labels:
              - service
              - namespace
    - resourceName: MongoDB
      templates:
        - templateName: MongdbState
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Up
            templateType: application
            resource: service
            expr: mongodb_up{@}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongdoResponseTime
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Response Time (us)
            templateType: application
            resource: type
            expr: rate(mongodb_mongod_op_latencies_latency_total{#}[5m]) / rate(mongodb_mongod_op_latencies_ops_total{#}[5m])
            unit: duration-us
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbAsserts
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Asserts Total
            templateType: application
            resource: type
            expr: rate(mongodb_asserts_total{#}[5m])
            unit: ""
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbCacheBytes
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache Size (bytes)
            templateType: application
            resource: type
            expr: mongodb_mongod_wiredtiger_cache_bytes{#}
            unit: bytes-B
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbConnections
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Connections
            templateType: application
            resource: state
            expr: mongodb_connections{#}
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MongodbCursor
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Cursor Open
            templateType: application
            resource: state
            expr: mongodb_mongod_metrics_cursor_open{#}
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MongodbDataSize
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: DB Size (bytes)
            templateType: application
            resource: db
            expr: mongodb_mongod_db_data_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - db
              - namespace
        - templateName: MongodbDocument
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Document Operations (op/s)
            templateType: application
            resource: service
            expr: irate(mongodb_mongod_metrics_document_total{#}[5m])
            unit: ""
            labels:
              - service
              - state
              - namespace
        - templateName: MongodbGlobalLock
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Global Locks
            templateType: application
            resource: service
            expr: mongodb_mongod_global_lock_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbIndexSize
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Index Size (bytes)
            templateType: application
            resource: db
            expr: mongodb_mongod_db_index_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - db
              - namespace
        - templateName: MongodbLockQueue
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Lock Queue
            templateType: application
            resource: type
            expr: mongodb_mongod_global_lock_current_queue{#}
            unit: ""
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbMemory
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Memory Usage (M bytes)
            templateType: application
            resource: service
            expr: mongodb_memory{#}
            unit: bytes-MB
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbObjects
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Objects Total
            templateType: application
            resource: db
            expr: mongodb_mongod_db_objects_total{#}
            unit: ""
            labels:
              - service
              - db
              - namespace
        - templateName: MongodbOplogSize
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Oplog Size (bytes)
            templateType: application
            resource: service
            expr: mongodb_mongod_replset_oplog_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - type
              - namespace
        - templateName: MongodbPageFaults
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Page Faults Total
            templateType: application
            resource: service
            expr: mongodb_extra_info_page_faults_total{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbQPS
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: QPS
            templateType: application
            resource: service
            expr: sum(rate(mongodb_op_counters_total{type=~"query|getmore",#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbReceivedBytes
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Network Received (bytes/s)
            templateType: application
            resource: service
            expr: irate(mongodb_network_bytes_total{state="in_bytes",#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MongodbSentBytes
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Network Sent (bytes/s)
            templateType: application
            resource: service
            expr: irate(mongodb_network_bytes_total{state="out_bytes",#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MongodbTPS
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: TPS
            templateType: application
            resource: service
            expr: sum(rate(mongodb_op_counters_total{type=~"insert|update|delete",#}[5m])) by (namespace,service)
            unit: ""
            labels:
              - service
              - namespace
        - templateName: MongodbWiredtigerCacheRate
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache Ratio (%)
            templateType: application
            resource: service
            expr:
              sum by (namespace,service) (mongodb_mongod_wiredtiger_cache_bytes{type="total",#})
              / sum by (namespace,service)(mongodb_mongod_wiredtiger_cache_bytes_total{#})
            unit: percent-0.0-1.0
            labels:
              - service
              - namespace
        - templateName: MongodbWiredtigerCacheBytes
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache I/O (bytes/s)
            templateType: application
            resource: service
            expr: rate(mongodb_mongod_wiredtiger_cache_bytes_total{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
              - type
        - templateName: MongodbWiredtigerDirtyCache
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Dirty Cache Rate
            templateType: application
            resource: service
            expr:
              100 * sum by (namespace,service) (mongodb_mongod_wiredtiger_cache_pages{type="dirty",#})
              / sum by (namespace,service) (mongodb_mongod_wiredtiger_cache_pages{type="total",#})
            unit: percent-0-100
            labels:
              - namespace
              - service
        - templateName: MongodbWiredtigerCacheEvicted
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: Wiredtiger Cache Evicted Rate
            templateType: application
            resource: service
            expr: rate(mongodb_mongod_wiredtiger_cache_evicted_total{#}[5m])
            unit: ""
            labels:
              - namesace
              - service
              - type
        - templateName: MongodbReceivedBytesV2
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: ReceivedBytes v2
            templateType: application
            resource: service
            expr: irate(mongodb_ss_network_bytesIn{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
        - templateName: MongodbSentBytesV2
          template:
            templateGroup: Database
            templateGroupResource: MongoDB
            templateShowName: SentBytes v2
            templateType: application
            resource: service
            expr: rate(mongodb_ss_network_bytesOut{#}[5m])
            unit: bytes/sec-B/s
            labels:
              - service
              - namespace
    - resourceName: Postgres
      templates:
        - templateName: PgXactRollback
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Xact Rollback
            templateType: application
            resource: service
            expr: irate(pg_stat_database_xact_rollback{#}[5m])
            unit: ""
            labels:
              - datname
              - namespace
              - service
        - templateName: PgActivifyCount
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Active Sessions
            templateType: application
            resource: service
            expr: pg_stat_activity_count{state="active",#}
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgCacheHitRate
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Cache Hit Ratio (%)
            templateType: application
            resource: service
            expr: pg_stat_database_blks_hit{#} / (pg_stat_database_blks_read{#} + pg_stat_database_blks_hit{#})
            unit: percent-0.0-1.0
            labels:
              - datname
              - service
              - namespace
        - templateName: PgConflicts
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Conflicts
            templateType: application
            resource: service
            expr: pg_stat_database_conflicts{#}
            unit: ""
            labels:
              - namespace
              - datname
              - service
        - templateName: PgDeadlocks
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Deadlocks
            templateType: application
            resource: service
            expr: pg_stat_database_deadlocks{#}
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgDeleteData
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Delete Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_deleted{#}[5m])
            unit: bytes-B
            labels:
              - namespace
              - datname
              - service
        - templateName: PgEffectiveCacheSizeBytes
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Effective Cache (bytes)
            templateType: application
            resource: service
            expr: pg_settings_effective_cache_size_bytes{#}
            unit: bytes-B
            labels:
              - namespace
              - service
        - templateName: PgFetchData
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: QPS
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_fetched{#}[5m])
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgIdleSessions
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Idle Sessions
            templateType: application
            resource: service
            expr: pg_stat_activity_count{state=~"idle|idle in transaction|idle in transaction (aborted)",#}
            unit: ""
            labels:
              - namespace
              - datname
              - service
              - state
        - templateName: PgInsertedDate
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Insert Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_inserted{#}[5m])
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgMaxConnections
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Max Connections
            templateType: application
            resource: service
            expr: pg_settings_max_connections{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: PgReturnData
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Return Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_returned{#}[5m])
            unit: ""
            labels:
              - namespace
              - datname
              - service
        - templateName: PgSharedBuffersBytes
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Shared Buffers (bytes)
            templateType: application
            resource: service
            expr: pg_settings_shared_buffers_bytes{#}
            unit: bytes-B
            labels:
              - namespace
              - service
        - templateName: PgState
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Up
            templateType: application
            resource: service
            expr: pg_static{#}
            unit: ""
            labels:
              - service
              - namespace
        - templateName: PgSyncTime
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Checkpoint Sync Time (ms)
            templateType: application
            resource: service
            expr: irate(pg_stat_bgwriter_checkpoint_sync_time{#}[5m])
            unit: duration-ms
            labels:
              - namespace
              - service
        - templateName: PgTmpBytes
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Temp File (bytes)
            templateType: application
            resource: service
            expr: irate(pg_stat_database_temp_bytes{#}[5m])
            unit: bytes-B
            labels:
              - namespace
              - service
              - datname
        - templateName: PgUpdatedData
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Update Rows Rate
            templateType: application
            resource: service
            expr: irate(pg_stat_database_tup_updated{#}[5m])
            unit: ""
            labels:
              - namespace
              - service
              - datname
        - templateName: PgWriteTime
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Checkpoint Write Time (ms)
            templateType: application
            resource: service
            expr: irate(pg_stat_bgwriter_checkpoint_write_time{#}[5m])
            unit: duration-ms
            labels:
              - service
              - namespace
        - templateName: PgXactCommit
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: TPS
            templateType: application
            resource: service
            expr: irate(pg_stat_database_xact_commit{#}[5m])
            unit: ""
            labels:
              - datname
              - service
              - datname
        - templateName: PgDBSize
          template:
            templateGroup: Database
            templateGroupResource: Postgres
            templateShowName: Database Size (bytes)
            templateType: application
            resource: service
            expr: pg_database_size_bytes{#}
            unit: bytes-B
            labels:
              - service
              - namespace
              - datname
- groupName: Opentelemetry
  resources:
    - resourceName: OtelJvm
      templates:
        - templateName: Jvm_memory_used_heap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Heap Memory Used
            templateType: all
            resource: pod
            expr: jvm_memory_used{area='heap',@}
            unit: bytes-B
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_memory_used_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Nonheap Memory Used
            templateType: all
            resource: pod
            expr: jvm_memory_used{area='nonheap',@}
            unit: bytes-B
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_memory_committed_heap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Heap Memory Committed
            templateType: all
            resource: pod
            expr: jvm_memory_committed{area='heap',@}
            unit: bytes-B
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_memory_committed_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Nonheap Memory Committed
            templateType: all
            resource: pod
            expr: jvm_memory_committed{area='nonheap',@}
            unit: bytes-B
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_pause_count_major
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Major GC
            templateType: all
            resource: pod
            expr: jvm_gc_pause_count{action="end of major GC",@}
            unit: ""
            labels:
              - cause
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_pause_count_minor
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Minor GC
            templateType: all
            resource: pod
            expr: jvm_gc_pause_count{action='end of minor GC',@}
            unit: ""
            labels:
              - cause
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_pause_duration
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: GC Duration
            templateType: all
            resource: pod
            expr: histogram_quantile(1, sum by(le,action,pod,namespace,service_name) (rate(jvm_gc_pause_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - action
              - pod
              - namespace
              - service_name
        - templateName: Jvm_memory_max
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Heap Memory Max
            templateType: all
            resource: pod
            expr: jvm_memory_max{area='heap',@}
            unit: bytes-B
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_memory_max_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Nonheap Memory Max
            templateType: all
            resource: pod
            expr: jvm_memory_max{area='nonheap',@}
            unit: bytes-B
            labels:
              - id
              - namespace
              - service_name
              - pod
        - templateName: Jvm_buffer_memory_used
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Buffer Pool Memory Usead
            templateType: all
            resource: pod
            expr: jvm_buffer_memory_used{@}
            unit: bytes-B
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_buffer_total_capacity
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Buffer Pool Memory Capacity
            templateType: all
            resource: pod
            expr: jvm_buffer_total_capacity{@}
            unit: bytes-B
            labels:
              - id
              - namespace
              - pod
              - service_name
        - templateName: Jvm_threads_states
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Threads States
            templateType: all
            resource: pod
            expr: jvm_threads_states{@}
            unit: ""
            labels:
              - state
              - pod
              - namespace
              - service_name
        - templateName: Jvm_buffer_count
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Buffer Pool Count
            templateType: all
            resource: pod
            expr: jvm_buffer_count{@}
            unit: ""
            labels:
              - id
              - pod
              - namespace
              - service_name
        - templateName: Jvm_threads_live
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Threads Live
            templateType: all
            resource: pod
            expr: jvm_threads_live{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_threads_peak
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Threads Peak
            templateType: all
            resource: pod
            expr: jvm_threads_peak{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_classes_loaded
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Class Loaded
            templateType: all
            resource: pod
            expr: jvm_classes_loaded{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_threads_daemon
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Threads Daemon
            templateType: all
            resource: pod
            expr: jvm_threads_daemon{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_live_data_size
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: GC Live Date
            templateType: all
            resource: pod
            expr: jvm_gc_live_data_size{@}
            unit: bytes-B
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_max_data_size
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: GC Max Data
            templateType: all
            resource: pod
            expr: jvm_gc_max_data_size{@}
            unit: bytes-B
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Process_files_max
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Process Files Max
            templateType: all
            resource: pod
            expr: process_files_max{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Process_files_open
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Process Files Open
            templateType: all
            resource: pod
            expr: process_files_open{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: System_cpu_usage
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: System CPU Usage
            templateType: all
            resource: pod
            expr: system_cpu_usage{@}
            unit: percent-0.0-1.0
            labels:
              - pod
              - namespace
              - service_name
        - templateName: System_load_average_1m
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: System Load 1
            templateType: all
            resource: pod
            expr: system_load_average_1m{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Process_cpu_usage
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: Process CPU Usage
            templateType: all
            resource: pod
            expr: process_cpu_usage{@}
            unit: percent-0.0-1.0
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_memory_allocated
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: GC Memory Allocated
            templateType: all
            resource: pod
            expr: rate(jvm_gc_memory_allocated{@}[5m])
            unit: bytes-B
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jvm_gc_memory_promoted
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJvm
            templateShowName: GC Memory Promoted
            templateType: all
            resource: pod
            expr: rate(jvm_gc_memory_promoted{@}[5m])
            unit: bytes-B
            labels:
              - pod
              - namespace
              - service_name
    - resourceName: OtelJdbc
      templates:
        - templateName: Jdbc_connections_max
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Connections Max
            templateType: all
            resource: pod
            expr: jdbc_connections_max{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Jdbc_connections_min
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Connection Min
            templateType: all
            resource: pod
            expr: jdbc_connections_min{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Db_client_connections_use_time
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Connections Use Time P95
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.95, sum by(pod, pool_name,namespace,service_name,le)
              (rate(db_client_connections_use_time_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - namespace
              - service_name
              - pool_name
        - templateName: Db_client_connections_wait_time
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Connections Wait Time P95
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.95, sum by(pod,pool_name,namespace,service_name,le)
              (rate(db_client_connections_wait_time_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - pool_name
              - namespace
              - service_name
        - templateName: Hikaricp_connections
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections
            templateType: all
            resource: pod
            expr: hikaricp_connections{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Hikaricp_connections_acquire
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections Acquire Time P95
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.95, sum by(pod,pool,namespace,service_name,le)
              (rate(hikaricp_connections_acquire_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - pool
              - namespace
              - service_name
        - templateName: Hikaricp_connections_active
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections Active
            templateType: all
            resource: pod
            expr: hikaricp_connections_active{@}
            unit: ""
            labels:
              - pod
              - namespace
              - pool
              - service_name
        - templateName: Hikaricp_connections_creation
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections Creation Time P95
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.95, sum by(pod,pool,namespace,service_name,le)
              (rate(hikaricp_connections_creation_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - pool
              - namespace
              - service_name
        - templateName: Hikaricp_connections_usage
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections Usage Time P95
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.95, sum by(pod,pool,namespace,service_name,le)
              (rate(hikaricp_connections_usage_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - pool
              - namespace
              - service_name
        - templateName: Hikaricp_connections_pending
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections Pending
            templateType: all
            resource: pod
            expr: hikaricp_connections_pending{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Hikaricp_connections_max
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Hikaricp Connections Max
            templateType: all
            resource: pod
            expr: hikaricp_connections_max{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
        - templateName: Db_client_connections_create_time
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Connections Create Time P95
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.95, sum by(pod, pool_name,namespace,service_name,le)
              (rate(db_client_connections_create_time_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - namespace
              - poo_name
              - service_name
        - templateName: Db_client_connections_usage
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelJdbc
            templateShowName: Connection Usage
            templateType: all
            resource: pod
            expr: db_client_connections_usage{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - state
    - resourceName: OtelHttp
      templates:
        - templateName: Http_client_duration
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Client Duration P90
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.9, sum by(service_name,pod,namespace,le,net_peer_name)
              (rate(http_client_duration_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - namespace
              - net_peer_name
              - service_name
        - templateName: Http_client_request_size
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Client Request Size Byte
            templateType: all
            resource: pod
            expr:
              histogram_quantile(1, sum by(service_name,pod,namespace,le,net_peer_name)
              (rate(http_client_request_size_bucket{@}[5m])))
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - net_peer_name
        - templateName: Http_client_response_size
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Client Response Size Byte
            templateType: all
            resource: pod
            expr:
              histogram_quantile(1, sum by(service_name,pod,namespace,le,net_peer_name)
              (rate(http_client_response_size_bucket{@}[5m])))
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - net_peer_name
              - service_name
        - templateName: Http_server_duration
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Server Duration P90
            templateType: all
            resource: pod
            expr:
              histogram_quantile(0.9, sum by(service_name,pod,namespace,le,net_host_name,http_route)
              (rate(http_server_duration_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - pod
              - namespace
              - service_name
              - net_host_name
              - http_route
        - templateName: Http_server_request_size
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Server Request Size Byte
            templateType: all
            resource: pod
            expr:
              histogram_quantile(1, sum by(service_name,pod,namespace,le,net_host_name)
              (rate(http_server_request_size_bucket{@}[5m])))
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - net_host_name
        - templateName: Http_server_response_size
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Server Response Size Byte
            templateType: all
            resource: pod
            expr:
              histogram_quantile(1, sum by(service_name,pod,namespace,le,net_host_name)
              (rate(http_server_response_size_bucket{@}[5m])))
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - net_host_name
        - templateName: Http_server_active_requests
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Server Active Requests
            templateType: all
            resource: pod
            expr: http_server_active_requests{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - net_host_name
        - templateName: Http_client_duration_avg
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Client Duration Average
            templateType: all
            resource: pod
            expr: http_client_duration_sum{@} / http_client_duration_count{@}
            unit: duration-ms
            labels:
              - pod
              - namespace
              - service_name
              - net_peer_name
        - templateName: Http_server_duration_avg
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelHttp
            templateShowName: HTTP Server Duration Average
            templateType: all
            resource: pod
            expr: http_server_duration_sum{@}/http_server_duration_count{@}
            unit: duration-ms
            labels:
              - pod
              - namespace
              - servcei_name
              - http_route
    - resourceName: OtelLog
      templates:
        - templateName: Logback_events
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelLog
            templateShowName: Logback Events Last 5 Min
            templateType: all
            resource: pod
            expr: delta(logback_events{@}[5m])
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - level
    - resourceName: OtelKafka
      templates:
        - templateName: Kafka_consumer_bytes_consumed_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Comsumer Comsumed Rate Byte
            templateType: all
            resource: pod
            expr: kafka_consumer_bytes_consumed_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
              - topic
        - templateName: Kafka_consumer_commit_latency_avg
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Commit Latency Average
            templateType: all
            resource: pod
            expr: kafka_consumer_commit_latency_avg{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_commit_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Commit Rate
            templateType: all
            resource: pod
            expr: kafka_consumer_commit_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_commit_latency_max
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Commit Latency Max
            templateType: all
            resource: pod
            expr: kafka_consumer_commit_latency_max{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_connection_close_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Cconnection Close Rate
            templateType: all
            resource: pod
            expr: kafka_consumer_connection_close_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_connection_count
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Connection
            templateType: all
            resource: pod
            expr: kafka_consumer_connection_count{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_connection_creation_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: ConsumerC onnection Creation Rate
            templateType: all
            resource: pod
            expr: kafka_consumer_connection_creation_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_fetch_latency_avg
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Fetch Latency Average
            templateType: all
            resource: pod
            expr: kafka_consumer_fetch_latency_avg{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_fetch_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Cconsumer Fetch Rate
            templateType: all
            resource: pod
            expr: kafka_consumer_fetch_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_fetch_size_avg
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Fetch Size Average
            templateType: all
            resource: pod
            expr: kafka_consumer_fetch_size_avg{@}
            unit: bytes-B
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_incoming_byte_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Incoming Byte
            templateType: all
            resource: pod
            expr: kafka_consumer_incoming_byte_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_io_ratio
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer IO Ratio
            templateType: all
            resource: pod
            expr: kafka_consumer_io_ratio{@}
            unit: percent-0.0-1.0
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_network_io_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Network IO Rate
            templateType: all
            resource: pod
            expr: kafka_consumer_network_io_rate{@}
            unit: percent-0.0-1.0
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_outgoing_byte_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Outgoing Byte
            templateType: all
            resource: pod
            expr: kafka_consumer_outgoing_byte_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_consumer_request_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Consumer Request Rate
            templateType: all
            resource: pod
            expr: kafka_consumer_request_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_byte_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Byte
            templateType: all
            resource: pod
            expr: kafka_producer_byte_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
              - topic
        - templateName: Kafka_producer_compression_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Compression Rate
            templateType: all
            resource: pod
            expr: kafka_producer_compression_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_connection_creation_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Connection Creation Rate
            templateType: all
            resource: pod
            expr: kafka_producer_connection_creation_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_incoming_byte_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Incoming Byte Rate
            templateType: all
            resource: pod
            expr: kafka_producer_incoming_byte_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_io_ratio
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer IO Ratio
            templateType: all
            resource: pod
            expr: kafka_producer_io_ratio{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_network_io_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Network IO Rate
            templateType: all
            resource: pod
            expr: kafka_producer_network_io_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_outgoing_byte_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Outgoing Byte
            templateType: all
            resource: pod
            expr: kafka_producer_outgoing_byte_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_request_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Request Rate
            templateType: all
            resource: pod
            expr: kafka_producer_request_rate{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_waiting_threads
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Waiting Threads
            templateType: all
            resource: pod
            expr: kafka_producer_waiting_threads{@}
            unit: ""
            labels:
              - pod
              - namespace
              - service_name
              - client_id
        - templateName: Kafka_producer_topic_byte_rate
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelKafka
            templateShowName: Producer Topic Byte
            templateType: all
            resource: pod
            expr: kafka_producer_topic_byte_rate{@}
            unit: bytes/sec-B/s
            labels:
              - pod
              - namespace
              - service_name
              - client_id
    - resourceName: OtelProcessJvm
      templates:
        - templateName: Process_runtime_jvm_gc_duration_count
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: JVM GC Count
            templateType: all
            resource: pod
            expr: process_runtime_jvm_gc_duration_count{@}
            unit: ""
            labels:
              - service_name
              - pod
              - namespace
              - action
        - templateName: Process_runtime_jvm_buffer_count
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Buffer Count
            templateType: all
            resource: pod
            expr: process_runtime_jvm_buffer_count{@}
            unit: ""
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_buffer_limit
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Buffer Limit
            templateType: all
            resource: pod
            expr: process_runtime_jvm_buffer_limit{@}
            unit: ""
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_buffer_usage
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Buffer Usage
            templateType: all
            resource: pod
            expr: process_runtime_jvm_buffer_usage{@}
            unit: ""
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_classes_loaded
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Classes Loaded
            templateType: all
            resource: pod
            expr: process_runtime_jvm_classes_loaded{@}
            unit: ""
            labels:
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_cpu_utilization
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: CPU Utilization
            templateType: all
            resource: pod
            expr: process_runtime_jvm_cpu_utilization{@}
            unit: short
            labels:
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_classes_unloaded
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Classes Unloaded
            templateType: all
            resource: pod
            expr: process_runtime_jvm_classes_unloaded{@}
            unit: ""
            labels:
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_committed
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Heap Memory Committed
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_committed{type="heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_committed_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Non-Heap Memory Committed
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_committed{type="non_heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_usage
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Heap Memory Usage
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_usage{type="heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_usage_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Non-Heap Memory Usage
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_usage{type="non_heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_system_cpu_load_1m
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: System Load 1
            templateType: all
            resource: pod
            expr: process_runtime_jvm_system_cpu_load_1m{@}
            unit: ""
            labels:
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_system_cpu_utilization
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: System CPU Utilization
            templateType: all
            resource: pod
            expr: process_runtime_jvm_system_cpu_utilization{@}
            unit: short
            labels:
              - service_name
              - namespace
              - pod
        - templateName: Process_runtime_jvm_threads_count
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Threads Count (Daemon)
            templateType: all
            resource: pod
            expr: process_runtime_jvm_threads_count{@}
            unit: ""
            labels:
              - daemon
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_usage_after_last_gc
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Memory Usage After Last GC
            templateType: all
            resource: pod
            expr: memory_usage_after_last_gc{@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_init
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Heap Memory Init
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_init{type="heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_init_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Non-Heap Memory Init
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_init{type="non_heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_limit
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Heap Memory Limit
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_limit{type="heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_memory_limit_nonheap
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: Non-Heap Memory Limit
            templateType: all
            resource: pod
            expr: process_runtime_jvm_memory_limit{type="non_heap",@}
            unit: bytes-B
            labels:
              - pool
              - service_name
              - pod
              - namespace
        - templateName: Process_runtime_jvm_gc_duration
          template:
            templateGroup: Opentelemetry
            templateGroupResource: OtelProcessJvm
            templateShowName: GC Duration
            templateType: all
            resource: pod
            expr:
              histogram_quantile(1, sum by(le,action,pod,namespace,service_name)
              (rate(process_runtime_jvm_gc_duration_bucket{@}[5m])))
            unit: duration-ms
            labels:
              - action
              - pod
              - namespace
              - service_name
- groupName: Application
  resources:
    - resourceName: VMware
      templates:
        - templateName: VmwareVmCpuUsage
          template:
            templateGroup: Application
            templateGroupResource: VMware
            templateShowName: Vmware VirtualMachine CpuUsage
            templateType: application
            expr: ismc_vm_cpu_used_percentage{@}
            resource: vm
            unit: percent-0-100
            labels:
              - namespace
        - templateName: VmwareVmMemoryUsage
          template:
            templateGroup: Application
            templateGroupResource: VMware
            templateShowName: Vmware VirtualMachine MemoryUsage
            templateType: application
            expr: ismc_vm_memory_used_percentage{@}
            resource: vm
            unit: percent-0-100
            labels:
              - namespace
        - templateName: VmwareVmDiskUsage
          template:
            templateGroup: Application
            templateGroupResource: VMware
            templateShowName: Vmware VirtualMachine DiskUsage
            templateType: application
            expr: ismc_disk_used_percentage{@}
            resource: vm
            unit: percent-0-100
            labels:
              - namespace
              - disk
    - resourceName: Huawei
      templates:
        - templateName: HuaweiVMCpuUsage
          template:
            templateGroup: Application
            templateGroupResource: Huawei
            templateShowName: Huawei VirtualMachine CPU Usage
            templateType: application
            expr: ismc_vm_cpu_used_percentage{@}
            resource: vm
            unit: percent-0-100
            labels:
              - namespace
        - templateName: HuaweiVMMemUsage
          template:
            templateGroup: Application
            templateGroupResource: Huawei
            templateShowName: Huawei VirtualMachine Memory Usage
            templateType: application
            expr: ismc_vm_memory_used_percentage{@}
            resource: vm
            unit: percent-0-100
            labels:
              - namespace
        - templateName: HuaweiVMDiskUsage
          template:
            templateGroup: Application
            templateGroupResource: Huawei
            templateShowName: Huawei VirtualMachine Disk Usage
            templateType: application
            expr: ismc_disk_used_percentage{@}
            resource: vm
            unit: percent-0-100
            labels:
              - namespace
              - disk
    - resourceName: Kubernetes
      templates:
        - templateName: CPUUsedCores
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: CPU Used Cores
            templateType: application
            resource: pod
            expr: round(ismc_pod_cpu_used_cores{@}, 0.001)
            unit: short
            labels:
              - namespace
              - instance
        - templateName: CPUUsedPercent
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: CPU Used Percent
            templateType: application
            resource: pod
            expr: ismc_pod_cpu_usage_percent{@}
            unit: percent-0-100
            labels:
              - namespace
              - instance
        - templateName: MemoryUsedBytes
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Memory Used Bytes
            templateType: application
            resource: pod
            expr: round(ismc_pod_memory_used_bytes{@} / 1024 / 1024, 0.01)
            unit: bytes-MB
            labels:
              - namespace
              - instance
        - templateName: MemoryUsedPercent
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Memory Used Percent
            templateType: application
            resource: pod
            expr: ismc_pod_memory_usage_percent{@}
            unit: percent-0-100
            labels:
              - namespace
              - instance
        - templateName: NetworkInBps
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Network In Bps
            templateType: application
            resource: pod
            expr: round(ismc_pod_network_receive_bytes_per_5m{@}, 0.001)
            unit: bytes-B
            labels:
              - namespace
              - instance
        - templateName: NetworkOutBps
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Network Out Bps
            templateType: application
            resource: pod
            expr: round(ismc_pod_network_send_bytes_per_5m{@}, 0.001)
            unit: bytes-B
            labels:
              - namespace
              - instance
        - templateName: PodHealth
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Pod Health
            templateType: application
            resource: pod
            expr: ismc_pod_health{@}
            unit: short
            labels:
              - namespace
              - instance
        - templateName: RequestsPerSecond
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Requests Per Second
            templateType: application
            resource: pod
            expr: ismc_pod_calls_rate{@}
            unit: short
            labels:
              - namespace
              - instance
        - templateName: RequestsErrorRate
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Requests Error Rate
            templateType: application
            resource: pod
            expr: ismc_pod_calls_errors_rate{@}
            unit:
            labels:
              - namespace
              - instance
        - templateName: StorageUsedPercent
          template:
            templateGroup: Application
            templateGroupResource: Kubernetes
            templateShowName: Storage Used Percent
            templateType: application
            resource: persistentvolumeclaim
            expr: (ismc_pvc_storage_used_bytes{@} / ismc_pvc_storage_total_bytes{@}) * 100
            unit: percent-0-100
            labels:
              - namespace
              - instance