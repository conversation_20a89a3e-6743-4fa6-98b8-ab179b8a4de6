package ownerinject

import (
	"context"
	"slices"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
)

var _ store.Store = OwnerStore{}

// OwnerStore is a wrapper around a store.Store that provides additional functionality for owner references.
// it auto add object's parent to owner reference, in order to delete all children when parent is deleted.
type OwnerStore struct {
	store.Store
	root    store.Store
	options Options
	Scopes  []store.Scope
}

type Options struct {
	InjectResources []string
}

func NewOwnerInjectStore(store store.Store, options Options) OwnerStore {
	return OwnerStore{
		Store:   store,
		root:    store,
		options: options,
	}
}

func (s OwnerStore) Create(ctx context.Context, obj store.Object, opts ...store.CreateOption) error {
	if err := s.injectParentOwner(ctx, obj); err != nil {
		return err
	}
	return s.Store.Create(ctx, obj, opts...)
}

func (s OwnerStore) Scope(scope ...store.Scope) store.Store {
	return OwnerStore{
		root:    s.root,
		options: s.options,
		Store:   s.Store.Scope(scope...),
		Scopes:  append(s.Scopes, scope...),
	}
}

func (s OwnerStore) injectParentOwner(ctx context.Context, obj store.Object) error {
	resourcename, _ := store.GetResource(obj)
	if !slices.Contains(s.options.InjectResources, resourcename) {
		return nil
	}
	parentref, err := s.getOwner(ctx)
	if err != nil {
		// if parent not found, ignore it
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if parentref != nil {
		obj.SetOwnerReferences(append(obj.GetOwnerReferences(), *parentref))
	}
	return nil
}

func (s OwnerStore) getOwner(ctx context.Context) (*store.OwnerReference, error) {
	if len(s.Scopes) == 0 {
		return nil, nil
	}
	parentscopes, lastscope := s.Scopes[:len(s.Scopes)-1], s.Scopes[len(s.Scopes)-1]
	unstructured := store.Unstructured{}
	unstructured.SetResource(lastscope.Resource)

	if err := s.root.Scope(parentscopes...).Get(ctx, lastscope.Name, &unstructured); err != nil {
		return nil, err
	}
	ref := store.OwnerReference{
		UID:      unstructured.GetUID(),
		Name:     unstructured.GetName(),
		Resource: unstructured.GetResource(),
		Scopes:   unstructured.GetScopes(),
	}
	return &ref, nil
}
