package shared

import (
	"context"
	"strings"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type empty struct{}

func NewOptions() Options {
	return Options{
		ShareRelations: map[string][]string{
			"organizations": {
				"applications", // also "gateway"
				"resourcepools",
				"credentials", // oss credentials
			},
		},
	}
}

type Options struct {
	ShareRelations map[string][]string
}

// NewSharedController remove shared labels from objects when the share target is deleted
func NewSharedController(storage store.Store, options Options) (controller.Runable, error) {
	rec := &SharedReconciler{
		Store:   storage,
		Options: options,
	}
	watchsources := []controller.Source[controller.ResourceScopedKey]{}
	for resource := range options.ShareRelations {
		watchsources = append(watchsources, controller.StoreSource[controller.ResourceScopedKey]{
			Store:    storage,
			Resource: resource,
			KeyFunc: func(ctx context.Context, kind store.WatchEventType, obj store.Object) ([]controller.ResourceScopedKey, error) {
				if obj.GetDeletionTimestamp() != nil || kind == store.WatchEventDelete {
					return []controller.ResourceScopedKey{ResourceScopedKeyFromObject(obj)}, nil
				}
				return []controller.ResourceScopedKey{}, nil
			},
		})
	}
	c := controller.
		NewTypedController("Shared", rec).
		Watch(watchsources...)
	return c, nil
}

func ResourceScopedKeyFromObject(obj store.Object) controller.ResourceScopedKey {
	return controller.ResourceScopedKey{
		Resource: obj.GetResource(),
		Name:     obj.GetName(),
		Prefix:   controller.EncodeScopes(obj.GetScopes()),
	}
}

type SharedReconciler struct {
	Store   store.Store
	Options Options
}

var _ controller.InitializeReconciler = &SharedReconciler{}

// Initialize implements controller.InitializeReconciler.
func (s *SharedReconciler) Initialize(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.Info("shared controller initialize")
	defer log.Info("shared controller initialized")
	var errs []error
	for dest, sources := range s.Options.ShareRelations {
		if err := s.removeInvalidSharedLabelsOn(ctx, dest, sources); err != nil {
			errs = append(errs, err)
		}
	}
	if err := errors.NewAggregate(errs); err != nil {
		log.Error(err, "shared controller initialize error")
	}
	return nil
}

func (s *SharedReconciler) removeInvalidSharedLabelsOn(ctx context.Context, dest string, srcs []string) error {
	log := log.FromContext(ctx)

	allsources := &store.List[store.Unstructured]{}
	allsources.SetResource(dest)

	if err := s.Store.List(ctx, allsources, store.WithSubScopes()); err != nil {
		return err
	}

	scopednames := map[string]map[string]empty{}
	for _, item := range allsources.Items {
		prefix := controller.EncodeScopes(item.GetScopes())
		list, ok := scopednames[prefix]
		if !ok {
			list = map[string]empty{}
			scopednames[prefix] = list
		}
		list[item.GetName()] = empty{}
	}

	var errs []error
	for _, src := range srcs {
		srcobjs := &store.List[store.Unstructured]{}
		srcobjs.SetResource(src)
		if err := s.Store.List(ctx, srcobjs, store.WithSubScopes()); err != nil {
			return err
		}
		for _, srcobj := range srcobjs.Items {
			srclabels := srcobj.GetLabels()
			if len(srclabels) == 0 {
				continue
			}
			haschanged := false

			validtargets, ok := scopednames[controller.EncodeScopes(srcobj.GetScopes())]
			if !ok {
				// remove all labels
				for k := range srclabels {
					if strings.HasPrefix(k, base.LabelSharedOrganizationPrefix) {
						log.Info("remove invalid shared label", "resource", src, "name", srcobj.GetName(), "label", k)
						delete(srclabels, k)
						haschanged = true
					}
				}
			} else {
				// remove labels that are not in the valid targets
				for _, org := range base.ExtractSharedOrganizationFromLabels(srclabels) {
					if _, ok := validtargets[org]; !ok {
						key := base.GetShareddOrganizationLabel(org)
						delete(srclabels, key)
						log.Info("remove invalid shared label", "resource", src, "name", srcobj.GetName(), "label", key)
						haschanged = true
					}
				}
			}
			if haschanged {
				srcobj.SetLabels(srclabels)
				if err := s.Store.Scope(srcobjs.Scopes...).Update(ctx, &srcobj); err != nil {
					log.Error(err, "update shared labels error", "name", srcobj.GetName(), "resource", src)
					errs = append(errs, err)
				}
			}
		}
	}
	return errors.NewAggregate(errs)
}

// nolint: funlen,gocognit
func (s *SharedReconciler) Reconcile(ctx context.Context, key controller.ResourceScopedKey) (controller.Result, error) {
	return base.UnwrapReQueueError(s.reconcile(ctx, key))
}

func (s *SharedReconciler) reconcile(ctx context.Context, key controller.ResourceScopedKey) error {
	log := log.FromContext(ctx)

	log.Info("start reconcile")
	defer log.Info("finish reconcile")

	ref := store.ResourcedObjectReference{
		Name:     key.Name,
		Resource: key.Resource,
		Scopes:   controller.DecodeScopes(key.Prefix),
	}

	// check twice, make sure the object is deleted or in deletion
	uns := &store.Unstructured{}
	uns.SetResource(ref.Resource)
	if err := s.Store.Scope(ref.Scopes...).Get(ctx, ref.Name, uns); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
	} else {
		if uns.GetDeletionTimestamp() == nil {
			return nil
		}
	}

	checkResources, ok := s.Options.ShareRelations[key.Resource]
	if !ok {
		return nil
	}
	log.Info("remove labels shared to this", "name", ref.Name, "resource", ref.Resource)
	if err := RemoveSharedLabels(ctx, s.Store, ref, checkResources); err != nil {
		log.Error(err, "shared handler error")
		return err
	}
	return nil
}

func RemoveSharedLabels(ctx context.Context, storage store.Store, ref store.ResourcedObjectReference, sources []string) error {
	log := log.FromContext(ctx)

	scoped := storage.Scope(ref.Scopes...)
	toremove := ref.Name

	var errs []error
	for _, source := range sources {
		list := &store.List[store.Unstructured]{}
		list.SetResource(source)
		if err := scoped.List(ctx, list); err != nil {
			log.Error(err, "list error")
			return err
		}
		for _, obj := range list.Items {
			labels := obj.GetLabels()
			if labels == nil {
				continue
			}
			labelkey := base.GetShareddOrganizationLabel(toremove)
			if _, ok := labels[labelkey]; !ok {
				continue
			}
			delete(labels, labelkey)
			obj.SetLabels(labels)
			if err := scoped.Update(ctx, &obj); err != nil {
				errs = append(errs, err)
			}
		}
	}
	return errors.NewAggregate(errs)
}
