package notifiy

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
)

type NotifyAddress struct {
	Name    string `json:"name,omitempty"`
	Address string `json:"address,omitempty"`
}

type NotifyMessage struct {
	From    NotifyAddress   `json:"from,omitempty"`
	Tos     []NotifyAddress `json:"tos,omitempty"`
	Title   string          `json:"title,omitempty"`
	Content string          `json:"content,omitempty"`
}

type NotifierKind string

const (
	NotifyKindEmail NotifierKind = "email"
	NotifyKindSMS   NotifierKind = "sms"
)

type Notifier interface {
	// Send sends the verification code to the target
	// target is an email address or a phone number
	Send(ctx context.Context, message NotifyMessage) error
}

type NotifierConfig struct {
	store.ObjectMeta `json:",inline"`
	Kind             NotifierKind `json:"kind,omitempty"`
	Provider         string       `json:"provider,omitempty"`
	Preferred        bool         `json:"preferred,omitempty"`
	Config           string       `json:"config,omitempty"` // config is the json string of the provider options
}

// NotifyChannelHolder holds the notify channels
// and auto refesh the after the config is changed
type NotifierGetter interface {
	GetPreferred(ctx context.Context, kind NotifierKind) (Notifier, error)
	Get(ctx context.Context, name string) (Notifier, error)
}

type NofitierManager interface {
	NotifierGetter
	Register(ctx context.Context, config *NotifierConfig) error
}

var _ NofitierManager = &DefaultChannelHolder{}

func NewDefaultNotifierHolder() *DefaultChannelHolder {
	return &DefaultChannelHolder{
		preferred: make(map[NotifierKind]Notifier),
	}
}

type DefaultChannelHolder struct {
	mu        sync.RWMutex
	preferred map[NotifierKind]Notifier
	cache     map[string]Notifier
}

// Get implements NofitierManager.
func (d *DefaultChannelHolder) Get(ctx context.Context, name string) (Notifier, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()
	ch, ok := d.cache[name]
	if !ok {
		return nil, errors.NewNotFound("notifier", name)
	}
	return ch, nil
}

func (d *DefaultChannelHolder) GetPreferred(ctx context.Context, kind NotifierKind) (Notifier, error) {
	d.mu.RLock()
	defer d.mu.RUnlock()

	ch, ok := d.preferred[kind]
	if !ok {
		return nil, errors.NewUnsupported(fmt.Sprintf("no preferred channel for kind: %s", string(kind)))
	}
	return ch, nil
}

// Register implements NofitierManager.
func (d *DefaultChannelHolder) Register(ctx context.Context, config *NotifierConfig) error {
	key := store.ObjectIdentity(config)
	switch config.Kind {
	case NotifyKindEmail:
		switch config.Provider {
		default:
			options := &SMTPOptions{}
			if err := json.Unmarshal([]byte(config.Config), options); err != nil {
				return fmt.Errorf("failed to decode email options: %w", err)
			}
			ch, err := NewEmailNotifier(options)
			if err != nil {
				return fmt.Errorf("failed to create email notifier: %w", err)
			}
			d.mu.Lock()
			if config.Preferred {
				d.preferred[config.Kind] = ch
			}
			d.cache[key] = ch
			d.mu.Unlock()
		}
	}
	return nil
}
