package notifiy

import (
	"bytes"
	"context"
	"fmt"
	"regexp"

	"xiaoshiai.cn/common/email"
)

type SMTPOptions struct {
	email.SMTPOptions `json:",inline"`
	From              email.EmailAddress `json:"from,omitempty" description:"email address to send from"`
}

func NewEmailNotifier(smtpOptions *SMTPOptions) (*EmailNotifier, error) {
	return &EmailNotifier{Options: smtpOptions}, nil
}

var _ Notifier = &EmailNotifier{}

type EmailNotifier struct {
	Options *SMTPOptions
}

var EmailRegexp = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

func (e *EmailNotifier) Send(ctx context.Context, message NotifyMessage) error {
	for _, to := range message.Tos {
		if to.Address == "" || !EmailRegexp.MatchString(to.Address) {
			return fmt.Errorf("invalid email address: %s", to.Address)
		}
	}
	addresslist := make(email.EmailAddressList, 0, len(message.Tos))
	for _, to := range message.Tos {
		addresslist = append(addresslist, email.EmailAddress{Name: to.Name, Email: to.Address})
	}
	msg := &email.Email{
		From:        e.Options.From,
		To:          addresslist,
		Subject:     message.Title,
		ContentType: "text/html; charset=UTF-8",
		Body:        bytes.NewBufferString(message.Content),
	}
	if err := email.SendMail(ctx, e.Options.SMTPOptions, msg); err != nil {
		return fmt.Errorf("send email: %w", err)
	}
	return nil
}
