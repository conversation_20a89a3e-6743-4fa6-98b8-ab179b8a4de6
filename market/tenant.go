package market

import (
	"context"
	"net/http"

	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
)

// +k8s:openapi-gen=true
type UserProduct struct {
	store.ObjectMeta `json:",inline"`

	License UserLicnesePolicy `json:"license"`

	Category string `json:"category"`

	SubCategory string `json:"subcategory"`

	Type ProductType `json:"type"`

	// Details is extra details of the application
	Details ProductDetails `json:"details"`

	OS string `json:"os"` // Operating system

	Vendor string `json:"vendor"`

	// Versions is a list of versions of the application published to the market
	Versions []ProductVersion `json:"versions"`
}

type UserLicnesePolicy struct {
	Enabled bool `json:"enabled"`
}

func (a *API) ListUserProduct(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		opts := ListProductsOptions{
			ListOptions:     api.GetListOptions(r),
			Published:       ptr.To(true),
			Category:        api.Query(r, "category", ""),
			SubCategory:     api.Query(r, "subcategory", ""),
			Type:            ProductType(api.Query(r, "type", "")),
			PricingStrategy: PricingStrategy(api.Query(r, "pricingStrategy", "")),
		}
		list, err := ListProducts(ctx, storage, opts)
		if err != nil {
			return nil, err
		}
		items := make([]UserProduct, 0, len(list.Items))
		for _, val := range list.Items {
			items = append(items, ProductToUserProduct(val))
		}
		ret := store.List[UserProduct]{
			Total: list.Total,
			Page:  list.Page,
			Size:  list.Size,
			Items: items,
		}
		return ret, nil
	})
}

type UserProductWithLatestVersion struct {
	UserProduct `json:",inline"`
	Chart       *ProductChart `json:"chart,omitempty"`
}

func (a *API) GetUserProduct(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, appname string) (any, error) {
		app, err := a.GetProductWithLatest(r, appname, false)
		if err != nil {
			return nil, err
		}
		ret := UserProductWithLatestVersion{
			UserProduct: ProductToUserProduct(app.Product),
			Chart:       app.Chart,
		}
		return ret, nil
	})
}

type UserProductVersion struct {
	ProductVersion `json:",inline"`
	ProductChart   `json:",inline"`
}

func (a *API) GetUserProductVersion(w http.ResponseWriter, r *http.Request) {
	a.onMarketApplicationChartVersion(w, r, func(ctx context.Context, app, version string) (any, error) {
		return GetProductVersion(ctx, a.Store, a.Charts, app, version)
	})
}

func GetProductVersion(ctx context.Context, storage store.Store, charts artifact.ChartsProvider, name string, version string) (*UserProductVersion, error) {
	app, err := GetProduct(ctx, storage, name, false)
	if err != nil {
		return nil, err
	}

	for _, val := range app.Versions {
		if val.Version != version {
			continue
		}
		ret := &UserProductVersion{
			ProductVersion: val,
		}
		chart, err := charts.DownloadChart(ctx, name, version)
		if err != nil {
			// should ignore the error
			log.Error(err, "download helm chart", "version", version)
		} else {
			ret.ProductChart = *ProductChartFromChart(chart)
		}
		return ret, nil
	}
	return nil, errors.NewNotFound("version", version)
}

func ProductToUserProduct(product Product) UserProduct {
	return UserProduct{
		ObjectMeta:  product.ObjectMeta,
		License:     UserLicnesePolicy{Enabled: product.License.Enabled},
		Category:    product.Category,
		SubCategory: product.SubCategory,
		Type:        product.Type,
		Details:     product.Details,
		OS:          product.OS,
		Vendor:      product.Vendor,
		Versions:    product.Versions,
	}
}

func (a *API) userProductGroup() api.Group {
	return api.
		NewGroup("/products").
		Route(
			api.GET("").
				Doc("List market products").
				To(a.ListUserProduct).
				Param(
					api.QueryParam("category", "Filter by category").Optional(),
					api.QueryParam("subcategory", "Filter by subcategory").Optional(),
					api.QueryParam("type", "Filter by type").In(ApplicationTypeContainer, ApplicationTypeVirtualMachine).Optional(),
					api.QueryParam("pricingStrategy", "Filter by pricing strategy").In(PricingStrategyFree, PricingStrategyPaid).Optional(),
				).
				Param(api.PageParams...).
				Response(store.List[Product]{}),
			api.GET("/{product}").
				Doc("Get market product").
				To(a.GetUserProduct).
				Response(Product{}),

			api.GET("/{product}/versions/{version}").
				Doc("Get market product version").
				To(a.GetUserProductVersion).
				Response(UserProductVersion{}),
		)
}
