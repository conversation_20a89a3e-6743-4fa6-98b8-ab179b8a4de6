package market

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/license"
)

func NewMarketController(storage store.Store, skusvc product.SKUService, appsproject string, charts artifact.ChartsProvider) (*controller.Controller, error) {
	rec := &MarketProductController{
		Client:      storage,
		Charts:      charts,
		AppsProject: appsproject,
		SKUService:  skusvc,
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("products-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.
		NewController("products", br).
		Watch(
			controller.NewStoreSource(storage, &Product{}),
		)
	return c, nil
}

var _ controller.Reconciler[*Product] = &MarketProductController{}

type MarketProductController struct {
	Client      store.Store
	Charts      artifact.ChartsProvider
	SKUService  product.SKUService
	AppsProject string
}

const ReadWriteAccountName = "read-write-robot"

func (c *MarketProductController) Initlize(ctx context.Context) error {
	// init apps project
	if err := c.Charts.EnsureProject(ctx, c.AppsProject, false); err != nil {
		log.Error(err, "failed to ensure project", "project", c.AppsProject)
	}
	return nil
}

// Sync implements Reconciler.
func (c *MarketProductController) Sync(ctx context.Context, app *Product) (controller.Result, error) {
	return controller.Result{}, c.sync(ctx, app)
}

func (c *MarketProductController) sync(ctx context.Context, app *Product) error {
	if err := c.syncRegistryProject(ctx, app); err != nil {
		return err
	}
	if err := c.syncLicenseSigner(ctx, app); err != nil {
		return err
	}
	return nil
}

func (c *MarketProductController) syncRegistryProject(ctx context.Context, app *Product) error {
	appname := app.Name
	if err := c.Charts.EnsureProject(ctx, appname, !app.License.Enabled); err != nil {
		return err
	}
	if app.Status.Registry.Password == "" {
		account, err := c.Charts.GenerateRegistryAccount(ctx, appname, ReadWriteAccountName, artifact.RegistryAccountOptions{Pull: true, Push: true})
		if err != nil {
			return err
		}
		app.Status.Registry = ProductRegistry{Host: account.Host, Username: account.Username, Password: account.Password}
	}
	return nil
}

func (c *MarketProductController) syncLicenseSigner(ctx context.Context, marketapp *Product) error {
	// auto update license signer on app changes
	if marketapp.License.Enabled {
		signer := &license.LicenseSigner{
			ObjectMeta: store.ObjectMeta{
				Name: marketapp.Name,
			},
		}
		fn := func() error {
			// signer.ContentSchema = marketapp.License.ContentSchema
			// signer.FreeTrial = marketapp.License.FreeTrial
			return nil
		}
		storage := c.Client.Scope(marketapp.Scopes...)
		if err := store.CreateOrUpdate(ctx, storage, signer, fn); err != nil {
			return err
		}
	}
	return nil
}

// Remove implements Reconciler.
func (c *MarketProductController) Remove(ctx context.Context, app *Product) (controller.Result, error) {
	return controller.Result{}, c.remove(ctx, app)
}

func (c *MarketProductController) remove(ctx context.Context, app *Product) error {
	if err := c.Charts.RemoveRegistryAccount(ctx, app.Name, ReadWriteAccountName); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
	}
	// remove product skus
	skulist, err := c.SKUService.ListSKU(ctx, SKUCategory, app.Name, &product.ListSKUOptions{WithDisabled: true})
	if err != nil {
		return err
	}
	var errs []error
	for _, sku := range skulist.Items {
		log.FromContext(ctx).Info("delete sku", "sku", sku.Name, "product", app.Name)
		if err := c.SKUService.DeleteSKU(ctx, SKUCategory, app.Name, sku.Name); err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}
