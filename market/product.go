package market

import (
	"context"
	"time"

	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
)

// eg. prometheus-mysql-exporter is "used-for" mysql
const AnnotaionUsedFor = "used-for"

const (
	LabelCategory = common.GroupPrefix + "/category"
)

type Product struct {
	store.ObjectMeta `json:",inline"`

	License LicensePolicy `json:"license"`

	Category string `json:"category"`

	SubCategory string `json:"subcategory"`

	Type ProductType `json:"type"`

	// Details is extra details of the application
	Details ProductDetails `json:"details"`

	OS string `json:"os"` // Operating system

	Vendor string `json:"vendor"`
	// Published indicates whether the application is published to the market
	// if not , tenant can't see it
	Published bool `json:"published"`
	// Versions is a list of versions of the application published to the market
	Versions []ProductVersion `json:"versions"`

	Status ProductStatus `json:"status"`
}

type ProductVersion struct {
	Version           string    `json:"version"`
	Chart             string    `json:"chart"`       // Helm chart of this version
	ReleaseNote       string    `json:"releaseNote"` // Release note of this version
	CreationTimestamp time.Time `json:"creationTimestamp"`
}

type LicensePolicy struct {
	Enabled    bool           `json:"enabled"`
	Properties []SimpleSchema `json:"properties"`
}

type SimpleSchema struct {
	Key         string   `json:"key"`
	Description string   `json:"description,omitempty"`
	Type        string   `json:"type,omitempty"`
	Enum        []any    `json:"enum,omitempty"`
	Nullable    bool     `json:"nullable,omitempty"`
	Maximum     *float64 `json:"maximum,omitempty"`
	Minimum     *float64 `json:"minimum,omitempty"`
	Default     any      `json:"default,omitempty"`
}

type ProductDetails struct {
	OS   string `json:"os"` // Operating system
	Icon string `json:"icon"`
}

type ProductStatus struct {
	Message    string                 `json:"message"`
	Conditions []controller.Condition `json:"conditions"`
	Registry   ProductRegistry        `json:"registry"`
}

// +k8s:openapi-gen=true
type ProductRegistry struct {
	Host     string `json:"host"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type ProductType = cluster.ClusterCategory

const (
	ApplicationTypeContainer      ProductType = cluster.ClusterCategoryContainer
	ApplicationTypeVirtualMachine ProductType = cluster.ClusterCategoryVirtualMachine
)

type PricingStrategy string

const (
	PricingStrategyFree PricingStrategy = "free"
	PricingStrategyPaid PricingStrategy = "paid"
)

type ListProductsOptions struct {
	api.ListOptions `json:",inline"`
	Category        string          `json:"category,omitempty"`
	SubCategory     string          `json:"subcategory,omitempty"`
	Type            ProductType     `json:"type,omitempty"`
	IsSystem        *bool           `json:"isSystem,omitempty"`
	Published       *bool           `json:"published,omitempty"`
	PricingStrategy PricingStrategy `json:"pricingStrategy,omitempty"`
}

const CategorySystem = "system"
const CategoryMarketplace = "marketplace"

func ListProducts(ctx context.Context, storage store.Store, options ListProductsOptions) (*store.List[Product], error) {
	requirements := store.Requirements{}
	if options.Published != nil {
		requirements = append(requirements, store.RequirementEqual("published", *options.Published))
	}
	if options.Category != "" {
		requirements = append(requirements, store.RequirementEqual("category", options.Category))
	}
	if options.SubCategory != "" {
		requirements = append(requirements, store.RequirementEqual("subcategory", options.SubCategory))
	}
	if options.Type != "" {
		requirements = append(requirements, store.RequirementEqual("type", string(options.Type)))
	}
	switch options.PricingStrategy {
	case PricingStrategyFree:
		requirements = append(requirements, store.RequirementEqual("license.enabled", false))
	case PricingStrategyPaid:
		requirements = append(requirements, store.RequirementEqual("license.enabled", true))
	}
	opts := base.ListOptionsToStoreListOptions(options.ListOptions)
	if len(requirements) > 0 {
		opts = append(opts, store.WithFieldRequirements(requirements...))
	}
	if issys := options.IsSystem; issys != nil {
		if !*issys {
			opts = append(opts, store.WithLabelRequirements(store.NewRequirement(LabelCategory, store.NotEquals, CategorySystem)))
		} else {
			opts = append(opts, store.WithLabelRequirements(store.RequirementEqual(LabelCategory, CategorySystem)))
		}
	}
	list := &store.List[Product]{}
	if err := storage.List(ctx, list, opts...); err != nil {
		return nil, err
	}
	return list, nil
}
