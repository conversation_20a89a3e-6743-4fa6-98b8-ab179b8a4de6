package market

import (
	"context"
	"io"
	"net/http"
	"slices"
	"strings"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
)

type ApplicationChartMetadata struct {
	Chart           string `json:"chart"`
	Version         string `json:"version"`
	Published       bool   `json:"published"`
	ChartNotExisted bool   `json:"chartNotExisted"`
}

func (a *API) ListMarketApplicationCharts(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		marketapp := &Product{}
		if err := storage.Get(ctx, app, marketapp); err != nil {
			return nil, err
		}
		publishedMap := make(map[string]bool)
		for _, v := range marketapp.Versions {
			publishedMap[v.Version] = true
		}
		tags, err := a.Charts.ListCharts(ctx, app, api.ListOptions{})
		if err != nil {
			return nil, err
		}
		charts := make([]ApplicationChartMetadata, 0, len(tags.Items))
		for _, tag := range tags.Items {
			charts = append(charts, ApplicationChartMetadata{
				Chart:     app,
				Version:   tag.Name,
				Published: publishedMap[tag.Name],
			})
			delete(publishedMap, tag.Name)
		}
		for v := range publishedMap {
			charts = append(charts, ApplicationChartMetadata{
				Chart:           app,
				Version:         v,
				Published:       true,
				ChartNotExisted: true,
			})
		}
		// sort the charts by version
		slices.SortFunc(charts, func(a, b ApplicationChartMetadata) int {
			return strings.Compare(a.Version, b.Version)
		})
		paged := api.PageFromRequest(r, charts,
			func(item ApplicationChartMetadata) string {
				return item.Version
			},
			func(item ApplicationChartMetadata) time.Time {
				return time.Time{}
			})
		return paged, nil
	})
}

func (a *API) GetMarketApplicationChart(w http.ResponseWriter, r *http.Request) {
	a.onMarketApplicationChartVersion(w, r, func(ctx context.Context, app, version string) (any, error) {
		return a.Charts.GetChart(ctx, app, version)
	})
}

func (a *API) CreateMarketApplicationChart(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		var chartData io.Reader
		if contentType := r.Header.Get("Content-Type"); strings.HasPrefix(contentType, "multipart/form-data") {
			mf, _, err := r.FormFile("file")
			if err != nil {
				return nil, err
			}
			defer mf.Close()
			chartData = mf
		} else {
			chartData = r.Body
		}
		if err := a.Charts.UploadChart(ctx, app, chartData); err != nil {
			return nil, err
		}
		return api.Created, nil
	})
}

func (a *API) RemoveMarketApplicationChart(w http.ResponseWriter, r *http.Request) {
	a.onMarketApplicationChartVersion(w, r, func(ctx context.Context, app, version string) (any, error) {
		// check if the chart is published
		marketapp := &Product{}
		if err := a.Store.Get(ctx, app, marketapp); err != nil {
			return nil, err
		}
		ispublished := slices.ContainsFunc(marketapp.Versions, func(i ProductVersion) bool {
			return i.Version == version
		})
		if ispublished {
			return nil, errors.NewBadRequest("cannot remove published chart")
		}
		if err := a.Charts.RemoveChart(ctx, app, version); err != nil {
			return nil, err
		}
		return api.Accepted, nil
	})
}

func (a *API) onMarketApplicationChartVersion(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, app, version string) (any, error),
) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		return fn(ctx, app, api.Path(r, "version", ""))
	})
}

func (a *API) marketApplicationChartsGroup() api.Group {
	return api.
		NewGroup("/charts").
		Route(
			api.GET("").
				Doc("List application charts").
				To(a.ListMarketApplicationCharts).
				Param(api.PageParams...).
				Response(store.List[artifact.Artifact]{}),
			api.POST("").
				Doc("Upload a new application chart").
				To(a.CreateMarketApplicationChart).
				Param(api.BodyParam("chart", []byte{})),
			api.GET("/{version}").
				Doc("Download a specific application chart").
				Response(artifact.Artifact{}).
				To(a.GetMarketApplicationChart),
			api.DELETE("/{version}").
				Doc("Remove a specific application chart").
				To(a.RemoveMarketApplicationChart),
		)
}

func (a *API) ListMarketApplicationRepository(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		return a.Charts.ListRepositories(ctx, app, api.GetListOptions(r))
	})
}

func (a *API) ListMarketApplicationRepositoryTags(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		repository := api.Path(r, "repository", "")
		if repository == "" {
			return nil, errors.NewBadRequest("missing repository")
		}
		return a.Charts.ListRepositoryArtifacts(ctx, app, repository, api.GetListOptions(r))
	})
}

func (a *API) GetMarketApplicationRepositoryArtifactDetails(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		repository := api.Path(r, "repository", "")
		reference := api.Path(r, "reference", "")
		return a.Charts.GetArtifactDetails(ctx, app, repository, reference)
	})
}

func (a *API) marketApplicationImagesGroup() api.Group {
	return api.
		NewGroup("/repositories").
		Route(
			api.GET("").
				Doc("List application charts").
				To(a.ListMarketApplicationRepository).
				Param(api.PageParams...).
				Response(store.List[artifact.Repository]{}),
		).
		SubGroup(
			api.NewGroup("/{repository}").
				Route(
					api.GET("").
						Doc("List tags").
						To(a.ListMarketApplicationRepositoryTags).
						Param(api.PageParams...).
						Response(store.List[artifact.Artifact]{}),
					api.GET("/{reference}").
						Doc("Download a specific application chart").
						Response(artifact.Artifact{}).
						To(a.GetMarketApplicationRepositoryArtifactDetails),
					api.DELETE("/{reference}").
						Doc("Remove a specific application chart").
						To(a.RemoveMarketApplicationChart),
				),
		)
}

func (a *API) adminProductArtifactGroup() api.Group {
	return api.
		NewGroup("/admin-products/{product}").
		SubGroup(
			a.marketApplicationChartsGroup(),
			a.marketApplicationImagesGroup(),
		)
}
