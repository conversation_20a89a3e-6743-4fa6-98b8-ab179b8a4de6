package market

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func InitBuiltInActegories(ctx context.Context, storage store.Store) error {
	categories := []ProductCategory{
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "system",
				Alias:       "System",
				Description: "System related products",
			},
			Icon: "system",
			LocaleNames: map[string]string{
				"zh": "系统",
				"en": "System",
			},
			SubCategories: []CategoryData{
				{
					Name:        "database",
					Alias:       "Database",
					LocaleNames: map[string]string{"zh": "数据库", "en": "Database"},
				},
				{
					Name:        "exporter",
					Alias:       "Monitor",
					LocaleNames: map[string]string{"zh": "中间件监控", "en": "Monitor"},
				},
				{
					Name:        "database-backup",
					Alias:       "Database Backup",
					LocaleNames: map[string]string{"zh": "数据库备份", "en": "Database Backup"},
				},
				{
					Name:        "gateway",
					Alias:       "Gateway",
					LocaleNames: map[string]string{"zh": "网关", "en": "Gateway"},
				},
				{
					Name:        "middleware",
					Alias:       "Middleware",
					LocaleNames: map[string]string{"zh": "中间件", "en": "Middleware"},
				},
				{
					Name:        "hpa",
					Alias:       "HPA",
					LocaleNames: map[string]string{"zh": "HPA应用", "en": "HPA"},
				},
			},
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:  "marketplace",
				Alias: "Marketplace",
			},
			Icon: "marketplace",
			LocaleNames: map[string]string{
				"zh": "应用市场",
				"en": "Marketplace",
			},
			SubCategories: []CategoryData{
				{
					Name:  "bob",
					Alias: "Bob",
					LocaleNames: map[string]string{
						"zh": "油田应用", "en": "Oilfield App",
					},
				},
				{
					Name:  "smart",
					Alias: "Smart",
					LocaleNames: map[string]string{
						"zh": "智算应用", "en": "Smart App",
					},
				},
				{
					Name:  "ai",
					Alias: "AI",
					LocaleNames: map[string]string{
						"zh": "AI应用", "en": "AI App",
					},
				},
				{
					Name:  "os",
					Alias: "OS",
					LocaleNames: map[string]string{
						"zh": "操作系统", "en": "Operating System",
					},
				},
			},
		},
	}
	var errs []error
	for _, cate := range categories {
		if err := store.CreateIfNotExists(ctx, storage, &cate); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

type ProductCategory struct {
	store.ObjectMeta `json:",inline"`
	Icon             string            `json:"icon"`
	Hidden           bool              `json:"hidden"`
	LocaleNames      map[string]string `json:"localeNames,omitempty"`
	SubCategories    []CategoryData    `json:"subCategories"`
}

type CategoryData struct {
	Name          string            `json:"name"`
	Annotations   map[string]string `json:"annotations"`
	Alias         string            `json:"alias"`
	Icon          string            `json:"icon"`
	Hidden        bool              `json:"hidden"`
	LocaleNames   map[string]string `json:"localeNames,omitempty"`
	Description   string            `json:"description"`
	SubCategories []CategoryData    `json:"subCategories"`
}

func (c CategoryData) ToProductCategory() (*ProductCategory, error) {
	// generate a name if not provided
	cate := &ProductCategory{
		ObjectMeta: store.ObjectMeta{
			Name:        c.Name,
			Alias:       c.Alias,
			Description: c.Description,
			Annotations: c.Annotations,
		},
		Icon:          c.Icon,
		LocaleNames:   c.LocaleNames,
		SubCategories: c.SubCategories,
		Hidden:        c.Hidden,
	}
	subcatename := map[string]struct{}{}
	subcateid := map[string]struct{}{}
	for i, sub := range cate.SubCategories {
		if sub.Alias == "" {
			return nil, errors.NewBadRequest("alias is required for subcategory")
		}
		if _, ok := subcatename[sub.Alias]; ok {
			return nil, errors.NewBadRequest(fmt.Sprintf("subcategory alias %s already exists", sub.Alias))
		}else{
			subcatename[sub.Alias] = struct{}{}
		}
		if sub.Name == "" {
			cate.SubCategories[i].Name = base.RandName("subcate")
		}
		if _, ok := subcateid[sub.Name]; ok {
			return nil, errors.NewBadRequest(fmt.Sprintf("subcategory name %s already exists", sub.Name))
		}else{
			subcateid[sub.Name] = struct{}{}
		}
	}
	return cate, nil
}

func FromProductCategory(c ProductCategory) CategoryData {
	return CategoryData{
		Name:          c.Name,
		LocaleNames:   c.LocaleNames,
		Hidden:        c.Hidden,
		Alias:         c.Alias,
		Description:   c.Description,
		Annotations:   c.Annotations,
		SubCategories: c.SubCategories,
		Icon:          c.Icon,
	}
}

func (a *API) ListProductCategories(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		listopts := api.GetListOptions(r)
		listopts.Size = 0 // set size to 0 to get all categories

		list, err := ListProductCategories(ctx, storage, listopts)
		if err != nil {
			return nil, err
		}
		ret := make([]CategoryData, 0, len(list.Items))
		for _, item := range list.Items {
			ret = append(ret, FromProductCategory(item))
		}
		return ret, nil
	})
}

func (a *API) GetProductCategory(w http.ResponseWriter, r *http.Request) {
	a.onProductCategory(w, r, func(ctx context.Context, cate string) (any, error) {
		return base.GenericGet(r, a.Store, &ProductCategory{}, cate)
	})
}

func (a *API) CreateProductCategory(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		req := CategoryData{}
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		if req.Name == "" {
			req.Name = base.RandName("cate")
		}
		if req.Alias == "" {
			return nil, errors.NewBadRequest("alias is required for product category")
		}
		productCategory, err := req.ToProductCategory()
		if err != nil {
			return nil, err
		}
		if err := storage.Create(r.Context(), productCategory); err != nil {
			return nil, err
		}
		return productCategory, nil
	})
}

func (a *API) UpdateProductCategory(w http.ResponseWriter, r *http.Request) {
	a.onProductCategory(w, r, func(ctx context.Context, cate string) (any, error) {
		req := CategoryData{}
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		if req.Alias == "" {
			return nil, errors.NewBadRequest("alias is required for product category")
		}
		productCategory, err := req.ToProductCategory()
		if err != nil {
			return nil, err
		}
		productCategory.SetName(cate)
		productCategory.SetResourceVersion(0)
		if err := a.Store.Update(r.Context(), productCategory); err != nil {
			return nil, err
		}
		return productCategory, nil
	})
}

func (a *API) RemoveProductCategory(w http.ResponseWriter, r *http.Request) {
	a.onProductCategory(w, r, func(ctx context.Context, cate string) (any, error) {
		return base.GenericDelete(r, a.Store, &ProductCategory{}, cate)
	})
}

func (a *API) onProductCategory(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, cate string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		category := api.Path(r, "category", "")
		if category == "" {
			return nil, errors.NewBadRequest("category is required")
		}
		return fn(ctx, category)
	})
}

func ListProductCategories(ctx context.Context, storage store.Store, opts api.ListOptions) (*store.List[ProductCategory], error) {
	list := &store.List[ProductCategory]{}
	listopts := base.ListOptionsToStoreListOptions(opts)
	if err := storage.List(ctx, list, listopts...); err != nil {
		return nil, err
	}
	return list, nil
}

func (a *API) ListProductMetadatasInSubCategory(w http.ResponseWriter, r *http.Request) {
	a.onProductCategory(w, r, func(ctx context.Context, cate string) (any, error) {
		subcategory := api.Path(r, "subcategory", "")
		if subcategory == "" {
			return nil, errors.NewBadRequest("subcategory is required")
		}
		listopts := api.GetListOptions(r)
		listopts.Size = 0 // set size to 0 to get all products in the subcategory

		list, err := ListProducts(ctx, a.Store, ListProductsOptions{
			ListOptions: listopts,
			Category:    cate,
			SubCategory: subcategory,
		})
		if err != nil {
			return nil, err
		}
		ret := make([]ProductMetadata, 0, len(list.Items))
		for _, item := range list.Items {
			ret = append(ret, ProductMetadata{
				Name:        item.Name,
				Alias:       item.Alias,
				Description: item.Description,
			})
		}
		return ret, nil
	})
}

type ProductMetadata struct {
	Name        string `json:"name"`
	Alias       string `json:"alias"`
	Description string `json:"description"`
}

func (a *API) adminProductCategoryGroup() api.Group {
	return api.
		NewGroup("/product-categories").
		Route(
			api.GET("").
				Doc("List product categories").
				To(a.ListProductCategories).
				Response([]CategoryData{}),

			api.GET("/{category}").
				Doc("Get product category").
				To(a.GetProductCategory).
				Response(CategoryData{}),

			api.POST("").
				Doc("Create product category").
				To(a.CreateProductCategory).
				Param(
					api.BodyParam("category", CategoryData{}),
				).
				Response(ProductCategory{}),

			api.PUT("/{category}").
				Doc("Update product category").
				To(a.UpdateProductCategory).
				Param(
					api.BodyParam("category", CategoryData{}),
				).
				Response(ProductCategory{}),

			api.DELETE("/{category}").
				Doc("Remove product category").
				To(a.RemoveProductCategory).
				Response(ProductCategory{}),

			api.GET("/{category}/subcategories/{subcategory}/productmetadata").
				Doc("List products in a subcategory").
				To(a.ListProductMetadatasInSubCategory).
				Response([]ProductMetadata{}),
		)
}

func (a *API) ListUserMarketplaceProductSubCategories(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		category := &ProductCategory{}
		if err := a.Store.Get(r.Context(), CategoryMarketplace, category); err != nil {
			if errors.IsNotFound(err) {
				return []CategoryData{}, nil
			}
			return nil, err
		}
		cates := []CategoryData{}
		for _, sub := range category.SubCategories {
			if sub.Hidden {
				continue
			}
			cates = append(cates, CategoryData{
				Name:        sub.Name,
				Alias:       sub.Alias,
				Annotations: sub.Annotations,
				Icon:        sub.Icon,
			})
		}
		return cates, nil
	})
}

func (a *API) productCategoryGroup() api.Group {
	return api.
		NewGroup("/marketplace-categories").
		Route(
			api.GET("").
				Doc("List product categories").
				To(a.ListUserMarketplaceProductSubCategories).
				Response([]CategoryData{}),
		)
}
