package market

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type ProductCategory struct {
	store.ObjectMeta `json:",inline"`
	Icon             string         `json:"icon"`
	Order            int            `json:"order"`
	Hidden           bool           `json:"hidden"`
	SubCateGories    []CategoryData `json:"subCategories"`
}

type CategoryData struct {
	Name          string            `json:"name"`
	Annotations   map[string]string `json:"annotations"`
	Alias         string            `json:"alias"`
	Icon          string            `json:"icon"`
	Order         int               `json:"order"`
	Hidden        bool              `json:"hidden"`
	Description   string            `json:"description"`
	SubCateGories []CategoryData    `json:"subCategories"`
}

func (c CategoryData) ToProductCategory() *ProductCategory {
	return &ProductCategory{
		ObjectMeta: store.ObjectMeta{
			Name:        c.Name,
			Alias:       c.<PERSON>,
			Description: c.Description,
			Annotations: c.Annotations,
		},
		Icon:          c.Icon,
		SubCateGories: c.SubCateGories,
		Order:         c.Order,
		Hidden:        c.Hidden,
	}
}

func FromProductCategory(c ProductCategory) CategoryData {
	return CategoryData{
		Name:          c.Name,
		Order:         c.Order,
		Hidden:        c.Hidden,
		Alias:         c.Alias,
		Description:   c.Description,
		Annotations:   c.Annotations,
		SubCateGories: c.SubCateGories,
		Icon:          c.Icon,
	}
}

func (a *API) ListProductCategories(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		listopts := api.GetListOptions(r)
		listopts.Size = 0 // set size to 0 to get all categories

		list, err := ListProductCategories(ctx, storage, listopts)
		if err != nil {
			return nil, err
		}
		ret := make([]CategoryData, 0, len(list.Items))
		for _, item := range list.Items {
			ret = append(ret, FromProductCategory(item))
		}
		return ret, nil
	})
}

func (a *API) CreateProductCategory(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		req := CategoryData{}
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		if req.Name == "" {
			req.Name = base.RandName("cate")
		}
		if req.Alias == "" {
			return nil, errors.NewBadRequest("alias is required for product category")
		}
		productCategory := req.ToProductCategory()
		if err := storage.Create(r.Context(), productCategory); err != nil {
			return nil, err
		}
		return productCategory, nil
	})
}

func (a *API) UpdateProductCategory(w http.ResponseWriter, r *http.Request) {
	a.onProductCategory(w, r, func(ctx context.Context, cate string) (any, error) {
		req := CategoryData{}
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		if req.Alias == "" {
			return nil, errors.NewBadRequest("alias is required for product category")
		}
		productCategory := req.ToProductCategory()
		productCategory.SetName(cate)
		productCategory.SetResourceVersion(0)
		if err := a.Store.Update(r.Context(), productCategory); err != nil {
			return nil, err
		}
		return productCategory, nil
	})
}

func (a *API) RemoveProductCategory(w http.ResponseWriter, r *http.Request) {
	a.onProductCategory(w, r, func(ctx context.Context, cate string) (any, error) {
		return base.GenericDelete(r, a.Store, &ProductCategory{}, cate)
	})
}

func (a *API) onProductCategory(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, cate string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		category := api.Path(r, "category", "")
		if category == "" {
			return nil, errors.NewBadRequest("category is required")
		}
		return fn(ctx, category)
	})
}

func ListProductCategories(ctx context.Context, storage store.Store, opts api.ListOptions) (*store.List[ProductCategory], error) {
	list := &store.List[ProductCategory]{}
	listopts := base.ListOptionsToStoreListOptions(opts)
	if err := storage.List(ctx, list, listopts...); err != nil {
		return nil, err
	}
	return list, nil
}

func (a *API) productCategoryGroup() api.Group {
	return api.
		NewGroup("/product-categories").
		Route(
			api.GET("").
				Doc("List product categories").
				To(a.ListProductCategories).
				Response([]CategoryData{}),

			api.POST("").
				Doc("Create product category").
				To(a.CreateProductCategory).
				Param(
					api.BodyParam("category", CategoryData{}),
				).
				Response(ProductCategory{}),

			api.PUT("/{category}").
				Doc("Update product category").
				To(a.UpdateProductCategory).
				Param(
					api.BodyParam("category", CategoryData{}),
				).
				Response(ProductCategory{}),

			api.DELETE("/{category}").
				Doc("Remove product category").
				To(a.RemoveProductCategory).
				Response(ProductCategory{}),
		)
}
