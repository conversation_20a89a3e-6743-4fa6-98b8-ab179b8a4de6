package market

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/product"
)

func NewAPI(base base.API, charts *artifact.ArtifactService, skus product.SKUService) *API {
	return &API{API: base, Charts: charts, SKUService: skus}
}

type API struct {
	base.API
	Charts     *artifact.ArtifactService
	SKUService product.SKUService
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Market").
		SubGroup(
			a.adminProductsGroup(),
			a.adminProductArtifactGroup(),
			a.adminProductCategoryGroup(),
			a.productCategoryGroup(),
			// a.userProductGroup(), // it registered in the public group
			a.skuGroup(),
		)
}

func (a *API) PublicGroup() api.Group {
	return api.
		NewGroup("").
		Tag("Market").
		SubGroup(
			a.userProductGroup(),
		)
}
