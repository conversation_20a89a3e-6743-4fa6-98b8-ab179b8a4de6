package market

import (
	"context"
	"encoding/json"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/product"
)

type ProductSKU struct {
	Name              string         `json:"name"`
	<PERSON><PERSON>             string         `json:"alias"`
	CreationTimestamp store.Time     `json:"creationTimestamp"`
	Description       string         `json:"description"`
	Content           map[string]any `json:"content" validate:"required"`
	Price             base.Price     `json:"price" validate:"required"`
	Enabled           bool           `json:"enabled"`
	Stock             int            `json:"stock"`
}

const SKUCategory = product.CategoryApplication

func (s *API) ListSKU(w http.ResponseWriter, r *http.Request) {
	s.OnProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		option := &product.ListSKUOptions{
			ListOptions:  api.GetListOptions(r),
			WithDisabled: true,
		}
		list, err := s.SKUService.ListSKU(ctx, SKUCategory, app, option)
		if err != nil {
			return nil, err
		}
		items := make([]ProductSKU, 0, len(list.Items))
		for _, sku := range list.Items {
			items = append(items, ProductSKUFromSKU(sku))
		}
		ret := store.List[ProductSKU]{
			Total: list.Total,
			Page:  list.Page,
			Size:  list.Size,
			Items: items,
		}
		return ret, nil
	})
}

func (s *API) GetSKU(w http.ResponseWriter, r *http.Request) {
	s.onSKU(w, r, func(ctx context.Context, storage store.Store, app, name string) (any, error) {
		sku, err := s.SKUService.GetSKU(ctx, SKUCategory, app, name)
		if err != nil {
			return nil, err
		}
		return ProductSKUFromSKU(*sku), nil
	})
}

type CreateSKU struct {
	Name        string         `json:"name" validate:"name"`
	Alias       string         `json:"alias" validate:"required"`
	Description string         `json:"description"`
	Content     map[string]any `json:"content"`
	Price       base.Price     `json:"price"`
	Enabled     bool           `json:"enabled"`
	Stock       int            `json:"stock"`
}

func (s *API) CreateSKU(w http.ResponseWriter, r *http.Request) {
	s.OnProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		req := &CreateSKU{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		productsku := &ProductSKU{
			Name:        req.Name,
			Description: req.Description,
			Alias:       req.Alias,
			Content:     req.Content,
			Price:       req.Price,
			Enabled:     req.Enabled,
			Stock:       req.Stock,
		}
		orderskudata, err := ProductSkuToSkuData(productsku)
		if err != nil {
			return nil, err
		}
		return s.SKUService.CreateSKU(ctx, SKUCategory, app, orderskudata)
	})
}

func ProductSkuToSkuData(productSKU *ProductSKU) (*product.SKUData, error) {
	addtionalbytes, err := json.Marshal(productSKU.Content)
	if err != nil {
		return nil, err
	}
	sku := &product.SKUData{
		Name:        productSKU.Name,
		Alias:       productSKU.Alias,
		Description: productSKU.Description,
		Additional:  string(addtionalbytes),
		Price:       productSKU.Price,
		Enabled:     productSKU.Enabled,
		Stock:       productSKU.Stock,
		StockPolicy: product.StockPolicyPerBatch,
	}
	return sku, nil
}

func ProductSKUFromSKU(sku product.SKUData) ProductSKU {
	return ProductSKU{
		Alias:             sku.Alias,
		Description:       sku.Description,
		CreationTimestamp: sku.CreationTimestamp,
		Name:              sku.Name,
		Price:             sku.Price,
		Enabled:           sku.Enabled,
		Stock:             sku.Stock,
		Content:           AddtionalToContent(sku.Additional),
	}
}

func AddtionalToContent(additional string) map[string]any {
	content := map[string]any{}
	_ = json.Unmarshal([]byte(additional), &content)
	return content
}

func (s *API) UpdateSKU(w http.ResponseWriter, r *http.Request) {
	s.onSKU(w, r, func(ctx context.Context, storage store.Store, app, name string) (any, error) {
		req := &ProductSKU{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		orderskudata, err := ProductSkuToSkuData(req)
		if err != nil {
			return nil, err
		}
		if err := s.SKUService.UpdateSKU(ctx, SKUCategory, app, name, orderskudata); err != nil {
			return nil, err
		}
		return orderskudata, nil
	})
}

func (s *API) DeleteSKU(w http.ResponseWriter, r *http.Request) {
	s.onSKU(w, r, func(ctx context.Context, storage store.Store, app, name string) (any, error) {
		return nil, s.SKUService.DeleteSKU(ctx, SKUCategory, app, name)
	})
}

func (s *API) onSKU(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, app, name string) (any, error)) {
	s.OnProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, errors.NewBadRequest("name is required")
		}
		return fn(ctx, storage, app, name)
	})
}

func (s *API) adminProductsSKUGroup() api.Group {
	return base.
		NewAdminProductsGroup("skus").
		Route(
			api.GET("").
				To(s.ListSKU).
				Operation("list sku").
				Param(api.PageParams...).
				Response(&store.List[product.SKUData]{}),
			api.POST("").
				Operation("create sku").
				To(s.CreateSKU).
				Param(
					api.BodyParam("sku", &CreateSKU{}),
				),
			api.GET("/{name}").
				Operation("get sku").
				To(s.GetSKU).
				Response(&product.SKUData{}),
			api.PUT("/{name}").
				Operation("update sku").
				To(s.UpdateSKU).
				Param(
					api.BodyParam("sku", &product.SKUData{}),
				),
			api.DELETE("/{name}").
				Operation("delete sku").
				To(s.DeleteSKU),
		)
}

func (s *API) ListUserSKU(w http.ResponseWriter, r *http.Request) {
	s.OnProduct(w, r, func(ctx context.Context, storage store.Store, app string) (any, error) {
		option := &product.ListSKUOptions{ListOptions: api.GetListOptions(r)}
		return s.SKUService.ListSKU(ctx, SKUCategory, app, option)
	})
}

func (s *API) productsSKUGroup() api.Group {
	return base.
		NewProductsGroup("skus").
		Route(
			api.GET("").
				To(s.ListUserSKU).
				Operation("list sku").
				Param(api.PageParams...).
				Response(&store.List[product.SKUData]{}),
		)
}

func (s *API) skuGroup() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			s.productsSKUGroup(),
			s.adminProductsSKUGroup(),
		)
}
