package market

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"slices"
	"strings"
	"time"

	"github.com/blang/semver/v4"
	"helm.sh/helm/v3/pkg/chart"
	"xiaoshiai.cn/common/errors"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/product"
)

type ProductChart struct {
	Metadata   *chart.Metadata   `json:"metadata"`
	Raw        []*chart.File     `json:"raw"`
	Values     map[string]any    `json:"values"`
	Schema     []byte            `json:"schema"`
	I18nSchema map[string][]byte `json:"i18nSchema"`
	Readme     []byte            `json:"readme"`
}

// values.schema.Zh-cn.json
// values.schema.En.json
// values.schema.json
var i18mSchemaRegexp = regexp.MustCompile(`^values\.schema(?:\.([^.]+))?\.json$`)

func ProductChartFromChart(helmchart *chart.Chart) *ProductChart {
	ret := &ProductChart{
		Metadata:   helmchart.Metadata,
		Values:     helmchart.Values,
		Schema:     helmchart.Schema,
		Raw:        helmchart.Raw,
		I18nSchema: map[string][]byte{},
	}
	for _, file := range helmchart.Raw {
		if strings.HasPrefix(file.Name, "README") {
			ret.Readme = file.Data
		}
		if matches := i18mSchemaRegexp.FindStringSubmatch(file.Name); len(matches) > 0 {
			lang := matches[1]
			lower := strings.ToLower(lang)
			key := lower
			switch lower {
			case "", "en":
				key = "en"
			case "zh-cn", "zh", "cn":
				key = "zh"
			}
			ret.I18nSchema[key] = file.Data
		}
	}
	return ret
}

func (a *API) ListProducts(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		opts := ListProductsOptions{
			ListOptions: api.GetListOptions(r),
			Category:    api.Query(r, "category", ""),
			SubCategory: api.Query(r, "subcategory", ""),
			Type:        ProductType(api.Query(r, "type", "")),
			IsSystem:    api.Query[*bool](r, "system", nil),
			Published:   api.Query[*bool](r, "published", nil),
		}
		return ListProducts(ctx, storage, opts)
	})
}

type ProductWithVersion struct {
	Product `json:",inline"`
	Chart   *ProductChart `json:"chart,omitempty"`
}

func (a *API) GetProduct(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		return a.GetProductWithLatest(r, name, true)
	})
}

func GetProduct(ctx context.Context, storage store.Store, name string, withUnpublished bool) (*Product, error) {
	app := &Product{}
	if err := storage.Get(ctx, name, app); err != nil {
		return nil, err
	}
	if !app.Published && !withUnpublished {
		return nil, liberrors.NewNotFound("marketapplication", name)
	}
	return app, nil
}

func (a *API) GetProductWithLatest(r *http.Request, name string, withUnpublished bool) (*ProductWithVersion, error) {
	return GetProductWithVersion(r.Context(), a.Store, a.Charts, name, "", withUnpublished)
}

func GetProductWithVersion(ctx context.Context, storage store.Store, charts artifact.ChartsProvider, name string, version string, withUnpublished bool) (*ProductWithVersion, error) {
	app, err := GetProduct(ctx, storage, name, withUnpublished)
	if err != nil {
		return nil, err
	}
	ret := &ProductWithVersion{Product: *app}

	if v := GetVersionOrLatest(app.Versions, version); v != nil {
		chart, err := charts.DownloadChart(ctx, name, v.Version)
		if err != nil {
			// should ignore the error
			log.Error(err, "download helm chart", "version", v.Version)
		} else {
			ret.Chart = ProductChartFromChart(chart)
		}
	}
	return ret, nil
}

func (a *API) CreateProduct(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		marketapp := &Product{}
		if err := api.Body(r, marketapp); err != nil {
			return nil, err
		}
		if err := ValidateAndCompleteProduct(marketapp); err != nil {
			return nil, err
		}
		if err := storage.Create(r.Context(), marketapp); err != nil {
			return nil, err
		}
		return marketapp, nil
	})
}

func ValidateAndCompleteProduct(marketapp *Product) error {
	if err := base.ValidateName(marketapp.Name); err != nil {
		return err
	}
	if marketapp.OS == "" {
		marketapp.OS = marketapp.Details.OS
	}
	if marketapp.Category == "" {
		return liberrors.NewBadRequest("category is required")
	}
	if marketapp.Type == "" {
		return liberrors.NewBadRequest("type is required")
	}
	allowedTypes := []ProductType{ApplicationTypeContainer, ApplicationTypeVirtualMachine}
	if !slices.Contains(allowedTypes, marketapp.Type) {
		return liberrors.NewBadRequest(fmt.Sprintf("type %s is invalid, must be one of %s", marketapp.Type, allowedTypes))
	}
	// complete
	if marketapp.Labels == nil {
		marketapp.Labels = map[string]string{}
	}
	marketapp.Labels[LabelCategory] = marketapp.Category
	return nil
}

func (a *API) UpdateProduct(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		marketapp := &Product{}
		if err := api.Body(r, marketapp); err != nil {
			return nil, err
		}
		if objname := marketapp.GetName(); objname != "" && objname != name {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, name)
		}
		if err := ValidateAndCompleteProduct(marketapp); err != nil {
			return nil, err
		}
		marketapp.SetName(name)
		marketapp.SetResourceVersion(0)

		if err := storage.Update(r.Context(), marketapp); err != nil {
			return nil, err
		}
		return marketapp, nil
	})
}

func (a *API) DeleteProduct(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		exists := &Product{}
		if err := storage.Get(ctx, name, exists); err != nil {
			return nil, err
		}
		if len(exists.Versions) > 0 {
			return nil, liberrors.NewBadRequest("can't delete application with published versions")
		}
		if exists.Published {
			return nil, liberrors.NewBadRequest("can't delete published application")
		}
		return base.GenericDelete(r, storage, &Product{}, name)
	})
}

func (a *API) PublishProduct(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		app := &Product{}
		if err := a.Store.Get(ctx, name, app); err != nil {
			return nil, err
		}
		if len(app.Versions) == 0 {
			return nil, liberrors.NewBadRequest("can't publish application without any version")
		}
		if app.License.Enabled {
			// check must have at least one enabled sku
			if err := a.checkAtLeastOnEnabledSKU(ctx, name); err != nil {
				return nil, err
			}
		}
		obj := &Product{ObjectMeta: store.ObjectMeta{Name: name}}
		patch := store.RawPatch(store.PatchTypeMergePatch, []byte(`{"published":true}`))
		if err := a.Store.Patch(ctx, obj, patch); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (s *API) checkAtLeastOnEnabledSKU(ctx context.Context, app string) error {
	list, err := s.SKUService.ListSKU(ctx, SKUCategory, app, &product.ListSKUOptions{})
	if err != nil {
		return err
	}
	if len(list.Items) == 0 {
		return errors.NewBadRequest("must have at least one enabled sku")
	}
	return nil
}

func (a *API) UnpublishProduct(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		obj := &Product{ObjectMeta: store.ObjectMeta{Name: name}}
		patch := store.RawPatch(store.PatchTypeMergePatch, []byte(`{"published":false}`))
		if err := a.Store.Patch(ctx, obj, patch); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) PublishProductVersion(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, appname string) (any, error) {
		thisversion := &ProductVersion{}
		if err := api.Body(r, thisversion); err != nil {
			return nil, err
		}
		if thisversion.Version == "" || appname == "" {
			return nil, liberrors.NewBadRequest("name and chart are required")
		}
		app := &Product{ObjectMeta: store.ObjectMeta{Name: appname}}
		if err := a.Store.Get(ctx, appname, app); err != nil {
			return nil, err
		}
		for _, v := range app.Versions {
			if v.Version == thisversion.Version {
				return nil, fmt.Errorf("version %s already exists", v.Version)
			}
		}
		// check version exists in charts
		ok, err := a.Charts.ExistsChart(ctx, appname, thisversion.Version)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, liberrors.NewBadRequest(fmt.Sprintf("chart %s not found", appname))
		}
		thisversion.CreationTimestamp = time.Now()
		newversions := append(app.Versions, *thisversion)

		// sort versions by creation timestamp
		SortVersions(newversions)
		if err := a.Store.Patch(ctx, app, base.MergePatchFromStruct(map[string]any{"versions": newversions})); err != nil {
			return nil, err
		}
		return thisversion, nil
	})
}

// SortVersions sorts versions by creation timestamp in descending order
// other words, the latest version is the first element
func SortVersions(versions []ProductVersion) {
	slices.SortFunc(versions, func(i, j ProductVersion) int {
		// sort by semver version
		if cmp := SemverCompare(j.Version, i.Version); cmp != 0 {
			return cmp
		}
		// fallback to creation timestamp
		return j.CreationTimestamp.Compare(i.CreationTimestamp)
	})
}

func GetVersionOrLatest(versions []ProductVersion, version string) *ProductVersion {
	if version == "" {
		return GetLatestVersion(versions)
	}
	for _, v := range versions {
		if v.Version == version {
			return &v
		}
	}
	return nil
}

func GetLatestVersion(versions []ProductVersion) *ProductVersion {
	if len(versions) == 0 {
		return nil
	}
	SortVersions(versions)
	return &versions[0]
}

func SemverCompare(a, b string) int {
	aver, err := semver.Parse(a)
	if err != nil {
		return 0
	}
	bver, err := semver.Parse(b)
	if err != nil {
		return 0
	}
	return aver.Compare(bver)
}

func (a *API) UnpublishMarketApplicationVersion(w http.ResponseWriter, r *http.Request) {
	a.onProduct(w, r, func(ctx context.Context, storage store.Store, name string) (any, error) {
		version := api.Path(r, "version", "")
		if version == "" {
			return nil, liberrors.NewBadRequest("version is required")
		}
		app := &Product{ObjectMeta: store.ObjectMeta{Name: name}}
		if err := a.Store.Get(ctx, name, app); err != nil {
			return nil, err
		}
		newversions := slices.DeleteFunc(app.Versions, func(i ProductVersion) bool {
			return i.Version == version
		})
		if err := a.Store.Patch(ctx, app, base.MergePatchFromStruct(map[string]any{"versions": newversions})); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) onProduct(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, name string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		name := api.Path(r, "product", "")
		return fn(ctx, storage, name)
	})
}

func (a *API) adminProductsGroup() api.Group {
	return api.
		NewGroup("/admin-products").
		Route(
			api.GET("").
				Doc("List market products").
				To(a.ListProducts).
				Param(api.PageParams...).
				Param(
					api.QueryParam("published", "Filter by published").Optional(),
					api.QueryParam("category", "Filter by category").Optional(),
					api.QueryParam("subcategory", "Filter by subcategory").Optional(),
					api.QueryParam("type", "Filter by type").In(ApplicationTypeContainer, ApplicationTypeVirtualMachine).Optional(),
				).
				Response(store.List[Product]{}),
			api.POST("").Doc("Create market product").
				To(a.CreateProduct).
				Param(api.BodyParam("product", Product{})).
				Response(Product{}),
			api.GET("/{product}").
				Doc("Get market product").
				Param(api.QueryParam("chart", "Show helm chart of the latest version")).
				To(a.GetProduct).
				Response(ProductWithVersion{}),
			api.PUT("/{product}").
				Doc("Update market product").
				To(a.UpdateProduct).Param(api.BodyParam("product", Product{})).Response(Product{}),
			api.DELETE("/{product}").
				Doc("Delete market product").
				To(a.DeleteProduct),
			api.POST("/{product}:publish").
				Doc("Publish market product").
				To(a.PublishProduct).Response(Product{}),
			api.POST("/{product}:unpublish").
				Doc("Unpublish market product").
				To(a.UnpublishProduct).Response(Product{}),

			api.POST("/{product}/versions").
				Doc("Publish market product version").
				To(a.PublishProductVersion).
				Param(api.BodyParam("version", ProductVersion{})).
				Response(ProductVersion{}),
			api.DELETE("/{product}/versions/{version}").
				Doc("Unpublish market product version").
				To(a.UnpublishMarketApplicationVersion).Response(ProductVersion{}),
		)
}
