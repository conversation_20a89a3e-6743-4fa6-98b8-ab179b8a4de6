package application

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/market"
)

type ApplicationUpgradableVersion struct {
	Version string `json:"version,omitempty"`
}

func NewProductsChangeSource(storage store.Store) controller.Source[controller.ScopedKey] {
	return controller.NewCustomStoreSource(storage, "products", func(ctx context.Context, kind store.WatchEventType, obj store.Object) ([]controller.ScopedKey, error) {
		if kind == store.WatchEventDelete || kind == store.WatchEventCreate {
			return nil, nil
		}
		productname := obj.GetName()

		options := []store.ListOption{
			store.WithSubScopes(),
			store.WithFieldRequirements(store.RequirementEqual("product.name", productname)),
		}
		applications := store.List[Application]{}
		if err := storage.List(ctx, &applications, options...); err != nil {
			log.FromContext(ctx).Error(err, "failed to list applications")
			return nil, nil
		}
		triggers := []controller.ScopedKey{}
		for _, app := range applications.Items {
			if app.Product.Name == productname {
				triggers = append(triggers, controller.ScopedKeyFromObject(&app))
			}
		}
		return triggers, nil
	})
}

func (c *ApplicationController) syncCheckUpgrade(ctx context.Context, app *Application) error {
	productname, productversion := app.Product.Name, app.Product.Version
	product := &market.Product{}
	if err := c.Client.Get(ctx, productname, product); err != nil {
		return err
	}
	app.Status.Upgradable = ApplicationUpgradableVersion{
		Version: GetNewerVersion(product, productversion),
	}
	return nil
}

func GetNewerVersion(product *market.Product, current string) string {
	if !product.Published {
		return ""
	}
	versions := product.Versions
	if len(versions) == 0 {
		return ""
	}
	market.SortVersions(versions)
	latestversion := versions[0].Version

	if market.SemverCompare(current, latestversion) >= 0 {
		return ""
	}
	return latestversion
}
