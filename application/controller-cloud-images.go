package application

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application/tasks"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/ismc/common"
	cloud "xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/vmoci"
)

func (c *ApplicationController) ensureVirtualMachineImage(ctx context.Context, clusterref store.ObjectReference, _ cloud.Provider, app *Application, source *ToDeploy, resources []CloudResource, zone string) error {
	log := log.FromContext(ctx)
	// check all image already exists
	for _, resource := range resources {
		switch typed := resource.(type) {
		case *VirtualMachine:
			vmname, vmzone := typed.Name, typed.Zone

			imagename := or(typed.ImageName, typed.Image.Name, typed.Image.ID)
			if imagename == "" {
				return fmt.Errorf("image name is empty for vm %s", vmname)
			}
			if !IsRegistryImage(imagename) {
				return nil
			}

			imginfo := vmoci.ImportImageInfo{
				ResourceGroup: source.Release.Namespace,
				ImageName:     EscapeImageName(imagename),
				Image:         imagename,
				Zone:          def(vmzone, zone),
			}
			if license := source.License; license != nil {
				for _, sec := range license.RegistrySecrets {
					if matchRegistrySecret(imagename, sec.Registry) {
						imginfo.Username = sec.Username
						imginfo.Password = sec.Password
					}
				}
			}
			revappref := encodeRefence(store.ObjectReferenceFrom(app))
			if false {
				// check if the image will be pulling at background
				ok, img, err := c.CloudImageManager.Check(ctx, clusterref, revappref, imginfo)
				if err != nil {
					return err
				}
				if !ok {
					return base.ReQueue(time.Minute)
				}
				if img != nil {
					// set the image id to the vm
				}
			} else {
				img, err := c.CloudImageManager.Pull(ctx, clusterref, imginfo)
				if err != nil {
					return err
				}
				log.Info("set image id to vm", "vm", vmname, "id", img.ID, "image", imagename)
				typed.Image.ID = img.ID
			}
		}
	}
	return nil
}

func matchRegistrySecret(image string, registry string) bool {
	host := strings.TrimPrefix(strings.TrimPrefix(registry, "https://"), "http://")
	return strings.Contains(image, host)
}

func or(i ...string) string {
	for _, s := range i {
		if s != "" {
			return s
		}
	}
	return ""
}

func def(use, def string) string {
	if use == "" {
		return def
	}
	return use
}

type ImagePulledSource struct {
	queue controller.TypedQueue[controller.ScopedKey]
}

func (s *ImagePulledSource) Run(ctx context.Context, queue controller.TypedQueue[controller.ScopedKey]) error {
	log := log.FromContext(ctx)
	log.Info("image pulled source start")
	s.queue = queue
	return nil
}

func NewCloudImageManager(ctx context.Context, clouds cluster.CloudInfoGetter, notfify *ImagePulledSource) *CloudImageManager {
	man := &CloudImageManager{
		Clouds: clouds,
		Notify: notfify,
	}
	man.Tasks = tasks.NewTaskManager(man.pulltask, tasks.TaskManagerOptions[imageTaskinfo]{
		Concurent: 10,
		OnEvent:   man.onEvent,
	})
	go man.Tasks.Run(ctx)
	return man
}

// CloudImageManager is used to pull images from cloud
// if multiple tasks are pulling the same image, the manager should only pull once
// other tasks should wait for the pulling task to finish
// if the pulling task is canceled, the manager should cancel the pulling task
// if all tasks associated with the image are canceled, the manager should cancel the pulling task
type CloudImageManager struct {
	Clouds cluster.CloudInfoGetter
	Tasks  *tasks.TaskManager[imageTaskinfo]

	Notify              *ImagePulledSource
	UseEscapedImageName bool
}

type imageTaskinfo struct {
	vmoci.ImportImageInfo
	ClusterRefID string
}

func (c *CloudImageManager) Pull(ctx context.Context, cluster store.ObjectReference, info vmoci.ImportImageInfo) (*common.Descripter, error) {
	return c.pull(ctx, imageTaskinfo{ImportImageInfo: info, ClusterRefID: encodeRefence(cluster)})
}

func (c *CloudImageManager) Check(ctx context.Context, cluster store.ObjectReference, taskid string, info vmoci.ImportImageInfo) (bool, *common.Image, error) {
	log := log.FromContext(ctx).WithValues("image", info.Image)
	ok, img, err := c.checkExists(ctx, cluster, info)
	if err != nil {
		log.Error(err, "check image exists failed")
		return false, nil, err
	}
	if ok {
		if img.Status.Phase == common.ImagePhaseReady {
			log.Info("image already exists")
			return true, img, nil
		}
		// it ok to reimport the image if it's not ready
		log.Info("image exists but not ready")
	} else {
		// trigger the pulling task on background
		log.Info("image not exists, trigger pulling task")
	}

	taskkey := imageTaskinfo{ImportImageInfo: info, ClusterRefID: encodeRefence(cluster)}

	result, ok := c.Tasks.Get(taskid, taskkey)
	if ok {
		if !result.Finished {
			log.Info("task is running, wait for it")
			return false, nil, nil
		}
		// once the task is finished, remove it from the task manager
		c.Tasks.Cancel(taskid, taskkey)
		if result.Err != nil {
			log.Error(result.Err, "task failed")
			return false, nil, result.Err
		}
		// the task is finished
		log.Info("task finished")
		return true, img, nil
	}
	log.Info("submit the task", "task", taskid)
	c.Tasks.Submit(taskid, taskkey)
	return false, nil, nil
}

func (c *CloudImageManager) Cancel(ctx context.Context, cluster store.ObjectReference, taskid string) error {
	c.Tasks.CancelAll(taskid)
	return nil
}

func (c *CloudImageManager) pulltask(ctx context.Context, info imageTaskinfo) error {
	_, err := c.pull(ctx, info)
	return err
}

func (c *CloudImageManager) pull(ctx context.Context, info imageTaskinfo) (*cloud.Descripter, error) {
	log := log.FromContext(ctx).WithName("pull-image").WithValues("image", info.Image)
	ok, img, err := c.checkExists(ctx, decodeReference(info.ClusterRefID), info.ImportImageInfo)
	if err != nil {
		log.Error(err, "check image exists failed")
		return nil, err
	}
	if ok {
		if img.Status.Phase == common.ImagePhaseReady {
			log.Info("image already exists")
			return &img.Descripter, nil
		}
		// it ok to reimport the image if it's not ready
		log.Info("image exists but not ready, try reimport it")
	}
	cloudinfo, err := c.Clouds.Get(ctx, decodeReference(info.ClusterRefID))
	if err != nil {
		return nil, err
	}
	provider, err := cloudinfo.CloudProvider()
	if err != nil {
		return nil, err
	}
	// select the image formats
	clusterTypeSupportImageFormats := map[cluster.ClusterType][]vmoci.ImageFormat{
		cluster.ClusterTypeVmware: {vmoci.ImageFormatOVA, vmoci.ImageFormatVMDK},
		cluster.ClusterTypeHuawei: {vmoci.ImageFormatQCOW2, vmoci.ImageFormatIMG, vmoci.ImageFormatVHD, vmoci.ImageFormatVHDX},
	}
	formats, ok := clusterTypeSupportImageFormats[cloudinfo.Type()]
	if !ok {
		// default support qemu image formats
		formats = []vmoci.ImageFormat{vmoci.ImageFormatQCOW2, vmoci.ImageFormatIMG, vmoci.ImageFormatRaw}
	}
	log.Info("pull image from registry", "image", info.Image)
	desc, err := vmoci.ImportImageFromRegistryToCloud(ctx, info.ImportImageInfo, formats, provider)
	if err != nil {
		return nil, err
	}
	log.Info("image pulled", "id", desc.ID)
	if desc.ID == "" {
		ok, img, err := c.checkExists(ctx, decodeReference(info.ClusterRefID), info.ImportImageInfo)
		if err != nil {
			return nil, err
		}
		if !ok {
			return nil, fmt.Errorf("image not exists after pulling")
		}
		return &img.Descripter, nil
	}
	return desc, nil
}

// checkExists checks if the image exists in the cloud
// if the image exists, it returns the image id
func (c *CloudImageManager) checkExists(ctx context.Context, cluster store.ObjectReference, info vmoci.ImportImageInfo) (bool, *cloud.Image, error) {
	if info.ImageName == "" || info.Image == "" {
		return false, nil, errors.NewBadRequest("image name is empty")
	}
	cloudinfo, err := c.Clouds.Get(ctx, cluster)
	if err != nil {
		return false, nil, err
	}
	provider, err := cloudinfo.CloudProvider()
	if err != nil {
		return false, nil, err
	}
	imglist, err := provider.ListImages(ctx, cloud.ListImageOptions{
		Name: info.ImageName,
		Zone: info.Zone, // set zone to filter the image, if provider's image is not zone based, it should be ignored
	})
	if err != nil {
		return false, nil, err
	}
	for _, img := range imglist.Items {
		if img.Name == info.ImageName {
			return true, &img, nil
		}
	}
	return false, nil, nil
}

var imageReplacer = strings.NewReplacer("/", "-", ":", "-")

func EscapeImageName(raw string) string {
	// 如果能够直接使用原始的镜像名称，就不需要转义，转义后的名称可读性下降
	// 如果一定要转义，可以在实现层转义
	if true {
		return raw
	}
	return imageReplacer.Replace(raw)
}

func (c *CloudImageManager) onEvent(ctx context.Context, e tasks.TaskEvent[imageTaskinfo]) {
	log := log.FromContext(ctx)
	log.Info("task event", "task", e.ID, "success", e.Success, "error", e.Err)
	if e.Success {
		for _, watcher := range e.Watchers {
			// notify the watcher
			appref := decodeReference(watcher)
			log.Info("image pulled, notify the app", "app", appref)
			if c.Notify.queue != nil {
				c.Notify.queue.Add(controller.NewScopedKey(appref.Scopes, appref.Name))
			}
		}
	}
}

func encodeRefence(cluster store.ObjectReference) string {
	bytes, _ := json.Marshal(cluster)
	return string(bytes)
}

func decodeReference(s string) store.ObjectReference {
	var ref store.ObjectReference
	_ = json.Unmarshal([]byte(s), &ref)
	return ref
}

// IsRegistryImage checks if the image is a registry image
// it's simple but enough for now
func IsRegistryImage(image string) bool {
	splits := strings.Split(image, "/")
	if len(splits) < 3 {
		return false
	}
	if strings.Contains(splits[0], ".") {
		return true
	}
	return false
}
