package database

import (
	"context"
	"encoding/json"
	"fmt"
	"maps"
	"net/http"
	"reflect"
	"slices"
	"strconv"
	"time"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/application/kubeblocks"
	"xiaoshiai.cn/core/cluster"
)

const (
	DefaultRetention = "7d"
)

type ScheduledBackup struct {
	store.ObjectMeta `json:",inline"`
	Method           string `json:"method,omitempty"`
	Cron             string `json:"cron,omitempty"`
	Retention        string `json:"retention,omitempty"`
	// Owner is the owner of the scheduled backup
	// if owner exists, we can't modify the scheduled backup
	Owner       *store.OwnerReference `json:"owner,omitempty"`
	Destination BackupDestination     `json:"destination,omitempty"`
	Enabled     bool                  `json:"enabled,omitempty"`
	Status      ScheduledBackupStatus `json:"status,omitempty"`
}

type BackupDestination map[string]string

type ScheduledBackupStatus struct {
	Message string `json:"message,omitempty"`
	Phase   string `json:"phase,omitempty"`
}

type Backup struct {
	store.ObjectMeta    `json:",inline"`
	ScheduledBackupName string       `json:"scheduledBackupName,omitempty"`
	Method              string       `json:"method,omitempty"`
	Retention           string       `json:"retention,omitempty"`
	Status              BackupStatus `json:"status,omitempty"`
}

type BackupStatus struct {
	Message             string         `json:"message,omitempty"`
	Phase               string         `json:"phase,omitempty"`
	StartTimestamp      *store.Time    `json:"startTimestamp,omitempty"`
	CompletionTimestamp *store.Time    `json:"completionTimestamp,omitempty"`
	ExpirationTimestamp *store.Time    `json:"expirationTimestamp,omitempty"`
	Location            BackupLocation `json:"location,omitempty"`
	TimeRange           *TimeRange     `json:"timeRange,omitempty"`
	Actions             []ActionStatus `json:"actions,omitempty"`
}
type ActionStatus struct {
	Name                string                  `json:"name,omitempty"`
	TargetPodName       string                  `json:"targetPodName,omitempty"`
	Phase               string                  `json:"phase,omitempty"`
	StartTimestamp      *metav1.Time            `json:"startTimestamp,omitempty"`
	CompletionTimestamp *metav1.Time            `json:"completionTimestamp,omitempty"`
	FailureReason       string                  `json:"failureReason,omitempty"`
	ActionType          string                  `json:"actionType,omitempty"`
	AvailableReplicas   *int32                  `json:"availableReplicas,omitempty"`
	ObjectRef           *corev1.ObjectReference `json:"objectRef,omitempty"`
	TotalSize           string                  `json:"totalSize,omitempty"`
	TimeRange           *BackupTimeRange        `json:"timeRange,omitempty"`
	VolumeSnapshots     []VolumeSnapshotStatus  `json:"volumeSnapshots,omitempty"`
}

type BackupTimeRange struct {
	TimeZone string       `json:"timeZone,omitempty"`
	Start    *metav1.Time `json:"start,omitempty"`
	End      *metav1.Time `json:"end,omitempty"`
}

type VolumeSnapshotStatus struct {
	Name        string `json:"name,omitempty"`
	ContentName string `json:"contentName,omitempty"`
	VolumeName  string `json:"volumeName,omitempty"`
	Size        string `json:"size,omitempty"`
	TargetName  string `json:"targetName,omitempty"`
}

type TimeRange struct {
	From *store.Time `json:"from,omitempty"`
	To   *store.Time `json:"to,omitempty"`
}

type BackupLocation struct {
	Type string `json:"type,omitempty"`
	Path string `json:"path,omitempty"`
	Size string `json:"size,omitempty"`
}

func (a *API) ListBackups(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		namespace := app.Cluster.Namespace
		cli := op.Info.Cached

		allbackups := []kubeblocks.Backup{}

		if schedulebackup := api.Query(r, "schedulebackup", ""); schedulebackup != "" {
			backuplist := unstructured.UnstructuredList{}
			backuplist.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackups)
			labelsel := client.MatchingLabels{
				"dataprotection.kubeblocks.io/backup-schedule": schedulebackup,
			}
			if err := cli.List(ctx, &backuplist, client.InNamespace(namespace), labelsel); err != nil {
				return nil, err
			}
			items, err := ListToTyped[kubeblocks.Backup](
				ctx, cli, kubeblocks.GroupVersionKindKubeBlocksBackups, client.InNamespace(namespace), labelsel,
			)
			if err != nil {
				return nil, err
			}
			allbackups = append(allbackups, items...)
		} else {
			backupschedules, err := ListToTyped[kubeblocks.BackupSchedule](ctx, cli,
				kubeblocks.GroupVersionKindKubeBlocksBackupSchedules, client.InNamespace(namespace), client.MatchingLabels{"app.kubernetes.io/instance": application.GetRelease(app).Name})
			if err != nil {
				return nil, err
			}
			for _, item := range backupschedules {
				backuplist := unstructured.UnstructuredList{}
				backuplist.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackups)
				labelsel := client.MatchingLabels{
					"dataprotection.kubeblocks.io/backup-schedule": item.Name,
				}
				if err := cli.List(ctx, &backuplist, client.InNamespace(namespace), labelsel); err != nil {
					return nil, err
				}
				items, err := ListToTyped[kubeblocks.Backup](
					ctx, cli, kubeblocks.GroupVersionKindKubeBlocksBackups, client.InNamespace(namespace), labelsel,
				)
				if err != nil {
					return nil, err
				}
				allbackups = append(allbackups, items...)
			}
		}

		backups := make([]Backup, 0, len(allbackups))
		for _, item := range allbackups {
			backups = append(backups, kubeblockBackupToBackup(item))
		}
		slices.SortFunc(backups, func(a, b Backup) int {
			return b.CreationTimestamp.Compare(a.CreationTimestamp.Time)
		})
		if standalone := api.Query(r, "standalone", false); standalone {
			backups = slices.DeleteFunc(backups, func(b Backup) bool {
				return b.ScheduledBackupName != ""
			})
		}
		return api.PageFromRequest(r, backups,
			func(b Backup) string { return b.Name },
			func(b Backup) time.Time { return b.CreationTimestamp.Time },
		), nil
	})
}

func kubeblockBackupToBackup(kubeblocksbackup kubeblocks.Backup) Backup {
	ret := Backup{
		ObjectMeta: store.ObjectMeta{
			Name:              kubeblocksbackup.GetName(),
			CreationTimestamp: kubeblocksbackup.GetCreationTimestamp(),
			DeletionTimestamp: kubeblocksbackup.GetDeletionTimestamp(),
		},
		Method:              kubeblocksbackup.Spec.BackupMethod,
		Retention:           kubeblocksbackup.Spec.RetentionPeriod,
		ScheduledBackupName: getmapval(kubeblocksbackup.GetLabels(), "dataprotection.kubeblocks.io/backup-schedule"),
		Status: BackupStatus{
			Message:             kubeblocksbackup.Status.FailureReason,
			Phase:               kubeblocksbackup.Status.Phase,
			StartTimestamp:      kubeblocksbackup.Status.StartTimestamp,
			CompletionTimestamp: kubeblocksbackup.Status.CompletionTimestamp,
			ExpirationTimestamp: kubeblocksbackup.Status.Expiration,
			Location: BackupLocation{
				Path: kubeblocksbackup.Status.Path,
				Size: kubeblocksbackup.Status.TotalSize,
			},
		},
	}

	msg := ""
	for _, action := range kubeblocksbackup.Status.Actions {
		if action.FailureReason != "" {
			msg += action.FailureReason + ", "
		}
	}
	ret.Status.Message += msg

	if timerange := kubeblocksbackup.Status.TimeRange; timerange != nil {
		ret.Status.TimeRange = &TimeRange{From: timerange.Start, To: timerange.End}
	}
	return ret
}

func getmapval(m map[string]string, key ...string) string {
	if m == nil {
		return ""
	}
	for _, k := range key {
		if v, ok := m[k]; ok {
			return v
		}
	}
	return ""
}

func backupToKubeBlockBackup(backup Backup, rls application.ApplicationRelease, policyname string) *unstructured.Unstructured {
	kubeblocksbackup := kubeblocks.Backup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      rls.Name + "-" + backup.Name,
			Namespace: rls.Namespace,
			Labels:    map[string]string{"app.kubernetes.io/instance": rls.Name},
		},
		Spec: kubeblocks.BackupSpec{
			BackupMethod:     backup.Method,
			RetentionPeriod:  backup.Retention,
			BackupPolicyName: policyname,
			DeletionPolicy:   "Delete",
		},
	}
	obj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&kubeblocksbackup)
	if err != nil {
		return nil
	}
	uns := &unstructured.Unstructured{Object: obj}
	uns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackups)
	return uns
}

type KubeBlocksBackup struct{}

func (a *API) GetBackup(w http.ResponseWriter, r *http.Request) {
	a.onBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		namespace := app.Cluster.Namespace
		backup, err := GetToTyped[kubeblocks.Backup](ctx, op.Info.Client,
			kubeblocks.GroupVersionKindKubeBlocksBackups, name, namespace)
		if err != nil {
			return nil, err
		}

		// check if the backup is owned by the application
		if labels := backup.GetLabels(); labels == nil || labels["app.kubernetes.io/instance"] != application.GetRelease(app).Name {
			return nil, errors.NewNotFound("backup", name)
		}
		return kubeblockBackupToBackup(backup), nil
	})
}

func (a *API) CreateBackup(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		backup := Backup{}
		if err := api.Body(r, &backup); err != nil {
			return nil, err
		}
		rls := application.GetRelease(app)
		if backup.Name == "" {
			backup.Name = rls.Name + "-" + NowStr()
		}
		if backup.Retention == "" {
			backup.Retention = DefaultRetention
		}
		if backup.Method == "" {
			backup.Method = "xtrabackup"
		}
		cli := op.Info.Client
		// get policy
		policyname, err := autoDetectBackupPolicy(ctx, cli, rls)
		if err != nil {
			return nil, err
		}
		kubeblocksbackup := backupToKubeBlockBackup(backup, rls, policyname)
		if err := op.Info.Client.Create(ctx, kubeblocksbackup); err != nil {
			return nil, err
		}
		created := UnstructuredTo[kubeblocks.Backup](kubeblocksbackup)
		backup = kubeblockBackupToBackup(created)
		return backup, nil
	})
}

func autoDetectBackupPolicy(ctx context.Context, cli client.Client, rls application.ApplicationRelease) (string, error) {
	policylist := unstructured.UnstructuredList{}
	policylist.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackupPolicies)
	labelsel := client.MatchingLabels{"app.kubernetes.io/instance": rls.Name}
	if err := cli.List(ctx, &policylist, client.InNamespace(rls.Namespace), labelsel); err != nil {
		return "", err
	}
	if len(policylist.Items) == 0 {
		return "", errors.NewInternalError(fmt.Errorf("backup maybe not enabled"))
	}
	return policylist.Items[0].GetName(), nil
}

type RestoreOptions struct {
	NewName          string    `json:"newName,omitempty"`
	RestorePointTime time.Time `json:"restorePointTime,omitempty"`
}

func (a *API) RestoreBackupToNewDatabase(w http.ResponseWriter, r *http.Request) {
	a.onBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		restoreOptions := RestoreOptions{}
		if err := api.Body(r, &restoreOptions); err != nil {
			return nil, err
		}
		if restoreOptions.NewName == "" {
			return nil, errors.NewBadRequest("newName is required")
		}

		rls := application.GetRelease(app)

		backupuns := &unstructured.Unstructured{}
		backupuns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackups)
		if err := op.Info.Client.Get(ctx, client.ObjectKey{Namespace: rls.Namespace, Name: name}, backupuns); err != nil {
			return nil, err
		}
		backup := &kubeblocks.Backup{}
		if err := runtime.DefaultUnstructuredConverter.FromUnstructured(backupuns.Object, backup); err != nil {
			return nil, err
		}
		restoreAnnotations, err := getRestoreAnnotations(backup, restoreOptions)
		if err != nil {
			return nil, err
		}
		newvalues := app.Values.DeepCopy()

		// merge annotations
		if annotations, ok := newvalues.Object["annotations"].(map[string]string); ok {
			maps.Copy(restoreAnnotations, annotations)
		}
		newvalues.Object["annotations"] = restoreAnnotations

		// disable backup on restore
		newvalues.Object["backup"] = map[string]any{"enabled": false}

		newdb := Database{
			ObjectMeta: store.ObjectMeta{
				Name: restoreOptions.NewName,
			},
			Cluster: app.Cluster,
			Values:  *newvalues,
			Product: app.Product,
		}
		createdapp, err := application.CreateApplication(ctx, a.Store, app.Scopes, DatabaseToApplication(&newdb))
		if err != nil {
			return nil, err
		}
		return DatabaseFromApplication(*createdapp), nil
	})
}

func getRestoreAnnotations(backup *kubeblocks.Backup, options RestoreOptions) (map[string]string, error) {
	backupType := backup.Labels["dataprotection.kubeblocks.io/backup-type"]
	if backup.Status.Phase != "Completed" && backupType != "Continuous" {
		return nil, errors.NewBadRequest("backup is not completed")
	}
	if backupType == "Continuous" {
		if options.RestorePointTime.IsZero() {
			return nil, errors.NewBadRequest("restore point time is required for continuous backup")
		}
		if backup.Status.TimeRange == nil {
			return nil, errors.NewBadRequest("backup time range is not available")
		}
		if options.RestorePointTime.Before(backup.Status.TimeRange.Start.Time) || options.RestorePointTime.After(backup.Status.TimeRange.End.Time) {
			return nil, errors.NewBadRequest("restore point time is not in backup time range")
		}
	}
	component := getComponentNameOrShadingName(backup.Labels)
	if component == "" {
		return nil, fmt.Errorf("component not found in backup")
	}
	info := map[string]string{}
	info["name"] = backup.Name
	info["namespace"] = backup.Namespace
	// - "Serial"
	// - "Parallel"
	info["volumeRestorePolicy"] = "Parallel"
	// Controls the timing of PostReady actions during the recovery process.
	//
	// If false (default), PostReady actions execute when the Component reaches the "Running" state.
	// If true, PostReady actions are delayed until the entire Cluster is "Running,"
	// ensuring the cluster's overall stability before proceeding.
	info["doReadyRestoreAfterClusterRunning"] = strconv.FormatBool(false)
	if !options.RestorePointTime.IsZero() {
		info["restoreTime"] = options.RestorePointTime.Format(time.RFC3339)
	}
	// currently not use restore env
	if false {
		env := []corev1.EnvVar{}
		bytes, err := json.Marshal(env)
		if err != nil {
			return nil, err
		}
		info["restoreEnv"] = string(bytes)
	}
	if connectionPassword := getmapval(backup.Annotations, "dataprotection.kubeblocks.io/connection-password"); connectionPassword != "" {
		info["connectionPassword"] = connectionPassword
	}
	if encryptedSystemAccountsString := getmapval(backup.Annotations, "kubeblocks.io/encrypted-system-accounts"); encryptedSystemAccountsString != "" {
		encryptedSystemAccountsMap := map[string]map[string]string{}
		_ = json.Unmarshal([]byte(encryptedSystemAccountsString), &encryptedSystemAccountsMap)
		// only set systemAccounts owned by this component
		if thiscomponentsystemaccounts := encryptedSystemAccountsMap[component]; thiscomponentsystemaccounts != nil {
			info["encryptedSystemAccounts"] = jsonencode(thiscomponentsystemaccounts)
		}
	}
	restoreannotaion := map[string]map[string]string{}
	restoreannotaion[component] = info

	annotations := map[string]string{
		"kubeblocks.io/restore-from-backup": jsonencode(restoreannotaion),
	}
	return annotations, nil
}

func getComponentNameOrShadingName(labels map[string]string) string {
	return getmapval(labels, "apps.kubeblocks.io/component-name", "apps.kubeblocks.io/sharding-name")
}

func jsonencode(v any) string {
	bytes, _ := json.Marshal(v)
	return string(bytes)
}

func (a *API) RestoreBackup(w http.ResponseWriter, r *http.Request) {
	a.onBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		rls := application.GetRelease(app)

		backupuns := &unstructured.Unstructured{}
		backupuns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackups)
		if err := op.Info.Client.Get(ctx, client.ObjectKey{Namespace: rls.Namespace, Name: name}, backupuns); err != nil {
			return nil, err
		}
		backup := &kubeblocks.Backup{}
		if err := runtime.DefaultUnstructuredConverter.FromUnstructured(backupuns.Object, backup); err != nil {
			return nil, err
		}
		restore := &kubeblocks.OpsRequest{
			ObjectMeta: metav1.ObjectMeta{
				Name:      rls.Name + "-" + NowStr(),
				Namespace: rls.Namespace,
				Labels: map[string]string{
					"app.kubernetes.io/instance": rls.Name,
				},
			},
			Spec: kubeblocks.OpsRequestSpec{
				ClusterName: rls.Name,
				Type:        kubeblocks.RestoreType,
				SpecificOpsRequest: kubeblocks.SpecificOpsRequest{
					Restore: &kubeblocks.Restore{
						BackupName:      backup.Name,
						BackupNamespace: backup.Namespace,
					},
				},
			},
		}
		obj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(restore)
		if err != nil {
			return nil, err
		}
		restoreuns := &unstructured.Unstructured{Object: obj}
		restoreuns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksOpsRequest)

		if err := op.Info.Client.Create(ctx, restoreuns); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) DeleteBackup(w http.ResponseWriter, r *http.Request) {
	a.onBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		backup := &unstructured.Unstructured{}
		backup.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackups)
		backup.SetName(name)
		backup.SetNamespace(app.Cluster.Namespace)
		if err := op.Info.Client.Delete(ctx, backup); err != nil {
			if apierrors.IsNotFound(err) {
				return nil, errors.NewNotFound("backup", name)
			}
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) onBackup(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, backup string) (any, error)) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		name := api.Path(r, "backup", "")
		if name == "" {
			return nil, errors.NewBadRequest("backup name is required")
		}
		return fn(ctx, op, app, name)
	})
}

func (a *API) ListScheduledBackups(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		namespace := app.Cluster.Namespace
		cli := op.Info.Client

		labelsel := client.MatchingLabels{
			"app.kubernetes.io/instance": application.GetRelease(app).Name,
		}
		schedulebackups, err := ListToTyped[kubeblocks.BackupSchedule](ctx, cli,
			kubeblocks.GroupVersionKindKubeBlocksBackupSchedules, client.InNamespace(namespace), labelsel)
		if err != nil {
			return nil, err
		}

		backuppolicies, _ := ListToTyped[kubeblocks.BackupPolicy](ctx, cli,
			kubeblocks.GroupVersionKindKubeBlocksBackupPolicies, client.InNamespace(namespace), labelsel)
		policiesmap := map[string]kubeblocks.BackupPolicy{}
		for _, item := range backuppolicies {
			policiesmap[item.Name] = item
		}

		backuprepomap := map[string]kubeblocks.BackupRepo{}
		for _, policy := range policiesmap {
			if policy.Spec.BackupRepoName == nil {
				continue
			}
			backuprepo, err := GetToTyped[kubeblocks.BackupRepo](ctx, cli,
				kubeblocks.GroupVersionKindKubeBlocksBackupRepos, *policy.Spec.BackupRepoName, "")
			if err != nil {
				log.FromContext(ctx).Error(err, "failed to get backup repo", "name", *policy.Spec.BackupRepoName)
				continue
			}
			backuprepomap[backuprepo.Name] = backuprepo
		}

		ret := make([]ScheduledBackup, 0, len(schedulebackups))
		for _, item := range schedulebackups {
			schedulebackup := kubeblockScheduledBackupToScheduledBackup(item)
			if !schedulebackup.Enabled {
				continue
			}
			if policy, ok := policiesmap[item.Spec.BackupPolicyName]; ok {
				if policy.Spec.BackupRepoName != nil {
					if repo, ok := backuprepomap[*policy.Spec.BackupRepoName]; ok {
						schedulebackup.Destination = repo.Spec.Config
						if repo.Status.Phase != "Ready" {
							schedulebackup.Status.Phase = repo.Status.Phase
							conditionmessages := ""
							for _, condition := range repo.Status.Conditions {
								if condition.Message != "" {
									conditionmessages += fmt.Sprintf("%v: %v\n", condition.Type, condition.Message)
								}
							}
							schedulebackup.Status.Message += fmt.Sprintf(
								"Backup repo %v is not ready: %s", *policy.Spec.BackupRepoName, conditionmessages,
							)
						}
					}
				}
			}
			ret = append(ret, schedulebackup)
		}
		return api.PageFromRequest(r, ret,
			func(b ScheduledBackup) string { return b.Name },
			func(b ScheduledBackup) time.Time { return b.CreationTimestamp.Time },
		), nil
	})
}

func GetToTyped[T any](ctx context.Context, cli client.Client, gvk schema.GroupVersionKind, name string, namespace string) (T, error) {
	uns := &unstructured.Unstructured{}
	uns.SetGroupVersionKind(gvk)
	if err := cli.Get(ctx, client.ObjectKey{Name: name, Namespace: namespace}, uns); err != nil {
		return *new(T), err
	}
	return UnstructuredTo[T](uns), nil
}

func UnstructuredTo[T any](uns *unstructured.Unstructured) T {
	var t T
	runtime.DefaultUnstructuredConverter.FromUnstructured(uns.Object, &t)
	return t
}

func ListToTyped[T any](ctx context.Context, cli client.Client, gvk schema.GroupVersionKind, opts ...client.ListOption) ([]T, error) {
	list := &unstructured.UnstructuredList{}
	list.SetGroupVersionKind(gvk)
	if err := cli.List(ctx, list, opts...); err != nil {
		return nil, err
	}
	return UnstructuredListItemTo[T](list), nil
}

func UnstructuredListItemTo[T any](list *unstructured.UnstructuredList) []T {
	ret := make([]T, 0, len(list.Items))
	for _, item := range list.Items {
		var t T
		runtime.DefaultUnstructuredConverter.FromUnstructured(item.Object, &t)
		ret = append(ret, t)
	}
	return ret
}

func kubeblockScheduledBackupToScheduledBackup(kubeblocksschedulebackup kubeblocks.BackupSchedule) ScheduledBackup {
	var schedule kubeblocks.SchedulePolicy
	// use first enabled method
	for _, item := range kubeblocksschedulebackup.Spec.Schedules {
		if ptr.Deref(item.Enabled, false) {
			schedule = item
			break
		}
	}
	if schedule.BackupMethod == "" && len(kubeblocksschedulebackup.Spec.Schedules) > 0 {
		schedule = kubeblocksschedulebackup.Spec.Schedules[0]
	}
	var owner *store.OwnerReference
	for _, ownerreference := range kubeblocksschedulebackup.GetOwnerReferences() {
		owner = &store.OwnerReference{
			Name:     ownerreference.Name,
			Resource: ownerreference.Kind,
		}
	}
	return ScheduledBackup{
		ObjectMeta: store.ObjectMeta{
			Name:              kubeblocksschedulebackup.GetName(),
			CreationTimestamp: kubeblocksschedulebackup.GetCreationTimestamp(),
			DeletionTimestamp: kubeblocksschedulebackup.GetDeletionTimestamp(),
		},
		Method:    schedule.BackupMethod,
		Owner:     owner,
		Cron:      schedule.CronExpression,
		Retention: schedule.RetentionPeriod,
		Enabled:   ptr.Deref(schedule.Enabled, false),
		Status: ScheduledBackupStatus{
			Message: kubeblocksschedulebackup.Status.FailureReason,
			Phase:   string(kubeblocksschedulebackup.Status.Phase),
		},
	}
}

func scheduledBackupToKubeBlockScheduledBackup(backup ScheduledBackup, rls application.ApplicationRelease, backuppolicy string) *unstructured.Unstructured {
	kubeblocksschedulebackup := kubeblocks.BackupSchedule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      rls.Name + "-" + backup.Name,
			Namespace: rls.Namespace,
			Labels:    map[string]string{"app.kubernetes.io/instance": rls.Name},
		},
		Spec: kubeblocks.BackupScheduleSpec{
			BackupPolicyName: backuppolicy,
			Schedules: []kubeblocks.SchedulePolicy{
				{
					CronExpression:  backup.Cron,
					RetentionPeriod: backup.Retention,
					BackupMethod:    backup.Method,
					Enabled:         ptr.To(backup.Enabled),
				},
			},
		},
	}
	obj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&kubeblocksschedulebackup)
	if err != nil {
		return nil
	}
	uns := &unstructured.Unstructured{Object: obj}
	uns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackupSchedules)
	return uns
}

func (a *API) GetScheduledBackup(w http.ResponseWriter, r *http.Request) {
	a.onScheduledBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		schedulebackup, err := GetToTyped[kubeblocks.BackupSchedule](ctx, op.Info.Client,
			kubeblocks.GroupVersionKindKubeBlocksBackupSchedules, name, app.Cluster.Namespace)
		if err != nil {
			return nil, err
		}
		// check if the backup is owned by the application
		if labels := schedulebackup.GetLabels(); labels == nil || labels["app.kubernetes.io/instance"] != application.GetRelease(app).Name {
			return nil, errors.NewNotFound("schedulebackup", name)
		}
		return kubeblockScheduledBackupToScheduledBackup(schedulebackup), nil
	})
}

func (a *API) UpdateScheduledBackup(w http.ResponseWriter, r *http.Request) {
	a.onScheduledBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		schedulebackup := ScheduledBackup{}
		if err := api.Body(r, &schedulebackup); err != nil {
			return nil, err
		}
		rls := application.GetRelease(app)
		cli := op.Info.Client

		uns := &unstructured.Unstructured{}
		uns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackupSchedules)

		if err := cli.Get(ctx, client.ObjectKey{Namespace: rls.Namespace, Name: name}, uns); err != nil {
			return nil, err
		}
		orginal := uns.DeepCopy()
		fn := func(i int, schedule map[string]any) {
			if i != 0 {
				return
			}
			schedule["enabled"] = schedulebackup.Enabled
			schedule["backupMethod"] = schedulebackup.Method
			schedule["cronExpression"] = schedulebackup.Cron
			schedule["retentionPeriod"] = schedulebackup.Retention
		}
		application.NestedSliceForeachFunc(uns.Object, fn, "spec", "schedules")
		if !reflect.DeepEqual(uns.Object, orginal.Object) {
			if err := cli.Patch(ctx, uns, client.MergeFrom(orginal)); err != nil {
				return nil, err
			}
		}
		updated := UnstructuredTo[kubeblocks.BackupSchedule](uns)
		return kubeblockScheduledBackupToScheduledBackup(updated), nil
	})
}

func (a *API) CreateScheduledBackup(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		schedulebackup := ScheduledBackup{}
		if err := api.Body(r, &schedulebackup); err != nil {
			return nil, err
		}
		rls := application.GetRelease(app)
		if schedulebackup.Name == "" {
			schedulebackup.Name = rls.Name + "-" + time.Now().Format("20060102150405")
		}
		if schedulebackup.Retention == "" {
			schedulebackup.Retention = DefaultRetention
		}
		if schedulebackup.Method == "" {
			schedulebackup.Method = "xtrabackup"
		}
		if schedulebackup.Cron == "" {
			return nil, errors.NewBadRequest("cron is required")
		}
		cli := op.Info.Client
		// get policy
		policyname, err := autoDetectBackupPolicy(ctx, cli, rls)
		if err != nil {
			return nil, err
		}
		schedulebackup.Enabled = true
		kubeblocksschedulebackup := scheduledBackupToKubeBlockScheduledBackup(schedulebackup, rls, policyname)
		if err := cli.Create(ctx, kubeblocksschedulebackup); err != nil {
			return nil, err
		}
		created := UnstructuredTo[kubeblocks.BackupSchedule](kubeblocksschedulebackup)
		schedulebackup = kubeblockScheduledBackupToScheduledBackup(created)
		return schedulebackup, nil
	})
}

func (a *API) DeleteScheduledBackup(w http.ResponseWriter, r *http.Request) {
	a.onScheduledBackup(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		schedulebackup := &unstructured.Unstructured{}
		schedulebackup.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackupSchedules)
		schedulebackup.SetName(name)
		schedulebackup.SetNamespace(app.Cluster.Namespace)
		if err := op.Info.Client.Delete(ctx, schedulebackup); err != nil {
			if apierrors.IsNotFound(err) {
				return nil, errors.NewNotFound("schedulebackup", name)
			}
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) onScheduledBackup(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, backup string) (any, error)) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		name := api.Path(r, "schedulebackup", "")
		if name == "" {
			return nil, errors.NewBadRequest("schedulebackup name is required")
		}
		return fn(ctx, op, app, name)
	})
}

func (a *API) ListOperations(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		namespace := app.Cluster.Namespace
		cli := op.Info.Client

		opsRequestList := unstructured.UnstructuredList{}
		opsRequestList.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksOpsRequest)

		labelsel := client.MatchingLabels{
			"app.kubernetes.io/instance": application.GetRelease(app).Name,
		}

		if err := cli.List(ctx, &opsRequestList, client.InNamespace(namespace), labelsel); err != nil {
			return nil, err
		}

		operations := make([]Operation, 0, len(opsRequestList.Items))
		for _, item := range opsRequestList.Items {
			operations = append(operations, kubeblockOpsRequestToOperation(&item))
		}

		return api.PageFromRequest(r, operations,
			func(o Operation) string { return o.Name },
			func(o Operation) time.Time { return o.CreationTimestamp.Time },
		), nil
	})
}

func (a *API) GetOperation(w http.ResponseWriter, r *http.Request) {
	a.onOperation(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		namespace := app.Cluster.Namespace
		opsRequest := &unstructured.Unstructured{}
		opsRequest.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksOpsRequest)
		if err := op.Info.Client.Get(ctx, client.ObjectKey{Namespace: namespace, Name: name}, opsRequest); err != nil {
			return nil, err
		}
		// check if the operation is owned by the application
		if labels := opsRequest.GetLabels(); labels == nil || labels["app.kubernetes.io/instance"] != application.GetRelease(app).Name {
			return nil, errors.NewNotFound("operation", name)
		}
		return kubeblockOpsRequestToOperation(opsRequest), nil
	})
}

func (a *API) DeleteOperation(w http.ResponseWriter, r *http.Request) {
	a.onOperation(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, name string) (any, error) {
		opsRequest := &unstructured.Unstructured{}
		opsRequest.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksOpsRequest)
		opsRequest.SetName(name)
		opsRequest.SetNamespace(app.Cluster.Namespace)
		if err := op.Info.Client.Delete(ctx, opsRequest); err != nil {
			if apierrors.IsNotFound(err) {
				return nil, errors.NewNotFound("operation", name)
			}
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) onOperation(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, op cluster.ContainerOperation, app *application.Application, operation string) (any, error)) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		name := api.Path(r, "operation", "")
		if name == "" {
			return nil, errors.NewBadRequest("operation name is required")
		}
		return fn(ctx, op, app, name)
	})
}

func NowStr() string {
	return time.Now().Format("20060102150405")
}

type Operation struct {
	store.ObjectMeta `json:",inline"`
	Type             string          `json:"type,omitempty"`
	Status           OperationStatus `json:"status,omitempty"`
}

type OperationStatus struct {
	Phase               string      `json:"phase,omitempty"`
	Progress            string      `json:"progress,omitempty"`
	StartTimestamp      *store.Time `json:"startTimestamp,omitempty"`
	CompletionTimestamp *store.Time `json:"completionTimestamp,omitempty"`
	Message             string      `json:"message,omitempty"`
}

func kubeblockOpsRequestToOperation(opsRequest *unstructured.Unstructured) Operation {
	kubeblocksOpsRequest := kubeblocks.OpsRequest{}
	runtime.DefaultUnstructuredConverter.FromUnstructured(opsRequest.Object, &kubeblocksOpsRequest)
	ret := Operation{
		ObjectMeta: store.ObjectMeta{
			Name:              opsRequest.GetName(),
			CreationTimestamp: opsRequest.GetCreationTimestamp(),
			DeletionTimestamp: opsRequest.GetDeletionTimestamp(),
		},
		Type: string(kubeblocksOpsRequest.Spec.Type),
		Status: OperationStatus{
			Phase:          string(kubeblocksOpsRequest.Status.Phase),
			Progress:       kubeblocksOpsRequest.Status.Progress,
			StartTimestamp: kubeblocksOpsRequest.Status.StartTimestamp.DeepCopy(),
			Message:        getOperationMessage(&kubeblocksOpsRequest),
		},
	}
	return ret
}

func getOperationMessage(opsRequest *kubeblocks.OpsRequest) string {
	// Extract message from components status if available
	if len(opsRequest.Status.Components) > 0 {
		for _, compStatus := range opsRequest.Status.Components {
			if compStatus.Message != "" {
				return compStatus.Message
			}
		}
	}
	return ""
}

type BackupMethod struct {
	Method    string `json:"method,omitempty"`
	Retention string `json:"retention,omitempty"`
	Cron      string `json:"cron,omitempty"`
}

func (a *API) ListBackupMethods(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		uns := &unstructured.Unstructured{}
		uns.SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksBackupPolicyTemplates)

		cli := op.Info.Client

		objkey := client.ObjectKey{
			Name:      fmt.Sprintf("%s-backup-policy-template", app.Product.Name),
			Namespace: app.Cluster.Namespace,
		}
		if err := cli.Get(ctx, objkey, uns); err != nil {
			if apierrors.IsNotFound(err) {
				// no backup policy template found, return empty list
				return []BackupMethod{}, nil
			}
			return nil, err
		}

		template := kubeblocks.BackupPolicyTemplate{}
		runtime.DefaultUnstructuredConverter.FromUnstructured(uns.Object, &template)

		methods := []BackupMethod{}
		for _, policy := range template.Spec.BackupPolicies {
			for _, schedule := range policy.Schedules {
				methods = append(methods, BackupMethod{
					Method:    schedule.BackupMethod,
					Retention: schedule.RetentionPeriod,
					Cron:      schedule.CronExpression,
				})
			}
		}
		return methods, nil
	})
}

func (a *API) backupGroup() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			api.NewGroup("/operations").
				Route(
					api.GET("").Operation("list database operations").
						To(a.ListOperations).
						Param(
							api.PageParams...,
						).
						Response(store.List[Operation]{}),

					api.GET("/{operation}").
						To(a.GetOperation).
						Operation("get database operation").
						Response(Operation{}),
				),

			api.NewGroup("/backupmethods").
				Route(
					api.GET("").
						To(a.ListBackupMethods).
						Operation("list database backup methods").
						Response([]BackupMethod{}),
				),

			api.NewGroup("/backups").
				Route(
					api.GET("").
						To(a.ListBackups).
						Operation("list database backups").
						Param(
							api.QueryParam("schedulebackup", "filter by schedulebackup").Optional(),
							api.QueryParam("standalone", "filter standalone backups").Optional(),
						).
						Param(
							api.PageParams...,
						).
						Response(store.List[Backup]{}),

					api.GET("/{backup}").
						To(a.GetBackup).
						Operation("get database backup").
						Response(Backup{}),

					api.POST("").
						To(a.CreateBackup).
						Operation("create database backup").
						Param(
							api.BodyParam("body", &Backup{}),
						).
						Response(Backup{}),

					api.POST("/{backup}:restore").
						To(a.RestoreBackupToNewDatabase).
						Param(
							api.BodyParam("body", &RestoreOptions{}),
						).
						Operation("restore to current backup"),

					api.DELETE("/{backup}").
						To(a.DeleteBackup).
						Operation("delete database backup").
						Response(Backup{}),
				),
			api.NewGroup("/schedulebackups").
				Route(
					api.GET("").
						To(a.ListScheduledBackups).
						Operation("list database scheduled backups").
						Param(
							api.PageParams...,
						).
						Response(store.List[ScheduledBackup]{}),

					api.GET("/{schedulebackup}").
						To(a.GetScheduledBackup).
						Operation("get database scheduled backup").
						Response(ScheduledBackup{}),

					api.PUT("/{schedulebackup}").
						To(a.UpdateScheduledBackup).
						Operation("update database scheduled backup").
						Response(ScheduledBackup{}),

					api.POST("").
						To(a.CreateScheduledBackup).
						Operation("create database scheduled backup").
						Param(
							api.BodyParam("body", &ScheduledBackup{}),
						).
						Response(ScheduledBackup{}),

					api.DELETE("/{schedulebackup}").
						To(a.DeleteScheduledBackup).
						Operation("delete database scheduled backup").
						Response(ScheduledBackup{}),
				),
		)
}
