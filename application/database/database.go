package database

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/helm"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/loadbalancer"
	"xiaoshiai.cn/core/observerability"
	"xiaoshiai.cn/core/observerability/dashboard"
	"xiaoshiai.cn/core/pay"
)

func NewAPI(base base.API, mongoStore store.Store, cloudinfo cluster.CloudInfoGetter, charts artifact.ChartsProvider, lb loadbalancer.Loadbalancer, dashboards map[string]dashboard.DashboradConfiguration, pay *pay.PaySystem) *API {
	api := &API{API: base}
	api.APP = application.NewCustomApplicationAPI(base,
		api.onDatabaseApplication,
		GetGenericonListApplicationFunc(base),
		cloudinfo, charts, pay)
	api.Status = application.NewApplicationStatusAPI(base, mongoStore, cloudinfo, api.onDatabaseApplication)
	api.Observerability = observerability.NewApplicationObservabilityAPI(api.Status, dashboards)
	api.LoadBalancer = application.NewCustomApplicationLoadbalancerAPI(base, cloudinfo, lb, api.onDatabaseApplication)
	return api
}

func GetGenericonListApplicationFunc(b base.API) application.OnListApplicationFunc {
	return func(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, scopes []store.Scope) (any, error)) {
		b.OnTenantOrTenantOrganizationScopes(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
			if parentapp := api.Path(r, "database", ""); parentapp != "" {
				scopes = append(scopes, base.ScopeApplication(parentapp))
			}
			return fn(ctx, scopes)
		})
	}
}

type API struct {
	base.API
	APP             *application.ApplicationAPI
	Status          *application.ApplicationStatusAPI
	Observerability *observerability.ApplicationObservabilityAPI
	LoadBalancer    *application.ApplicationLoadbalancerAPI
}

// Database is a tenant scopes resource
type Database struct {
	store.ObjectMeta `json:",inline"`
	Product          application.ProductReference   `json:"product,omitempty"`
	Cluster          application.ApplicationCluster `json:"cluster,omitempty"`
	Values           helm.HelmValues                `json:"values,omitempty"`
	Status           application.ApplicationStatus  `json:"status,omitempty"`
	Paused           bool                           `json:"paused,omitempty"`
}

type DatabaseType string

func (a *API) ListDatabases(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		return ListDatabases(r, a.Store, tenant, org)
	})
}

func ListDatabases(r *http.Request, root store.Store, tenant, org string, opts ...store.ListOption) (store.List[Database], error) {
	ret := store.List[Database]{}
	root = root.Scope(base.ScopeTenantOrganization(tenant, org)...)

	opts = append(opts, store.WithLabelRequirements(
		store.RequirementEqual(application.LabelApplicationSubCategory, base.ProductSubCategoryDatabase)))
	applist, err := base.GenericList(r, root, &store.List[application.Application]{}, opts...)
	if err != nil {
		return ret, err
	}
	for _, app := range applist.Items {
		ret.Items = append(ret.Items, DatabaseFromApplication(app))
	}
	return ret, nil
}

func (a *API) GetDatabase(w http.ResponseWriter, r *http.Request) {
	a.onDatabase(w, r, func(ctx context.Context, tenant, org, database string) (any, error) {
		appref := store.ObjectReference{
			Name:   database,
			Scopes: base.ScopeTenantOrganization(tenant, org),
		}
		app, err := application.GetApplication(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		db := DatabaseFromApplication(*app)
		return db, nil
	})
}

func DatabaseFromApplication(app application.Application) Database {
	return Database{
		ObjectMeta: app.ObjectMeta,
		Cluster:    app.Cluster,
		Values:     app.Values,
		Product:    app.Product,
		Status:     app.Status,
		Paused:     app.Paused,
	}
}

func (a *API) CreateDatabase(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		database := &Database{}
		if err := api.Body(r, database); err != nil {
			return nil, err
		}
		if err := a.checkBeforeSetDatabase(ctx, database); err != nil {
			return nil, err
		}
		app := DatabaseToApplication(database)
		appscopes := base.ScopeTenantOrganization(tenant, org)

		if isDryRun := api.Query(r, "dry-run", false); isDryRun {
			return a.APP.DryRunApplication(ctx, appscopes, app)
		}
		app, err := application.CreateApplication(ctx, a.Store, appscopes, app)
		if err != nil {
			return nil, err
		}
		return DatabaseFromApplication(*app), nil
	})
}

func (a *API) UpdateDatabase(w http.ResponseWriter, r *http.Request) {
	a.onDatabase(w, r, func(ctx context.Context, tenant, org, database string) (any, error) {
		gatewareq := &Database{}
		if err := api.Body(r, gatewareq); err != nil {
			return nil, err
		}
		if err := a.checkBeforeSetDatabase(ctx, gatewareq); err != nil {
			return nil, err
		}
		app := DatabaseToApplication(gatewareq)
		appscopes := base.ScopeTenantOrganization(tenant, org)
		app.Name = database
		app, err := application.UpdateApplication(ctx, a.Store, appscopes, app)
		if err != nil {
			return nil, err
		}
		return DatabaseFromApplication(*app), nil
	})
}

func (a *API) checkBeforeSetDatabase(_ context.Context, database *Database) error {
	return nil
}

func DatabaseToApplication(database *Database) *application.Application {
	if database.Labels == nil {
		database.Labels = map[string]string{}
	}
	database.Labels[base.LabelApplicationNotOrignial] = base.ValueTrue
	return &application.Application{
		ObjectMeta:               database.ObjectMeta,
		Cluster:                  database.Cluster,
		Values:                   database.Values,
		Product:                  database.Product,
		AllowClusterSopeResource: true,
		Paused:                   database.Paused,
		Status:                   database.Status,
	}
}

func (a *API) onDatabaseApplication(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error)) {
	a.onDatabase(w, r, func(ctx context.Context, tenant string, org, database string) (any, error) {
		appref := store.ObjectReference{
			Name:   database,
			Scopes: base.ScopeTenantOrganization(tenant, org),
		}
		if subapp := api.Path(r, "operation", ""); subapp != "" {
			appref.Scopes = append(appref.Scopes, base.ScopeApplication(appref.Name))
			appref.Name = subapp
		}
		appscopestorage := a.Store.Scope(appref.Scopes...).Scope(base.ScopeApplication(appref.Name))
		return fn(ctx, appref, appscopestorage)
	})
}

func (a *API) onDatabase(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, org, database string) (any, error)) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		database := api.Path(r, "database", "")
		if database == "" {
			return nil, errors.NewBadRequest("database name is required")
		}
		return fn(ctx, tenant, org, database)
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Database").
		SubGroup(
			base.NewTenantOrganizationGroup("databases").
				Route(
					api.GET("").
						Doc("List databases").
						To(a.ListDatabases).
						Param(
							api.QueryParam("watch", "watch changes and return them as a server-sent events stream").Optional(),
							api.QueryParam("product", "product name").Optional(),
						).
						Param(api.PageParams...).
						Response(store.List[application.Application]{}),

					api.POST("").
						Doc("Create database").
						To(a.CreateDatabase).
						Param(api.QueryParam("dry-run", "dry run mode").Optional().Format("bool")).
						Param(api.BodyParam("database", Database{})).
						Response(Database{}),

					api.GET("/{database}").
						Doc("Get database").
						To(a.GetDatabase).
						Response(Database{}),

					api.PUT("/{database}").
						Doc("Update database").
						To(a.UpdateDatabase).
						Param(api.BodyParam("database", Database{})).
						Response(Database{}),

					api.DELETE("/{database}").
						Doc("Delete database").
						To(a.APP.DeleteApplication),

					api.POST("/{database}:pause").
						Doc("Pause database").
						To(a.APP.PauseApplication),

					api.POST("/{database}:resume").
						Doc("Resume database").
						To(a.APP.ResumeApplication),
				),

			// currently not used
			// base.NewTenantOrganizationGroup("databases/{database}/operations/").
			// SubGroup(a.APP.ApplicationsGroup("operations")),

			base.NewTenantOrganizationGroup("databases/{database}").
				SubGroup(
					a.Status.Group(),
					a.LoadBalancer.Group(),
					a.Observerability.Group(),
					a.accountGroup(),
					a.endpointGroup(),
					a.backupGroup(),
				),
			base.NewTenantOrganizationGroup("databases/{database}/operations/{operation}").
				SubGroup(
					a.Status.Group(),
					a.Observerability.Group(),
				),
		)
}
