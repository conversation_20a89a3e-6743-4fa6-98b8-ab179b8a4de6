package application

import (
	"context"
	"fmt"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/license"
)

func NewTenantApplicationLicenseController(storage store.Store) (*controller.Controller, error) {
	rec := &ApplicationLicenseController{
		Client: storage,
	}
	br := controller.NewBetterReconciler(rec, storage)
	c := controller.
		NewController("application-licenses", br).
		Watch(
			controller.NewStoreSource(storage, &license.License{}),
		)
	return c, nil
}

type ApplicationLicenseController struct {
	Client store.Store
}

// Sync implements controller.Reconciler.
func (a *ApplicationLicenseController) Sync(ctx context.Context, obj *license.License) (controller.Result, error) {
	return base.UnwrapReQueueError(a.sync(ctx, obj))
}

func (a *ApplicationLicenseController) sync(ctx context.Context, obj *license.License) error {
	if obj.Status.Valid || obj.Status.UsedBy == nil || obj.Status.UsedBy.Resource != "applications" {
		return nil
	}
	ref := obj.Status.UsedBy
	// license expired
	app := &Application{}
	if err := a.Client.Scope(ref.Scopes...).Get(ctx, ref.Name, app); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	// pause the application
	app.Paused = true
	if err := a.Client.Scope(app.Scopes...).Status().Update(ctx, app); err != nil {
		return err
	}
	return nil
}

// Remove implements controller.Reconciler.
func (a *ApplicationLicenseController) Remove(ctx context.Context, obj *license.License) (controller.Result, error) {
	return controller.Result{}, nil
}

func AcquireLicense(ctx context.Context, root store.Store, app *Application, dryrun bool) (*license.License, error) {
	licensename := app.License.Name
	if licensename == "" {
		return nil, nil
	}
	// just allow use license at or under current application scope
	licensescopedstore := root.Scope(app.Scopes...)

	license := &license.License{}
	if err := licensescopedstore.Get(ctx, licensename, license); err != nil {
		return nil, err
	}
	if !license.Status.Valid {
		return nil, fmt.Errorf("license %s is not valid", licensename)
	}
	if license.For != "" && license.For != app.Product.Name {
		return nil, fmt.Errorf("license %s is not for product %s", licensename, app.Product.Name)
	}

	currentref := store.ResourcedObjectReferenceFrom(app)

	if usedby := license.Status.UsedBy; usedby != nil {
		if !usedby.Equals(currentref) {
			return nil, fmt.Errorf("license %s is currently used by %s", licensename, usedby.String())
		}
		return license, nil
	}
	if !dryrun {
		// update license status
		license.Status.UsedBy = &currentref
		if err := licensescopedstore.Status().Update(ctx, license); err != nil {
			return nil, err
		}
	}
	return license, nil
}

func ReleaseLicense(ctx context.Context, storage store.Store, app *Application, dryrun bool) error {
	licenseref := app.Status.License.InUse
	if licenseref.Name == "" {
		return nil
	}
	licensescopedstore := storage.Scope(licenseref.Scopes...)
	license := &license.License{}
	if err := licensescopedstore.Get(ctx, licenseref.Name, license); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	usedby := license.Status.UsedBy
	if usedby != nil && usedby.Name == app.Name {
		license.Status.UsedBy = nil
		if !dryrun {
			if err := licensescopedstore.Status().Update(ctx, license); err != nil {
				return err
			}
		}
	}
	return nil
}
