package application

import (
	"context"
	"fmt"
	"net"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/loadbalancer"
)

type LoadBalancer struct {
	store.ObjectMeta `json:",inline"`
	// Target is the target of the loadbalancer's destination.
	// it's the service name on kubernetes.
	Service string `json:"service,omitempty"`
	// it's the instance id on the cloud.
	IP       string             `json:"ip,omitempty"`
	Port     int                `json:"port,omitempty"`
	Protocol string             `json:"protocol,omitempty"`
	Status   LoadBalancerStatus `json:"status,omitempty"`
}

type LoadBalancerPhase string

const (
	LoadBalancerPhasePending LoadBalancerPhase = "Pending"
	LoadBalancerPhaseReady   LoadBalancerPhase = "Ready"
	LoadBalancerPhaseFailed  LoadBalancerPhase = "Failed"
)

type LoadBalancerStatus struct {
	Message   string                 `json:"message,omitempty"`
	Phase     LoadBalancerPhase      `json:"phase,omitempty"`
	Endpoints []LoadbalancerEndpoint `json:"endpoints,omitempty"`
}

type LoadbalancerEndpoint struct {
	URL      string   `json:"url,omitempty"`
	DNS      string   `json:"dns,omitempty"`
	IPs      []string `json:"ips,omitempty"`
	Protocol string   `json:"protocol,omitempty"`
	Port     int      `json:"port,omitempty"`
}

func NewApplicationLoadbalancerAPI(base base.API, cloudinfo cluster.CloudInfoGetter, svc loadbalancer.Loadbalancer) *ApplicationLoadbalancerAPI {
	return NewCustomApplicationLoadbalancerAPI(base, cloudinfo, svc, base.OnApplication)
}

func NewCustomApplicationLoadbalancerAPI(base base.API, cloudinfo cluster.CloudInfoGetter, svc loadbalancer.Loadbalancer, onapp OnApplicationFunc) *ApplicationLoadbalancerAPI {
	return &ApplicationLoadbalancerAPI{
		API:           base,
		CloudInfo:     cloudinfo,
		LoadBalancer:  svc,
		OnApplication: onapp,
	}
}

type ApplicationLoadbalancerAPI struct {
	base.API
	CloudInfo     cluster.CloudInfoGetter
	OnApplication OnApplicationFunc
	LoadBalancer  loadbalancer.Loadbalancer
}

func (a *ApplicationLoadbalancerAPI) ListLoadbalancers(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error) {
		return a.ListApplicationLoadbalancers(r, appref, appscopestorage)
	})
}

func (a *ApplicationLoadbalancerAPI) ListApplicationLoadbalancers(r *http.Request, appref store.ObjectReference, appscopestorage store.Store) (*store.List[LoadBalancer], error) {
	lblist, err := base.GenericList(r, appscopestorage, &store.List[LoadBalancer]{})
	if err != nil {
		return nil, err
	}
	return lblist, nil
}

func (a *ApplicationLoadbalancerAPI) CreateLoadbalancer(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error) {
		obj := &LoadBalancer{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if err := a.CreateApplicationLoadbalancer(ctx, appscopestorage, obj); err != nil {
			return nil, err
		}
		return obj, nil
	})
}

func (a *ApplicationLoadbalancerAPI) CreateApplicationLoadbalancer(ctx context.Context, appscopestorage store.Store, obj *LoadBalancer) error {
	if err := a.check(ctx, obj); err != nil {
		return err
	}
	if err := appscopestorage.Create(ctx, obj); err != nil {
		return err
	}
	return nil
}

func (a *ApplicationLoadbalancerAPI) check(_ context.Context, obj *LoadBalancer) error {
	if obj.Service == "" && obj.IP == "" {
		return errors.NewBadRequest("service or ip is required")
	}
	if obj.IP != "" {
		if nip := net.ParseIP(obj.IP); nip == nil {
			return errors.NewBadRequest(fmt.Sprintf("ip %s is invalid", obj.IP))
		}
	}
	if obj.Port <= 0 || obj.Port > 65535 {
		return errors.NewBadRequest(fmt.Sprintf("port %d is invalid", obj.Port))
	}
	if obj.Protocol == "" {
		obj.Protocol = "TCP"
	}
	return nil
}

func (a *ApplicationLoadbalancerAPI) GetLoadbalancer(w http.ResponseWriter, r *http.Request) {
	a.onApplicationLoadbalancer(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store, loadbalancername string) (any, error) {
		return base.GenericGet(r, appscopestorage, &LoadBalancer{}, loadbalancername)
	})
}

func (a *ApplicationLoadbalancerAPI) DeleteLoadbalancer(w http.ResponseWriter, r *http.Request) {
	a.onApplicationLoadbalancer(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store, loadbalancername string) (any, error) {
		return a.DeleteApplicationLoadbalancer(ctx, appscopestorage, loadbalancername)
	})
}

func (a *ApplicationLoadbalancerAPI) DeleteApplicationLoadbalancer(ctx context.Context, appscopestorage store.Store, loadbalancername string) (*LoadBalancer, error) {
	lb := &LoadBalancer{
		ObjectMeta: store.ObjectMeta{Name: loadbalancername},
	}
	if err := appscopestorage.Delete(ctx, lb); err != nil {
		return nil, err
	}
	return lb, nil
}

func (a *ApplicationLoadbalancerAPI) UpdateLoadbalancer(w http.ResponseWriter, r *http.Request) {
	a.onApplicationLoadbalancer(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store, loadbalancername string) (any, error) {
		obj := &LoadBalancer{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if objname := obj.GetName(); objname != "" && objname != loadbalancername {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, loadbalancername)
		}
		if err := a.check(ctx, obj); err != nil {
			return nil, err
		}
		obj.SetName(loadbalancername)
		obj.SetResourceVersion(0)
		if err := appscopestorage.Update(r.Context(), obj); err != nil {
			return nil, err
		}
		return obj, nil
	})
}

func (a *ApplicationLoadbalancerAPI) onApplicationLoadbalancer(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store, loadbalancername string) (any, error)) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error) {
		loadbalancername := api.Path(r, "loadbalancer", "")
		if loadbalancername == "" {
			return nil, errors.NewBadRequest("loadbalancer name is required")
		}
		return f(ctx, appref, appscopestorage, loadbalancername)
	})
}

func (a *ApplicationLoadbalancerAPI) Group() api.Group {
	return api.NewGroup("/loadbalancers").
		Route(
			api.GET("").
				To(a.ListLoadbalancers).
				Operation("list loadbalancers").
				Param(api.PageParams...).
				Response(&store.List[LoadBalancer]{}),

			api.POST("").
				To(a.CreateLoadbalancer).
				Operation("create loadbalancer").
				Param(api.BodyParam("loadbalancer", &LoadBalancer{})).
				Response(&LoadBalancer{}),

			api.GET("/{loadbalancer}").
				To(a.GetLoadbalancer).
				Operation("get loadbalancer").
				Response(&LoadBalancer{}),

			api.DELETE("/{loadbalancer}").
				To(a.DeleteLoadbalancer).
				Operation("delete loadbalancer").
				Response(&LoadBalancer{}),

			api.PUT("/{loadbalancer}").
				To(a.UpdateLoadbalancer).
				Operation("update loadbalancer").
				Param(api.BodyParam("loadbalancer", &LoadBalancer{})).
				Response(&LoadBalancer{}),
		)
}
