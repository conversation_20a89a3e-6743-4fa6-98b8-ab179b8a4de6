package tasks

import (
	"context"
	"sync"
	"time"

	"k8s.io/client-go/util/workqueue"
)

type TaskEvent[T comparable] struct {
	Watchers []string
	ID       T
	Success  bool
	Err      error
}

type taskhandle struct {
	cancel  func()
	canceld bool
	lasterr error
	lastRun time.Time
	runs    int

	// record who interested in this task
	watchersmu sync.RWMutex
	watchers   map[string]struct{}
}

func newTaskHandle(watcher string) *taskhandle {
	return &taskhandle{watchers: map[string]struct{}{watcher: {}}}
}

func (t *taskhandle) Watchers() []string {
	t.watchersmu.RLock()
	defer t.watchersmu.RUnlock()
	watchers := make([]string, 0, len(t.watchers))
	for w := range t.watchers {
		watchers = append(watchers, w)
	}
	return watchers
}

func (t *taskhandle) has(caller string) bool {
	t.watchersmu.RLock()
	defer t.watchersmu.RUnlock()
	_, ok := t.watchers[caller]
	return ok
}

func (t *taskhandle) addWatcher(w string) {
	t.watchersmu.Lock()
	defer t.watchersmu.Unlock()
	t.watchers[w] = struct{}{}
}

func (t *taskhandle) removeWatcher(w string) int {
	t.watchersmu.Lock()
	defer t.watchersmu.Unlock()
	delete(t.watchers, w)

	return len(t.watchers)
}

// TaskManager holds the tasks and can control(start/stop) the tasks.
type TaskManager[T comparable] struct {
	concurent   int
	ratelimiter workqueue.TypedRateLimitingInterface[T]
	enableRetry bool

	tasks map[T]*taskhandle
	mu    sync.RWMutex

	fn func(ctx context.Context, id T) error
	ev func(ctx context.Context, e TaskEvent[T])
}

type TaskManagerOptions[T comparable] struct {
	Concurent   int
	Ratelimiter workqueue.TypedRateLimiter[T]
	OnEvent     func(ctx context.Context, e TaskEvent[T])
	WithRetry   bool
}

func NewTaskManager[T comparable](do func(ctx context.Context, id T) error, options TaskManagerOptions[T]) *TaskManager[T] {
	ratelimiter := options.Ratelimiter
	if ratelimiter == nil {
		ratelimiter = workqueue.DefaultTypedControllerRateLimiter[T]()
	}
	concurrent := options.Concurent
	if concurrent <= 0 {
		concurrent = 1
	}
	onEvent := options.OnEvent
	if onEvent == nil {
		onEvent = func(ctx context.Context, e TaskEvent[T]) {}
	}
	return &TaskManager[T]{
		ratelimiter: workqueue.NewTypedRateLimitingQueue[T](ratelimiter),
		tasks:       map[T]*taskhandle{},
		fn:          do,
		concurent:   concurrent,
		enableRetry: options.WithRetry,
		ev:          onEvent,
	}
}

type TaskResult struct {
	// Finished indicates whether the task is finished.
	Finished   bool  `json:"finished,omitempty"`
	RetryCount int   `json:"retry_count,omitempty"`
	Err        error `json:"err,omitempty"`
}

func (m *TaskManager[T]) Get(caller string, id T) (*TaskResult, bool) {
	task := m.get(id)
	if task == nil {
		return nil, false
	}
	if !task.has(caller) {
		return nil, false
	}
	result := &TaskResult{
		Finished:   !task.lastRun.IsZero(),
		Err:        task.lasterr,
		RetryCount: task.runs,
	}
	return result, true
}

// Submit submits a task to the manager.
// it will not submit the task if the task is already running.
// it will retry the task if the task is failed.
func (m *TaskManager[T]) Submit(caller string, id T) {
	exists := m.get(id)
	if exists != nil {
		exists.addWatcher(caller)
		return
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	m.tasks[id] = newTaskHandle(caller)
	m.ratelimiter.Add(id)
}

func (m *TaskManager[T]) get(id T) *taskhandle {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.tasks[id]
}

// CancelAll stops all the tasks that caller is watching.
func (m *TaskManager[T]) CancelAll(caller string) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	for id, task := range m.tasks {
		if task.removeWatcher(caller) == 0 {
			task.canceld = true
			if task.cancel != nil {
				task.cancel()
			}
			delete(m.tasks, id)
		}
	}
}

func (m *TaskManager[T]) Cancel(caller string, id T) {
	m.mu.RLock()
	task, ok := m.tasks[id]
	m.mu.RUnlock()
	if !ok {
		return
	}
	// remove the caller from the watchers
	// if there are still watchers, do not cancel the task
	if task.removeWatcher(caller) == 0 {
		// if there are no one watching the task, cancel the task
		task.canceld = true
		if task.cancel != nil {
			task.cancel()
		}
	}
}

func (m *TaskManager[T]) Run(ctx context.Context) error {
	go func() {
		<-ctx.Done()
		m.mu.Lock()
		defer m.mu.Unlock()
		for _, task := range m.tasks {
			task.canceld = true
			if task.cancel != nil {
				task.cancel()
			}
		}
		m.ratelimiter.ShutDown()
	}()
	wg := sync.WaitGroup{}
	wg.Add(m.concurent)
	for i := 0; i < m.concurent; i++ {
		go func(index int) {
			defer func() {
				wg.Done()
			}()
			for {
				select {
				case <-ctx.Done():
					return
				default:
					id, shutdown := m.ratelimiter.Get()
					if shutdown {
						return
					}
					if err := m.process(ctx, id); err != nil {
						if m.enableRetry {
							m.ratelimiter.AddRateLimited(id)
						}
					} else {
						m.mu.Lock()
						delete(m.tasks, id)
						m.mu.Unlock()
						m.ratelimiter.Forget(id)
					}
					m.ratelimiter.Done(id)
				}
			}
		}(i)
	}
	wg.Wait()
	return nil
}

func (m *TaskManager[T]) process(ctx context.Context, id T) error {
	m.mu.RLock()
	task, ok := m.tasks[id]
	m.mu.RUnlock()
	if !ok {
		return nil
	}
	if task.canceld {
		return nil
	}

	taskctx, taskcancel := context.WithCancel(ctx)
	defer taskcancel()
	task.cancel, task.canceld = taskcancel, false

	err := m.fn(taskctx, id)
	task.runs++
	task.lastRun = time.Now()
	task.lasterr = err

	if err != nil {
		if task.canceld {
			m.ev(ctx, TaskEvent[T]{
				ID: id, Err: err,
				Watchers: task.Watchers(),
			})
			return nil // cancelled
		} else {
			m.ev(ctx, TaskEvent[T]{
				ID: id, Err: err,
				Watchers: task.Watchers(),
			})
			return err // retry
		}
	} else {
		m.ev(ctx, TaskEvent[T]{
			ID: id, Success: ok,
			Watchers: task.Watchers(),
		})
		return nil // done
	}
}
