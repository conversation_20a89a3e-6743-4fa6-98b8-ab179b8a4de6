package application

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/market"
	"xiaoshiai.cn/core/pay"
)

const LabelApplicationUsedFor = market.AnnotaionUsedFor

const (
	LabelApplicationCategory    = base.LabelApplicationCategory
	LabelApplicationSubCategory = base.LabelApplicationSubCategory
	LabelApplicationProduct     = base.LabelApplicationProduct
)

type ApplicationAPI struct {
	base.API
	CloudInfo             cluster.CloudInfoGetter
	Charts                artifact.ChartsProvider
	OnApplication         OnApplicationFunc
	OnListApplicationFunc OnListApplicationFunc
	Pay                   *pay.PaySystem
}

func NewApplicationAPI(base base.API, cloudinfo cluster.CloudInfoGetter, charts artifact.ChartsProvider, pay *pay.PaySystem) *ApplicationAPI {
	return NewCustomApplicationAPI(base,
		base.OnApplication,
		base.OnPosiibleApplicationScopes,
		cloudinfo, charts, pay)
}

func NewCustomApplicationAPI(base base.API, onapp OnApplicationFunc, onlistapp OnListApplicationFunc, cloudinfo cluster.CloudInfoGetter, charts artifact.ChartsProvider, pay *pay.PaySystem) *ApplicationAPI {
	return &ApplicationAPI{
		API:                   base,
		OnApplication:         onapp,
		OnListApplicationFunc: onlistapp,
		CloudInfo:             cloudinfo,
		Charts:                charts,
		Pay:                   pay,
	}
}

func (a *ApplicationAPI) ListApplication(w http.ResponseWriter, r *http.Request) {
	a.OnListApplicationFunc(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		labelrequirements := store.Requirements{
			store.Requirement{
				// do not list application that is not original
				Key: base.LabelApplicationNotOrignial, Operator: store.NotEquals, Values: []any{base.ValueTrue},
			},
		}
		if category := api.Query(r, "category", ""); category != "" {
			labelrequirements = append(labelrequirements, store.RequirementEqual(LabelApplicationCategory, category))
		}
		if subCategory := api.Query(r, "subcategory", ""); subCategory != "" {
			labelrequirements = append(labelrequirements, store.RequirementEqual(LabelApplicationSubCategory, subCategory))
		}
		if product := api.Query(r, "product", ""); product != "" {
			labelrequirements = append(labelrequirements, store.RequirementEqual(LabelApplicationProduct, product))
		}
		var opt []store.ListOption
		if len(labelrequirements) > 0 {
			opt = append(opt, store.WithLabelRequirements(labelrequirements...))
		}
		list := &store.List[Application]{}
		storage := a.Store.Scope(scopes...)
		if api.Query(r, "watch", false) {
			return nil, base.GenericWatch(w, r, storage, list, opt...)
		}
		if err := base.GenericListFromRequest(r, storage, list, opt...); err != nil {
			return nil, err
		}
		list = base.OmitListFunc(list, OmitApplication)
		return list, nil
	})
}

func OmitApplication(app *Application) {
	app.Scopes = nil
	app.OwnerReferences = nil
	app.Finalizers = nil
	app.ResourceVersion = 0
	app.Values.Object = nil
	app.Status.PausedObjects = nil
	app.Status.Values.Object = nil
	app.Status.Conditions = nil
	app.Status.Objects = nil
	app.Status.Note = ""
	app.Status.Cloud = ApplicationCloudStatus{}
	app.Status.Product.Details.Icon = ""
	app.Status.Product.License.Properties = nil
	app.Status.ObservedRevision = 0
}

func (a *ApplicationAPI) CreateApplication(w http.ResponseWriter, r *http.Request) {
	a.OnListApplicationFunc(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		req := &Application{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		if len(scopes) > 0 {
			parentscopes, last := scopes[:len(scopes)-1], scopes[len(scopes)-1]
			if last.Resource == "applications" {
				parent := &Application{}
				if err := a.Store.Scope(parentscopes...).Get(ctx, last.Name, parent); err != nil {
					return nil, err
				}
				// override release name
				req.ReleaseName = parent.Name + "-" + req.Name
				if req.Cluster.ObjectReference.Name == "" {
					req.Cluster = parent.Cluster
				}
				if req.Cluster.Namespace == "" {
					req.Cluster.Namespace = parent.Cluster.Namespace
				}
			}
		}
		if isDryRun := api.Query(r, "dry-run", false); isDryRun {
			return a.DryRunApplication(ctx, scopes, req)
		}
		// disable cluster scope resource by default
		req.AllowClusterSopeResource = false
		return CreateApplication(ctx, a.Store, scopes, req)
	})
}

func (a *ApplicationAPI) GetApplication(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, _ store.Store) (any, error) {
		if api.Query(r, "watch", false) {
			list := &store.List[Application]{}
			storage := a.Store.Scope(appref.Scopes...)
			return nil, base.GenericWatchWithName(w, r, storage, list, appref.Name)
		}
		return GetApplication(ctx, a.Store, appref)
	})
}

func (a *ApplicationAPI) UpdateApplication(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, _ store.Store) (any, error) {
		app := &Application{}
		if err := api.Body(r, app); err != nil {
			return nil, err
		}
		if objname := app.GetName(); objname != "" && objname != appref.Name {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, appref.Name)
		}
		// disable cluster scope resource by default
		app.AllowClusterSopeResource = false
		app.Name = appref.Name
		return UpdateApplication(ctx, a.Store, appref.Scopes, app)
	})
}

func (a *ApplicationAPI) DeleteApplication(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, _ store.Store) (any, error) {
		return DeleteApplication(ctx, a.Store, appref)
	})
}

func (a *ApplicationAPI) PauseApplication(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, _ store.Store) (any, error) {
		return PauseApplication(ctx, a.Store, appref)
	})
}

func (a *ApplicationAPI) ResumeApplication(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, _ store.Store) (any, error) {
		return ResumeApplication(ctx, a.Store, appref)
	})
}

func (a *ApplicationAPI) ApplicationsGroup(varname string) api.Group {
	return api.
		NewGroup("").
		Route(
			api.GET("").
				Doc("List applications").
				To(a.ListApplication).
				Param(
					api.QueryParam("watch", "watch changes and return them as a server-sent events stream").Optional(),
					api.QueryParam("category", "product category").Optional(),
					api.QueryParam("subcategory", "product subcategory").Optional(),
					api.QueryParam("product", "product name").Optional(),
				).
				Param(api.PageParams...).
				Response(store.List[Application]{}),

			api.POST("").
				Doc("Create application").
				To(a.CreateApplication).
				Param(api.QueryParam("dry-run", "dry run mode").Optional().Format("bool")).
				Param(api.BodyParam("application", Application{})).
				ResponseStatus(http.StatusAccepted, DryRunResult{}, "dry run result").
				Response(Application{}),

			api.GET("/{"+varname+"}").
				Doc("Get application").
				To(a.GetApplication).Response(Application{}),

			api.PUT("/{"+varname+"}").
				Doc("Update application").
				To(a.UpdateApplication).
				Param(api.BodyParam("application", Application{})).Response(Application{}),

			api.DELETE("/{"+varname+"}").
				Doc("Delete application").
				To(a.DeleteApplication),

			api.POST("/{"+varname+"}:pause").
				Doc("Pause application").
				To(a.PauseApplication),

			api.POST("/{"+varname+"}:resume").
				Doc("Resume application").
				To(a.ResumeApplication),
		)
}
