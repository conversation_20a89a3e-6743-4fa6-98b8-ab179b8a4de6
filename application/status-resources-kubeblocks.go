package application

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/core/application/kubeblocks"
	"xiaoshiai.cn/core/base"
)

const KubeBlocksGroupSuffix = kubeblocks.Group

const LoadKubeBlocksSubresourceOnlyOnce = true

func (a ResourceLister) listKubeBlocksResources(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	switch parent.GetObjectKind().GroupVersionKind().GroupKind() {
	case kubeblocks.GroupVersionKindKubeBlocksClusters.GroupKind():
		return a.listKubeBlocksClusterResources(ctx, app, namespace, parent, match)
	case kubeblocks.GroupVersionKindKubeBlocksComponents.GroupKind():
		return a.listKubeBlocksComponentResources(ctx, app, namespace, parent, match)
	case kubeblocks.GroupVersionKindKubeBlocksInstanceSets.GroupKind():
		return a.listKubeBlocksInstanceSetResources(ctx, app, namespace, parent, match)
	case kubeblocks.GroupVersionKindKubeBlocksConfigurations.GroupKind():
		return a.listKubeBlocksConfigurationResources(ctx, app, namespace, parent, match)
	case kubeblocks.GroupVersionKindKubeBlocksBackupSchedules.GroupKind():
		return a.listKubeBlocksBackupScheduleResources(ctx, app, namespace, parent, match)
	}
	return nil, nil
}

func (a ResourceLister) listKubeBlocksClusterResources(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	if !match.Empty() && !IsParentResource(parent, match) {
		return nil, nil
	}

	labels := CommonLabelsFromApp(app)
	instancelabels := map[string]string{
		"app.kubernetes.io/managed-by": "kubeblocks",
		"app.kubernetes.io/instance":   GetRelease(app).Name,
	}

	allresources := []runtime.Object{}

	// components
	components := &unstructured.UnstructuredList{}
	components.GetObjectKind().SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksComponents)

	if err := a.Client.List(ctx, components, client.InNamespace(namespace), client.MatchingLabels(labels)); err != nil {
		return nil, err
	}
	for i, comp := range components.Items {
		if !hasOwner(&comp, parent) {
			continue
		}
		if matchGroupKind(&comp, match) {
			allresources = append(allresources, &components.Items[i])
		}
		children, err := a.listKubeBlocksComponentResources(ctx, app, namespace, &comp, match)
		if err != nil {
			return nil, err
		}
		allresources = append(allresources, children...)
	}

	// secrets
	if match.Empty() || match == GroupKindSecret {
		secrets := &corev1.SecretList{}
		if err := a.Client.List(ctx, secrets, client.InNamespace(namespace), client.MatchingLabels(instancelabels)); err != nil {
			return nil, err
		}
		for i := range secrets.Items {
			if !LoadKubeBlocksSubresourceOnlyOnce && !hasOwner(&secrets.Items[i], parent) {
				continue
			}
			if matchGroupKind(&secrets.Items[i], match) {
				allresources = append(allresources, &secrets.Items[i])
			}
		}
	}
	// configmaps
	if match.Empty() || match == GroupKindConfigMap {
		configmaps := &corev1.ConfigMapList{}
		if err := a.Client.List(ctx, configmaps, client.InNamespace(namespace), client.MatchingLabels(instancelabels)); err != nil {
			return nil, err
		}
		for i := range configmaps.Items {
			if !LoadKubeBlocksSubresourceOnlyOnce && !hasOwner(&configmaps.Items[i], parent) {
				continue
			}
			if matchGroupKind(&configmaps.Items[i], match) {
				allresources = append(allresources, &configmaps.Items[i])
			}
		}
	}

	// services
	if match.Empty() || match == GroupKindService {
		services := &corev1.ServiceList{}
		if err := a.Client.List(ctx, services, client.InNamespace(namespace), client.MatchingLabels(instancelabels)); err != nil {
			return nil, err
		}
		for i := range services.Items {
			if !LoadKubeBlocksSubresourceOnlyOnce && !hasOwner(&services.Items[i], parent) {
				continue
			}
			if matchGroupKind(&services.Items[i], match) {
				allresources = append(allresources, &services.Items[i])
			}
		}
	}

	// pods
	if match.Empty() || match == GroupKindPod {
		if LoadKubeBlocksSubresourceOnlyOnce {
			pods, err := listPodsMatch(ctx, a.Client, namespace, nil, client.MatchingLabels(instancelabels), match)
			if err != nil {
				return nil, err
			}
			allresources = append(allresources, pods...)
		} else {
			pods, err := listPodsMatch(ctx, a.Client, namespace, parent, client.MatchingLabels(instancelabels), match)
			if err != nil {
				return nil, err
			}
			allresources = append(allresources, pods...)
		}
	}

	return allresources, nil
}

func (a ResourceLister) listKubeBlocksBackupScheduleResources(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	// TODO
	return nil, nil
}

func (a ResourceLister) listKubeBlocksComponentResources(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	if !match.Empty() && !IsParentResource(parent, match) {
		return nil, nil
	}

	labels := CommonLabelsFromApp(app)
	allresources := []runtime.Object{}

	// instance sets
	instanceSetList := &unstructured.UnstructuredList{}
	instanceSetList.GetObjectKind().SetGroupVersionKind(kubeblocks.GroupVersionKindKubeBlocksInstanceSets)
	if err := a.Client.List(ctx, instanceSetList, client.InNamespace(namespace), client.MatchingLabels(labels)); err != nil {
		return nil, err
	}

	for i, instanceSet := range instanceSetList.Items {
		if !hasOwner(&instanceSet, parent) {
			continue
		}
		if matchGroupKind(&instanceSet, match) {
			allresources = append(allresources, &instanceSetList.Items[i])
		}
		children, err := a.listKubeBlocksInstanceSetResources(ctx, app, namespace, &instanceSet, match)
		if err != nil {
			return nil, err
		}
		allresources = append(allresources, children...)
	}

	return allresources, nil
}

func (a ResourceLister) listKubeBlocksInstanceSetResources(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	matchInstancesetlabels := client.MatchingLabels{
		"app.kubernetes.io/managed-by":       "kubeblocks",
		"workloads.kubeblocks.io/managed-by": "InstanceSet",
		"workloads.kubeblocks.io/instance":   parent.GetName(),
	}

	allresources := []runtime.Object{}

	// services
	if match.Empty() || match == GroupKindService {
		services := &corev1.ServiceList{}
		if err := a.Client.List(ctx, services, client.InNamespace(namespace), matchInstancesetlabels); err != nil {
			return nil, err
		}
		for i := range services.Items {
			if !hasOwner(&services.Items[i], parent) {
				continue
			}
			if matchGroupKind(&services.Items[i], match) {
				allresources = append(allresources, &services.Items[i])
			}
		}
	}
	return allresources, nil
}

func (a ResourceLister) listKubeBlocksConfigurationResources(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	return nil, nil
}

func calcKubeBlocksResourceRequirements(res runtime.Object, limits, requests, storages corev1.ResourceList) {
	gk := res.GetObjectKind().GroupVersionKind().GroupKind()
	switch gk {
	case kubeblocks.GroupVersionKindKubeBlocksClusters.GroupKind():
		isuns, ok := res.(*unstructured.Unstructured)
		if !ok {
			return
		}
		kubeblockscluster := &kubeblocks.Cluster{}
		runtime.DefaultUnstructuredConverter.FromUnstructured(isuns.Object, kubeblockscluster)

		for _, componentSpec := range kubeblockscluster.Spec.ComponentSpecs {
			thislimits, thisrequests := componentSpec.Resources.Limits.DeepCopy(), componentSpec.Resources.Requests.DeepCopy()
			thisstorages := corev1.ResourceList{}
			for _, vt := range componentSpec.VolumeClaimTemplates {
				base.AddResourceList(thislimits, vt.Spec.Resources.Limits)
				base.AddResourceList(thisrequests, vt.Spec.Resources.Requests)
				base.AddResourceList(thisstorages, corev1.ResourceList{
					corev1.ResourceName(ptr.Deref(vt.Spec.StorageClassName, "")): thisrequests[corev1.ResourceStorage],
				})
			}
			base.MultiplyResourceList(thislimits, int64(componentSpec.Replicas))
			base.MultiplyResourceList(thisrequests, int64(componentSpec.Replicas))
			base.MultiplyResourceList(thisstorages, int64(componentSpec.Replicas))

			base.AddResourceList(limits, thislimits)
			base.AddResourceList(requests, thisrequests)
			base.AddResourceList(storages, thisstorages)
		}

		for _, shardingSpec := range kubeblockscluster.Spec.ShardingSpecs {
			thislimits, thisrequests := shardingSpec.Template.Resources.Limits.DeepCopy(), shardingSpec.Template.Resources.Requests.DeepCopy()
			thisstorages := corev1.ResourceList{}
			for _, vt := range shardingSpec.Template.VolumeClaimTemplates {
				base.AddResourceList(thislimits, vt.Spec.Resources.Limits)
				base.AddResourceList(thisrequests, vt.Spec.Resources.Requests)
				base.AddResourceList(thisstorages, corev1.ResourceList{
					corev1.ResourceName(ptr.Deref(vt.Spec.StorageClassName, "")): thisrequests[corev1.ResourceStorage],
				})
			}
			multiplier := int64(shardingSpec.Template.Replicas) * int64(shardingSpec.Shards)
			base.MultiplyResourceList(thislimits, multiplier)
			base.MultiplyResourceList(thisrequests, multiplier)
			base.MultiplyResourceList(thisstorages, multiplier)

			base.AddResourceList(limits, thislimits)
			base.AddResourceList(requests, thisrequests)
			base.AddResourceList(storages, thisstorages)
		}
	}
}
