package application

import (
	"context"
	"fmt"
	"time"

	"helm.sh/helm/v3/pkg/chart"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/helm"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/cluster/resourcepool"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/market"
)

func NewTenantApplicationController(ctx context.Context, storage store.Store, chart artifact.ChartsProvider, cloudinfo cluster.CloudInfoGetter) (*controller.Controller, error) {
	imagesource := &ImagePulledSource{}

	rec := &ApplicationController{
		Client:            storage,
		Charts:            chart,
		Clouds:            cloudinfo,
		CloudImageManager: NewCloudImageManager(ctx, cloudinfo, imagesource),
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("application-controller"),
		controller.WithPatchStatus(), // use patch to update status
	)
	c := controller.
		NewController("applications", br,
			controller.WithConcurrent[controller.ScopedKey](10)).
		Watch(controller.NewStoreSource(storage, &Application{})).
		Watch(NewProductsChangeSource(storage)).
		Watch(imagesource)
	return c, nil
}

var _ controller.Reconciler[*Application] = &ApplicationController{}

type ApplicationController struct {
	Client            store.Store
	Charts            artifact.ChartsProvider
	Clouds            cluster.CloudInfoGetter
	CloudImageManager *CloudImageManager
}

// Sync implements Reconciler.
func (c *ApplicationController) Sync(ctx context.Context, app *Application) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, app, base.ScopeHandler[*Application]{
		OnTenant:                        c.syncTenant,
		OnTenantWorkspace:               c.syncWorkspace,
		OnTenantOrganization:            c.syncOrgApplication,
		OnTenantOrganizationApplication: c.syncSubApplication,
	})
}

func (c *ApplicationController) syncTenant(ctx context.Context, tenant string, app *Application) (controller.Result, error) {
	return c.syncApplication(ctx, app)
}

func (c *ApplicationController) syncWorkspace(ctx context.Context, tenant, workspace string, app *Application) (controller.Result, error) {
	// workspace application currently not enabled
	return controller.Result{}, nil
}

func (c *ApplicationController) syncOrgApplication(ctx context.Context, tenant, org string, obj *Application) (controller.Result, error) {
	return c.syncApplication(ctx, obj)
}

func (c *ApplicationController) syncSubApplication(ctx context.Context, tenant, org, app string, obj *Application) (controller.Result, error) {
	return c.syncApplication(ctx, obj)
}

func (c *ApplicationController) syncApplication(ctx context.Context, app *Application) (controller.Result, error) {
	if err := c.syncApplicationN(ctx, app); err != nil {
		app.Status.Message = controller.CensorError(err)
		app.Status.Phase = ApplicationPhaseFailed
		return controller.Result{}, err
	} else {
		return controller.Result{}, nil
	}
}

func (c *ApplicationController) syncApplicationN(ctx context.Context, app *Application) error {
	log := log.FromContext(ctx)
	if app.Cluster.Namespace == "" {
		return fmt.Errorf("cluster namespace is required")
	}
	// backward compatibility
	if app.Product.Name == "" {
		app.Product = app.DepracatedApplication
		if err := c.Client.Update(ctx, app); err != nil {
			return err
		}
	}
	if app.Status.Product.Name == "" {
		marketapp, selectversion, err := SelectProductVersion(ctx, c.Client, app.Product.Name, app.Product.Version)
		if err != nil {
			return err
		}
		app.Status.Product = ApplicationReferenceExtendFromProduct(marketapp, selectversion.Version)
		if err := c.Client.Scope(app.Scopes...).Status().Update(ctx, app); err != nil {
			return err
		}
	}

	if err := c.syncRecordHistory(ctx, app); err != nil {
		return err
	}

	if err := c.syncCheckUpgrade(ctx, app); err != nil {
		log.Error(err, "field to check upgrade")
		return err
	}

	// lock license
	if _, err := SyncAcquireLicense(ctx, c.Client, app, false); err != nil {
		return err
	}

	cloudinfo, err := c.Clouds.Get(ctx, app.Cluster.ObjectReference)
	if err != nil {
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:    DeploymentConditionClusterConnected,
			Status:  controller.ConditionFalse,
			Message: controller.CensorError(err),
		})
		return err
	} else {
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:    DeploymentConditionClusterConnected,
			Status:  controller.ConditionTrue,
			Message: "Cluster connected",
		})
	}
	// paused stage
	if err := c.syncPause(ctx, cloudinfo, app); err != nil {
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:    DeploymentConditionPaused,
			Status:  controller.ConditionFalse,
			Message: controller.CensorError(err),
		})
		app.Status.Phase = ApplicationPhaseFailed
		return err
	}
	if app.Paused {
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:   DeploymentConditionPaused,
			Status: controller.ConditionTrue,
		})
		app.Status.Phase = ApplicationPhasePaused
		app.Status.Message = ""
		return nil
	} else {
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:   DeploymentConditionPaused,
			Status: controller.ConditionFalse,
		})
	}
	// install stage
	if latestrev := getLatestRevison(app); (latestrev == 0) || (latestrev != app.Status.ObservedRevision) {
		if err := c.syncInstall(ctx, cloudinfo, app); err != nil {
			controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
				Type:    DeploymentConditionInstalled,
				Status:  controller.ConditionFalse,
				Message: controller.CensorError(err),
			})
			app.Status.Phase = ApplicationPhaseFailed
			app.Status.Message = controller.CensorError(err)
			// when install failed, we do not need to retry
			return nil
		}
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:   DeploymentConditionInstalled,
			Status: controller.ConditionTrue,
		})
		// set phase
		app.Status.Phase = ApplicationPhaseInstalled
		app.Status.Message = ""
	} else {
		// already observed, use the last observed result as pahse
		log.Info("already observed", "revision", latestrev)
		// check observed result
		installcond := controller.FindStatusCondition(app.Status.Conditions, DeploymentConditionInstalled)
		if installcond != nil {
			if installcond.Status == controller.ConditionFalse {
				app.Status.Phase = ApplicationPhaseFailed
				app.Status.Message = installcond.Message
			} else {
				app.Status.Phase = ApplicationPhaseInstalled
				app.Status.Message = ""
				// update current revision
				setCurrentRevision(app, latestrev)
			}
		}
	}
	return nil
}

func (c *ApplicationController) syncPause(ctx context.Context, cloudinfo cluster.CloudInfo, app *Application) error {
	cate := cluster.ClusterCategoryFrom(cloudinfo.Type())
	switch cate {
	case cluster.ClusterCategoryContainer:
		if err := c.syncContainerPaused(ctx, cloudinfo, app); err != nil {
			return err
		}
	case cluster.ClusterCategoryVirtualMachine:
		if err := c.syncCloudPaused(ctx, cloudinfo, app); err != nil {
			return err
		}
	}
	return nil
}

func (c *ApplicationController) syncInstall(ctx context.Context, cloudinfo cluster.CloudInfo, app *Application) error {
	installed := controller.IsStatusConditionTrue(app.Status.Conditions, DeploymentConditionInstalled)

	resolvedValues, err := ResolveHelmValues(ctx, app)
	if err != nil {
		return err
	}
	// check should upgrade
	if installed &&
		app.Status.Product.Version == app.Product.Version && // version same
		equalMapValues(app.Status.Values.Object, resolvedValues) && // values same
		app.Status.License.InUse.Name == app.License.Name { // license same
		log.Info("already uptodate", "values", resolvedValues)

		app.Status.ObservedRevision = getLatestRevison(app)
		return nil
	}
	source, err := ParseApplicationVersion(ctx, c.Client, c.Charts, app, resolvedValues, false)
	if err != nil {
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:    DeploymentConditionInstalled,
			Status:  controller.ConditionFalse,
			Message: err.Error(),
		})
		app.Status.Phase = ApplicationPhaseFailed
		return err
	}
	cate := cluster.ClusterCategoryFrom(cloudinfo.Type())
	if source.MarketApplication.Type != cate {
		return fmt.Errorf("application %s is not allowed to deploy on %s cluster", app.Name, cate)
	}
	switch cate {
	case cluster.ClusterCategoryContainer:
		if err := c.syncContainer(ctx, cloudinfo, source, app); err != nil {
			return err
		}
	case cluster.ClusterCategoryVirtualMachine:
		if err := c.syncCloud(ctx, cloudinfo, source, app); err != nil {
			return err
		}
	default:
		return fmt.Errorf("unsupported cluster type %s", cloudinfo.Type())
	}
	// successed
	app.Status.Values = helm.HelmValues{Object: source.Values}
	app.Status.Product.Version = source.Version.Version
	now := time.Now()
	if app.Status.InstallTime.IsZero() {
		app.Status.InstallTime = now
		app.Status.UpdateTime = now
	} else {
		app.Status.UpdateTime = now
	}
	// clear message on success
	app.Status.Message = ""
	return nil
}

type ApplicationRelease struct {
	Name      string `json:"name,omitempty"`
	Namespace string `json:"namespace,omitempty"`
}

func GetRelease(app *Application) ApplicationRelease {
	if app.ReleaseName != "" {
		return ApplicationRelease{Name: app.ReleaseName, Namespace: app.Cluster.Namespace}
	}
	return ApplicationRelease{Name: app.Name, Namespace: app.Cluster.Namespace}
}

// Remove implements Reconciler.
func (c *ApplicationController) Remove(ctx context.Context, app *Application) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, app, base.ScopeHandler[*Application]{
		OnTenant:                        c.removeTenant,
		OnTenantWorkspace:               nil, // workspace application currently not enabled
		OnTenantOrganization:            c.removeOrgApplication,
		OnTenantOrganizationApplication: c.removeSubApplication,
	})
}

func (c *ApplicationController) removeTenant(ctx context.Context, tenant string, app *Application) (controller.Result, error) {
	return c.removeApplication(ctx, app)
}

func (c *ApplicationController) removeOrgApplication(ctx context.Context, tenant, org string, app *Application) (controller.Result, error) {
	return c.removeApplication(ctx, app)
}

func (c *ApplicationController) removeSubApplication(ctx context.Context, tenant, org, app string, obj *Application) (controller.Result, error) {
	return c.removeApplication(ctx, obj)
}

func (c *ApplicationController) removeApplication(ctx context.Context, app *Application) (controller.Result, error) {
	err := c.removeApplicationN(ctx, app)
	if err != nil {
		app.Status.Message = err.Error()
		app.Status.Phase = ApplicationPhaseFailed
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}

func (c *ApplicationController) removeApplicationN(ctx context.Context, app *Application) error {
	log := klog.FromContext(ctx)
	log.Info("remove application", "name", app.Name)

	// release license
	if err := ReleaseLicense(ctx, c.Client, app, false); err != nil {
		return err
	}
	// if application deployed but with error, remove will skip it
	// if !controller.IsStatusConditionTrue(app.Status.Conditions, string(DeploymentConditionInstalled)) {
	// 	return nil
	// }
	cloudinfo, err := c.Clouds.Get(ctx, app.Cluster.ObjectReference)
	if err != nil {
		// direct remove
		if errors.IsNotFound(err) {
			// check twice
			cluster := &cluster.Cluster{}
			if err := c.Client.Scope(app.Cluster.Scopes...).Get(ctx, app.Cluster.Name, cluster); err != nil {
				if errors.IsNotFound(err) {
					app.Status.Phase = ApplicationPhaseNone
					app.Status.Objects = nil
					app.Status.Message = ""
					return nil
				}
				return err
			}
		}
		controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
			Type:    DeploymentConditionClusterConnected,
			Status:  controller.ConditionFalse,
			Message: err.Error(),
		})
		return err
	}

	toremove := GetRelease(app)

	cate := cluster.ClusterCategoryFrom(cloudinfo.Type())
	switch cate {
	case cluster.ClusterCategoryContainer:
		if err := c.removeContainer(ctx, cloudinfo, toremove, app); err != nil {
			return err
		}
	case cluster.ClusterCategoryVirtualMachine:
		if err := c.removeCloud(ctx, cloudinfo, toremove, app); err != nil {
			return err
		}
	default:
		app.Status.Phase = ApplicationPhaseNone
		app.Status.Message = fmt.Sprintf("unsupported cluster type %s", cloudinfo.Type())
		return nil
	}

	controller.SetStatusCondition(&app.Status.Conditions, controller.StatusCondition{
		Type:    DeploymentConditionInstalled,
		Status:  controller.ConditionFalse,
		Message: "Removed",
	})
	app.Status.Phase = ApplicationPhaseNone
	app.Status.Objects = nil
	app.Status.Message = ""
	return nil
}

type ToDeploy struct {
	AppName           string
	Release           ApplicationRelease
	MarketApplication market.Product
	Type              market.ProductType
	AllowClusterScoep bool
	Version           market.ProductVersion
	Values            map[string]any
	Chart             *chart.Chart
	IsUpgrade         bool
	License           *license.License
	// NodeSelector is the node selector for the application to inject to the pod
	NodeSelector map[string]string
	NodeAffinity *corev1.NodeAffinity
}

type ToRemove struct {
	Name      string
	Namespace string
}

func ParseApplicationVersion(ctx context.Context, storage store.Store, charts artifact.ChartsProvider, dep *Application, values map[string]any, dryrun bool) (*ToDeploy, error) {
	// check version
	marketapp, selectedVersion, err := SelectProductVersion(ctx, storage, dep.Product.Name, dep.Product.Version)
	if err != nil {
		return nil, err
	}
	if dep.Product.Version == "" {
		dep.Product.Version = selectedVersion.Version
	}
	// update status
	fillProductInfo(dep, marketapp, selectedVersion.Version)
	dep.Status.Product = ApplicationReferenceExtendFromProduct(marketapp, selectedVersion.Version)

	if marketapp.License.Enabled {
		if dep.License.Name == "" {
			return nil, fmt.Errorf("application %s requires a license", dep.Name)
		}
		dep.Status.License.Required = true
	}
	var maybeNilLicense *license.License
	if licensename := dep.License.Name; licensename != "" {
		license := &license.License{}
		if err := storage.Scope(dep.Scopes...).Get(ctx, licensename, license); err != nil {
			return nil, err
		}
		maybeNilLicense = license
	}
	chart, err := charts.DownloadChart(ctx, selectedVersion.Chart, selectedVersion.Version)
	if err != nil {
		return nil, err
	}
	deploy := &ToDeploy{
		AppName:           dep.Name,
		Release:           GetRelease(dep),
		MarketApplication: *marketapp,
		Version:           *selectedVersion,
		Type:              marketapp.Type,
		IsUpgrade:         dep.Status.Phase == ApplicationPhaseInstalled,
		License:           maybeNilLicense,
		AllowClusterScoep: dep.AllowClusterSopeResource,
		Chart:             chart,
		Values:            values,
	}
	// resource pool
	// no resource pool set, select nodes not private node
	if poolname := dep.Cluster.ResourcePool; poolname != "" {
		if tenant, _ := base.TenantOrganizationFromScopes(dep.Scopes...); tenant != "" {
			// workloads can only be scheduled on resource pool labeled nodes
			deploy.NodeAffinity = &corev1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
					NodeSelectorTerms: []corev1.NodeSelectorTerm{
						{
							MatchExpressions: []corev1.NodeSelectorRequirement{
								{
									Key:      resourcepool.GetTenantResourcePoolLabelKey(tenant, poolname),
									Operator: corev1.NodeSelectorOpIn,
									Values:   []string{base.ValueTrue},
								},
							},
						},
					},
				},
			}
		} else {
			return nil, fmt.Errorf("resource pool %s is not allowed to deploy on %s", poolname, dep.Name)
		}
	} else {
		// do not select private node
		deploy.NodeAffinity = &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      base.LabelPrivateNode,
								Operator: corev1.NodeSelectorOpDoesNotExist,
							},
						},
					},
				},
			},
		}
	}
	return deploy, nil
}

func SyncAcquireLicense(ctx context.Context, storage store.Store, dep *Application, dryRun bool) (*license.License, error) {
	// check license required
	newlicense, err := AcquireLicense(ctx, storage, dep, dryRun)
	if err != nil {
		return nil, err
	}
	// check license changed
	if oldlicense := dep.Status.License.InUse; newlicense == nil || !oldlicense.Equals(store.ObjectReferenceFrom(newlicense)) {
		// release old license
		if err := ReleaseLicense(ctx, storage, dep, dryRun); err != nil {
			return nil, err
		}
	}
	// update to new license
	if newlicense != nil {
		dep.Status.License.InUse = store.ObjectReferenceFrom(newlicense)
	}
	return newlicense, nil
}

func SelectProductVersion(ctx context.Context, storage store.Store, name, version string) (*market.Product, *market.ProductVersion, error) {
	if name == "" {
		return nil, nil, fmt.Errorf("product name is required")
	}
	product := &market.Product{}
	if err := storage.Get(ctx, name, product); err != nil {
		return nil, nil, err
	}
	var selected *market.ProductVersion
	if version == "" {
		if len(product.Versions) == 0 {
			return nil, nil, fmt.Errorf("product %s has no versions", name)
		}
		selected = &product.Versions[0]
	} else {
		for _, v := range product.Versions {
			if v.Version == version {
				selected = &v
				break
			}
		}
	}
	if selected == nil {
		return nil, nil, errors.NewNotFound("product version", version)
	}
	if selected.Chart == "" {
		selected.Chart = product.Name
	}
	return product, selected, nil
}

func ResolveHelmValues(_ context.Context, app *Application) (map[string]any, error) {
	base := helm.HelmMergeMaps(map[string]any{}, app.Values.Object)
	return base, nil
}
