package application

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
)

func NewApplicationStatusAPI(base base.API, mongoStore store.Store, cloudinfo cluster.CloudInfoGetter, onapplication OnApplicationFunc) *ApplicationStatusAPI {
	return &ApplicationStatusAPI{
		API:           base,
		MongoStore:    mongoStore,
		CloudInfo:     cloudinfo,
		OnApplication: onapplication,
	}
}

func NewDefaultApplicationStatusAPI(base base.API, mongoStore store.Store, cloudinfo cluster.CloudInfoGetter) *ApplicationStatusAPI {
	return &ApplicationStatusAPI{
		API:           base,
		MongoStore:    mongoStore,
		CloudInfo:     cloudinfo,
		OnApplication: base.OnApplication,
	}
}

type ApplicationStatusAPI struct {
	base.API
	MongoStore    store.Store
	CloudInfo     cluster.CloudInfoGetter
	OnApplication OnApplicationFunc
}

type OnApplicationFunc func(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error))

type OnListApplicationFunc func(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, scopes []store.Scope) (any, error))

func (a *ApplicationStatusAPI) Group() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			a.resourcesGroup(),
			a.historiesGroup(),
		)
}
