package kubeblocks

import (
	corev1 "k8s.io/api/core/v1"
)

type ClusterService struct {
	Service           `json:",inline"`
	ShardingSelector  string `json:"shardingSelector,omitempty"`
	ComponentSelector string `json:"componentSelector,omitempty"`
}

type Service struct {
	Name         string             `json:"name"`
	ServiceName  string             `json:"serviceName,omitempty"`
	Annotations  map[string]string  `json:"annotations,omitempty"`
	Spec         corev1.ServiceSpec `json:"spec,omitempty"`
	RoleSelector string             `json:"roleSelector,omitempty"`
}

type TerminationPolicyType string

const (
	DoNotTerminate TerminationPolicyType = "DoNotTerminate"
	Halt           TerminationPolicyType = "Halt"
	Delete         TerminationPolicyType = "Delete"
	WipeOut        TerminationPolicyType = "WipeOut"
)

type MemberStatus struct {
	PodName             string       `json:"podName"`
	ReplicaRole         *ReplicaRole `json:"role,omitempty"`
	Ready               bool         `json:"ready,omitempty"`
	ReadyWithoutPrimary bool         `json:"readyWithoutPrimary"`
}

type ReplicaRole struct {
	Name       string `json:"name"`
	AccessMode string `json:"accessMode"`
	CanVote    bool   `json:"canVote"`
	IsLeader   bool   `json:"isLeader"`
}

type PasswordConfig struct {
	Length     int32  `json:"length,omitempty"`
	NumDigits  int32  `json:"numDigits,omitempty"`
	NumSymbols int32  `json:"numSymbols,omitempty"`
	LetterCase string `json:"letterCase,omitempty"`
	Seed       string `json:"seed,omitempty"`
}

type ProvisionSecretRef struct {
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}
