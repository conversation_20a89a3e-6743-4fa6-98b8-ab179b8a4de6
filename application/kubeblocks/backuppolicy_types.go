package kubeblocks

import metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

type BackupPolicy struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              BackupPolicySpec   `json:"spec,omitempty"`
	Status            BackupPolicyStatus `json:"status,omitempty"`
}

type BackupPolicySpec struct {
	BackupRepoName   *string           `json:"backupRepoName,omitempty"`
	PathPrefix       string            `json:"pathPrefix,omitempty"`
	BackoffLimit     *int32            `json:"backoffLimit,omitempty"`
	Target           *BackupTarget     `json:"target,omitempty"`
	Targets          []BackupTarget    `json:"targets,omitempty"`
	BackupMethods    []BackupMethod    `json:"backupMethods"`
	UseKopia         bool              `json:"useKopia"`
	EncryptionConfig *EncryptionConfig `json:"encryptionConfig,omitempty"`
}

type BackupPolicyStatus struct {
	Phase              string `json:"phase,omitempty"`
	Message            string `json:"message,omitempty"`
	ObservedGeneration int64  `json:"observedGeneration,omitempty"`
}
