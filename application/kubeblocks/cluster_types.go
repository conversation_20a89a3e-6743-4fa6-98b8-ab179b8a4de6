package kubeblocks

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

type ClusterSpec struct {
	ClusterDefRef     string                 `json:"clusterDefinitionRef,omitempty"`
	Topology          string                 `json:"topology,omitempty"`
	TerminationPolicy TerminationPolicyType  `json:"terminationPolicy"`
	ShardingSpecs     []ShardingSpec         `json:"shardingSpecs,omitempty"`
	ComponentSpecs    []ClusterComponentSpec `json:"componentSpecs,omitempty"`
	Services          []ClusterService       `json:"services,omitempty"`
	SchedulingPolicy  *SchedulingPolicy      `json:"schedulingPolicy,omitempty"`
	RuntimeClassName  *string                `json:"runtimeClassName,omitempty"`
	Backup            *ClusterBackup         `json:"backup,omitempty"`
}

type ClusterBackup struct {
	Enabled                   *bool  `json:"enabled,omitempty"`
	RetentionPeriod           string `json:"retentionPeriod,omitempty"`
	Method                    string `json:"method"`
	CronExpression            string `json:"cronExpression,omitempty"`
	StartingDeadlineMinutes   *int64 `json:"startingDeadlineMinutes,omitempty"`
	RepoName                  string `json:"repoName,omitempty"`
	PITREnabled               *bool  `json:"pitrEnabled,omitempty"`
	ContinuousMethod          string `json:"continuousMethod,omitempty"`
	IncrementalBackupEnabled  *bool  `json:"incrementalBackupEnabled,omitempty"`
	IncrementalCronExpression string `json:"incrementalCronExpression,omitempty"`
}

type ResourceMeta struct {
	Name         string   `json:"name"`
	MountPoint   string   `json:"mountPoint"`
	SubPath      string   `json:"subPath,omitempty"`
	AsVolumeFrom []string `json:"asVolumeFrom,omitempty"`
}

type SecretRef struct {
	ResourceMeta `json:",inline"`
	Secret       corev1.SecretVolumeSource `json:"secret"`
}

type ConfigMapRef struct {
	ResourceMeta `json:",inline"`
	ConfigMap    corev1.ConfigMapVolumeSource `json:"configMap"`
}

type UserResourceRefs struct {
	SecretRefs    []SecretRef    `json:"secretRefs,omitempty"`
	ConfigMapRefs []ConfigMapRef `json:"configMapRefs,omitempty"`
}

type InstanceUpdateStrategy struct {
	Partition      *int32              `json:"partition,omitempty"`
	MaxUnavailable *intstr.IntOrString `json:"maxUnavailable,omitempty"`
}

type InstanceTemplate struct {
	Name                 string                                `json:"name"`
	Replicas             *int32                                `json:"replicas,omitempty"`
	Annotations          map[string]string                     `json:"annotations,omitempty"`
	Labels               map[string]string                     `json:"labels,omitempty"`
	Image                *string                               `json:"image,omitempty"`
	SchedulingPolicy     *SchedulingPolicy                     `json:"schedulingPolicy,omitempty"`
	Resources            *corev1.ResourceRequirements          `json:"resources,omitempty"`
	Env                  []corev1.EnvVar                       `json:"env,omitempty"`
	Volumes              []corev1.Volume                       `json:"volumes,omitempty"`
	VolumeMounts         []corev1.VolumeMount                  `json:"volumeMounts,omitempty"`
	VolumeClaimTemplates []ClusterComponentVolumeClaimTemplate `json:"volumeClaimTemplates,omitempty"`
}

type ClusterStatus struct {
	ObservedGeneration   int64                             `json:"observedGeneration,omitempty"`
	Phase                string                            `json:"phase,omitempty"`
	Message              string                            `json:"message,omitempty"`
	Components           map[string]ClusterComponentStatus `json:"components,omitempty"`
	ClusterDefGeneration int64                             `json:"clusterDefGeneration,omitempty"`
	Conditions           []metav1.Condition                `json:"conditions,omitempty"`
}

type ShardingSpec struct {
	Name     string               `json:"name"`
	Template ClusterComponentSpec `json:"template"`
	Shards   int32                `json:"shards,omitempty"`
}

type ClusterComponentSpec struct {
	Name                             string                                `json:"name"`
	ComponentDef                     string                                `json:"componentDef,omitempty"`
	ServiceVersion                   string                                `json:"serviceVersion,omitempty"`
	ServiceRefs                      []ServiceRef                          `json:"serviceRefs,omitempty"`
	EnabledLogs                      []string                              `json:"enabledLogs,omitempty"`
	Labels                           map[string]string                     `json:"labels,omitempty"`
	Annotations                      map[string]string                     `json:"annotations,omitempty"`
	Env                              []corev1.EnvVar                       `json:"env,omitempty"`
	Replicas                         int32                                 `json:"replicas"`
	SchedulingPolicy                 *SchedulingPolicy                     `json:"schedulingPolicy,omitempty"`
	Resources                        corev1.ResourceRequirements           `json:"resources,omitempty"`
	VolumeClaimTemplates             []ClusterComponentVolumeClaimTemplate `json:"volumeClaimTemplates,omitempty"`
	Volumes                          []corev1.Volume                       `json:"volumes,omitempty"`
	Services                         []ClusterComponentService             `json:"services,omitempty"`
	SystemAccounts                   []ComponentSystemAccount              `json:"systemAccounts,omitempty"`
	TLS                              bool                                  `json:"tls,omitempty"`
	Issuer                           *Issuer                               `json:"issuer,omitempty"`
	ServiceAccountName               string                                `json:"serviceAccountName,omitempty"`
	InstanceUpdateStrategy           *InstanceUpdateStrategy               `json:"instanceUpdateStrategy,omitempty"`
	ParallelPodManagementConcurrency *intstr.IntOrString                   `json:"parallelPodManagementConcurrency,omitempty"`
	PodUpdatePolicy                  *string                               `json:"podUpdatePolicy,omitempty"`
	UserResourceRefs                 *UserResourceRefs                     `json:"userResourceRefs,omitempty"`
	Instances                        []InstanceTemplate                    `json:"instances,omitempty"`
	OfflineInstances                 []string                              `json:"offlineInstances,omitempty"`
	DisableExporter                  *bool                                 `json:"disableExporter,omitempty"`
	Stop                             *bool                                 `json:"stop,omitempty"`
}

type (
	ComponentMessageMap    map[string]string
	ClusterComponentStatus struct {
		Phase         string              `json:"phase,omitempty"`
		Message       ComponentMessageMap `json:"message,omitempty"`
		PodsReady     *bool               `json:"podsReady,omitempty"`
		PodsReadyTime *metav1.Time        `json:"podsReadyTime,omitempty"`
		MembersStatus []MemberStatus      `json:"membersStatus,omitempty"`
	}
)

type ClusterComponentVolumeClaimTemplate struct {
	Name        string                    `json:"name"`
	Labels      map[string]string         `json:"labels,omitempty"`
	Annotations map[string]string         `json:"annotations,omitempty"`
	Spec        PersistentVolumeClaimSpec `json:"spec,omitempty"`
}

type PersistentVolumeClaimSpec struct {
	AccessModes      []corev1.PersistentVolumeAccessMode `json:"accessModes,omitempty"`
	Resources        corev1.ResourceRequirements         `json:"resources,omitempty"`
	StorageClassName *string                             `json:"storageClassName,omitempty"`
	VolumeMode       *corev1.PersistentVolumeMode        `json:"volumeMode,omitempty"`
}

type Affinity struct {
	PodAntiAffinity string            `json:"podAntiAffinity,omitempty"`
	TopologyKeys    []string          `json:"topologyKeys,omitempty"`
	NodeLabels      map[string]string `json:"nodeLabels,omitempty"`
	Tenancy         string            `json:"tenancy,omitempty"`
}

type SchedulingPolicy struct {
	SchedulerName             string                            `json:"schedulerName,omitempty"`
	NodeSelector              map[string]string                 `json:"nodeSelector,omitempty"`
	NodeName                  string                            `json:"nodeName,omitempty"`
	Affinity                  *corev1.Affinity                  `json:"affinity,omitempty"`
	Tolerations               []corev1.Toleration               `json:"tolerations,omitempty"`
	TopologySpreadConstraints []corev1.TopologySpreadConstraint `json:"topologySpreadConstraints,omitempty"`
}

type TLSConfig struct {
	Enable bool    `json:"enable,omitempty"`
	Issuer *Issuer `json:"issuer,omitempty"`
}

type Issuer struct {
	Name      string        `json:"name"`
	SecretRef *TLSSecretRef `json:"secretRef,omitempty"`
}

type TLSSecretRef struct {
	Name string `json:"name"`
	CA   string `json:"ca"`
	Cert string `json:"cert"`
	Key  string `json:"key"`
}

type ClusterComponentService struct {
	Name        string             `json:"name"`
	ServiceType corev1.ServiceType `json:"serviceType,omitempty"`
	Annotations map[string]string  `json:"annotations,omitempty"`
	PodService  *bool              `json:"podService,omitempty"`
}

type ComponentSystemAccount struct {
	Name           string              `json:"name"`
	PasswordConfig *PasswordConfig     `json:"passwordConfig,omitempty"`
	SecretRef      *ProvisionSecretRef `json:"secretRef,omitempty"`
}

type ServiceRef struct {
	Name                   string                     `json:"name"`
	Namespace              string                     `json:"namespace,omitempty"`
	ClusterServiceSelector *ServiceRefClusterSelector `json:"clusterServiceSelector,omitempty"`
	ServiceDescriptor      string                     `json:"serviceDescriptor,omitempty"`
}

type ServiceRefClusterSelector struct {
	Cluster    string                        `json:"cluster"`
	Service    *ServiceRefServiceSelector    `json:"service,omitempty"`
	Credential *ServiceRefCredentialSelector `json:"credential,omitempty"`
}

type ServiceRefServiceSelector struct {
	Component string `json:"component,omitempty"`
	Service   string `json:"service"`
	Port      string `json:"port,omitempty"`
}

type ServiceRefCredentialSelector struct {
	Component string `json:"component"`
	Name      string `json:"name"`
}

type Cluster struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              ClusterSpec   `json:"spec,omitempty"`
	Status            ClusterStatus `json:"status,omitempty"`
}
