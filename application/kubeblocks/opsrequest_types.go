package kubeblocks

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

type OpsRequest struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              OpsRequestSpec   `json:"spec,omitempty"`
	Status            OpsRequestStatus `json:"status,omitempty"`
}

type OpsRequestSpec struct {
	ClusterName                           string  `json:"clusterName,omitempty"`
	Cancel                                bool    `json:"cancel,omitempty"`
	Force                                 bool    `json:"force,omitempty"`
	EnqueueOnForce                        bool    `json:"enqueueOnForce,omitempty"`
	Type                                  OpsType `json:"type"`
	TTLSecondsAfterSucceed                int32   `json:"ttlSecondsAfterSucceed,omitempty"`
	TTLSecondsAfterUnsuccessfulCompletion int32   `json:"ttlSecondsAfterUnsuccessfulCompletion,omitempty"`
	PreConditionDeadlineSeconds           *int32  `json:"preConditionDeadlineSeconds,omitempty"`
	TimeoutSeconds                        *int32  `json:"timeoutSeconds,omitempty"`
	SpecificOpsRequest                    `json:",inline"`
}

type SpecificOpsRequest struct {
	Upgrade               *Upgrade            `json:"upgrade,omitempty"`
	HorizontalScalingList []HorizontalScaling `json:"horizontalScaling,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	VolumeExpansionList   []VolumeExpansion   `json:"volumeExpansion,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	StartList             []ComponentOps      `json:"start,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	StopList              []ComponentOps      `json:"stop,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	RestartList           []ComponentOps      `json:"restart,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	SwitchoverList        []Switchover        `json:"switchover,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	VerticalScalingList   []VerticalScaling   `json:"verticalScaling,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	Reconfigures          []Reconfigure       `json:"reconfigures,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	ExposeList            []Expose            `json:"expose,omitempty"`
	Backup                *Backup             `json:"backup,omitempty"`
	Restore               *Restore            `json:"restore,omitempty"`
	RebuildFrom           []RebuildInstance   `json:"rebuildFrom,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
	CustomOps             *CustomOps          `json:"custom,omitempty"`
}

type ComponentOps struct {
	ComponentName string `json:"componentName"`
}

type RebuildInstance struct {
	ComponentOps           `json:",inline"`
	Instances              []Instance      `json:"instances"`
	InPlace                bool            `json:"inPlace,omitempty"`
	BackupName             string          `json:"backupName,omitempty"`
	SourceBackupTargetName string          `json:"sourceBackupTargetName,omitempty"`
	RestoreEnv             []corev1.EnvVar `json:"restoreEnv,omitempty" patchStrategy:"merge" patchMergeKey:"name"`
}

type Instance struct {
	Name           string `json:"name"`
	TargetNodeName string `json:"targetNodeName,omitempty"`
}

type Switchover struct {
	ComponentName       string `json:"componentName,omitempty"`
	ComponentObjectName string `json:"componentObjectName,omitempty"`
	InstanceName        string `json:"instanceName"`
}

type Upgrade struct {
	Components []UpgradeComponent `json:"components,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
}

type UpgradeComponent struct {
	ComponentOps            `json:",inline"`
	ComponentDefinitionName *string `json:"componentDefinitionName,omitempty"`
	ServiceVersion          *string `json:"serviceVersion,omitempty"`
}

type VerticalScaling struct {
	ComponentOps                `json:",inline"`
	corev1.ResourceRequirements `json:",inline"`
	Instances                   []InstanceResourceTemplate `json:"instances,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
}

type InstanceResourceTemplate struct {
	Name                        string `json:"name"`
	corev1.ResourceRequirements `json:",inline"`
}

type InstanceVolumeClaimTemplate struct {
	Name                 string                          `json:"name"`
	VolumeClaimTemplates []OpsRequestVolumeClaimTemplate `json:"volumeClaimTemplates" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
}

type VolumeExpansion struct {
	ComponentOps         `json:",inline"`
	VolumeClaimTemplates []OpsRequestVolumeClaimTemplate `json:"volumeClaimTemplates" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
	Instances            []InstanceVolumeClaimTemplate   `json:"instances,omitempty"  patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
}

type OpsRequestVolumeClaimTemplate struct {
	Storage resource.Quantity `json:"storage"`
	Name    string            `json:"name"`
}

type HorizontalScaling struct {
	ComponentOps `json:",inline"`
	Shards       *int32    `json:"shards,omitempty"`
	ScaleOut     *ScaleOut `json:"scaleOut,omitempty"`
	ScaleIn      *ScaleIn  `json:"scaleIn,omitempty"`
}

type ScaleOut struct {
	ReplicaChanger           `json:",inline"`
	NewInstances             []InstanceTemplate `json:"newInstances,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
	OfflineInstancesToOnline []string           `json:"offlineInstancesToOnline,omitempty"`
}

type ScaleIn struct {
	ReplicaChanger           `json:",inline"`
	OnlineInstancesToOffline []string `json:"onlineInstancesToOffline,omitempty"`
}

type ReplicaChanger struct {
	ReplicaChanges *int32                     `json:"replicaChanges,omitempty"`
	Instances      []InstanceReplicasTemplate `json:"instances,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
}

type InstanceReplicasTemplate struct {
	Name           string `json:"name"`
	ReplicaChanges int32  `json:"replicaChanges"`
}

type Reconfigure struct {
	ComponentOps   `json:",inline"`
	Configurations []ConfigurationItem `json:"configurations" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
}

type ConfigurationItem struct {
	Name   string            `json:"name"`
	Policy *UpgradePolicy    `json:"policy,omitempty"`
	Keys   []ParameterConfig `json:"keys" patchStrategy:"merge,retainKeys" patchMergeKey:"key"`
}

type CustomOps struct {
	OpsDefinitionName       string               `json:"opsDefinitionName"`
	ServiceAccountName      *string              `json:"serviceAccountName,omitempty"`
	MaxConcurrentComponents intstr.IntOrString   `json:"maxConcurrentComponents,omitempty"`
	CustomOpsComponents     []CustomOpsComponent `json:"components"  patchStrategy:"merge,retainKeys" patchMergeKey:"componentName"`
}

type CustomOpsComponent struct {
	ComponentOps `json:",inline"`
	Parameters   []Parameter `json:"parameters,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`
}

type Parameter struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type ParameterPair struct {
	Key   string  `json:"key"`
	Value *string `json:"value"`
}

type ParameterConfig struct {
	Key         string          `json:"key"`
	Parameters  []ParameterPair `json:"parameters,omitempty"`
	FileContent string          `json:"fileContent,omitempty"`
}

type ExposeSwitch string

const (
	EnableExposeSwitch  ExposeSwitch = "Enable"
	DisableExposeSwitch ExposeSwitch = "Disable"
)

type Expose struct {
	ComponentName string       `json:"componentName,omitempty"`
	Switch        ExposeSwitch `json:"switch"`
	Services      []OpsService `json:"services"`
}

type OpsService struct {
	Name           string                 `json:"name"`
	Annotations    map[string]string      `json:"annotations,omitempty"`
	Ports          []corev1.ServicePort   `json:"ports,omitempty" patchStrategy:"merge" patchMergeKey:"port"`
	RoleSelector   string                 `json:"roleSelector,omitempty"`
	PodSelector    map[string]string      `json:"podSelector,omitempty"`
	ServiceType    corev1.ServiceType     `json:"serviceType,omitempty"`
	IPFamilies     []corev1.IPFamily      `json:"ipFamilies,omitempty"`
	IPFamilyPolicy *corev1.IPFamilyPolicy `json:"ipFamilyPolicy,omitempty"`
}

type RefNamespaceName struct {
	Name      string `json:"name,omitempty"`
	Namespace string `json:"namespace,omitempty"`
}

type BackupRefSpec struct {
	Ref RefNamespaceName `json:"ref,omitempty"`
}

type PointInTimeRefSpec struct {
	Time *metav1.Time     `json:"time,omitempty"`
	Ref  RefNamespaceName `json:"ref,omitempty"`
}

type Restore struct {
	BackupName                        string          `json:"backupName"`
	BackupNamespace                   string          `json:"backupNamespace,omitempty"`
	RestorePointInTime                string          `json:"restorePointInTime,omitempty"`
	Env                               []corev1.EnvVar `json:"env,omitempty" patchStrategy:"merge" patchMergeKey:"name"`
	VolumeRestorePolicy               string          `json:"volumeRestorePolicy,omitempty"`
	DeferPostReadyUntilClusterRunning bool            `json:"deferPostReadyUntilClusterRunning,omitempty"`
}

type ScriptSecret struct {
	Name        string `json:"name"`
	UsernameKey string `json:"usernameKey,omitempty"`
	PasswordKey string `json:"passwordKey,omitempty"`
}

type ScriptFrom struct {
	ConfigMapRef []corev1.ConfigMapKeySelector `json:"configMapRef,omitempty"`
	SecretRef    []corev1.SecretKeySelector    `json:"secretRef,omitempty"`
}

type OpsRequestStatus struct {
	ClusterGeneration              int64                                `json:"clusterGeneration,omitempty"`
	Phase                          OpsPhase                             `json:"phase,omitempty"`
	Progress                       string                               `json:"progress"`
	LastConfiguration              LastConfiguration                    `json:"lastConfiguration,omitempty"`
	Components                     map[string]OpsRequestComponentStatus `json:"components,omitempty"`
	Extras                         []map[string]string                  `json:"extras,omitempty"`
	StartTimestamp                 metav1.Time                          `json:"startTimestamp,omitempty"`
	CompletionTimestamp            metav1.Time                          `json:"completionTimestamp,omitempty"`
	CancelTimestamp                metav1.Time                          `json:"cancelTimestamp,omitempty"`
	ReconfiguringStatusAsComponent map[string]*ReconfiguringStatus      `json:"reconfiguringStatusAsComponent,omitempty"`
	Conditions                     []metav1.Condition                   `json:"conditions,omitempty"`
}

type ProgressStatusDetail struct {
	Group       string         `json:"group,omitempty"`
	ObjectKey   string         `json:"objectKey,omitempty"`
	ActionName  string         `json:"actionName,omitempty"`
	ActionTasks []ActionTask   `json:"actionTasks,omitempty"`
	Status      ProgressStatus `json:"status"`
	Message     string         `json:"message,omitempty"`
	StartTime   metav1.Time    `json:"startTime,omitempty"`
	EndTime     metav1.Time    `json:"endTime,omitempty"`
}

type ActionTask struct {
	ObjectKey     string           `json:"objectKey"`
	Namespace     string           `json:"namespace"`
	Status        ActionTaskStatus `json:"status"`
	TargetPodName string           `json:"targetPodName,omitempty"`
	Retries       int32            `json:"retries,omitempty"`
}

type LastComponentConfiguration struct {
	Replicas                    *int32 `json:"replicas,omitempty"`
	Shards                      *int32 `json:"shards,omitempty"`
	corev1.ResourceRequirements `json:",inline,omitempty"`
	VolumeClaimTemplates        []OpsRequestVolumeClaimTemplate `json:"volumeClaimTemplates,omitempty"`
	Services                    []ClusterComponentService       `json:"services,omitempty"`
	TargetResources             map[string][]string             `json:"targetResources,omitempty"`
	Instances                   []InstanceTemplate              `json:"instances,omitempty"`
	OfflineInstances            []string                        `json:"offlineInstances,omitempty"`
	ServiceVersion              string                          `json:"serviceVersion,omitempty"`
	ComponentDefinitionName     string                          `json:"componentDefinitionName,omitempty"`
}

type LastConfiguration struct {
	Components map[string]LastComponentConfiguration `json:"components,omitempty"`
}

type OpsRequestComponentStatus struct {
	Phase           ClusterComponentPhase  `json:"phase,omitempty"`
	LastFailedTime  metav1.Time            `json:"lastFailedTime,omitempty"`
	PreCheckResult  *PreCheckResult        `json:"preCheck,omitempty"`
	ProgressDetails []ProgressStatusDetail `json:"progressDetails,omitempty"`
	Reason          string                 `json:"reason,omitempty"`
	Message         string                 `json:"message,omitempty"`
}

type OverrideBy struct {
	OpsName                    string `json:"opsName"`
	LastComponentConfiguration `json:",inline"`
}

type PreCheckResult struct {
	Pass    bool   `json:"pass"`
	Message string `json:"message,omitempty"`
}

type ReconfiguringStatus struct {
	Conditions          []metav1.Condition        `json:"conditions,omitempty"`
	ConfigurationStatus []ConfigurationItemStatus `json:"configurationStatus"`
}

type ConfigurationItemStatus struct {
	Name                     string            `json:"name"`
	UpdatePolicy             UpgradePolicy     `json:"updatePolicy,omitempty"`
	Status                   string            `json:"status,omitempty"`
	Message                  string            `json:"message,omitempty"`
	SucceedCount             int32             `json:"succeedCount"`
	ExpectedCount            int32             `json:"expectedCount"`
	LastAppliedStatus        string            `json:"lastStatus,omitempty"`
	LastAppliedConfiguration map[string]string `json:"lastAppliedConfiguration,omitempty"`
	UpdatedParameters        UpdatedParameters `json:"updatedParameters"`
}

type UpdatedParameters struct {
	AddedKeys   map[string]string `json:"addedKeys,omitempty"`
	DeletedKeys map[string]string `json:"deletedKeys,omitempty"`
	UpdatedKeys map[string]string `json:"updatedKeys,omitempty"`
}

type OpsType string

const (
	VerticalScalingType   OpsType = "VerticalScaling"
	HorizontalScalingType OpsType = "HorizontalScaling"
	VolumeExpansionType   OpsType = "VolumeExpansion"
	UpgradeType           OpsType = "Upgrade"
	ReconfiguringType     OpsType = "Reconfiguring"
	SwitchoverType        OpsType = "Switchover"
	RestartType           OpsType = "Restart"
	StopType              OpsType = "Stop"
	StartType             OpsType = "Start"
	ExposeType            OpsType = "Expose"
	DataScriptType        OpsType = "DataScript"
	BackupType            OpsType = "Backup"
	RestoreType           OpsType = "Restore"
	RebuildInstanceType   OpsType = "RebuildInstance"
	CustomType            OpsType = "Custom"
)

type UpgradePolicy string

const (
	NonePolicy                    UpgradePolicy = "none"
	NormalPolicy                  UpgradePolicy = "simple"
	RestartPolicy                 UpgradePolicy = "parallel"
	RollingPolicy                 UpgradePolicy = "rolling"
	AsyncDynamicReloadPolicy      UpgradePolicy = "autoReload"
	SyncDynamicReloadPolicy       UpgradePolicy = "operatorSyncUpdate"
	DynamicReloadAndRestartPolicy UpgradePolicy = "dynamicReloadBeginRestart"
)

type OpsPhase string

const (
	OpsPendingPhase    OpsPhase = "Pending"
	OpsCreatingPhase   OpsPhase = "Creating"
	OpsRunningPhase    OpsPhase = "Running"
	OpsCancellingPhase OpsPhase = "Cancelling"
	OpsSucceedPhase    OpsPhase = "Succeed"
	OpsCancelledPhase  OpsPhase = "Cancelled"
	OpsFailedPhase     OpsPhase = "Failed"
	OpsAbortedPhase    OpsPhase = "Aborted"
)

type ProgressStatus string

const (
	PendingProgressStatus    ProgressStatus = "Pending"
	ProcessingProgressStatus ProgressStatus = "Processing"
	FailedProgressStatus     ProgressStatus = "Failed"
	SucceedProgressStatus    ProgressStatus = "Succeed"
)

type ActionTaskStatus string

const (
	ProcessingActionTaskStatus ActionTaskStatus = "Processing"
	FailedActionTaskStatus     ActionTaskStatus = "Failed"
	SucceedActionTaskStatus    ActionTaskStatus = "Succeed"
)

type ClusterComponentPhase string

const (
	CreatingClusterCompPhase ClusterComponentPhase = "Creating"
	RunningClusterCompPhase  ClusterComponentPhase = "Running"
	UpdatingClusterCompPhase ClusterComponentPhase = "Updating"
	StoppingClusterCompPhase ClusterComponentPhase = "Stopping"
	StoppedClusterCompPhase  ClusterComponentPhase = "Stopped"
	DeletingClusterCompPhase ClusterComponentPhase = "Deleting"
	FailedClusterCompPhase   ClusterComponentPhase = "Failed"
	AbnormalClusterCompPhase ClusterComponentPhase = "Abnormal"
)

type OpsWorkloadType string

const (
	PodWorkload OpsWorkloadType = "Pod"
	JobWorkload OpsWorkloadType = "Job"
)
