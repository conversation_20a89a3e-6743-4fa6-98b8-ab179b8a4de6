package application

import (
	"context"
	"strconv"

	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

const (
	AnnotationConfigHash      = "application." + common.GroupPrefix + "/config-hash"
	AnnotationCurrentRevision = "application." + common.GroupPrefix + "/current-revision"
	AnnotationLatestRevision  = "application." + common.GroupPrefix + "/latest-revision"
)

func (c *ApplicationController) syncRecordHistory(ctx context.Context, app *Application) error {
	annotations := app.GetAnnotations()
	if annotations == nil {
		annotations = map[string]string{}
	}
	currenthash := ComputeApplicationConfigHash(app)
	log.FromContext(ctx).V(1).Info("sync record history", "currenthash", currenthash, "app", app.Name)
	// if the hash is the same, we don't need to update the history
	// this is used to avoid the update loop
	if getConfigHash(app) == currenthash {
		return nil
	}
	latestrev := getLatestRevison(app)
	if latestrev == 0 {
		// backward compatibility
		if cuerev := getCurrentRevision(app); cuerev != 0 {
			latestrev = cuerev
		}
	}

	nextrev := latestrev + 1
	annotations[AnnotationConfigHash] = currenthash
	annotations[AnnotationLatestRevision] = strconv.Itoa(nextrev)
	app.SetAnnotations(annotations)

	// update the application with the new revision
	if err := c.Client.Scope(app.Scopes...).Update(ctx, app); err != nil {
		return err
	}
	history := &ApplicationHistory{
		Product:    app.Product,
		Values:     app.Values,
		ConfigHash: currenthash,
		Revision:   nextrev,
		License:    app.License,
		ObjectMeta: store.ObjectMeta{Name: "revision-" + strconv.Itoa(nextrev)},
	}
	historiesscope := c.Client.Scope(app.Scopes...).Scope(base.ScopeApplication(app.Name))

	if err := historiesscope.Create(ctx, history); err != nil {
		if errors.IsAlreadyExists(err) {
			// remove the old history
			if err := historiesscope.Delete(ctx, &ApplicationHistory{ObjectMeta: store.ObjectMeta{Name: history.Name}}); err != nil {
				return err
			}
			return historiesscope.Create(ctx, history)
		}
	}
	return nil
}

func getLatestRevison(app *Application) int {
	annotations := app.GetAnnotations()
	if annotations == nil {
		return 0
	}
	currentrev, _ := strconv.Atoi(annotations[AnnotationLatestRevision])
	return currentrev
}

func getCurrentRevision(app *Application) int {
	annotations := app.GetAnnotations()
	if annotations == nil {
		return 0
	}
	currentrev, _ := strconv.Atoi(annotations[AnnotationCurrentRevision])
	return currentrev
}

func setCurrentRevision(app *Application, revision int) {
	annotations := app.GetAnnotations()
	if annotations == nil {
		annotations = map[string]string{}
	}
	annotations[AnnotationCurrentRevision] = strconv.Itoa(revision)
	app.SetAnnotations(annotations)
}

func getConfigHash(app *Application) string {
	annotations := app.GetAnnotations()
	if annotations == nil {
		return ""
	}
	return annotations[AnnotationConfigHash]
}

func ComputeApplicationConfigHash(app *Application) string {
	return base.ComputeHashToHex(app.Name, app.Product, app.Values, app.License)
}
