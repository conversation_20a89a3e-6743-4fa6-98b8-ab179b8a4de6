// +k8s:openapi-gen=true
package application

import (
	"time"

	corev1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/helm"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/loadbalancer"
	"xiaoshiai.cn/core/market"
	"xiaoshiai.cn/core/pay"
)

type (
	ObjectReference = corev1.ObjectReference
)

// +k8s:openapi-gen=true
type Application struct {
	store.ObjectMeta `json:",inline"`
	// Tenant is the tenant that this application is
	// it auto set by the system when create the application.
	// Tenant string `json:"tenant"`

	// Paused to pause the deployment.
	// it destroy all resources created by the deployment.
	// this field also controled by license , if license is not valid, it will be paused.
	// +optional
	Paused bool `json:"paused,omitempty"`

	// ReleaseName is the name of the application to deploy.
	ReleaseName string `json:"releaseName,omitempty"`

	// License is the license reference to deploy.
	License ApplicationLicense `json:"license,omitempty"`

	Cluster ApplicationCluster `json:"cluster,omitempty" validate:"required"`

	// Product is the application reference to deploy.
	Product ProductReference `json:"product,omitempty" validate:"required"`

	// AllowClusterSopeResource is the application allow to create cluster scope resource.
	AllowClusterSopeResource bool `json:"allowClusterSopeResource,omitempty"`

	// Product is the application reference to deploy.
	DepracatedApplication ProductReference `json:"application,omitempty" validate:"required"`

	// Values is the values to deploy.
	Values helm.HelmValues `json:"values,omitempty"`

	// Status is the status of the application.
	Status ApplicationStatus `json:"status,omitempty"`
}

type ApplicationLicense struct {
	Name string `json:"name,omitempty"`
}

// +enum
type ApplicationType string

const (
	ApplicationTypeUser   ApplicationType = "User"
	ApplicationTypeSystem ApplicationType = "System"
)

type ApplicationCluster struct {
	store.ObjectReference `json:",inline"`
	Namespace             string `json:"namespace,omitempty"`
	ResourcePool          string `json:"resourcePool,omitempty"`

	// Annotations is the annotations of the cluster.
	Annotations map[string]string   `json:"annotations,omitempty"`
	Labels      map[string]string   `json:"labels,omitempty"`
	Type        cluster.ClusterType `json:"type"`
}

// +enum
// +k8s:openapi-gen=true
type ApplicationPhase string

const (
	ApplicationPhaseNone      ApplicationPhase = ""
	ApplicationPhaseInstalled ApplicationPhase = "Installed"
	ApplicationPhasePaused    ApplicationPhase = "Paused"
	ApplicationPhaseFailed    ApplicationPhase = "Failed"
)

// +k8s:openapi-gen=true
type ApplicationStatus struct {
	// +optional
	Phase ApplicationPhase `json:"phase,omitempty"`

	// Conditions holds the state of the application controller's reconciliation functionality.
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	// +listType=map
	// +listMapKey=type
	Conditions []controller.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type"`

	// A human readable message indicating details about why the pod is in this condition.
	// +optional
	Message string `json:"message,omitempty"`

	// Note is a note message from helm install
	Note string `json:"note,omitempty"`

	// A brief CamelCase message indicating details about why is in this state.
	// e.g. 'Created'
	// +optional
	Reason string `json:"reason,omitempty"`

	// RFC 3339 date and time at which the object was first acknowledged by controller.
	// +optional
	StartTime time.Time `json:"startTime,omitempty"`

	// InstallTime is the time first installed.
	InstallTime time.Time `json:"installTime,omitempty"`

	// RFC 3339 date and time at which the object was last upgraded.
	// +optional
	UpdateTime time.Time `json:"updateTime,omitempty"`

	// ReleaseTime is the time to remove the application.
	// +optional
	ReleaseTime *time.Time `json:"releaseTime,omitempty"`

	// Objects is a list of objects that are part of this application.
	// +optional
	// +listType=atomic
	Objects []ObjectReference `json:"objects,omitempty"`

	// PausedObjects is a list of objects that been deleted on application paused.
	// +listType=atomic
	PausedObjects []ObjectReference `json:"pausedObjects,omitempty"`

	// Source is the information about the source currently being deployed.
	Product ProductReferenceExtend `json:"product,omitempty"`

	License ApplicationLicenseStatus `json:"license,omitempty"`

	// Values is the values of the application.
	Values helm.HelmValues `json:"values,omitempty"`

	// Cloud is the cloud information of the application.
	Cloud ApplicationCloudStatus `json:"cloud,omitempty"`

	// Endpoints is the urls to access the application.
	Endpoints []ApplicationEndpointStatus `json:"endpoints,omitempty"`

	// Upgrade if any newer version available
	Upgradable ApplicationUpgradableVersion `json:"upgradable,omitempty"`

	// ObservedRevision is the observed revision of the application.
	// it is used to compare if the resources changed.
	ObservedRevision int `json:"observedRevision,omitempty"`
}

type ApplicationCloudStatus struct {
	// ObservedResourceHash is the hash of the observed resources.
	// it is used to compare if the resources changed.
	// if not changed, the resources will not be and the sync will be skipped.
	ObservedResourceHash string                   `json:"observedResourceHash,omitempty"`
	ResourceGroup        string                   `json:"resourceGroup,omitempty"`
	PausedResouces       []PausedCloudResource    `json:"pausedResources,omitempty"`
	Resources            []CloudResourceReference `json:"resources,omitempty"`
}

type PausedCloudResource struct {
	CloudResourceReference `json:",inline"`
	LastStatus             string `json:"lastStatus,omitempty"`
}

type ApplicationEndpointStatus struct {
	Type ApplicationEndpointType `json:"type"`
	URL  string                  `json:"url"`
}

type ApplicationEndpointType string

const (
	ApplicationEndpointTypeService  ApplicationEndpointType = "Service"
	ApplicationEndpointTypeIngress  ApplicationEndpointType = "Ingress"
	ApplicationEndpointTypeDatabase ApplicationEndpointType = "Database"
)

type ApplicationLicenseStatus struct {
	Required bool                  `json:"required"`
	InUse    store.ObjectReference `json:"inUse"`
}

// +enum
type ResourcePoolType string

const (
	ResourcePoolTypeShared  ResourcePoolType = "Shared"
	ResourcePoolTypePrivate ResourcePoolType = "Private"
)

// +k8s:openapi-gen=true
type ProductReference struct {
	// Name is the name of the application to deploy.
	Name string `json:"name,omitempty"`
	// +optional
	Version string `json:"version,omitempty"`
}

// +k8s:openapi-gen=true
type ProductReferenceExtend struct {
	ProductReference `json:",inline"`

	Alias string `json:"alias,omitempty"`

	License market.LicensePolicy `json:"license"`

	Category string `json:"category"`

	SubCategory string `json:"subcategory"`

	Type market.ProductType `json:"type"`

	OS string `json:"os"`
	// Details is extra details of the application
	// +k8s:openapi-gen=true
	Details market.ProductDetails `json:"details"`

	Vendor string `json:"vendor"`
}

func ApplicationReferenceExtendFromProduct(product *market.Product, version string) ProductReferenceExtend {
	// TODO: should is fill at every time we list the application?
	return ProductReferenceExtend{
		ProductReference: ProductReference{Name: product.Name, Version: version},
		License:          market.LicensePolicy{Enabled: product.License.Enabled},
		Category:         product.Category,
		Alias:            product.Alias,
		SubCategory:      product.SubCategory,
		Type:             product.Type,
		Details:          product.Details,
		OS:               product.OS,
		Vendor:           product.Vendor,
	}
}

const (
	// DeploymentConditionInstalled is the application is installed condition
	DeploymentConditionInstalled string = "Installed"

	// DeploymentConditionPaused is the application is paused condition
	DeploymentConditionPaused string = "Paused"

	// DeploymentConditionClusterConnected is the target cluster is accessible condition
	DeploymentConditionClusterConnected string = "ClusterConnected"

	// DeploymentConditionLicenseValid is the license is valid condition
	DeploymentConditionLicenseValid string = "LicenseValid"
)

func NewAPI(base base.API, mongoStore store.Store, cloudinfo cluster.CloudInfoGetter, artifacts *artifact.ArtifactService, lbsvc loadbalancer.Loadbalancer, pay *pay.PaySystem) *API {
	return &API{
		APP:          NewApplicationAPI(base, cloudinfo, artifacts, pay),
		Status:       NewDefaultApplicationStatusAPI(base, mongoStore, cloudinfo),
		LoadBalancer: NewApplicationLoadbalancerAPI(base, cloudinfo, lbsvc),
	}
}

type API struct {
	APP          *ApplicationAPI
	Status       *ApplicationStatusAPI
	LoadBalancer *ApplicationLoadbalancerAPI
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Application").
		SubGroup(

			base.NewTenantGroup("applications").
				SubGroup(a.APP.ApplicationsGroup("application")),

			base.NewTenantOrganizationGroup("applications").
				SubGroup(a.APP.ApplicationsGroup("application")),

			base.NewTenantOrganizationGroup("applications/{application}/subapplications/").
				SubGroup(a.APP.ApplicationsGroup("subapplication")),

			base.NewAllApplicationGroup(
				a.Status.Group(),
				a.LoadBalancer.Group(),
			),
		)
}
