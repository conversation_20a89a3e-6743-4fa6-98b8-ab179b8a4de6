package application

import (
	"context"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/cluster"
	cloud "xiaoshiai.cn/core/ismc/common"
)

func (c *ApplicationController) syncCloudPaused(ctx context.Context, info cluster.CloudInfo, app *Application) error {
	rls := GetRelease(app)

	cloud, err := info.CloudProvider()
	if err != nil {
		return err
	}
	if app.Paused {
		if err := PauseCloudResource(ctx, cloud, rls.Name, rls.Namespace, &app.Status.Cloud); err != nil {
			return err
		}
	} else {
		if err := ResumeCloudResource(ctx, cloud, rls.Name, rls.Namespace, &app.Status.Cloud); err != nil {
			return err
		}
	}
	return nil
}

func PauseCloudResource(ctx context.Context, c cloud.Provider, name, resourcegroup string, cloudstatus *ApplicationCloudStatus) error {
	log := log.FromContext(ctx)
	alreadyPaused := make(map[CloudResourceReference]PausedCloudResource)
	for _, ref := range cloudstatus.PausedResouces {
		alreadyPaused[ref.CloudResourceReference] = ref
	}

	allpaused := make([]PausedCloudResource, 0)
	for _, ref := range cloudstatus.Resources {
		if paused, ok := alreadyPaused[ref]; ok {
			log.V(3).Info("resource already paused", "id", ref.ID, "name", ref.Name)
			allpaused = append(allpaused, paused)
			continue
		}
		switch ref.Resource {
		case CloudResourceTypeVirtualMachines:
			vm, err := c.GetVirtualMachine(ctx, ref.ID)
			if err != nil {
				if errors.IsNotFound(err) {
					continue
				}
				return err
			}
			if vm.Status.PowerState == cloud.PowerStateOn {
				log.Info("pause virtual machine", "id", ref.ID, "name", ref.Name)
				if err := c.SetVirtualMachinePower(ctx, ref.ID, cloud.PowerActionOff, cloud.VirtualMachinePowerOptions{Wait: true}); err != nil {
					if errors.IsNotFound(err) {
						continue
					}
					return err
				}
			}
			allpaused = append(allpaused, PausedCloudResource{
				LastStatus:             string(vm.Status.PowerState),
				CloudResourceReference: ref,
			})
		}
	}
	cloudstatus.PausedResouces = allpaused
	return nil
}

func ResumeCloudResource(ctx context.Context, c cloud.Provider, name, resourcegroup string, cloudstatus *ApplicationCloudStatus) error {
	for _, ref := range cloudstatus.PausedResouces {
		switch ref.Resource {
		case CloudResourceTypeVirtualMachines:
			if ref.LastStatus == string(cloud.PowerStateOn) {
				log.Info("poweron virtual machine", "id", ref.ID, "name", ref.Name)
				vm, err := c.GetVirtualMachine(ctx, ref.ID)
				if err != nil {
					if errors.IsNotFound(err) {
						continue
					}
					return err
				}
				if vm.Status.PowerState == cloud.PowerStateOff || vm.Status.PowerState == cloud.PowerStateSuspended {
					if err := c.SetVirtualMachinePower(ctx, ref.ID, cloud.PowerActionOn, cloud.VirtualMachinePowerOptions{}); err != nil {
						if errors.IsNotFound(err) {
							continue
						}
					}
					return err
				}
			}
		}
	}
	cloudstatus.PausedResouces = nil
	return nil
}
