package application

import (
	"context"
	"net/http/httptest"
	"testing"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/providers"
	hcs "xiaoshiai.cn/core/ismc/providers/hcs/bob"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
	"xiaoshiai.cn/core/ismc/providers/remote"
	"xiaoshiai.cn/core/ismc/providers/vmware"
	"xiaoshiai.cn/core/ismc/rest"
	"xiaoshiai.cn/core/ismc/vmoci"
)

func TestIsRegistryImage(t *testing.T) {
	tests := []struct {
		image string
		want  bool
	}{
		{image: "docker.io/library/nginx", want: true},
		{image: "docker.io/library/nginx:latest", want: true},
		{image: "library/ubuntu", want: false}, // we don't add the default registry
		{image: "ubuntu", want: false},
		{image: "ubuntu:latest", want: false},
		{image: "ubuntu-focal", want: false},
	}
	for _, tt := range tests {
		t.Run(tt.image, func(t *testing.T) {
			if got := IsRegistryImage(tt.image); got != tt.want {
				t.Errorf("IsRegistryImage() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRemoteProvider_ImportImage(t *testing.T) {
	// setup http test server
	ts := setupTestServer()
	defer ts.Close()
	ctx := context.Background()
	privider, err := remote.NewRemote(&remote.Options{Server: ts.URL})
	if err != nil {
		t.Fatal(err)
	}
	img := "registry.develop.xiaoshiai.cn/alpine/alpine:3.21.0"
	//img := "registry.develop.xiaoshiai.cn/alpine/alpine:latest"
	src := vmoci.ImportImageInfo{
		// Zone:      "az1.dc1",
		Image:     img,
		ImageName: EscapeImageName(img),
	}
	if _, err := vmoci.ImportImageFromRegistryToCloud(ctx, src, []vmoci.ImageFormat{vmoci.ImageFormatVMDK, vmoci.ImageFormatOVA}, privider); err != nil {
		t.Fatal(err)
	}
	t.Log("finished")
}

func setupTestServer() *httptest.Server {
	// noop := &noop.NoopProvider{}

	provider, err := providers.NewProvider(context.Background(), &providers.Options{
		Type: "vmware",
		Vmware: &vmware.Options{
			Address:    "*************",
			Username:   "<EMAIL>",
			Password:   "",
			Datacenter: "Datacenter",
		},
		HuaWei: &hcs.Options{
			Options: client.Options{
				Region:         "majnoon-dccloud-1",
				GlobalDomain:   "majnoon-dccloud.com",
				UserName:       "xiaoshiai",
				Domain:         "xiaoshiai",
				DefaultProject: "majnoon-dccloud-1_xiaoshiai",
				Password:       "",
				Proxy:          "http://************:8089",
			},
			UseSubscription: true,
		},
	})
	if err != nil {
		panic(err)
	}

	fakeprovider := rest.NewAPI(provider)
	handler := api.New().Group(fakeprovider.Group()).Build()
	return httptest.NewServer(handler)
}
