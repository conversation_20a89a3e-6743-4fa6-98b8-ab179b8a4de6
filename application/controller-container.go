package application

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"maps"
	"reflect"
	"strconv"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/sets"
	kubeyaml "k8s.io/apimachinery/pkg/util/yaml"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/yaml"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application/helm"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/cluster/metadata"
	"xiaoshiai.cn/core/license"
)

func (c *ApplicationController) syncContainer(ctx context.Context, info cluster.CloudInfo, source *ToDeploy, app *Application) error {
	clinets, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	// we can update the revision to avoid retry
	// set the observed revision no matter whther current resources deployed or not
	app.Status.ObservedRevision = getLatestRevison(app)

	cli := clinets.Client
	if err := c.syncLoggingFlow(ctx, cli, source, app); err != nil {
		log.FromContext(ctx).Error(err, "failed to sync logging flow")
	}
	rlsname, rlsnamespace, chart, values := source.Release.Name, source.Release.Namespace, source.Chart, source.Values

	postrender := ContainerPostRender{Store: c.Client, CloudInfo: c.Clouds}

	helmapplyoptions := helm.ApplyChartOptions{
		Values:         values,
		PostRenderFunc: postrender.ContainerPostRenderFunc(ctx, cli, source, app),
	}
	rls, changed, err := helm.ApplyChart(ctx, clinets.RestConfig, chart, rlsname, rlsnamespace, helmapplyoptions)
	if err != nil {
		return err
	}
	_ = changed

	unstructs, err := ReadObjects([]byte(rls.Manifest))
	if err != nil {
		return err
	}
	manged := []corev1.ObjectReference{}
	for _, obj := range unstructs {
		manged = append(manged, GetReference(obj))
	}
	app.Status.Objects = manged
	if rls.Info != nil {
		app.Status.Note = rls.Info.Notes
	}
	_ = c.syncContainerEndpoints(ctx, app, unstructs)
	return nil
}

func (c *ApplicationController) removeContainer(ctx context.Context, info cluster.CloudInfo, source ApplicationRelease, app *Application) error {
	kubeclients, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	cli := kubeclients.Client
	if _, err := helm.RemoveChart(ctx, kubeclients.RestConfig, source.Name, source.Namespace); err != nil {
		return err
	}
	// remove logging flow
	if err := c.removeLoggingFlow(ctx, cli, source, app); err != nil {
		log.FromContext(ctx).Error(err, "failed to remove logging flow")
	}
	if err := c.removeUnusedPVC(ctx, cli, source.Namespace, app); err != nil {
		log.FromContext(ctx).Error(err, "failed to remove unused pvc")
	}
	app.Status.Objects = nil
	return nil
}

func (r *ApplicationController) removeUnusedPVC(ctx context.Context, cli client.Client, namespace string, app *Application) error {
	if namespace == "" {
		return nil
	}
	labels := CommonLabelsFromApp(app)
	if len(labels) == 0 {
		return nil
	}
	pvcs := &corev1.PersistentVolumeClaimList{}
	if err := cli.List(ctx, pvcs, client.InNamespace(namespace), client.MatchingLabels(labels)); err != nil {
		return err
	}
	var errs []error
	for _, pvc := range pvcs.Items {
		if err := cli.Delete(ctx, &pvc); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

func GetReference(obj client.Object) corev1.ObjectReference {
	return corev1.ObjectReference{
		APIVersion: obj.GetObjectKind().GroupVersionKind().GroupVersion().String(),
		Kind:       obj.GetObjectKind().GroupVersionKind().Kind,
		Namespace:  obj.GetNamespace(),
		Name:       obj.GetName(),
	}
}

type ContainerPostRender struct {
	Store     store.Store
	CloudInfo cluster.CloudInfoGetter
}

func (r ContainerPostRender) ContainerPostRenderFunc(ctx context.Context, cli client.Client, source *ToDeploy, app *Application) helm.PostRenderFunc {
	return func(renderedManifests *bytes.Buffer) (modifiedManifests *bytes.Buffer, err error) {
		objects, err := ReadObjects(renderedManifests.Bytes())
		if err != nil {
			return nil, err
		}
		typedobjects := ConvertToTypedIfPossible(objects, cli.Scheme())
		// check preconditions
		if err := r.CheckContainerResources(ctx, cli, source, app, typedobjects); err != nil {
			return nil, err
		}
		// inject
		injectedobjects, err := r.ModifiContainerResources(ctx, cli, source, app, typedobjects)
		if err != nil {
			return nil, err
		}
		// write back
		modifiedManifests = &bytes.Buffer{}
		for _, obj := range injectedobjects {
			yamlbytes, err := yaml.Marshal(obj)
			if err != nil {
				return nil, err
			}
			if _, err := modifiedManifests.Write(yamlbytes); err != nil {
				return nil, err
			}
			if _, err := modifiedManifests.Write([]byte("\n---\n")); err != nil {
				return nil, err
			}
		}
		return modifiedManifests, nil
	}
}

func (r ContainerPostRender) CheckContainerResources(ctx context.Context, cli client.Client, source *ToDeploy, app *Application, typedobjects []client.Object) error {
	for _, obj := range typedobjects {
		switch typed := obj.(type) {
		case *networkingv1.Ingress:
			if classname := typed.Spec.IngressClassName; classname != nil && *classname != "" {
				if err := r.CheckIngressClass(ctx, app, cli, *classname); err != nil {
					return errors.NewInvalid(typed.Kind, typed.Name, err)
				}
			}
		case *corev1.PersistentVolumeClaim:
			if classname := typed.Spec.StorageClassName; classname != nil && *classname != "" {
				if err := r.CheckStorageClass(ctx, app, cli, *classname); err != nil {
					return errors.NewInvalid(typed.Kind, typed.Name, err)
				}
			}
		}
	}
	return nil
}

func (r ContainerPostRender) CheckIngressClass(ctx context.Context, app *Application, cli client.Client, name string) error {
	tenant, org := base.TenantOrganizationFromScopes(app.Scopes...)
	ok, err := metadata.PermitIngressClass(ctx, r.Store, cli, app.Cluster.ObjectReference, name, tenant, org)
	if err != nil {
		return fmt.Errorf("failed to check ingress class %s: %w", name, err)
	}
	if !ok {
		return fmt.Errorf("use of ingress class %s is not allowed", name)
	}
	return nil
}

func (r ContainerPostRender) CheckStorageClass(ctx context.Context, app *Application, cli client.Client, name string) error {
	tenant, org := base.TenantOrganizationFromScopes(app.Scopes...)
	ok, err := metadata.PermitStorageClass(ctx, r.Store, cli, app.Cluster.ObjectReference, name, tenant, org)
	if err != nil {
		return fmt.Errorf("failed to check storage class %s: %s", name, err.Error())
	}
	if !ok {
		return fmt.Errorf("use of storage class %s is not allowed", name)
	}
	return nil
}

func (r ContainerPostRender) ModifiContainerResources(ctx context.Context, cli client.Client, source *ToDeploy, app *Application, typedobjects []client.Object) ([]client.Object, error) {
	// inject labels
	typedobjects = InjectLabels(typedobjects, CommonLabelsFromApp(app))
	// inject node selector
	if source.NodeSelector != nil {
		typedobjects = InjectNodeSelector(typedobjects, source.NodeSelector)
	}
	// inject node affinity
	if source.NodeAffinity != nil {
		typedobjects = InjectNodeAffinity(typedobjects, source.NodeAffinity)
	}
	// inject license
	typedobjects = InjectLicense(source, typedobjects)
	// check namespace
	if err := CheckSetResourceNamespace(cli, source.Release.Namespace, source.AllowClusterScoep, typedobjects); err != nil {
		return nil, err
	}
	return typedobjects, nil
}

func ConvertToTypedIfPossible(list []*unstructured.Unstructured, schema *runtime.Scheme) []client.Object {
	typedobjects := []client.Object{}
	for _, obj := range list {
		typed, err := schema.ConvertToVersion(obj, obj.GroupVersionKind().GroupVersion())
		if err != nil {
			typedobjects = append(typedobjects, obj)
		} else {
			typedobjects = append(typedobjects, typed.(client.Object))
		}
	}
	return typedobjects
}

func InjectLicense(source *ToDeploy, orginalobjects []client.Object) []client.Object {
	license := source.License
	if license == nil {
		return orginalobjects
	}
	injectobjects := []client.Object{}
	// inject license into objects
	licensesecret := &corev1.Secret{
		TypeMeta:   metav1.TypeMeta{APIVersion: corev1.SchemeGroupVersion.String(), Kind: "Secret"},
		ObjectMeta: metav1.ObjectMeta{Name: source.Release.Name + "-license"},
		Data:       map[string][]byte{"license": []byte(license.Data)},
	}
	injectobjects = append(injectobjects, licensesecret)
	// inject pull secret
	if len(license.RegistrySecrets) > 0 {
		pullsecretname := source.Release.Name + "-registry-secret"
		injectobjects = append(injectobjects, buildImagePullSecret(pullsecretname, license.RegistrySecrets))
		orginalobjects = InjectPullSecret(orginalobjects, pullsecretname)
	}
	return append(orginalobjects, injectobjects...)
}

func buildImagePullSecret(name string, pullsecret []license.RegistryPullSecret) *corev1.Secret {
	list := map[string]any{}
	for _, secret := range pullsecret {
		regserver := secret.Registry
		if regserver == "" {
			regserver = "https://index.docker.io/v1/"
		}
		if !strings.HasPrefix(regserver, "http") {
			regserver = "https://" + regserver
		}
		list[regserver] = map[string]string{
			"username": secret.Username,
			"password": secret.Password,
			"auth":     base64.StdEncoding.EncodeToString([]byte(secret.Username + ":" + secret.Password)),
		}
	}
	data, _ := json.Marshal(map[string]any{"auths": list})
	return &corev1.Secret{
		TypeMeta:   metav1.TypeMeta{APIVersion: corev1.SchemeGroupVersion.String(), Kind: "Secret"},
		ObjectMeta: metav1.ObjectMeta{Name: name},
		Type:       corev1.SecretTypeDockerConfigJson,
		Data: map[string][]byte{
			corev1.DockerConfigJsonKey: data,
		},
	}
}

func InjectPullSecret(list []client.Object, secretname string) []client.Object {
	for _, item := range list {
		InjectPodTemplateIfPossible(item, func(podspec *corev1.PodTemplateSpec) {
			podspec.Spec.ImagePullSecrets = append(podspec.Spec.ImagePullSecrets, corev1.LocalObjectReference{Name: secretname})
		})
		InjectPodIfPossible(item, func(pod *corev1.Pod) {
			pod.Spec.ImagePullSecrets = append(pod.Spec.ImagePullSecrets, corev1.LocalObjectReference{Name: secretname})
		})
	}
	return list
}

func InjectLabels(list []client.Object, labels map[string]string) []client.Object {
	for _, item := range list {
		item.SetLabels(MergeLabels(item.GetLabels(), labels))
		InjectVolumeClaimTemplatesIfPossible(item, func(statefulset *appsv1.StatefulSet) {
			for i := range statefulset.Spec.VolumeClaimTemplates {
				statefulset.Spec.VolumeClaimTemplates[i].Labels = MergeLabels(statefulset.Spec.VolumeClaimTemplates[i].Labels, labels)
			}
		})
		// only inject instance label to pod selector
		if instancevalue, ok := labels[base.LabelInstance]; ok {
			InjectPodSelectorIfPossible(item, func(selector *metav1.LabelSelector) {
				// some workload may not have selector
				if selector == nil {
					return
				}
				selector.MatchLabels = MergeLabels(selector.MatchLabels, map[string]string{base.LabelInstance: instancevalue})
			})
		}
		InjectPodTemplateIfPossible(item, func(ps *corev1.PodTemplateSpec) {
			ps.Labels = MergeLabels(ps.Labels, labels)
		})
		InjectPodIfPossible(item, func(p *corev1.Pod) {
			p.Labels = MergeLabels(p.Labels, labels)
		})
	}
	return list
}

func InjectNodeSelector(list []client.Object, nodeselector map[string]string) []client.Object {
	for _, item := range list {
		InjectPodTemplateIfPossible(item, func(ps *corev1.PodTemplateSpec) {
			ps.Spec.NodeSelector = MergeLabels(ps.Spec.NodeSelector, nodeselector)
		})
		InjectPodIfPossible(item, func(p *corev1.Pod) {
			p.Spec.NodeSelector = MergeLabels(p.Spec.NodeSelector, nodeselector)
		})
	}
	return list
}

func InjectNodeAffinity(list []client.Object, nodeaffinity *corev1.NodeAffinity) []client.Object {
	if nodeaffinity == nil {
		return list
	}
	for _, item := range list {
		InjectPodTemplateIfPossible(item, func(ps *corev1.PodTemplateSpec) {
			if ps.Spec.Affinity == nil {
				ps.Spec.Affinity = &corev1.Affinity{}
			}
			if ps.Spec.Affinity.NodeAffinity == nil {
				ps.Spec.Affinity.NodeAffinity = nodeaffinity
				return
			}
			// merge node affinity terms
			if nodeaffinity.PreferredDuringSchedulingIgnoredDuringExecution != nil {
				ps.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution = append(
					ps.Spec.Affinity.NodeAffinity.PreferredDuringSchedulingIgnoredDuringExecution,
					nodeaffinity.PreferredDuringSchedulingIgnoredDuringExecution...,
				)
			}
			if nodeaffinity.RequiredDuringSchedulingIgnoredDuringExecution != nil {
				if ps.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution == nil {
					ps.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution = &corev1.NodeSelector{}
				}
				ps.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms = append(
					ps.Spec.Affinity.NodeAffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms,
					nodeaffinity.RequiredDuringSchedulingIgnoredDuringExecution.NodeSelectorTerms...,
				)
			}
		})
	}
	return list
}

func InjectVolumeClaimTemplatesIfPossible(item client.Object, fn func(statefulset *appsv1.StatefulSet)) {
	switch typed := item.(type) {
	case *appsv1.StatefulSet:
		fn(typed)
	}
}

func InjectPodIfPossible(item client.Object, onPod func(*corev1.Pod)) {
	pod, ok := item.(*corev1.Pod)
	if ok {
		onPod(pod)
	}
}

func InjectPodTemplateIfPossible(item client.Object, onPodTemplate func(*corev1.PodTemplateSpec)) {
	switch typed := item.(type) {
	case *appsv1.Deployment:
		onPodTemplate(&typed.Spec.Template)
	case *appsv1.StatefulSet:
		onPodTemplate(&typed.Spec.Template)
	case *appsv1.DaemonSet:
		onPodTemplate(&typed.Spec.Template)
	case *batchv1.Job:
		onPodTemplate(&typed.Spec.Template)
	case *batchv1.CronJob:
		onPodTemplate(&typed.Spec.JobTemplate.Spec.Template)
	}
}

func InjectPodSelectorIfPossible(litem client.Object, fn func(*metav1.LabelSelector)) {
	switch typed := litem.(type) {
	case *appsv1.Deployment:
		fn(typed.Spec.Selector)
	case *appsv1.StatefulSet:
		fn(typed.Spec.Selector)
	case *appsv1.DaemonSet:
		fn(typed.Spec.Selector)
	case *batchv1.Job:
		fn(typed.Spec.Selector)
	case *batchv1.CronJob:
		fn(typed.Spec.JobTemplate.Spec.Selector)
	}
}

func MergeLabels(dest, src map[string]string) map[string]string {
	if dest == nil {
		dest = map[string]string{}
	}
	maps.Copy(dest, src)
	return dest
}

func CheckSetResourceNamespace[T client.Object](cli client.Client, ns string, allowClusterScoped bool, list []T) error {
	for i, item := range list {
		isScoped, err := cli.IsObjectNamespaced(item)
		if err != nil {
			return err
		}
		if isScoped {
			if exitstns := item.GetNamespace(); exitstns != "" && exitstns != ns {
				return fmt.Errorf("object %s/%s crossed namespace", item.GetNamespace(), item.GetName())
			} else {
				item.SetNamespace(ns)
			}
		} else {
			if !allowClusterScoped {
				return fmt.Errorf("object %s/%s is cluster scoped which is not allowed",
					item.GetObjectKind().GroupVersionKind().String(), item.GetName())
			}
			item.SetNamespace("")
		}
		list[i] = item
	}
	return nil
}

func equalMapValues(a, b map[string]any) bool {
	return (len(a) == 0 && len(b) == 0) || reflect.DeepEqual(a, b)
}

const ReadCache = 4096

func ReadObjects(data []byte) ([]*unstructured.Unstructured, error) {
	d := kubeyaml.NewYAMLOrJSONDecoder(bytes.NewReader(data), ReadCache)
	var objs []*unstructured.Unstructured
	for {
		u := &unstructured.Unstructured{}
		if err := d.Decode(u); err != nil {
			if err == io.EOF {
				break
			}
			return objs, fmt.Errorf("failed to unmarshal manifest: %v", err)
		}
		if u.Object == nil || len(u.Object) == 0 {
			continue // skip empty object
		}
		objs = append(objs, u)
	}
	return objs, nil
}

func GetLoggingFlow(instancename, name, namespace string) *unstructured.Unstructured {
	return &unstructured.Unstructured{
		Object: map[string]any{
			"apiVersion": "logging.banzaicloud.io/v1beta1",
			"kind":       "Flow",
			"metadata": map[string]any{
				"name":      name,
				"namespace": namespace,
			},
			"spec": map[string]any{
				"match": []map[string]any{
					{
						"select": map[string]any{
							"labels": map[string]string{
								base.LabelInstance: instancename,
							},
						},
					},
				},
				"globalOutputRefs": []string{
					"kubegems-container-console-output",
				},
			},
		},
	}
}

func (r *ApplicationController) syncLoggingFlow(ctx context.Context, cli client.Client, source *ToDeploy, app *Application) error {
	uns := GetLoggingFlow(source.Release.Name, app.Name, source.Release.Namespace)

	if err := cli.Get(ctx, client.ObjectKeyFromObject(uns), uns); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		return cli.Create(ctx, uns)
	}
	return nil
}

func (r *ApplicationController) removeLoggingFlow(ctx context.Context, cli client.Client, source ApplicationRelease, app *Application) error {
	return client.IgnoreNotFound(cli.Delete(ctx, GetLoggingFlow(source.Name, app.Name, source.Namespace)))
}

func (r *ApplicationController) syncContainerEndpoints(_ context.Context, app *Application, objects []*unstructured.Unstructured) error {
	endpoints := []ApplicationEndpointStatus{}
	var errors []error
	for _, obj := range objects {
		gvk := obj.GetObjectKind().GroupVersionKind()
		switch gvk {
		case corev1.SchemeGroupVersion.WithKind("Service"):
			svc := &corev1.Service{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, svc); err != nil {
				errors = append(errors, err)
				continue
			}
			for _, port := range svc.Spec.Ports {
				endpoints = append(endpoints, ApplicationEndpointStatus{
					Type: ApplicationEndpointTypeService,
					URL:  fmt.Sprintf("%s://%s.%s:%d", DetectPortSchema(port), svc.Name, svc.Namespace, port.Port),
				})
			}
		case networkingv1.SchemeGroupVersion.WithKind("Ingress"):
			ing := &networkingv1.Ingress{}
			if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, ing); err != nil {
				errors = append(errors, err)
				continue
			}
			tlshosts := sets.NewString()
			for _, tls := range ing.Spec.TLS {
				for _, host := range tls.Hosts {
					tlshosts.Insert(host)
				}
			}
			for _, rule := range ing.Spec.Rules {
				if host := rule.Host; host != "" {
					if tlshosts.Has(host) {
						endpoints = append(endpoints, ApplicationEndpointStatus{
							Type: ApplicationEndpointTypeIngress,
							URL:  fmt.Sprintf("https://%s", host),
						})
					} else {
						endpoints = append(endpoints, ApplicationEndpointStatus{
							Type: ApplicationEndpointTypeIngress,
							URL:  fmt.Sprintf("http://%s", host),
						})
					}
				}
			}
		}
	}
	app.Status.Endpoints = endpoints
	return nil
}

func DetectPortSchema(p corev1.ServicePort) string {
	if app := p.AppProtocol; app != nil {
		return *app
	}
	if name := p.Name; name != "" {
		name := strings.ToLower(name)
		if strings.Contains(name, "https") {
			return "https"
		}
		if strings.Contains(name, "http") {
			return "http"
		}
		if strings.Contains(name, "grpc") {
			return "grpc"
		}
	}
	return DetectPortSchemaFrom(int(p.Port), string(p.Protocol))
}

func DetectPortSchemaFrom(port int, protocol string) string {
	if strings.ToLower(protocol) == "udp" {
		return "udp"
	}
	switch port {
	case 443:
		return "https"
	case 80, 8080:
		return "http"
	case 3306:
		return "mysql"
	case 5432:
		return "postgresql"
	case 6379:
		return "redis"
	case 27017:
		return "mongodb"
	default:
		if str := strconv.Itoa(int(port)); strings.Contains(str, "80") {
			return "http"
		}
		return "tcp"
	}
}
