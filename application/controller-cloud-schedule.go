package application

import (
	"context"
	"fmt"

	"golang.org/x/exp/rand"
	"k8s.io/utils/set"
	"xiaoshiai.cn/common/log"
	cloud "xiaoshiai.cn/core/ismc/common"
)

func SelectZone(ctx context.Context, c cloud.Provider, resourcese []CloudResource) (string, error) {
	log := log.FromContext(ctx)
	var allowzones *set.Set[string]
	for _, res := range resourcese {
		switch typed := res.(type) {
		case *VirtualMachine:
			if typed.Zone != "" {
				log.Info("zone selected from resource", "zone", typed.Zone, "resource", res)
				return typed.Zone, nil
			}
			// chekc if diskclass is set and have zones
			for _, disk := range typed.Disks {
				if disk.DiskClass != "" {
					diskclass, err := c.GetDiskClass(ctx, disk.DiskClass)
					if err != nil {
						return "", err
					}
					// diskclass it zones based
					if len(diskclass.AvailableZones) == 0 {
						continue
					}
					thisallow := set.New(diskclass.AvailableZones...)
					if allowzones == nil {
						allowzones = &thisallow
					} else {
						allowzones.Intersection(thisallow)
					}
				}
			}
		case *Disk:
			if typed.Zone != "" {
				log.Info("zone selected from resource", "zone", typed.Zone, "resource", res)
				return typed.Zone, nil
			}
		}
	}
	if allowzones != nil {
		list := allowzones.UnsortedList()
		log.Info("zone filterd from diskclass", "zones", list)
		if len(list) == 0 {
			return "", fmt.Errorf("no available zone, diskclass filtered")
		}
		if len(list) == 1 {
			log.Info("zone selected from diskclass", "zone", list[0])
			return list[0], nil
		}
		selected := list[rand.Intn(len(list))]
		log.Info("zone selected random from diskclass", "zone", selected)
		return selected, nil
	}
	zones, err := c.ListZones(ctx, cloud.ListZoneOptions{})
	if err != nil {
		return "", err
	}
	if len(zones.Items) == 0 {
		return "", fmt.Errorf("no available zone")
	}
	// TODO: select zone based on load
	selected := zones.Items[rand.Intn(len(zones.Items))]
	log.Info("zone selected", "zone", selected)
	return selected.ID, nil
}
