package apply

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"

	"github.com/go-logr/logr"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	apimeta "k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const (
	DefaultFieldOwner = "ismc"
)

type DiffResult struct {
	Creats  []*unstructured.Unstructured
	Applys  []*unstructured.Unstructured
	Removes []*unstructured.Unstructured
}

func Diff(managed []corev1.ObjectReference, resources []*unstructured.Unstructured) DiffResult {
	result := DiffResult{}
	managedmap := map[corev1.ObjectReference]bool{}
	for _, item := range managed {
		mapkey := corev1.ObjectReference{
			APIVersion: item.APIVersion,
			Kind:       item.Kind,
			Namespace:  item.Namespace,
			Name:       item.Name,
		}
		managedmap[mapkey] = false
	}
	for _, item := range resources {
		man := GetReference(item)
		if _, ok := managedmap[man]; !ok {
			result.Creats = append(result.Creats, item)
		} else {
			result.Applys = append(result.Applys, item)
		}
		managedmap[man] = true
	}
	for k, v := range managedmap {
		if !v {
			uns := &unstructured.Unstructured{}
			uns.SetAPIVersion(k.APIVersion)
			uns.SetKind(k.Kind)
			uns.SetName(k.Name)
			uns.SetNamespace(k.Namespace)
			result.Removes = append(result.Removes, uns)
		}
	}
	return result
}

func NewDefaultSyncOptions() *SyncOptions {
	return &SyncOptions{
		ServerSideApply: true,
		CreateNamespace: true,
		CleanCRD:        false,
	}
}

type SyncOptions struct {
	ServerSideApply bool
	CreateNamespace bool
	Managed         []corev1.ObjectReference // managed resources use to detect if a resource should be removed
	CleanCRD        bool
}

type Apply struct {
	Client client.Client
}

func (a Apply) Sync(ctx context.Context, defaultnamespace string, resources []*unstructured.Unstructured, options SyncOptions) ([]corev1.ObjectReference, error) {
	resources, err := SetResourceDefaultNamespace(a.Client, defaultnamespace, resources)
	if err != nil {
		return nil, err
	}
	log := logr.FromContextOrDiscard(ctx)
	errs := []string{}
	newmanaged := []corev1.ObjectReference{}
	if options.CreateNamespace {
		if err := a.createNsIfNotExists(ctx, defaultnamespace); err != nil {
			return nil, err
		}
	}
	// apply
	diff := Diff(options.Managed, resources)
	for _, item := range append(diff.Creats, diff.Applys...) {
		log.Info("apply resource", "resource", item.GetObjectKind().GroupVersionKind().String(), "name", item.GetName(), "namespace", item.GetNamespace())

		if err := ApplyResource(ctx, a.Client, item, ApplyOptions{ServerSideApply: options.ServerSideApply}); err != nil {
			err = fmt.Errorf("create: %s %s/%s: %v", item.GetObjectKind().GroupVersionKind().String(), item.GetNamespace(), item.GetName(), err)
			log.Error(err, "creating resource")
			errs = append(errs, err.Error())
			continue
		}
		newmanaged = append(newmanaged, GetReference(item)) // set managed
	}
	// remove
	for _, item := range diff.Removes {
		if IsCRD(item) && !options.CleanCRD {
			continue
		}
		partial := item
		log.Info("deleting resource", "resource", partial.GetObjectKind().GroupVersionKind().String(), "name", partial.GetName(), "namespace", partial.GetNamespace())
		if err := a.Client.Delete(ctx, partial, &client.DeleteOptions{}); err != nil {
			if !apierrors.IsNotFound(err) {
				err = fmt.Errorf("remove: %s %s/%s: %v", partial.GetObjectKind().GroupVersionKind().String(), partial.GetNamespace(), partial.GetName(), err)
				log.Error(err, "deleting resource")
				errs = append(errs, err.Error())
				// if not removed, keep in managed
				newmanaged = append(newmanaged, GetReference(item)) // set managed
				continue
			}
		}
	}
	// sort manged
	slices.SortFunc(newmanaged, func(i, j corev1.ObjectReference) int { return strings.Compare(i.APIVersion, j.APIVersion) })
	if len(errs) > 0 {
		return newmanaged, errors.New(strings.Join(errs, "\n"))
	} else {
		return newmanaged, nil
	}
}

func (a Apply) createNsIfNotExists(ctx context.Context, name string) error {
	if name == "" {
		return nil
	}
	ns := &corev1.Namespace{ObjectMeta: metav1.ObjectMeta{Name: name}}
	_, err := controllerutil.CreateOrUpdate(ctx, a.Client, ns, func() error { return nil })
	return err
}

type ApplyOptions struct {
	ServerSideApply bool
	FieldOwner      string
}

func ApplyResource(ctx context.Context, cli client.Client, obj client.Object, options ApplyOptions) error {
	// use typed object to merge
	isunstructured := false
	if _, isunstructured = obj.(*unstructured.Unstructured); isunstructured {
		gvk := obj.GetObjectKind().GroupVersionKind()
		if cli.Scheme().Recognizes(gvk) {
			typedobj, err := cli.Scheme().ConvertToVersion(obj, gvk.GroupVersion())
			if err != nil {
				return err
			}
			obj = typedobj.(client.Object)
			isunstructured = false
		}
	}
	exists, _ := obj.DeepCopyObject().(client.Object)
	if err := cli.Get(ctx, client.ObjectKeyFromObject(exists), exists); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		return cli.Create(ctx, obj)
	}

	var patch client.Patch
	var patchoptions []client.PatchOption
	if options.ServerSideApply {
		if options.FieldOwner == "" {
			options.FieldOwner = DefaultFieldOwner
		}
		obj.SetManagedFields(nil)
		patch = client.Apply
		patchoptions = append(patchoptions,
			client.FieldOwner(options.FieldOwner),
			client.ForceOwnership,
		)
	} else {
		if isunstructured || IsCRD(obj) {
			patch = client.MergeFrom(exists)
		} else {
			patch = client.StrategicMergeFrom(exists)
		}
	}
	// patch
	return cli.Patch(ctx, obj, patch, patchoptions...)
}

func IsCRD(obj client.Object) bool {
	gvk := obj.GetObjectKind().GroupVersionKind()
	return gvk.Group == "apiextensions.k8s.io" && gvk.Kind == "CustomResourceDefinition"
}

func SetResourceDefaultNamespace[T client.Object](cli client.Client, ns string, list []T) ([]T, error) {
	for i, item := range list {
		isScoped, err := cli.IsObjectNamespaced(item)
		if err != nil {
			return nil, err
		}
		if isScoped && item.GetNamespace() == "" {
			item.SetNamespace(ns)
		}
		list[i] = item
	}
	return list, nil
}

func NamespacedScopeOfGVK(cli client.Client, gvk schema.GroupVersionKind) (apimeta.RESTScopeName, error) {
	restmapping, err := cli.RESTMapper().RESTMapping(gvk.GroupKind())
	if err != nil {
		return "", fmt.Errorf("failed to get restmapping: %w", err)
	}
	return restmapping.Scope.Name(), nil
}

func NamespacedScopeOf(cli client.Client, obj runtime.Object) (apimeta.RESTScopeName, error) {
	gvk, err := apiutil.GVKForObject(obj, cli.Scheme())
	if err != nil {
		return "", err
	}
	return NamespacedScopeOfGVK(cli, gvk)
}

func GetReference(obj client.Object) corev1.ObjectReference {
	return corev1.ObjectReference{
		APIVersion: obj.GetObjectKind().GroupVersionKind().GroupVersion().String(),
		Kind:       obj.GetObjectKind().GroupVersionKind().Kind,
		Namespace:  obj.GetNamespace(),
		Name:       obj.GetName(),
	}
}
