package application

import (
	"context"
	"fmt"
	"strings"

	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	corecluster "xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/cluster/metadata"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/market"
)

const CheckApplicationCluster = false

func CreateApplication(ctx context.Context, root store.Store, scopes []store.Scope, app *Application) (*Application, error) {
	app.Scopes = scopes
	if err := CheckOnApplicationChange(ctx, root, app); err != nil {
		return nil, err
	}
	return app, root.Scope(scopes...).Create(ctx, app)
}

func GetApplication(ctx context.Context, root store.Store, appref store.ObjectReference) (*Application, error) {
	app := &Application{}
	if err := root.Scope(appref.Scopes...).Get(ctx, appref.Name, app); err != nil {
		return nil, err
	}
	return app, nil
}

func UpdateApplication(ctx context.Context, root store.Store, scopes []store.Scope, app *Application) (*Application, error) {
	app.Scopes = scopes
	if err := CheckOnApplicationChange(ctx, root, app); err != nil {
		return nil, err
	}
	return app, root.Scope(app.Scopes...).Update(ctx, app)
}

func DeleteApplication(ctx context.Context, root store.Store, appref store.ObjectReference) (*Application, error) {
	app := &Application{ObjectMeta: store.ObjectMeta{Name: appref.Name}}
	// delete application with foreground propagation, wait until all sub resources deleted
	return app, root.Scope(appref.Scopes...).Delete(ctx, app, store.WithDeletePropagation(store.DeletePropagationForeground))
}

func PauseApplication(ctx context.Context, storage store.Store, appref store.ObjectReference) (*Application, error) {
	app := &Application{ObjectMeta: store.ObjectMeta{Name: appref.Name}}
	if err := storage.Scope(appref.Scopes...).Patch(ctx, app, base.MergePatchFromStruct(map[string]any{"paused": true})); err != nil {
		return nil, err
	}
	return app, nil
}

func ResumeApplication(ctx context.Context, storage store.Store, appref store.ObjectReference) (*Application, error) {
	app := &Application{ObjectMeta: store.ObjectMeta{Name: appref.Name}}
	if err := storage.Scope(appref.Scopes...).Patch(ctx, app, base.MergePatchFromStruct(map[string]any{"paused": false})); err != nil {
		return nil, err
	}
	return app, nil
}

func CheckOnApplicationChange(ctx context.Context, root store.Store, app *Application) error {
	if err := base.ValidateName(app.Name); err != nil {
		return err
	}
	if app.Product.Name == "" {
		return liberrors.NewBadRequest(".product.name is required")
	}
	if app.Cluster.Namespace == "" {
		app.Cluster.Namespace = getNamespaceFromScopes(app.Scopes)
	}
	marketapp, selectversion, err := SelectProductVersion(ctx, root, app.Product.Name, app.Product.Version)
	if err != nil {
		return err
	}

	if CheckApplicationCluster {
		// check cluster allowed
		tenant, org := base.TenantOrganizationFromScopes(app.Scopes...)
		ok, err := metadata.PermitCluster(ctx, root, app.Cluster.ObjectReference, tenant, org)
		if err != nil {
			return err
		}
		if !ok {
			return fmt.Errorf("deployment to cluster %s is not allowed", app.Cluster.Name)
		}
	}

	// check cluster
	cluster := &corecluster.Cluster{}
	if err := root.Scope(app.Cluster.Scopes...).Get(ctx, app.Cluster.Name, cluster); err != nil {
		return err
	}
	// check cluster match
	if corecluster.ClusterCategoryFrom(cluster.Type) != marketapp.Type {
		return fmt.Errorf("application type %s can not deploy to cluster type %s", marketapp.Type, cluster.Type)
	}
	// fill status for frontend
	fillProductInfo(app, marketapp, selectversion.Version)
	fillClusterInfo(app, cluster)
	app.Status.Product = ApplicationReferenceExtendFromProduct(marketapp, selectversion.Version)

	// check license
	if marketapp.License.Enabled {
		if app.License.Name == "" {
			return liberrors.NewBadRequest("license is required")
		}
		// check same license already used by other application
		applist := &store.List[Application]{}
		if err := root.Scope(app.Scopes...).List(ctx, applist); err != nil {
			return err
		}
		for _, item := range applist.Items {
			if item.Name == app.Name {
				continue
			}
			if item.License.Name != "" && item.License.Name == app.License.Name {
				return fmt.Errorf("license %s is already used by application %s", app.License.Name, item.Name)
			}
		}
		if _, err := GetCheckLicense(ctx, root, app, false); err != nil {
			return err
		} else {
			app.Status.License = ApplicationLicenseStatus{Required: true}
		}
	}
	return nil
}

func GetCheckLicense(ctx context.Context, root store.Store, app *Application, dryrun bool) (*license.License, error) {
	licenseref := app.License
	if licenseref.Name == "" {
		return nil, fmt.Errorf("application %s requires a license", app.Name)
	}
	license := &license.License{}
	if err := root.Scope(app.Scopes...).Get(ctx, licenseref.Name, license); err != nil {
		return nil, err
	}
	if !license.Status.Valid {
		return nil, fmt.Errorf("license %s is not valid", licenseref.Name)
	}
	if license.For != "" && license.For != app.Product.Name {
		return nil, fmt.Errorf("license %s is not for product %s", licenseref.Name, app.Product.Name)
	}
	if usedby := license.Status.UsedBy; usedby != nil && !usedby.Equals(store.ResourcedObjectReferenceFrom(app)) {
		return nil, fmt.Errorf("license %s is currently used by %s", licenseref.Name, usedby.String())
	}
	return license, nil
}

func getNamespaceFromScopes(scopes []store.Scope) string {
	list := []string{}
	for _, scope := range scopes {
		switch scope.Resource {
		// allow tenant or tenant organization
		case "tenants", "organizations":
			list = append(list, scope.Name)
		// subapp use same namespace with app, so skip it
		case "applications":
		}
	}
	return strings.Join(list, "-")
}

// fillProductInfo fill product info back to application in oder to show in frontend
func fillProductInfo(app *Application, product *market.Product, version string) {
	if app.Labels == nil {
		app.Labels = make(map[string]string)
	}
	app.Labels[LabelApplicationCategory] = product.Category
	app.Labels[LabelApplicationSubCategory] = product.SubCategory
	app.Labels[LabelApplicationProduct] = product.Name
	if productaanotations := product.Annotations; productaanotations != nil {
		// filter used for
		app.Labels[LabelApplicationUsedFor] = productaanotations[LabelApplicationUsedFor]
	}
	if version != "" {
		app.Product.Version = version
	}
}

func fillClusterInfo(app *Application, cluster *cluster.Cluster) {
	app.Cluster.Annotations = cluster.Annotations
	app.Cluster.Labels = cluster.Labels
	app.Cluster.Type = cluster.Type
}
