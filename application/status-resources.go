package application

import (
	"context"
	"fmt"
	"net/http"

	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/cluster"
	cloud "xiaoshiai.cn/core/ismc/common"
)

var ErrUnsupportedOperation = fmt.Errorf("unsupported operation")

type ResourceOperationInterface interface {
	// common
	List(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Get(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Patch(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Update(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Delete(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)

	// container
	Events(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	SubResource(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Exec(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Log(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	ListResourcePods(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Children(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)

	// vms
	VNC(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
	Power(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error)
}

func (a *ApplicationStatusAPI) ListResource(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.List(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) GetResource(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Get(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) UpdtaeResource(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		if app.Paused {
			return nil, liberrors.NewForbidden(meta.GroupVersionResource.String(), meta.Name, fmt.Errorf("application is paused"))
		}
		return op.Update(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) PatchResource(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		if app.Paused {
			return nil, liberrors.NewForbidden(meta.GroupVersionResource.String(), meta.Name, fmt.Errorf("application is paused"))
		}
		return op.Patch(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) DeleteResource(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Delete(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) SubResource(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.SubResource(w, r, app, meta)
	})
}

func (a *ApplicationStatusAPI) Exec(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Exec(w, r, app, meta)
	})
}

func (a *ApplicationStatusAPI) ListResourcePods(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.ListResourcePods(w, r, app, meta)
	})
}

func (a *ApplicationStatusAPI) ListResourceChildren(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Children(w, r, app, meta)
	})
}

func (a *ApplicationStatusAPI) ListEvents(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Events(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) Log(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Log(w, r, app, meta)
	})
}

func (a *ApplicationStatusAPI) VNC(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.VNC(w, r, app, meta)
	})
}

func (a *ApplicationStatusAPI) SetPower(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationResourceOperation(w, r, func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error) {
		return op.Power(r, app, meta)
	})
}

func (a *ApplicationStatusAPI) OnApplicationResourceOperation(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, op ResourceOperationInterface, app *Application, meta cluster.RequestMetadata) (any, error)) {
	a.OnApplicationCluster(w, r, func(ctx context.Context, info cluster.CloudInfo, app *Application) (any, error) {
		meta := cluster.ResourceMetaFromRequest(r)
		meta.Namespace = app.Cluster.Namespace

		switch cluster.ClusterCategoryFrom(info.Type()) {
		case cluster.ClusterCategoryContainer:
			op, err := cluster.NewContainerOperation(info)
			if err != nil {
				return nil, err
			}
			lister := &ContainerResourceLister{Operation: op}
			return f(ctx, lister, app, meta)
		case cluster.ClusterCategoryVirtualMachine:
			cloud, err := info.CloudProvider()
			if err != nil {
				return nil, err
			}
			lister := &CloudResourceOperation{Client: CloudClient{Cloud: cloud}}
			return f(ctx, lister, app, meta)
		default:
			return nil, fmt.Errorf("unsupported cluster type %s", info.Type())
		}
	})
}

func (a *ApplicationStatusAPI) OnApplicationContainerClusterMeta(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, op cluster.ContainerOperation, app *Application, meta cluster.RequestMetadata) (any, error)) {
	a.OnApplicationCluster(w, r, func(ctx context.Context, info cluster.CloudInfo, app *Application) (any, error) {
		op, err := cluster.NewContainerOperation(info)
		if err != nil {
			return nil, err
		}
		meta := cluster.ResourceMetaFromRequest(r)
		meta.Namespace = app.Cluster.Namespace
		return f(ctx, op, app, meta)
	})
}

func (a *ApplicationStatusAPI) OnApplicationContainerCluster(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, op cluster.ContainerOperation, app *Application) (any, error)) {
	a.OnApplicationCluster(w, r, func(ctx context.Context, info cluster.CloudInfo, app *Application) (any, error) {
		op, err := cluster.NewContainerOperation(info)
		if err != nil {
			return nil, err
		}
		return f(ctx, op, app)
	})
}

func (a *ApplicationStatusAPI) OnApplicationCloudCluster(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, op cloud.Provider, app *Application) (any, error)) {
	a.OnApplicationCluster(w, r, func(ctx context.Context, op cluster.CloudInfo, app *Application) (any, error) {
		cloud, err := op.CloudProvider()
		if err != nil {
			return nil, err
		}
		return f(ctx, cloud, app)
	})
}

func (a *ApplicationStatusAPI) OnApplicationCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, info cluster.CloudInfo, app *Application) (any, error)) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error) {
		app := &Application{}
		if err := a.Store.Scope(appref.Scopes...).Get(ctx, appref.Name, app); err != nil {
			return nil, err
		}
		info, err := a.CloudInfo.Get(ctx, app.Cluster.ObjectReference)
		if err != nil {
			return nil, err
		}
		// backward compatibility
		if app.Cluster.Type == "" {
			app.Cluster.Type = info.Type()
		}
		return fn(ctx, info, app)
	})
}

func (a *ApplicationStatusAPI) resourcesGroup() api.Group {
	return api.
		NewGroup("/resources").
		Route(
			api.GET("").
				Doc("List application resources").
				To(a.ListResource).Response([]any{}),
			api.GET("/{group}/{version}/{resource}").
				Doc("List application resource items").
				To(a.ListResource),
			api.GET("/{group}/{version}/{resource}/{name}").
				Doc("Get application resource").
				To(a.GetResource),
			api.DELETE("/{group}/{version}/{resource}/{name}").
				Doc("Delete application resource").
				To(a.DeleteResource),
			api.PUT("/{group}/{version}/{resource}/{name}").
				Doc("Update application resource").
				To(a.UpdtaeResource),
			api.PATCH("/{group}/{version}/{resource}/{name}").
				Doc("Patch application resource").
				To(a.PatchResource),
			api.GET("/{group}/{version}/{resource}/{name}/events").
				Doc("List application resource events").
				To(a.ListEvents),
			api.Any("/{group}/{version}/{resource}/{name}/{subresource}*").
				Doc("Subresource").
				To(a.SubResource),
			api.GET("/{group}/{version}/{resource}/{name}:exec").
				Doc("Execute command in pod").
				To(a.Exec),
			api.GET("/{group}/{version}/{resource}/{name}:log").
				Doc("Get pod logs").
				To(a.Log),
			// deployment/statefulset/daemonset pods
			api.GET("/{group}/{version}/{resource}/{name}:pods").
				Doc("List pods of application resource").
				To(a.ListResourcePods),
			api.GET("/{group}/{version}/{resource}/{name}:children").
				Doc("List pods of application resource").
				To(a.ListResourceChildren),

			// vm
			api.GET("/{group}/{version}/{resource}/{name}/vnc").
				Doc("vnc").
				To(a.VNC),
			api.Any("/{group}/{version}/{resource}/{name}/vnc/{path}*").
				Doc("vnc").
				To(a.VNC),
			api.POST("/{group}/{version}/{resource}/{name}/power").
				Doc("Set virtual machine power").
				To(a.SetPower),
		)
}
