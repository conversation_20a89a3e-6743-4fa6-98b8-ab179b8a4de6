package application

import (
	"context"
	"fmt"
	"net/http"
	"slices"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/selection"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/controller-runtime/pkg/client"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/application/kubeblocks"
	"xiaoshiai.cn/core/cluster"
)

var (
	GroupKindPod         = schema.GroupKind{Group: "", Kind: "Pod"}
	GroupKindJob         = schema.GroupKind{Group: "batch", Kind: "Job"}
	GroupKindCronJob     = schema.GroupKind{Group: "batch", Kind: "CronJob"}
	GroupKindReplicaSet  = schema.GroupKind{Group: "apps", Kind: "ReplicaSet"}
	GroupKindDeployment  = schema.GroupKind{Group: "apps", Kind: "Deployment"}
	GroupKindSecret      = schema.GroupKind{Group: "", Kind: "Secret"}
	GroupKindStatefulSet = schema.GroupKind{Group: "apps", Kind: "StatefulSet"}
	GroupKindDaemonSet   = schema.GroupKind{Group: "apps", Kind: "DaemonSet"}
	GroupKindService     = schema.GroupKind{Group: "", Kind: "Service"}
	GroupKindConfigMap   = schema.GroupKind{Group: "", Kind: "ConfigMap"}
)

var ResourceParents = map[schema.GroupKind][]schema.GroupKind{
	GroupKindPod: {
		GroupKindReplicaSet,
		GroupKindStatefulSet,
		GroupKindDaemonSet,
		GroupKindJob,
		kubeblocks.GroupVersionKindKubeBlocksInstanceSets.GroupKind(),
	},
	GroupKindService: {
		kubeblocks.GroupVersionKindKubeBlocksComponents.GroupKind(),
		kubeblocks.GroupVersionKindKubeBlocksInstanceSets.GroupKind(),
	},
	GroupKindConfigMap: {
		kubeblocks.GroupVersionKindKubeBlocksComponents.GroupKind(),
		kubeblocks.GroupVersionKindKubeBlocksClusters.GroupKind(),
		kubeblocks.GroupVersionKindKubeBlocksConfigurations.GroupKind(),
	},
	GroupKindSecret: {
		kubeblocks.GroupVersionKindKubeBlocksComponents.GroupKind(),
		kubeblocks.GroupVersionKindKubeBlocksClusters.GroupKind(),
		kubeblocks.GroupVersionKindKubeBlocksConfigurations.GroupKind(),
	},
	kubeblocks.GroupVersionKindKubeBlocksBackups.GroupKind(): {
		kubeblocks.GroupVersionKindKubeBlocksBackupSchedules.GroupKind(),
	},
	kubeblocks.GroupVersionKindKubeBlocksInstanceSets.GroupKind(): {
		kubeblocks.GroupVersionKindKubeBlocksComponents.GroupKind(),
	},
	kubeblocks.GroupVersionKindKubeBlocksComponents.GroupKind(): {
		kubeblocks.GroupVersionKindKubeBlocksClusters.GroupKind(),
	},
	GroupKindReplicaSet: {
		GroupKindDeployment,
	},
	GroupKindJob: {
		GroupKindCronJob,
	},
}

func IsParentResource(parent runtime.Object, child schema.GroupKind) bool {
	parentgk := parent.GetObjectKind().GroupVersionKind().GroupKind()
	if parents, ok := ResourceParents[child]; ok {
		for _, p := range parents {
			if p == parentgk || IsParentResource(parent, p) {
				return true
			}
		}
	}
	return false
}

var _ ResourceOperationInterface = &ContainerResourceLister{}

type ContainerResourceLister struct {
	Operation cluster.ContainerOperation
}

// Exec implements ResourceListerInterface.
func (c *ContainerResourceLister) Exec(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, true); err != nil {
		return nil, err
	}
	return c.Operation.PodExec(w, r, meta)
}

// Events implements ResourceListerInterface.
func (c *ContainerResourceLister) Events(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, false); err != nil {
		return nil, err
	}
	return c.Operation.ListEvents(r, meta)
}

// Children implements ResourceListerInterface.
func (c *ContainerResourceLister) Children(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, false); err != nil {
		return nil, err
	}
	return c.Operation.ListResourceChildren(w, r, meta)
}

// ListResourcePods implements ResourceListerInterface.
func (c *ContainerResourceLister) ListResourcePods(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, false); err != nil {
		return nil, err
	}
	return c.Operation.ListResourcePods(w, r, meta)
}

// Log implements ResourceListerInterface.
func (c *ContainerResourceLister) Log(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, false); err != nil {
		return nil, err
	}
	return c.Operation.PodLog(w, r, meta)
}

// SubResource implements ResourceListerInterface.
func (c *ContainerResourceLister) SubResource(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, true); err != nil {
		return nil, err
	}
	return c.Operation.SubResource(w, r, meta)
}

// VNC implements ResourceListerInterface.
func (c *ContainerResourceLister) VNC(w http.ResponseWriter, r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	return nil, ErrUnsupportedOperation
}

// Power implements ResourceListerInterface.
func (c *ContainerResourceLister) Power(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	return nil, ErrUnsupportedOperation
}

// List implements ResourceListerInterface.
func (c *ContainerResourceLister) List(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	return c.ListResources(r.Context(), app, meta)
}

func (c *ContainerResourceLister) ListResources(ctx context.Context, app *Application, meta cluster.RequestMetadata) ([]runtime.Object, error) {
	lister := ResourceLister{
		Client: c.Operation.Info.Client,
	}
	if meta.GroupVersionResource.Empty() {
		return lister.ListResourcesMactchParents(ctx, app, meta.Namespace, app.Status.Objects, schema.GroupKind{})
	}
	gvk, err := lister.Client.RESTMapper().KindFor(meta.GroupVersionResource)
	if err != nil {
		return nil, err
	}
	switch gvk {
	case corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaim"):
		// since we inject common labels to resources, simplely list by labels is ok.
		matchlabels := CommonLabelsFromApp(app)
		return lister.ListResourcesMactchLabels(ctx, meta.Namespace, matchlabels, meta.GroupVersionResource)
	default:
		return lister.ListResourcesMactchParents(ctx, app, meta.Namespace, app.Status.Objects, gvk.GroupKind())
	}
}

// Get implements ResourceListerInterface.
func (c *ContainerResourceLister) Get(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, false); err != nil {
		return nil, err
	}
	return c.Operation.Get(r, meta)
}

// Update implements ResourceListerInterface.
func (c *ContainerResourceLister) Update(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, true); err != nil {
		return nil, err
	}
	return c.Operation.Update(r, meta)
}

// Patch implements ResourceListerInterface.
func (c *ContainerResourceLister) Patch(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, true); err != nil {
		return nil, err
	}
	return c.Operation.Patch(r, meta)
}

// Delete implements ResourceListerInterface.
func (c *ContainerResourceLister) Delete(r *http.Request, app *Application, meta cluster.RequestMetadata) (any, error) {
	if err := c.checkPermit(r.Context(), app, meta, true); err != nil {
		return nil, err
	}
	return c.Operation.Delete(r, meta)
}

func (c *ContainerResourceLister) checkPermit(_ context.Context, _ *Application, meta cluster.RequestMetadata, isWrite bool) error {
	// if is node resource, only allow read
	// no any application can control node
	// node resource use to get node metrics only now
	if meta.GroupVersionResource == corev1.SchemeGroupVersion.WithResource("nodes") {
		if isWrite {
			return liberrors.NewForbidden(meta.GroupVersionResource.String(), meta.Name, fmt.Errorf("readonly"))
		}
	}
	// TODO: permission check
	return nil
}

var kindOrder = map[string]int{
	"Pod":         50,
	"Deployment":  45,
	"Service":     40,
	"ConfigMap":   30,
	"Secret":      20,
	"CronJob":     15,
	"Job":         10,
	"ReplicaSet":  5,
	"StatefulSet": 4,
	"DaemonSet":   3,
}

func sortResources(resources []runtime.Object) {
	slices.SortFunc(resources, func(a, b runtime.Object) int {
		av, bv := kindOrder[a.GetObjectKind().GroupVersionKind().Kind], kindOrder[b.GetObjectKind().GroupVersionKind().Kind]
		if av != bv {
			if av < bv {
				return 1
			}
			return -1
		}
		// by creationtimetamp
		ameta, err := meta.Accessor(a)
		if err != nil {
			return 0
		}
		bmeta, err := meta.Accessor(b)
		if err != nil {
			return 0
		}

		at := ameta.GetCreationTimestamp()
		bt := bmeta.GetCreationTimestamp()
		if at.Equal(&bt) {
			return strings.Compare(ameta.GetName(), bmeta.GetName())
		}
		if at.Before(&bt) {
			return 1
		} else {
			return -1
		}
	})
}

type ResourceLister struct {
	Client client.Client
}

func (a ResourceLister) ListResourcesMactchLabels(ctx context.Context, namespace string, labels map[string]string, match schema.GroupVersionResource) ([]runtime.Object, error) {
	gvk, err := a.Client.RESTMapper().KindFor(match)
	if err != nil {
		return nil, err
	}
	obj := a.NewObjectListFromGVK(gvk)
	if err := a.Client.List(ctx, obj, client.InNamespace(namespace), client.MatchingLabels(labels)); err != nil {
		return nil, err
	}
	list, err := meta.ExtractList(obj)
	if err != nil {
		return nil, err
	}
	list = a.completeMoreDetails(ctx, namespace, list)
	sortResources(list)
	return list, nil
}

func (a ResourceLister) ListResourcesMactchParents(ctx context.Context, app *Application, namespace string, parents []ObjectReference, match schema.GroupKind) ([]runtime.Object, error) {
	allresources := []runtime.Object{}
	for _, parent := range parents {
		if !matchGroupKind(&parent, match) && !IsParentResource(&parent, match) {
			continue
		}
		obj := a.NewObjectFromGVK(parent.GroupVersionKind())
		if err := a.Client.Get(ctx, client.ObjectKey{Namespace: namespace, Name: parent.Name}, obj); err != nil {
			if apierrors.IsNotFound(err) {
				continue
			}
			return nil, err
		}
		if matchGroupKind(obj, match) {
			allresources = append(allresources, obj)
		}
		children, err := a.ListChildren(ctx, app, namespace, obj, match)
		if err != nil {
			return nil, err
		}
		allresources = append(allresources, children...)
	}
	allresources = a.completeMoreDetails(ctx, namespace, allresources)
	sortResources(allresources)
	return allresources, nil
}

func (a ResourceLister) NewObjectFromGVK(gvk schema.GroupVersionKind) client.Object {
	obj, _ := a.Client.Scheme().New(gvk)
	newobj, ok := obj.(client.Object)
	if !ok {
		uns := &unstructured.Unstructured{}
		uns.SetAPIVersion(gvk.GroupVersion().String())
		uns.SetKind(gvk.Kind)
		return uns
	}
	return newobj
}

func (a ResourceLister) NewObjectListFromGVK(gvk schema.GroupVersionKind) client.ObjectList {
	obj, _ := a.Client.Scheme().New(gvk)
	newobj, ok := obj.(client.ObjectList)
	if !ok {
		uns := &unstructured.UnstructuredList{}
		uns.SetAPIVersion(gvk.GroupVersion().String())
		uns.SetKind(gvk.Kind)
		return uns
	}
	return newobj
}

func (a ResourceLister) ListChildren(ctx context.Context, app *Application, namespace string, parent client.Object, match schema.GroupKind) ([]runtime.Object, error) {
	if !match.Empty() && !IsParentResource(parent, match) {
		return nil, nil
	}
	if strings.HasSuffix(parent.GetObjectKind().GroupVersionKind().Group, KubeBlocksGroupSuffix) {
		return a.listKubeBlocksResources(ctx, app, namespace, parent, match)
	}
	switch val := parent.(type) {
	case *appsv1.Deployment:
		childrenKinds := []schema.GroupKind{
			GroupKindReplicaSet,
			GroupKindPod,
		}
		if !match.Empty() && !slices.Contains(childrenKinds, match) {
			return nil, nil
		}
		replicas := &appsv1.ReplicaSetList{}
		if err := a.Client.List(ctx, replicas, client.InNamespace(namespace), client.MatchingLabels(val.Spec.Selector.MatchLabels)); err != nil {
			return nil, err
		}
		var allmatches []runtime.Object
		for i, replica := range replicas.Items {
			if !hasOwner(&replica, val) {
				continue
			}
			if match.Empty() || match == GroupKindReplicaSet {
				allmatches = append(allmatches, &replicas.Items[i])
			}
			children, err := a.ListChildren(ctx, app, namespace, &replica, match)
			if err != nil {
				return nil, err
			}
			allmatches = append(allmatches, children...)
		}
		return allmatches, nil
	case *batchv1.CronJob:
		childrenKinds := []schema.GroupKind{
			GroupKindJob,
			GroupKindPod,
		}
		if !match.Empty() && !slices.Contains(childrenKinds, match) {
			return nil, nil
		}
		jobs := &batchv1.JobList{}
		if err := a.Client.List(ctx, jobs,
			client.InNamespace(namespace),
			// todo: how to filter by cronjob?
		); err != nil {
			return nil, err
		}
		var allchildren []runtime.Object
		for i, job := range jobs.Items {
			if !hasOwner(&job, val) {
				continue
			}
			if matchGroupKind(&job, match) {
				allchildren = append(allchildren, &jobs.Items[i])
			}
			children, err := a.ListChildren(ctx, app, namespace, &job, match)
			if err != nil {
				return nil, err
			}
			allchildren = append(allchildren, children...)
		}
		return allchildren, nil
	case *appsv1.ReplicaSet:
		return listPodsMatch(ctx, a.Client, namespace, val, client.MatchingLabels(val.Spec.Selector.MatchLabels), match)
	case *batchv1.Job:
		req, err := labels.NewRequirement("controller-uid", selection.In, []string{string(val.UID)})
		if err != nil {
			return nil, err
		}
		listopt := client.MatchingLabelsSelector{Selector: labels.NewSelector().Add(*req)}
		return listPodsMatch(ctx, a.Client, namespace, val, listopt, match)
	case *appsv1.StatefulSet:
		return listPodsMatch(ctx, a.Client, namespace, val, client.MatchingLabels(val.Spec.Selector.MatchLabels), match)
	case *appsv1.DaemonSet:
		return listPodsMatch(ctx, a.Client, namespace, val, client.MatchingLabels(val.Spec.Selector.MatchLabels), match)
	}
	return nil, nil
}

func matchGroupKind(obj runtime.Object, match schema.GroupKind) bool {
	if match.Empty() {
		return true
	}
	return obj.GetObjectKind().GroupVersionKind().GroupKind() == match
}

func listPodsMatch(ctx context.Context, cli client.Client, namespace string, owner client.Object, listopt client.ListOption, match schema.GroupKind) ([]runtime.Object, error) {
	if !match.Empty() && match != GroupKindPod {
		return nil, nil
	}
	pods := &corev1.PodList{}
	if err := cli.List(ctx, pods, client.InNamespace(namespace), listopt); err != nil {
		return nil, err
	}
	allpods := make([]runtime.Object, 0, len(pods.Items))
	for i, pod := range pods.Items {
		if owner != nil && !hasOwner(&pod, owner) {
			continue
		}
		allpods = append(allpods, &pods.Items[i])
	}
	return allpods, nil
}

func hasOwner(obj client.Object, owner client.Object) bool {
	for _, ref := range obj.GetOwnerReferences() {
		if ref.UID == owner.GetUID() {
			return true
		}
	}
	return false
}

func (a ResourceLister) completeMoreDetails(ctx context.Context, ns string, list []runtime.Object) []runtime.Object {
	// complete pvc pods
	list = a.completePvcPods(ctx, ns, list)
	return list
}

func (a ResourceLister) completePvcPods(ctx context.Context, ns string, list []runtime.Object) []runtime.Object {
	var pods []corev1.Pod
	for i, obj := range list {
		if obj.GetObjectKind().GroupVersionKind() == corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaim") {
			if pods == nil {
				podlist := &corev1.PodList{}
				_ = a.Client.List(ctx, podlist, client.InNamespace(ns))
				pods = podlist.Items
			}
			metaobj, err := meta.Accessor(obj)
			if err != nil {
				continue
			}
			usepodsname := sets.New[string]()
			for _, pod := range pods {
				for _, volume := range pod.Spec.Volumes {
					if volume.PersistentVolumeClaim != nil && volume.PersistentVolumeClaim.ClaimName == metaobj.GetName() {
						usepodsname.Insert(pod.GetName())
						break
					}
				}
			}
			if len(usepodsname) == 0 {
				continue
			}
			unsobj, err := runtime.DefaultUnstructuredConverter.ToUnstructured(obj)
			if err != nil {
				continue
			}
			unstructured.SetNestedStringSlice(unsobj, sets.List(usepodsname), "status", "mountedByPods")
			list[i] = &unstructured.Unstructured{Object: unsobj}
		}
	}
	return list
}
