package core

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/garbagecollector"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/core/agent"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/auth/provider/casdoor"
	"xiaoshiai.cn/core/bootstrap"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/cluster/privatenode"
	"xiaoshiai.cn/core/cluster/resourcepool"
	"xiaoshiai.cn/core/edge/tunnel"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/loadbalancer"
	"xiaoshiai.cn/core/market"
	"xiaoshiai.cn/core/message"
	"xiaoshiai.cn/core/message/events"
	notify "xiaoshiai.cn/core/message/option"
	"xiaoshiai.cn/core/observerability"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/oss"
	"xiaoshiai.cn/core/pay"
	"xiaoshiai.cn/core/pay/cost"
	"xiaoshiai.cn/core/rbac"
	"xiaoshiai.cn/core/resourcequota"
	"xiaoshiai.cn/core/system/ownerinject"
	"xiaoshiai.cn/core/system/shared"
	"xiaoshiai.cn/core/tenant"
	"xiaoshiai.cn/core/wallet"
)

type ControllerOptions struct {
	Listen                string                    `json:"listen,omitempty"`
	LeaderElection        bool                      `json:"leaderElection,omitempty"`
	LeaderElectionTimeout time.Duration             `json:"leaderElectionTimeout,omitempty"`
	API                   *APIOptions               `json:"api,omitempty"`
	Etcd                  *etcdcache.Options        `json:"etcd,omitempty"`
	Mongodb               *mongo.MongoDBOptions     `json:"mongodb,omitempty"`
	OCI                   *artifact.OCIOptions      `json:"oci,omitempty"`
	ObjectStorage         *oss.ObjectStorageOptions `json:"objectStorage,omitempty"`
	Edge                  *agent.ServerOptions      `json:"edge,omitempty"`
	LoadBalancer          *loadbalancer.Options     `json:"loadBalancer,omitempty"`
	AuthCache             *auth.CacheOptions        `json:"authCache,omitempty"`
	Casdoor               *casdoor.Options          `json:"casdoor,omitempty"`
	Notify                *notify.NotifyOptions     `json:"notify,omitempty"`
	EnabledControllers    []string                  `json:"enabledControllers,omitempty"`
	Costs                 *cost.Options             `json:"costs,omitempty"`
	Pay                   *pay.Options              `json:"pay,omitempty"`
}

func NewdefaultControllerOptions() *ControllerOptions {
	edge := agent.NewDefaultServerOptions()
	edge.DeviceID = "controller-" + uuid.NewString()
	mongodb := mongo.NewDefaultMongoOptions(MongodbDatabase)
	return &ControllerOptions{
		Listen:                ":8080",
		LeaderElectionTimeout: 30 * time.Second,
		API:                   DefaultAPIOptions(),
		Etcd:                  etcdcache.NewDefaultOptions(),
		OCI:                   artifact.NewDefaultOCIOptions(),
		ObjectStorage:         oss.NewDefaultObjectStorageOptions(),
		Edge:                  edge,
		Mongodb:               mongodb,
		LoadBalancer:          loadbalancer.NewDefaultOptions(),
		Casdoor:               casdoor.NewDefaultOptions(),
		Notify:                notify.NewDefaultNotifyOptions(),
		EnabledControllers:    []string{},
		Costs:                 cost.NewDefaultOptions(),
		AuthCache:             auth.NewDefaultCacheOptions(),
		Pay:                   pay.NewDefaultOptions(),
	}
}

type ControllerDependencies struct {
	Store            store.Store
	MongoStore       *mongo.MongoStorage
	AuthProvider     auth.Provider
	CloudInfoHolder  cluster.CloudInfoHolder
	ObjectStorage    *oss.ObjectStorageService
	ArtifactService  *artifact.ArtifactService
	ControllerTunnel *ControllerTunnel
	// WalletService         wallet.WalletService
	ObjectProviderContext *oss.ObjectProviderContext
	// SKUService            product.SKUService
	LoadBalancerService loadbalancer.Loadbalancer
	// OrderService          order.OrderService
	// OrderEventQueue       queue.Queue
	EventsQueue queue.Queue
	Notify      *notify.NotifyOptions
	Recorder    events.Recorder
	Pay         *pay.PaySystem
}

func NewControllerDependencies(ctx context.Context, options *ControllerOptions) (*ControllerDependencies, error) {
	etcdstore, err := NewEtcdStore(ctx, options.Etcd)
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd store: %w", err)
	}
	mainstore := ownerinject.NewOwnerInjectStore(etcdstore, ownerinject.Options{
		InjectResources: GarbageCollectorResources,
	})

	mongostore, err := NewMongoStore(ctx, options.Mongodb)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongodb store: %w", err)
	}

	var authp auth.Provider
	casdoorauthp, err := casdoor.NewProvider(ctx, options.Casdoor)
	if err != nil {
		return nil, err
	}
	authp = auth.NewProviderWithCacheOptions(options.AuthCache, casdoorauthp)

	var osssvc *oss.ObjectStorageService
	if options.ObjectStorage.Enabled {
		osss, err := oss.NewObjectStorageService(ctx, mainstore, options.ObjectStorage)
		if err != nil {
			return nil, err
		}
		osssvc = osss
	}

	controllerTunnel, err := NewControllerTunnel(ctx, options.Edge)
	if err != nil {
		return nil, err
	}

	cloudinfo := cluster.NewDefaultCloudInfoHolder(ctx, controllerTunnel.Tunnel)

	artifactsvc, err := artifact.NewArtifactService(ctx, options.OCI)
	if err != nil {
		return nil, err
	}
	eventsquque := NewEventsQueue(mongostore)

	recorder := events.NewQueueRecorder(ctx, eventsquque, 64)

	objectProviderContext := oss.NewObjectProviderContext()

	loadbalancerService, err := loadbalancer.NewDefaultLoadbalancer(options.LoadBalancer)
	if err != nil {
		return nil, err
	}

	paysystem, err := pay.New(ctx, mainstore, mongostore, recorder, artifactsvc, options.Pay)
	if err != nil {
		return nil, err
	}

	return &ControllerDependencies{
		Store:            mainstore,
		MongoStore:       mongostore,
		AuthProvider:     authp,
		ObjectStorage:    osssvc,
		ArtifactService:  artifactsvc,
		CloudInfoHolder:  cloudinfo,
		ControllerTunnel: controllerTunnel,
		// WalletService:         walletservice,
		ObjectProviderContext: objectProviderContext,
		// SKUService:            skuservice,
		LoadBalancerService: loadbalancerService,
		// OrderService:          orderservice,
		// OrderEventQueue:       orderQueue,
		EventsQueue: eventsquque,
		Notify:      options.Notify,
		Recorder:    recorder,
		Pay:         paysystem,
	}, nil
}

func NewControllerTunnel(ctx context.Context, options *agent.ServerOptions) (*ControllerTunnel, error) {
	tunnel, err := agent.NewTunnel(ctx, options.DeviceID)
	if err != nil {
		return nil, err
	}
	return &ControllerTunnel{Tunnel: tunnel, Options: options}, nil
}

type ControllerTunnel struct {
	Tunnel  *agent.EdgeTunnel
	Options *agent.ServerOptions
}

func (t *ControllerTunnel) Connect(ctx context.Context, ready chan struct{}) error {
	randaddr := "controller-" + uuid.NewString()
	closeonce := sync.Once{}
	return t.Tunnel.ConnectUpstream(ctx, t.Options.Server, randaddr, func(i tunnel.Interface) {
		closeonce.Do(func() {
			close(ready)
		})
	})
}

func RunController(ctx context.Context, options *ControllerOptions) error {
	svc, err := NewControllerDependencies(ctx, options)
	if err != nil {
		return err
	}
	// init
	if err := bootstrap.InitStorage(ctx, svc.Store, svc.MongoStore, svc.Pay.SKUService); err != nil {
		return fmt.Errorf("bootstrap.InitStorage: %w", err)
	}

	controller, err := NewControllerManager(ctx, svc, options)
	if err != nil {
		return err
	}
	api := api.
		New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
		)

	tunnelconnected := make(chan struct{})

	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		// wait for tunnel connected
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-tunnelconnected:
			return controller.Run(ctx)
		}
	})
	eg.Go(func() error {
		return api.Serve(ctx, options.Listen)
	})
	eg.Go(func() error {
		return svc.ControllerTunnel.Connect(ctx, tunnelconnected)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

type ControllerManager struct {
	manager *controller.ControllerManager
	svc     *ControllerDependencies
}

func NewControllerManager(ctx context.Context, svc *ControllerDependencies, options *ControllerOptions) (*ControllerManager, error) {
	storage := svc.Store
	manager := controller.NewControllerManager()
	if options.LeaderElection {
		manager.WithStoreLeaderElection(storage, "bob-controller", options.LeaderElectionTimeout)
	}
	if len(options.EnabledControllers) > 0 {
		manager.EnableController(options.EnabledControllers...)
	}
	cm := &ControllerManager{
		manager: manager,
		svc:     svc,
	}

	clusters, err := cluster.NewClusterController(ctx, storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(clusters)

	apps, err := application.NewTenantApplicationController(ctx, storage, svc.ArtifactService, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(apps)

	appslicense, err := application.NewTenantApplicationLicenseController(storage)
	if err != nil {
		return nil, err
	}
	manager.AddController(appslicense)

	licenses, err := license.NewTenantLicenseController(storage, svc.Recorder, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(licenses)

	quotas, err := resourcequota.NewResourceQuotaController(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(quotas)

	quotaclean, err := resourcequota.NewResourceQuotaCleanController(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(quotaclean)

	tenants, err := tenant.NewTenantController(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(tenants)

	// TODO: remove this controller
	if svc.ObjectStorage != nil {
		osscontroller, err := oss.NewTenantObjectStorageController(svc.ObjectStorage)
		if err != nil {
			return nil, err
		}
		manager.AddController(osscontroller)
		osscredential, err := oss.NewCredentialController(svc.ObjectStorage)
		if err != nil {
			return nil, err
		}
		manager.AddController(osscredential)
	}

	oganizations, err := organization.NewOrganizationController(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(oganizations)

	marketapplications, err := market.NewMarketController(storage, svc.Pay.SKUService, svc.ArtifactService.ChartsProject, svc.ArtifactService)
	if err != nil {
		return nil, err
	}
	manager.AddController(marketapplications)

	alertrule, err := observerability.NewAlertRuleController(storage, svc.MongoStore, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(alertrule)

	alertchannel, err := observerability.NewAlertChannelReconciler(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(alertchannel)

	metricsdashboard, err := observerability.NewAutoCreateDashboardFromApplication(storage, svc.CloudInfoHolder, svc.ArtifactService)
	if err != nil {
		return nil, err
	}
	manager.AddController(metricsdashboard)

	licensesigner, err := license.NewLicenseSignerController(storage)
	if err != nil {
		return nil, err
	}
	manager.AddController(licensesigner)

	tenantwallet, err := wallet.NewTenantWalletController(storage, svc.Pay.Wallet)
	if err != nil {
		return nil, err
	}
	manager.AddController(tenantwallet)

	licenserequests, err := license.NewLicenseRequestsController(storage, svc.ArtifactService)
	if err != nil {
		return nil, err
	}
	manager.AddController(licenserequests)

	privatenodeclaim, err := privatenode.NewTenantPrivateNodeController(storage, svc.Recorder, svc.CloudInfoHolder, svc.Pay.SKUService)
	if err != nil {
		return nil, err
	}
	manager.AddController(privatenodeclaim)

	privatenodes, err := privatenode.NewPrivateNodeController(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(privatenodes)

	clustersku, err := privatenode.NewClusterSkuController(storage, svc.Pay.SKUService)
	if err != nil {
		return nil, err
	}
	manager.AddController(clustersku)

	resourcepool, err := resourcepool.NewResourcePoolController(storage, svc.CloudInfoHolder)
	if err != nil {
		return nil, err
	}
	manager.AddController(resourcepool)

	ossproviders, err := oss.NewObjectStorageProviderController(storage, svc.ObjectProviderContext)
	if err != nil {
		return nil, err
	}
	manager.AddController(ossproviders)

	gc, err := garbagecollector.NewGarbageCollector(storage, garbagecollector.GarbageCollectorOptions{
		MonitorResources: GarbageCollectorResources,
		SetParentAsOwner: true,
	})
	if err != nil {
		return nil, err
	}
	manager.AddController(gc)

	shared, err := shared.NewSharedController(storage, shared.NewOptions())
	if err != nil {
		return nil, err
	}
	manager.AddController(shared)

	userrole, err := rbac.NewUserRoleController(storage)
	if err != nil {
		return nil, err
	}
	manager.AddController(userrole)

	loadbalancer, err := application.NewLoadbalancerController(ctx, storage, svc.CloudInfoHolder, svc.LoadBalancerService)
	if err != nil {
		return nil, err
	}
	manager.AddController(loadbalancer)

	pays := pay.NewPaySystemController(svc.Pay)
	manager.AddController(pays)

	resourcecosts := cost.NewResourceCostCounter(storage, svc.MongoStore, svc.CloudInfoHolder, svc.Pay.BillingService, options.Costs)
	manager.AddController(resourcecosts)

	messages, err := message.NewMessageConsumer(svc.Store, svc.MongoStore, svc.EventsQueue, svc.Pay.Wallet, svc.AuthProvider, svc.Notify)
	if err != nil {
		return nil, err
	}
	manager.AddController(messages)

	return cm, nil
}

func (c *ControllerManager) Run(ctx context.Context) error {
	// init all cluster infos
	if err := cluster.InitCloudInfoHolder(ctx, c.svc.Store, c.svc.CloudInfoHolder); err != nil {
		return err
	}
	return c.manager.Run(ctx)
}
