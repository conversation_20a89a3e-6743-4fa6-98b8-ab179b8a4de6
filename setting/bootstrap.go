package setting

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"strings"

	"xiaoshiai.cn/common/crypto"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/store"
)

func GetOrCreateEncryptKey(ctx context.Context, storage store.Store) (string, error) {
	log := log.FromContext(ctx)
	key, err := GetSettingValue(ctx, storage, "device.key")
	if err != nil {
		return "", err
	}
	if key == "" {
		log.Info("generating new device key")
		key = rand.RandomAlphaNumeric(32)
		log.Info("saving new device key", "key", strings.ReplaceAll(key, key[16:], "..."))
		if err := SetSettingValue(ctx, storage, "device.key", key); err != nil {
			return "", err
		}
		return key, nil
	}
	log.Info("using existing device key", "key", strings.ReplaceAll(key, key[16:], "..."))
	return key, nil
}

func GetOrCreateSessionKey(ctx context.Context, storage store.Store) (*rsa.PrivateKey, error) {
	log := log.FromContext(ctx)
	val, err := GetSettingValue(ctx, storage, "session.key")
	if err != nil {
		return nil, err
	}
	if val == "" {
		log.Info("generating new session key")
		key, err := crypto.GenerateRSAKey()
		if err != nil {
			return nil, err
		}
		// pem encode
		x509keyder := x509.MarshalPKCS1PrivateKey(key)
		x509keypem := string(pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509keyder}))
		log.Info("saving new session key", "key", x509keypem)
		if err := SetSettingValue(ctx, storage, "session.key", x509keypem); err != nil {
			return nil, err
		}
		return key, nil
	}
	// pem decode
	log.Info("using existing session key", "key", val)
	block, _ := pem.Decode([]byte(val))
	if block == nil {
		return nil, liberrors.NewBadRequest("invalid session key")
	}
	return x509.ParsePKCS1PrivateKey(block.Bytes)
}

const SettingEmailTemplate = "email.template"

type EmailTemplate struct {
	Title   string `json:"title,omitempty"`
	Contnet string `json:"content,omitempty"`
}

// EmailTemplateValues is the values to template the email Contnet
type EmailTemplateValues struct {
	Code string
	Link string
}

const (
	DefaultEmailTemplateContent = `
<html>
<body>
<h1>Verify your email</h1>
<p>Your verification code is: <strong>{{.Code}}</strong></p>
{{- if .Link}}
<p>Or open this link to verify: <a href="{{.Link}}">{{.Link}}</a></p>
{{- end}}
</body>
</html>
`
)

func GetOrCreateEmailTemplate(ctx context.Context, storage store.Store) (*EmailTemplate, error) {
	log := log.FromContext(ctx)
	template := &EmailTemplate{}
	ok, err := GetSetting(ctx, storage, SettingEmailTemplate, template)
	if err != nil {
		return nil, err
	}
	if !ok {
		log.Info("creating default email template")
		defaultTemplate := &EmailTemplate{
			Title:   "Please verify your email",
			Contnet: DefaultEmailTemplateContent,
		}
		if err := SetSetting(ctx, storage, SettingEmailTemplate, defaultTemplate); err != nil {
			return nil, err
		}
		return defaultTemplate, nil
	}
	return template, nil
}
