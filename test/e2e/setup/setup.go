package setup

import (
	"context"
	"errors"
	"fmt"
	"time"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/etcdcache"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core"
	"xiaoshiai.cn/core/agent"
	"xiaoshiai.cn/core/cluster"
)

func SetupEdgeTunnel(ctx context.Context) (*agent.EdgeTunnel, error) {
	edgeOptions := agent.NewDefaultServerOptions()
	edgeOptions.DeviceID = "test-device-id"

	controllerTunnel, err := core.NewControllerTunnel(ctx, edgeOptions)
	if err != nil {
		return nil, err
	}
	ready := make(chan struct{})
	go controllerTunnel.Connect(ctx, ready)
	select {
	case <-ready:
		return controllerTunnel.Tunnel, nil
	case <-time.After(10 * time.Second):
		return nil, errors.New("Timeout waiting for controller tunnel to connect")
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

func SetupEtcdStore(ctx context.Context) (store.Store, error) {
	etcdoptions := etcdcache.NewDefaultOptions()
	etcdoptions.Servers = []string{
		"http://bob-etcd-0.bob-etcd-headless.bob:2379",
		"http://bob-etcd-1.bob-etcd-headless.bob:2379",
		"http://bob-etcd-2.bob-etcd-headless.bob:2379",
	}
	etcdstore, err := core.NewEtcdStore(ctx, etcdoptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create etcd store: %w", err)
	}
	return etcdstore, nil
}

func SetupMongoStore(ctx context.Context) (*mongo.MongoStorage, error) {
	mongooptions := mongo.NewDefaultMongoOptions("bob")
	mongooptions.Address = "bob-mongodb-headless.bob:27017"
	mongooptions.Username = "root"
	mongooptions.Password = "q1u9D20L0I"
	mongostore, err := core.NewMongoStore(ctx, mongooptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongo store: %w", err)
	}
	return mongostore, nil
}

func SetupTestCloudInfo(ctx context.Context, storage store.Store) (cluster.CloudInfoGetter, func(), error) {
	etcdstore, err := SetupEtcdStore(ctx)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to setup etcd store: %w", err)
	}

	edgeTunnel, err := SetupEdgeTunnel(ctx)
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to setup edge tunnel: %w", err)
	}

	cloudinfo := cluster.NewDefaultCloudInfoHolder(ctx, edgeTunnel)
	if err := cluster.InitCloudInfoHolder(ctx, etcdstore, cloudinfo); err != nil {
		return nil, func() {}, fmt.Errorf("failed to init cloud info holder: %w", err)
	}
	return cloudinfo, func() {}, nil
}
