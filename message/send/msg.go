package send

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/message/events"
)

// 消息类型
type MessageType string

const (
	MessageTypeTicket         MessageType = "ticket"         // 工单
	MessageTypeSystem         MessageType = "system"         // 系统
	MessageTypeApplication    MessageType = "application"    // 应用
	MessageTypeInfrastructure MessageType = "infrastructure" // 基础设置
	MessageTypeBill           MessageType = "bill"           // 账单
	MessageTypeOther          MessageType = "other"          // 其他

	// 通知界面刷新
	MessageTypeNotifyUser                 MessageType = "notify-user"                   // 用户权限变更
	MessageTypeNotifyTenantOrOrganization MessageType = "notify-tenant-or-organization" // 企业，组织基本信息变更
	MessageTypeNotifyAnnouncement         MessageType = "notify-announcement"           // 修改，删除公告通知变更
)

// 消息级别
type MessageLevel string

const (
	MessageLevelInfo      MessageLevel = "info"      // 普通消息
	MessageLevelImportant MessageLevel = "important" // 重要消息
)

// 消息collection
type Message struct {
	store.ObjectMeta `json:",inline"`
	Action           string                 `json:"action,omitempty"`  // 资源动作
	Subject          string                 `json:"subject,omitempty"` // 资源动作的发起人
	Parents          []api.AttrbuteResource `json:"parents,omitempty"`
	EventReason      events.Reason          `json:"eventReason,omitempty"`

	ResourceType string      `json:"resourceType,omitempty"` // 消息所属的资源类型
	ResourceName string      `json:"resourceName,omitempty"` // 消息所属的资源名称
	Type         MessageType `json:"type"`                   // 消息类型
	Related      []string    `json:"related"`                // 消息关联的用户

	Organization string       `json:"organization,omitempty"` // 资源所属的组织
	Tenant       string       `json:"tenant,omitempty"`       // 资源所属租户
	Title        string       `json:"title"`                  // 消息标题
	Level        MessageLevel `json:"level"`                  // 消息级别
	Content      string       `json:"content"`                // 消息内容
	Details      string       `json:"details,omitempty"`      // 消息详情
}

// 用户订阅的消息类型collection
type MessageSubscribe struct {
	store.ObjectMeta `json:",inline"`
	Subs             []MessageSubscribeInfo `json:"subs"` // 用户订阅的消息类型
}

type MessageSubscribeInfo struct {
	Type     MessageType          `json:"type"`
	Channels []MessageSendChannel `json:"channels"`
}

type MessageSendChannel string

const (
	MessageSendChannelSiteMessage MessageSendChannel = "siteMessage" // 站内信
	MessageSendChannelEmail       MessageSendChannel = "email"       // 邮件
	MessageSendChannelShotMessage MessageSendChannel = "shotMessage" // 短信
	MessageSendChannelDingding    MessageSendChannel = "dingding"    // 钉钉
	MessageSendChannelFeishu      MessageSendChannel = "feishu"      // 飞书
	MessageSendChannelMSTeams     MessageSendChannel = "msteams"     // msteams
)

type MessageReadState string

const (
	MessageReadStateUnread MessageReadState = "unread"
	MessageReadStateRead   MessageReadState = "read"
)

// 用户历史消息collection
type MessageHistory struct {
	store.ObjectMeta `json:",inline"`
	User             string `json:"user"` // 用户id
	//EventReason                events.Reason `json:"eventReason,omitempty"`
	MessageGenerationTimestamp store.Time  `json:"messageGenerationTimestamp,omitempty"`
	Level                      string      `json:"level,omitempty"`
	Type                       MessageType `json:"type,omitempty"`

	ReadState MessageReadState `json:"readstate"` // 是否已读
	Message   Message          `json:"message"`   // 消息ID
}
