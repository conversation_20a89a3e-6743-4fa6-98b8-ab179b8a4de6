package message

import (
	"context"
	"sync"

	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/message/send"
)

type wsConnection struct {
	conn   *websocket.Conn
	user   string
	ctx    context.Context
	cancel context.CancelFunc
}

// websocket 连接管理器
type wsConnectionManager struct {
	connections sync.Map // map[string]*WSConnection
	storage     store.TransactionStore
	db          *mongo.Database
}

func newWSConnectionManager(storage store.TransactionStore, db *mongo.Database) *wsConnectionManager {
	return &wsConnectionManager{
		connections: sync.Map{},
		storage:     storage,
		db:          db,
	}
}

// addConnection 添加新的WebSocket连接
func (m *wsConnectionManager) addConnection(user string, conn *websocket.Conn) *wsConnection {
	ctx, cancel := context.WithCancel(context.Background())
	wsConn := &wsConnection{
		conn:   conn,
		user:   user,
		ctx:    ctx,
		cancel: cancel,
	}
	m.connections.Store(user, wsConn)
	// 启动消息处理
	go m.watchUserMessages(wsConn)
	return wsConn
}

func (m *wsConnectionManager) watchUserMessages(wsConn *wsConnection) {
	history := m.db.Collection("messagehistories")
	pipeline := mongo.Pipeline{
		{{"$match", bson.D{
			{"fullDocument.user", wsConn.user},
			{"operationType", "insert"},
		}}},
	}
	stream, err := history.Watch(wsConn.ctx, pipeline)
	if err != nil {
		log.FromContext(wsConn.ctx).Error(err, "failed to create change stream")
		return
	}
	defer stream.Close(wsConn.ctx)

	for {
		select {
		case <-wsConn.ctx.Done():
			return
		default:
			if !stream.Next(wsConn.ctx) {
				if stream.Err() != nil {
					log.FromContext(wsConn.ctx).Error(stream.Err(), "change stream error")
				}
				return
			}

			var event struct {
				FullDocument send.MessageHistory `bson:"fullDocument"`
			}
			if err := stream.Decode(&event); err != nil {
				log.FromContext(wsConn.ctx).Error(err, "failed to decode message history")
				continue
			}
			if err := wsConn.conn.WriteJSON(event.FullDocument.Message); err != nil {
				log.FromContext(wsConn.ctx).Error(err, "failed to write message to websocket")
				return
			}
		}
	}
}

// removeConnection 移除WebSocket连接
func (m *wsConnectionManager) removeConnection(user string, wsConn *wsConnection) {
	if actual, ok := m.connections.Load(user); ok {
		actualConn := actual.(*wsConnection)
		if actualConn == wsConn {
			m.connections.Delete(user)
			wsConn.cancel()
		}
	}
}

// AddConnection implements MessageService.
func (a *API) addConnection(user string, conn *websocket.Conn) *wsConnection {
	return a.wsManager.addConnection(user, conn)
}

// RemoveConnection implements MessageService.
func (a *API) removeConnection(user string, wsConn *wsConnection) {
	a.wsManager.removeConnection(user, wsConn)
}

func (a *API) reciveMessage(ctx context.Context, msg *send.Message) error {
	return a.storage.Create(ctx, msg)
}
