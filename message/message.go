package message

import (
	"context"
	"fmt"
	"net/http"

	"github.com/gorilla/websocket"
	mongodriver "go.mongodb.org/mongo-driver/mongo"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/message/send"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&send.Message{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"type"},
				{"title"},
				{"level"},
			},
		})

	mongo.GlobalObjectsScheme.Register(&send.MessageSubscribe{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
		})

	mongo.GlobalObjectsScheme.Register(&send.MessageHistory{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"user"},
				{"readstate"},
				{"message.type"},
			},
		})
}

type API struct {
	storage   store.TransactionStore
	db        *mongodriver.Database
	wsManager *wsConnectionManager
}

func NewAPI(storage store.TransactionStore, db *mongodriver.Database) *API {
	return &API{
		storage:   storage,
		db:        db,
		wsManager: newWSConnectionManager(storage, db),
	}
}

// 接收消息
func (a *API) ReciveMessage(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		msg := &send.Message{}
		if err := api.Body(r, msg); err != nil {
			return nil, err
		}
		return nil, a.reciveMessage(ctx, msg)
	})
}

// 用户订阅的消息类型collection
type UserSubscribeRequest struct {
	Subs []send.MessageSubscribeInfo `json:"subs"` // 用户订阅的消息类型
}

// 获取用户订阅详情
func (a *API) GetSubscribe(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		sub := &send.MessageSubscribe{}
		if err := a.storage.Get(ctx, user, sub); err != nil {
			if !liberrors.IsNotFound(err) {
				return nil, err
			}
			// not found , use empty
			sub = &send.MessageSubscribe{
				ObjectMeta: store.ObjectMeta{Name: user},
				Subs:       []send.MessageSubscribeInfo{},
			}
		}
		return sub, nil
	})
}

// 设置用户订阅
func (a *API) SetSubscribe(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		req := &UserSubscribeRequest{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		sub := &send.MessageSubscribe{
			ObjectMeta: store.ObjectMeta{Name: user},
		}
		updatefunc := func() error {
			sub.Subs = req.Subs
			return nil
		}
		if err := store.CreateOrUpdate(ctx, a.storage, sub, updatefunc); err != nil {
			return nil, err
		}
		return sub, nil
	})
}

func (a *API) MessageCenter(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		up := websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		}
		conn, err := up.Upgrade(w, r, nil)
		if err != nil {
			return nil, err
		}
		// 添加到连接管理器
		wsConn := a.addConnection(user, conn)
		defer a.removeConnection(user, wsConn)
		// 处理连接关闭
		for {
			_, _, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					log.FromContext(ctx).Error(err, "websocket connection closed unexpectedly")
				}
				break
			}
		}
		return nil, nil
	})
}

type ListUserMessageOption struct {
	api.ListOptions `json:",inline"`
	User            string `json:"user"`
	Read            string `json:"read"`
	Type            string `json:"type"`
}

// 获取用户订阅的所有消息
func (a *API) ListCurrentUserMessage(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		listoptions := ListUserMessageOption{
			ListOptions: api.GetListOptions(r),
			User:        user,
			Read:        api.Query(r, "read", ""),
			Type:        api.Query(r, "type", ""),
		}
		return a.listUserMessage(ctx, listoptions)
	})
}

func (a *API) listUserMessage(ctx context.Context, options ListUserMessageOption) (store.List[send.MessageHistory], error) {
	reqs := store.Requirements{
		store.RequirementEqual("user", options.User),
		store.Requirement{
			Key:      "message.type",
			Operator: store.NotIn,
			Values: []any{
				send.MessageTypeNotifyUser,
				send.MessageTypeNotifyTenantOrOrganization,
				send.MessageTypeNotifyAnnouncement,
			},
		},
	}
	if options.Read != "" {
		reqs = append(reqs, store.RequirementEqual("readstate", options.Read))
	}
	if options.Type != "" {
		reqs = append(reqs, store.RequirementEqual("message.type", options.Type))
	}
	opts := base.ListOptionsToStoreListOptions(options.ListOptions)
	opts = append(opts, store.WithFieldRequirements(reqs...))

	list := store.List[send.MessageHistory]{}
	if err := a.storage.List(ctx, &list, opts...); err != nil {
		return list, err
	}
	return list, nil
}

func (a *API) GetMessage(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		msg := &send.MessageHistory{}
		if err := a.storage.Get(ctx, name, msg, store.WithGetFieldRequirements(store.RequirementEqual("user", user))); err != nil {
			return nil, err
		}
		if msg.ReadState == send.MessageReadStateUnread {
			patch := store.MapMergePatch{
				"readstate": send.MessageReadStateRead,
			}
			if err := a.storage.Patch(ctx, msg, patch); err != nil {
				log.FromContext(ctx).Error(err, "failed to update message read state")
			}
		}
		return msg, nil
	})
}

const (
	BatchActionRead   BatchAction = "read"
	BatchActionDelete BatchAction = "delete"
)

type BatchAction string

type BatchMessages struct {
	// 是否全部操作
	All bool `json:"all,omitempty"`
	// 批量操作的消息名称
	Names []string `json:"names,omitempty"`
}

func (a *API) BatchMessageOperation(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		action := api.Path(r, "action", "")
		if action == "" {
			return nil, fmt.Errorf("action is required")
		}
		messages := &BatchMessages{}
		if err := api.Body(r, messages); err != nil {
			return nil, err
		}
		switch BatchAction(action) {
		case BatchActionDelete:
			return nil, a.batchDeleteMessages(ctx, user, messages)
		case BatchActionRead:
			return nil, a.batchReadMessage(ctx, user, messages)
		}
		return nil, nil
	})
}

func (a *API) batchDeleteMessages(ctx context.Context, user string, messages *BatchMessages) error {
	requirements := store.Requirements{
		store.RequirementEqual("user", user),
	}
	if !messages.All {
		requirements = append(requirements, store.Requirement{
			Key: "name", Operator: store.In, Values: store.StringsToAny(messages.Names),
		})
	}
	list := store.List[send.MessageHistory]{}
	return a.storage.DeleteBatch(ctx, &list, store.WithDeleteBatchFieldRequirements(requirements...))
}

func (a *API) batchReadMessage(ctx context.Context, user string, messages *BatchMessages) error {
	requirements := store.Requirements{
		store.RequirementEqual("user", user),
		store.RequirementEqual("readstate", "unread"),
	}
	if !messages.All {
		requirements = append(requirements, store.Requirement{Key: "name", Operator: store.In, Values: store.StringsToAny(messages.Names)})
	}
	patchBatch := store.MapMergePatchBacth{
		"readstate": send.MessageReadStateRead,
	}
	list := store.List[send.MessageHistory]{}
	return a.storage.PatchBatch(ctx, &list, patchBatch, store.WithPatchBatchFieldRequirements(requirements...))
}

func (a *API) DeleteSingleMessage(w http.ResponseWriter, r *http.Request) {
	base.OnCurrentUser(w, r, func(ctx context.Context, user string) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		return nil, a.batchDeleteMessages(ctx, user, &BatchMessages{
			Names: []string{name},
		})
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Message").
		SubGroup(
			a.CurrentGroup(),
			a.NotifyGroup(),
		)
}

func (a *API) NotifyGroup() api.Group {
	return api.
		NewGroup("/notify").
		Tag("Message").
		Route(
			api.GET("").
				Operation("message center with websocket").
				To(a.MessageCenter),
		)
}

func (a *API) CurrentGroup() api.Group {
	return base.NewCurrentGroup().
		SubGroup(
			api.NewGroup("/subscribe").
				Route(
					api.GET("").
						Operation("get subscribe").
						To(a.GetSubscribe).
						Response(send.MessageSubscribe{}),

					api.PUT("").
						Operation("set subscribe").
						Param(api.BodyParam("subscribe", UserSubscribeRequest{})).
						To(a.SetSubscribe),
				),
			api.NewGroup("/messages").
				Route(
					api.GET("").
						Operation("list message").
						Param(api.PageParams...).
						Param(
							api.QueryParam("read", "read state").
								In(send.MessageReadStateUnread, send.MessageReadStateRead).
								Optional(),
							api.QueryParam("type", "message type").
								In(
									send.MessageTypeTicket,
									send.MessageTypeSystem,
									send.MessageTypeApplication,
									send.MessageTypeInfrastructure,
									send.MessageTypeBill,
									send.MessageTypeOther,
								).
								Optional(),
						).
						To(a.ListCurrentUserMessage).
						Response(store.List[send.MessageHistory]{}),

					api.POST(":{action}").
						Operation("batch message operation").
						Param(api.BodyParam("batch", BatchMessages{})).
						Param(api.PathParam("action", "batch action").In(BatchActionRead, BatchActionDelete)).
						To(a.BatchMessageOperation),

					api.GET("/{name}").
						Operation("get message").
						Param(api.PathParam("name", "message name")).
						To(a.GetMessage).
						Response(send.MessageHistory{}),

					api.DELETE("/{name}").
						Operation("delete message").
						To(a.DeleteSingleMessage),
				),
		)
}
