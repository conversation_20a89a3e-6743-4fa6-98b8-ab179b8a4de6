package message

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/announcement"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster/privatenode"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/message/option"
	"xiaoshiai.cn/core/message/send"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/rbac"
	"xiaoshiai.cn/core/tenant"
	"xiaoshiai.cn/core/ticketv2"
	"xiaoshiai.cn/core/wallet"
)

var _ controller.Runable = &MessageConsumer{}

type UserinfoGetter interface {
	GetUser(ctx context.Context, username string) (*auth.UserProfile, error)
}

type MessageConsumer struct {
	store        store.Store
	mongo        store.Store
	dispatcher   *MessageDispatcher
	messageQueue queue.Queue
	user         UserinfoGetter
	wallet       wallet.WalletService
}

func NewMessageConsumer(storage store.Store, mongo store.Store, messageQueue queue.Queue, wallet wallet.WalletService, user UserinfoGetter, opt *option.NotifyOptions) (*MessageConsumer, error) {
	siteSender, err := NewSiteSender(mongo)
	if err != nil {
		return nil, err
	}
	senders := []MessageSender{siteSender}

	// 注册邮件发送器
	if opt.Email.Enabled {
		emailSender, err := NewEmailSender(opt.Email)
		if err != nil {
			return nil, err
		}
		senders = append(senders, emailSender)
	}
	return NewMessageConsumerWithSender(storage, mongo, messageQueue, wallet, user, senders...)
}

func NewMessageConsumerWithSender(storage store.Store, mongo store.Store, messageQueue queue.Queue, wallet wallet.WalletService, user UserinfoGetter, sender ...MessageSender) (*MessageConsumer, error) {
	dispatcher := NewMessageDispatcher()
	for _, s := range sender {
		if err := dispatcher.RegisterSender(s); err != nil {
			return nil, err
		}
	}
	consumer := &MessageConsumer{
		store:        storage,
		messageQueue: messageQueue,
		mongo:        mongo,
		user:         user,
		dispatcher:   dispatcher,
		wallet:       wallet,
	}
	return consumer, nil
}

// Name implements controller.Runable.
func (m *MessageConsumer) Name() string {
	return "MessageConsumer"
}

// Run implements controller.Runable.
func (m *MessageConsumer) Run(ctx context.Context) error {
	return wait.PollUntilContextCancel(ctx, 10*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log.Info("message consumer running")
		return false, m.run(ctx)
	})
}

func (m *MessageConsumer) run(ctx context.Context) error {
	log := log.FromContext(ctx)
	return m.messageQueue.Consume(ctx, func(ctx context.Context, id string, data []byte) error {
		event := events.Event{}
		if err := json.Unmarshal(data, &event); err != nil {
			// ignore invalid event
			log.Error(err, "failed to unmarshal event", "data", string(data))
			return nil
		}
		if err := m.Consume(ctx, event); err != nil {
			log.Error(err, "failed to consume event", "event", event)
		}
		return nil
	}, queue.ConsumeOptions{})
}

type RawMessage struct {
	Level        send.MessageLevel         `json:"level"`
	Tenant       tenant.Tenant             `json:"tenant,omitempty"`
	Organization organization.Organization `json:"organization,omitempty"`
	Event        events.Event              `json:"event"`
	Values       any                       `json:"values,omitempty"`
	User         any                       `json:"user"`
	System       SystemOptions             `json:"system,omitempty"`
}

type SystemOptions struct {
	Addr      string `json:"addr,omitempty"`
	AdminAddr string `json:"adminAddr,omitempty"`
}

func (m *MessageConsumer) Consume(ctx context.Context, event events.Event) error {
	// no related object
	if event.Object.Name == "" {
		return nil
	}
	tenantname, organizationname := base.TenantOrganizationFromScopes(event.Object.Scopes...)
	raw := RawMessage{
		Tenant:       tenant.Tenant{ObjectMeta: store.ObjectMeta{Name: tenantname}},
		Organization: organization.Organization{ObjectMeta: store.ObjectMeta{Name: organizationname}},
		Event:        event,
		Level:        send.MessageLevelInfo,
	}

	var sendToUsers []string
	sendtoall := false
	switch event.Reason {
	case events.AnnouncementReasonCreated:
		admins, _ := rbac.ListTenantOrOrganizationAdmin(ctx, m.store, event.Object.Scopes...)
		sendToUsers = admins
		sendtoall = true
		an := &announcement.Announcement{}
		if err := m.mongo.Scope(event.Object.Scopes...).Get(ctx, event.Object.Name, an); err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		raw.Values = an
	case events.LicenseReasonExpireSoon, events.LicenseReasonExpired:
		li := &license.License{}
		if err := m.store.Scope(event.Object.Scopes...).Get(ctx, event.Object.Name, li); err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		raw.Values = li
		admins, _ := rbac.ListTenantOrOrganizationAdmin(ctx, m.store, event.Object.Scopes...)
		sendToUsers = admins
		if event.Reason == events.LicenseReasonExpired {
			raw.Level = send.MessageLevelImportant
		}
		if event.Annotations[events.AnnotationKeyUsers] != "" {
			for _, u := range strings.Split(event.Annotations[events.AnnotationKeyUsers], ",") {
				if strings.TrimSpace(u) != "" {
					sendToUsers = append(sendToUsers, u)
				}
			}
		}
	case events.PrivateNodeReasonExpireSoon, events.PrivateNodeReasonExpired,
		events.PrivateNodeReasonReleaseSoon, events.PrivateNodeReasonReleased:
		pvc := &privatenode.PrivateNodeClaim{}
		if err := m.store.Scope(event.Object.Scopes...).Get(ctx, event.Object.Name, pvc); err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		raw.Values = pvc
		admins, _ := rbac.ListTenantOrOrganizationAdmin(ctx, m.store, event.Object.Scopes...)
		sendToUsers = admins
		if event.Reason == events.PrivateNodeReasonExpired {
			raw.Level = send.MessageLevelImportant
		}
	case events.ReasonTicketOpened, events.ReasonTicketReOpened, events.ReasonTicketAssigned, events.ReasonTicketReplied, events.ReasonTickerClosed:
		ticket := &ticketv2.BobTicket{}
		if err := m.mongo.Get(ctx, event.Object.Name, ticket); err != nil {
			// if ticket is deleted, ignore it
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		raw.Values = ticket
		switch event.Reason {
		case events.ReasonTicketOpened, events.ReasonTicketReOpened, events.ReasonTicketAssigned:
			if ticket.Module.ApprovalUser != "" {
				sendToUsers = append(sendToUsers, ticket.Module.ApprovalUser)
			}
		case events.ReasonTicketReplied, events.ReasonTickerClosed:
			if ticket.CreateUser != "" {
				sendToUsers = append(sendToUsers, ticket.CreateUser)
			}
		}
	case events.ReasonWalletRecharge:
		tenantname = event.Object.Name
		if m.wallet != nil {
			walletObj, err := m.wallet.Get(ctx, event.Object.Name)
			if errors.IsNotFound(err) {
				return nil
			}
			raw.Values = walletObj
		}
		tenantAdmins, _ := rbac.ListTenantOrOrganizationAdmin(ctx, m.store, base.ScopeTenant(tenantname))
		sendToUsers = tenantAdmins

	case events.UserPermissionsRefresh:
		sendToUsers = append(sendToUsers, event.Object.Name)
	case events.TenantOrganizationRefresh:
		admins, _ := rbac.ListTenantOrOrganizationAdmin(ctx, m.store, event.Object.Scopes...)
		sendToUsers = append(sendToUsers, admins...)
		sysAdmins, _ := rbac.ListSystemAdmin(ctx, m.store)
		sendToUsers = append(sendToUsers, sysAdmins...)
	case events.AnnouncementReasonDeleted, events.AnnouncementReasonUpdated:
		sendtoall = true
	}

	// fill full tenant info, it used in template
	if tenantname != "" {
		// ignore error
		_ = m.store.Get(ctx, tenantname, &raw.Tenant)
		if organizationname != "" {
			_ = m.store.Scope(base.ScopeTenant(tenantname)).Get(ctx, organizationname, &raw.Organization)
		}
	}

	sendtype := EventDescriptionType(event)
	sendfunc := func(ctx context.Context, user *auth.UserProfile, channels []send.MessageSendChannel) error {
		return m.dispatcher.SendMessageToChannels(ctx, user, raw, channels)
	}
	return m.sendOnType(ctx, sendtoall, deduplicate(sendToUsers), sendtype, sendfunc)
}

type SendFunc func(ctx context.Context, user *auth.UserProfile, channels []send.MessageSendChannel) error

func deduplicate[T comparable](slice []T) []T {
	if len(slice) == 0 {
		return []T{}
	}
	seen := make(map[T]struct{})
	result := make([]T, 0, len(slice))

	for _, item := range slice {
		if _, ok := seen[item]; !ok {
			seen[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

var refreshNotifyTypes = []send.MessageType{
	send.MessageTypeNotifyUser,
	send.MessageTypeNotifyTenantOrOrganization,
	send.MessageTypeNotifyAnnouncement,
}

func (m *MessageConsumer) sendOnType(ctx context.Context, sendToAll bool, sendToUsers []string, sendtype send.MessageType, onuser SendFunc) error {
	// 处理刷新通知类型
	if slices.Contains(refreshNotifyTypes, sendtype) {
		return m.handleRefreshNotification(ctx, sendToAll, sendToUsers, sendtype, onuser)
	}
	// 处理普通消息类型
	if sendToAll {
		return m.sendToAllUsers(ctx, sendtype, onuser)
	}
	return m.sendToSpecificUsers(ctx, sendToUsers, sendtype, onuser)
}

func (m *MessageConsumer) handleRefreshNotification(ctx context.Context, sendToAll bool, sendToUsers []string, sendtype send.MessageType, onuser SendFunc) error {
	var errs []error
	if sendToAll {
		list := store.List[send.MessageSubscribe]{}
		if err := m.mongo.List(ctx, &list); err != nil {
			errs = append(errs, fmt.Errorf("failed to list subscribers: %w", err))
		}
		for i := range list.Items {
			ms := createRefreshSubscription(list.Items[i].Name, sendtype)
			if err := m.sendSubscription(ctx, ms, sendtype, onuser); err != nil {
				errs = append(errs, fmt.Errorf("failed to send to user %s: %w", list.Items[i].Name, err))
			}
		}
	} else {
		for _, userID := range sendToUsers {
			ms := createRefreshSubscription(userID, sendtype)
			if err := m.sendSubscription(ctx, ms, sendtype, onuser); err != nil {
				errs = append(errs, fmt.Errorf("failed to send to user %s: %w", userID, err))
			}
		}
	}
	return errors.NewAggregate(errs)
}

// sendToAllUsers 向所有订阅用户发送消息
func (m *MessageConsumer) sendToAllUsers(ctx context.Context, sendtype send.MessageType, onuser SendFunc) error {
	var errs []error
	list := store.List[send.MessageSubscribe]{}
	if err := m.mongo.List(ctx, &list); err != nil {
		errs = append(errs, fmt.Errorf("failed to list subscribers: %w", err))
	}
	for i := range list.Items {
		if err := m.sendSubscription(ctx, &list.Items[i], sendtype, onuser); err != nil {
			errs = append(errs, fmt.Errorf("failed to send to user %s: %w", list.Items[i].Name, err))
		}
	}
	return errors.NewAggregate(errs)
}

// sendToSpecificUsers 向指定用户发送消息
func (m *MessageConsumer) sendToSpecificUsers(ctx context.Context, userIDs []string, sendtype send.MessageType, onuser SendFunc) error {
	var errs []error
	for _, userID := range userIDs {
		sub := &send.MessageSubscribe{}
		if err := m.mongo.Get(ctx, userID, sub); err != nil {
			if errors.IsNotFound(err) {
				continue // 用户未设置订阅，跳过
			}
			errs = append(errs, fmt.Errorf("failed to get subscription for user %s: %w", userID, err))
			continue
		}

		if err := m.sendSubscription(ctx, sub, sendtype, onuser); err != nil {
			errs = append(errs, fmt.Errorf("failed to send to user %s: %w", userID, err))
		}
	}
	return errors.NewAggregate(errs)
}

// createRefreshSubscription 创建刷新通知的订阅信息
func createRefreshSubscription(userID string, sendtype send.MessageType) *send.MessageSubscribe {
	return &send.MessageSubscribe{
		ObjectMeta: store.ObjectMeta{Name: userID},
		Subs: []send.MessageSubscribeInfo{
			{
				Type:     sendtype,
				Channels: []send.MessageSendChannel{send.MessageSendChannelSiteMessage},
			},
		},
	}
}

func (m *MessageConsumer) sendSubscription(ctx context.Context, sub *send.MessageSubscribe, subtype send.MessageType, onuser SendFunc) error {
	channels := getSubscribedChannels(subtype, sub.Subs)
	if len(channels) == 0 {
		return nil
	}
	// 为每个渠道发送消息
	userinfo, err := m.user.GetUser(ctx, sub.Name)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil // 用户不存在，跳过
		}
		return err
	}
	return onuser(ctx, userinfo, channels)
}

func EventDescriptionType(e events.Event) send.MessageType {
	if strings.HasPrefix(string(e.Reason), "Ticket") {
		return send.MessageTypeTicket
	}
	switch e.Reason {
	case events.AnnouncementReasonCreated:
		return send.MessageTypeSystem
	case events.LicenseReasonExpireSoon, events.LicenseReasonExpired:
		return send.MessageTypeApplication
	case events.PrivateNodeReasonExpireSoon, events.PrivateNodeReasonExpired,
		events.PrivateNodeReasonReleaseSoon, events.PrivateNodeReasonReleased:
		return send.MessageTypeInfrastructure
	case events.ReasonWalletRecharge, events.ReasonWalletRefund, events.ReasonWalletPurchase:
		return send.MessageTypeBill
	case events.UserPermissionsRefresh:
		return send.MessageTypeNotifyUser
	case events.TenantOrganizationRefresh:
		return send.MessageTypeNotifyTenantOrOrganization
	case events.AnnouncementReasonDeleted, events.AnnouncementReasonUpdated:
		return send.MessageTypeNotifyAnnouncement
	default:
		return send.MessageTypeOther
	}
}

type MessageDispatcher struct {
	Senders map[send.MessageSendChannel]MessageSender
}

func NewMessageDispatcher() *MessageDispatcher {
	return &MessageDispatcher{
		Senders: make(map[send.MessageSendChannel]MessageSender),
	}
}

func (md *MessageDispatcher) RegisterSender(sender MessageSender) error {
	sendertype := sender.Type()
	if _, ok := md.Senders[sendertype]; ok {
		return fmt.Errorf("sender %s already registered", sendertype)
	}
	md.Senders[sendertype] = sender
	return nil
}

func (md *MessageDispatcher) SendMessageToChannels(ctx context.Context, userinfo *auth.UserProfile, msg RawMessage, channels []send.MessageSendChannel) error {
	log := log.FromContext(ctx)
	var errs []error
	for _, channel := range channels {
		sender, ok := md.Senders[channel]
		if !ok {
			continue
		}
		log.V(3).Info("send message", "channel", channel, "user", userinfo.Name)
		if err := sender.Send(ctx, userinfo, msg); err != nil {
			log.Error(err, "failed to send message", "channel", channel, "user", userinfo.Name)
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

func getSubscribedChannels(msgType send.MessageType, subs []send.MessageSubscribeInfo) []send.MessageSendChannel {
	for _, sub := range subs {
		if sub.Type == msgType {
			return sub.Channels
		}
	}
	return nil
}
