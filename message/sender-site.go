package message

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/announcement"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/cluster/privatenode"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/message/send"
	"xiaoshiai.cn/core/ticketv2"
	"xiaoshiai.cn/core/wallet"
)

var DefaultI18nSiteMessageTemplate = I18nTemplate{
	events.AnnouncementReasonCreated: {
		LanguageZhCN: {
			Title:   "公告",
			Content: "{{ .values.title }}",
			Details: `<h2>{{ .values.title }}</h2>

<article style="background-color: #f9f9f9; padding: 16px; margin-top: 12px;">
	<p>{{.values.content}}</p>
</article>
`,
		},
		LanguageEnUS: {
			Title:   "Announcement",
			Content: "{{ .values.title }}",
			Details: `<h2>{{ .values.title }}</h2>

<article style="background-color: #f9f9f9; padding: 16px; margin-top: 12px;">
	<p>{{.values.content}}</p>
</article>
`,
		},
	},
	events.LicenseReasonExpireSoon: {
		LanguageZhCN: {
			Title:   "许可证即将过期",
			Content: "应用 {{.values.for}} 的许可证还有 {{.event.annotations.days}} 天过期",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>

<p>这是一个提醒，您的许可证即将在{{.event.annotations.days}}天后过期。请查看以下详细信息：</p>

<ul>
<li><strong>企业：</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>组织：</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>许可证编号:</strong> {{.values.name}} </li>
<li><strong>签发日期：</strong> {{.values.notBefore | formatTimeWithZone}} </li>
<li><strong>到期日期：</strong> {{.values.notAfter | formatTimeWithZone}} </li>
<li><strong>授权应用：</strong> {{.values.for}} </li>
</ul>

<p><strong>需要采取的操作：</strong></p>
<p>请在到期前更新你的应用许可，以免服务中断。</p>

<p>如果您有任何疑问或需要帮助，请随时联系我们的支持团队。</p>

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "License Is About To Expire",
			Content: "License for {{.values.for}} is about to expire in {{.event.annotations.days}} days",
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>This is a reminder that your license is about to expire in {{.event.annotations.days}} days. Please review the details below:</p>

<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>Organization:</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>License ID:</strong> {{.values.name}} </li>
<li><strong>Issue Date:</strong> {{.values.notBefore | formatTimeWithZone}} </li>
<li><strong>Expiration Date:</strong> {{.values.notAfter | formatTimeWithZone}} </li>
<li><strong>Product:</strong> {{.values.for}} </li>
</ul>

<p><strong>Action Required:</strong></p>
<p>Please renew your application license before the expiration date to avoid service interruption. </p>

<p>If you have any questions or need assistance, feel free to contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.LicenseReasonExpired: {
		LanguageZhCN: {
			Title:   "许可证已过期",
			Content: "应用 {{.values.for}} 的许可证已于 {{.values.notAfter}} 过期",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>

<p>您的许可证已于 <strong>{{.values.notAfter | formatTimeWithZone}}</strong> 过期。请查看以下详细信息：</p>

<ul>
<li><strong>企业：</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>组织：</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>许可证编号：</strong> {{.values.name}} </li>
<li><strong>签发日期：</strong> {{.values.notBefore | formatTimeWithZone}} </li>
<li><strong>到期日期：</strong> {{.values.notAfter | formatTimeWithZone}} </li>
<li><strong>产品：</strong> {{.values.for}} </li>
</ul>

<p><strong>需要采取的操作：</strong></p>
<p>为了继续使用我们的服务，请尽快续订您的许可证。</p>

<p>如果您有任何疑问或需要帮助，请随时联系我们的支持团队。</p>

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "License Expired",
			Content: "License for {{.values.for}} has expired",
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>Your license has expired on <strong>{{.values.notAfter | formatTimeWithZone}}</strong>. Please review the details below:</p>

<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>Organization:</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>License ID:</strong> {{.values.name}} </li>
<li><strong>Issue Date:</strong> {{.values.notBefore | formatTimeWithZone}} </li>
<li><strong>Expiration Date:</strong> {{.values.notAfter | formatTimeWithZone}} </li>
<li><strong>Product:</strong> {{.values.for}} </li>
</ul>

<p><strong>Action Required:</strong></p>
<p>To continue using our services, please renew your license as soon as possible.</p>

<p>If you have any questions or need assistance, feel free to contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.PrivateNodeReasonExpireSoon: {
		LanguageZhCN: {
			Title:   "专属节点即将过期",
			Content: "企业 {{.tenant.alias}} 的专属节点 {{.values.name}} 还有 {{.event.annotations.days}} 天过期",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>

<p>您的专属节点即将到期。请查看以下详细信息：</p>

<ul>
<li><strong>企业：</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>节点名称：</strong> {{.values.name}} </li>
<li><strong>所属集群：</strong> {{.values.cluster.name}} </li>
<li><strong>创建日期：</strong> {{.values.creationTimestamp | formatTimeWithZone}} </li>
<li><strong>到期日期：</strong> {{.values.expire | formatTimeWithZone}} </li>
</ul>

<p><strong>需要采取的操作：</strong></p>
<p>请在到期日期之前续订您的节点，以避免服务中断。</p>

<p>如果您有任何疑问或需要帮助，请随时联系我们的支持团队。</p>

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "PrivateNode Is About To Expire",
			Content: "Enterprise {{.tenant.alias}}'s private node {{.values.name}} is about to expire in {{.event.annotations.days}} days",
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>Your private node is about to expire. Please review the details below:</p>

<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>Node Name:</strong> {{.values.name}} </li>
<li><strong>Cluster:</strong> {{.values.cluster.name}} </li>
<li><strong>Creation Date:</strong> {{.values.creationTimestamp | formatTimeWithZone}} </li>
<li><strong>Expiration Date:</strong> {{.values.expire | formatTimeWithZone}} </li>
</ul>

<p><strong>Action Required:</strong></p>
<p>Please renew your node before the expiration date to avoid service interruption.</p>

<p>If you have any questions or need assistance, feel free to contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.PrivateNodeReasonExpired: {
		LanguageZhCN: {
			Title:   "专属节点已过期",
			Content: "企业 {{.tenant.alias}} 的专属节点 {{.values.name}} 已过期",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>

<p>您的专属节点已到期。</p>

<ul>
<li><strong>企业：</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>节点名称：</strong> {{.values.name}} </li>
<li><strong>所属集群：</strong> {{.values.cluster.name}} </li>
<li><strong>创建日期：</strong> {{.values.creationTimestamp | formatTimeWithZone}} </li>
<li><strong>到期日期：</strong> {{.values.expire | formatTimeWithZone}} </li>
</ul>

<p><strong>需要采取的操作：</strong></p>
<p>请续订您的节点，以避免服务中断。</p>

<p>如果您有任何疑问或需要帮助，请随时联系我们的支持团队。</p>

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "PrivateNode Expired",
			Content: "Enterprise {{.tenant.alias}}'s private node {{.values.name}} has expired",
			Details: `
<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>Your private node expired. </p>

<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>Node Name:</strong> {{.values.name}} </li>
<li><strong>Cluster:</strong> {{.values.cluster.name}} </li>
<li><strong>Creation Date:</strong> {{.values.creationTimestamp | formatTimeWithZone}} </li>
<li><strong>Expiration Date:</strong> {{.values.expire | formatTimeWithZone}} </li>
</ul>

<p><strong>Action Required:</strong></p>
<p>Please renew your node to avoid service interruption.</p>

<p>If you have any questions, please contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.ReasonWalletRecharge: {
		LanguageZhCN: {
			Title:   "充值成功",
			Content: `您的企业钱包已成功充值{{.event.annotations.delta}}`,
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>
<p>您的企业钱包已成功充值</p>

<ul>
<li><strong>企业：</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
{{- if and .event.annotations .event.annotations.delta }}
<li><strong>充值金额：</strong> {{.event.annotations.delta}} </li>
{{- end }}
<li><strong>余额：</strong> {{ printf "%0.2f" .values.balance }} </li>
</ul>

<p>如果您有任何疑问或需要帮助，请联系我们的支持团队。</p>

<p>谢谢！</p>`,
		},
		LanguageEnUS: {
			Title:   "Recharge Successful",
			Content: `The Enterprise {{.tenant.name}} recharged {{.event.annotations.delta}}`,
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>
<p>The Enterprise wallet has been successfully recharged.</p>

<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
{{- if and .event.annotations .event.annotations.delta }}
<li><strong>Recharge Amount:</strong> {{.event.annotations.delta}} </li>
{{- end }}
<li><strong>Balance:</strong> {{ printf "%0.2f" .values.balance }} </li>
</ul>

<p>If you have any questions or need assistance, please contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.ReasonTicketOpened: {
		LanguageZhCN: {
			Title:   "新工单",
			Content: "有标题为：{{ .values.title }}的工单需要您处理",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})</p>

<p>有新的工单需要你处理，请尽快处理。</p>

<ul>
<li><strong>工单编号：</strong> {{.values.name}} </li>
<li><strong>提交者：</strong> {{.values.createUser}} </li>
<li><strong>标题：</strong> {{.values.title}} </li>
</ul>

<p><strong>描述：</strong></p>
<p>{{ (first .values.comments).comment }}</p>


<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "New Ticket",
			Content: "The Ticket {{ .values.title }} needs you to deal with",
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }})</p>

<p>There is a new ticket that needs your processing, please process it as soon as possible.</p>

<ul>
<li><strong>Ticket ID:</strong> {{.values.name}} </li>
<li><strong>Submitted By:</strong> {{.values.createUser}} </li>
<li><strong>Title:</strong> {{.values.title}} </li>
</ul>

<p><strong>Description:</strong></p>
<p>{{ (first .values.comments).comment }}</p>

<p>Thank you,</p>`,
		},
	},
	/*
		{title:xxxx,name:xxxx}
	*/
	events.ReasonTicketReOpened: {
		LanguageZhCN: {
			Title:   "重开工单",
			Content: "标题为：{{ .values.title }}的工单被重新打开了",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>

<p>有工单被重新打开，请尽快处理</p>

<ul>
<li><strong>工单编号：</strong> {{.values.name}} </li>
<li><strong>主题：</strong> {{.values.title}} </li>
</ul>

<p>如果有其他问题，请随时联系我们的支持团队。</p>

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "Ticket Reopened",
			Content: "The Ticket {{ .values.title }} has been reopened",
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>The ticket has been reopened, please process it as soon as possible</p>

<ul>
<li><strong>Ticket ID:</strong> {{.values.name}} </li>
<li><strong>Title:</strong> {{.values.title}} </li>
</ul>

<p>If you have any further questions, feel free to contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.ReasonTicketAssigned: {
		LanguageZhCN: {
			Title:   "工单指派",
			Content: "{{ .event.annotations.operator }}指派了一个工单给你",
			Details: `<p>尊敬的{{ .user.name }} ({{ .user.displayName }})：</p>

<p>{{ .event.annotations.operator }}指派了一个工单给你，请尽快处理。</p>

<ul>
<li><strong>工单编号：</strong> {{.values.name}} </li>
<li><strong>主题：</strong> {{.values.title}} </li>
</ul>

<p>如果有任何问题，请随时联系我们的支持团队。</p>

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "Assign Ticket",
			Content: "A Ticket is assigned to you by {{ .event.annotations.operator }}",
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>{{ .event.annotations.operator }} has assigned a ticket to you, please process it as soon as possible.</p>

<ul>
<li><strong>Ticket ID:</strong> {{.values.name}} </li>
<li><strong>Title:</strong> {{.values.title}} </li>
</ul>

<p>If you have any further questions, feel free to contact our support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.ReasonTicketReplied: {
		LanguageZhCN: {
			Title:   "工单处理",
			Content: "标题为：{{ .values.title }}的工单正在处理中",
			Details: `<p>尊敬的 {{ .user.name }}({{ .user.displayName }})</p>

<p>您的工单 {{ .values.title }} 正在被处理。请查看以下详细信息：</p>

<h3>回复信息：</h3>
<ul>
<li><strong>回复时间：</strong> {{.event.creationTimestamp | formatTimeWithZone}}</li>

{{- if (and .event.annotations .event.annotations.comment )}}
<p><strong>回复内容：</strong></p>
<p>{{ .event.annotations.comment }}</p>
{{- end }}
</ul>

{{- if .system.addr }}
<p>您可以通过以下链接查看和管理此工单：</p>
{{- $link := printf "%s/tickets/%s" .system.addr .values.name }}
<a href="{{ $link }}">{{ $link }}</a>
{{- end }}

<p>谢谢，</p>`,
		},
		LanguageEnUS: {
			Title:   "Processing Ticket",
			Content: `Ticket {{ .values.title }} is being processed`,
			Details: `<p>Dear {{ .user.name }} ({{ .user.displayName }}),</p>

<p>Your ticket "<strong>{{ .values.title }}</strong>" is being processed. Please find the details below:</p>

<div class="ticket-info">
	<p><strong>Reply Time:</strong> {{.event.creationTimestamp | formatTimeWithZone}}</p>

	{{- if (and .event.annotations .event.annotations.comment) }}
	<p><strong>Reply Content:</strong></p>
	<p>{{ .event.annotations.comment }}</p>
	{{- end }}
</div>

{{- if .system.addr }}
	{{ $link := printf "%s/tickets/%s" .system.addr .values.name }}
	<p>You can view and manage this ticket at the link below:</p>
	<p><a href="{{ $link }}">{{ $link }}</a></p>
{{- end }}

<p>Thank you,</p>`,
		},
	},
	events.ReasonTickerClosed: {
		LanguageZhCN: {
			Title:   "工单关闭",
			Content: "标题为：{{ .values.title }}的工单被{{ .event.annotations.operator }}关闭了",
			Details: `<p>尊敬的 {{ .user.name }}({{ .user.displayName }})</p>

<p>您的工单({{.values.name}})已关闭，感谢您的反馈。</p>

<ul>
<li><strong>工单编号：</strong> {{.values.name}} </li>
<li><strong>主题：</strong> {{.values.title}} </li>
<li><strong>操作人：</strong> {{.event.annotations.operator}} </li>
</ul>

<p>如果您的问题未得到解决，您可以重新打开该工单或联系相关人员。</p>

<p>感谢您的支持</p>`,
		},
		LanguageEnUS: {
			Title:   "Ticket Closed",
			Content: "The Ticket {{ .values.title }} has been closed by {{ .event.annotations.operator }}",
			Details: `<p>Dear {{ .user.name }}({{ .user.displayName }}),</p>

<p>Your ticket ({{.values.name}}) has been closed. Thank you for your feedback.</p>

<ul>
<li><strong>Ticket ID:</strong> {{.values.name}} </li>
<li><strong>Title:</strong> {{.values.title}} </li>
<li><strong>Operator:</strong> {{.event.annotations.operator}} </li>
</ul>

<p>If you have questions or concerns, please reopen the ticket or contact the support team.</p>

<p>Thank you,</p>`,
		},
	},
	events.UserPermissionsRefresh: {
		LanguageZhCN: {
			Title:   "用户权限变更",
			Content: "{{ .user.name }}({{ .user.displayName }})的权限已发生变更",
			Details: `<h2>用户权限变更</h2>
<p>尊敬的 {{ .user.name }}({{ .user.displayName }}),</p>
<p>您的权限已发生变更.</p>
<ul>
<li><strong>企业:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>组织:</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>变更时间:</strong> {{.event.creationTimestamp | formatTimeWithZone}} </li>
</ul>

<p>如果有任何问题，请随时联系我们的支持团队。</p>

<p>谢谢，</p>
<p><strong>系统通知</strong></p>`,
		},
		LanguageEnUS: {
			Title:   "User Permission Changes",
			Content: "{{ .user.name }}({{ .user.displayName }}) permissions have changed",
			Details: `<h2>User Permission Changed Notification</h2>
<p>Dear {{ .user.name }}({{ .user.displayName }}),</p>
<p>Your permission has been changed.</p>
<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>Organization:</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>Change Time:</strong> {{.event.creationTimestamp | formatTimeWithZone}} </li>

</ul>

<p>If you have questions or concerns, please contact the support team.</p>

<p>Thank you,</p>
<p><strong>System Notification</strong></p>`,
		},
	},
	events.TenantOrganizationRefresh: {
		LanguageZhCN: {
			Title:   "企业或组织信息变更",
			Content: "企业或组织信息变更",
			Details: `<h2>企业或组织信息变更</h2>
<ul>
<li><strong>企业:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>组织:</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>变更时间:</strong> {{.event.creationTimestamp | formatTimeWithZone}} </li>
</ul>
<p>如果有任何问题，请随时联系我们的支持团队。</p>

<p>谢谢，</p>
<p><strong>系统通知</strong></p>`,
		},
		LanguageEnUS: {
			Title:   "Tenant or Organization Information Changed",
			Content: "Tenant or Organization Information Changed",
			Details: `<h2>Tenant or Organization Information Changed</h2>
<ul>
<li><strong>Enterprise:</strong> {{.tenant.alias}}({{.tenant.name}}) </li>
<li><strong>Organization:</strong> {{.organization.alias}}({{.organization.name}}) </li>
<li><strong>Change Time:</strong> {{.event.creationTimestamp | formatTimeWithZone}} </li>

</ul>

<p>If you have questions or concerns, please contact the support team.</p>

<p>Thank you,</p>
<p><strong>System Notification</strong></p>`,
		},
	},
}

const (
	SiteMessageTemplate = `<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <style>
      .message__template {
        font-family: Arial, sans-serif;
        color: #333;
        padding: 20px;
        line-height: 1.6;
        background-color: #f9f9f9;
      }
      .message__template__header,
      .message__template__footer {
        text-align: center;
        margin-bottom: 30px;
      }
      .message__template__header .logo {
        display: inline-flex;
        align-items: center;
        font-size: 32px;
        font-weight: bold;
        margin-top: 12px;
      }
      .message__template__header img {
        margin-right: 16px;
      }
      .message__template__details {
        background-color: #fff;
        text-align: left;
        line-height: 1.6;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 0 8px rgba(0, 0, 0, 0.05);
        max-width: 600px;
        margin: 0 auto;
      }
      .message__template h2 {
        color: #0056b3;
      }
      .message__template a {
        color: #007bff;
        text-decoration: none;
      }
      .ticket-info {
        margin-top: 20px;
      }
      .ticket-info strong {
        display: inline-block;
        width: 120px;
      }
      .message__template__footer {
        font-size: 12px;
        color: #888;
      }
    </style>
  </head>
  <body class="message__template">
    <div class="message__template__header">
      <h3>Majnoon ISMC Group</h3>
      <div class="logo">
        <img
          src="https://kubegems.oss-cn-chengdu.aliyuncs.com/kubegems.io/ismc-message.png"
          alt="Casdoor Logo"
          height="64px"
        />
        ISMC
      </div>
    </div>

    <div class="message__template__details">{{ .details }}</div>

    <div class="message__template__footer">
      <p>
        ISMC is a brand operated by Majnoon organization. For more info, please
        visit <a href="https://xiaoshiai.cn">xiaoshiai.cn</a>
      </p>
    </div>
  </body>
</html>`
)

func NewSiteSender(mongo store.Store) (*SiteSender, error) {
	return NewSiteSenderWithTemplate(mongo, DefaultI18nSiteMessageTemplate)
}

func NewSiteSenderWithTemplate(mongo store.Store, config I18nTemplate) (*SiteSender, error) {
	templates, err := NewI18nMessageTemplates(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create message templates: %w", err)
	}
	siteMessageTemplate, err := NewTextTemplate(SiteMessageTemplate)
	if err != nil {
		return nil, fmt.Errorf("failed to create email template: %w", err)
	}
	return &SiteSender{
		mongo:               mongo,
		templates:           templates,
		siteMessageTemplate: siteMessageTemplate,
	}, nil
}

type SiteSender struct {
	mongo               store.Store
	templates           MessageTemplate
	siteMessageTemplate *Template
}

func fromReasonGetAnnotations(userinfo *auth.UserProfile, raw RawMessage) map[string]string {
	var mapping = make(map[string]string)
	switch raw.Event.Reason {
	case events.AnnouncementReasonCreated:
		value, ok := raw.Values.(*announcement.Announcement)
		if !ok {
			break
		}
		mapping["title"] = value.Title
		mapping["content"] = value.Content
		mapping["creator"] = value.Creator

	case events.LicenseReasonExpireSoon, events.LicenseReasonExpired:
		value, ok := raw.Values.(*license.License)
		if !ok {
			break
		}
		mapping["for"] = value.For
		mapping["days"] = raw.Event.Annotations["days"]
		mapping["userName"] = userinfo.Name
		mapping["userAlias"] = userinfo.DisplayName
		mapping["tenantName"] = raw.Tenant.Name
		mapping["tenantAlias"] = raw.Tenant.Alias
		mapping["organizationName"] = raw.Organization.Name
		mapping["organizationAlias"] = raw.Organization.Alias
		mapping["name"] = value.Name
		mapping["notBefore"] = value.NotBefore.String()
		mapping["notAfter"] = value.NotAfter.String()

	case events.PrivateNodeReasonExpireSoon, events.PrivateNodeReasonExpired,
		events.PrivateNodeReasonReleaseSoon, events.PrivateNodeReasonReleased:
		value, ok := raw.Values.(*privatenode.PrivateNodeClaim)
		if !ok {
			break
		}
		if value.Status.ReleaseTimestamp != nil {
			mapping["releaseTimestamp"] = value.Status.ReleaseTimestamp.String()
		}
		mapping["tenantName"] = raw.Tenant.Name
		mapping["tenantAlias"] = raw.Tenant.Alias
		mapping["name"] = value.Name
		mapping["days"] = raw.Event.Annotations["days"]
		mapping["userName"] = userinfo.Name
		mapping["userAlias"] = userinfo.DisplayName
		mapping["clusterName"] = value.Cluster.Name
		mapping["creationTimestamp"] = value.CreationTimestamp.String()
		mapping["expire"] = value.Expire.String()

	case events.ReasonWalletRecharge:
		value, ok := raw.Values.(*wallet.Wallet)
		if !ok {
			break
		}
		mapping["delta"] = raw.Event.Annotations["delta"]
		mapping["userName"] = userinfo.Name
		mapping["userAlias"] = userinfo.DisplayName
		mapping["tenantName"] = raw.Tenant.Name
		mapping["tenantAlias"] = raw.Tenant.Alias
		mapping["balance"] = fmt.Sprintf("%0.2f", value.Balance.Float64())

	case events.ReasonTicketOpened, events.ReasonTicketReOpened, events.ReasonTicketAssigned, events.ReasonTicketReplied, events.ReasonTickerClosed:
		value, ok := raw.Values.(*ticketv2.BobTicket)
		if !ok {
			break
		}
		mapping["title"] = value.Title
		mapping["userName"] = userinfo.Name
		mapping["userAlias"] = userinfo.DisplayName
		mapping["name"] = value.Name
		mapping["createUser"] = value.CreateUser
		if len(value.Comments) > 0 {
			mapping["comment"] = value.Comments[0].Comment
		}
		mapping["operator"] = raw.Event.Annotations["operator"]
		mapping["replayTimestamp"] = raw.Event.CreationTimestamp.String()
		mapping["replayComment"] = raw.Event.Annotations["comment"]
	}
	return mapping
}

// Send implements messageSender.
func (s *SiteSender) Send(ctx context.Context, userinfo *auth.UserProfile, raw RawMessage) error {
	annotations := fromReasonGetAnnotations(userinfo, raw)
	msg, err := s.templates.Template(ctx, userinfo, LanguageEnUS, raw)
	if err != nil {
		return err
	}
	siteMsgBody, err := s.siteMessageTemplate.Execute(msg)
	if err != nil {
		return err
	}
	mh := &send.MessageHistory{
		ObjectMeta: store.ObjectMeta{
			Name: uuid.NewString(),
		},
		User:                       userinfo.Name,
		MessageGenerationTimestamp: msg.CreationTimestamp,
		Level:                      string(msg.Level),
		Type:                       msg.Type,
		ReadState:                  send.MessageReadStateUnread,
		// backward compatible
		Message: send.Message{
			ObjectMeta: store.ObjectMeta{
				Annotations: annotations,
			},
			EventReason:  raw.Event.Reason,
			Tenant:       raw.Tenant.Name,
			Organization: raw.Organization.Name,
			Type:         msg.Type,
			Level:        msg.Level,
			Title:        msg.Title,
			Content:      msg.Content,
			Details:      siteMsgBody,
			ResourceType: raw.Event.Object.Resource,
		},
	}
	return s.mongo.Create(ctx, mh)
}

// SenderType implements messageSender.
func (s *SiteSender) Type() send.MessageSendChannel {
	return "siteMessage"
}
