package message

import (
	"bytes"
	"context"
	"testing"

	"xiaoshiai.cn/common/email"
)

func TestSendEmail(t *testing.T) {
	ctx := context.Background()
	opts := email.SMTPOptions{
		Address:  "smtps://smtp.feishu.cn:25",
		Username: "",
		Password: "",
		Insecure: true,
	}
	to := ""
	if to == "" || !emailRegexp.MatchString(to) {
		t.Fatalf("invalid email address: %s", to)
	}
	addresslist := email.EmailAddressList{
		email.EmailAddress{Name: "zk", Email: to},
	}
	emailmsg := &email.Email{
		From:        email.EmailAddress{Name: "ISMC", Email: ""},
		To:          addresslist,
		Subject:     "Test Eamil",
		ContentType: "text/html; charset=UTF-8",
		Body:        bytes.NewBufferString("This is test content"),
	}
	if err := email.SendMail(ctx, opts, emailmsg); err != nil {
		t.Fatalf("send email: %v", err)
	}
	t.Log("success")
}
