package message

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"slices"
	texttemplate "text/template"
	"time"

	"github.com/Masterminds/sprig/v3"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/message/events"
)

var DefaultTemplateConfig = TitleContentTemplate{
	Title: `[{{ .event.reason }}] System Notification`,
	Content: `<h2>System Event Notification</h2>

<p>Hello, {{ .user.name }}({{ .user.displayName }}),</p>

<p>A new event has occurred in the system. Please find the details below:</p>

<h3>Event Details:</h3>
<ul>
<li><strong>Event ID:</strong> {{ .event.uid }}</li>
<li><strong>Event Type (Reason):</strong> {{ .event.reason }}</li>
<li><strong>Associated Object:</strong> {{ .event.object.resource }} - {{ .event.object.name }}</li>
{{- if .tenant }}
<li><strong>Tenant:</strong> {{ .tenant.alias }}({{ .tenant.name }})</li>
{{- end }}
{{- if .organization }}
<li><strong>Organization:</strong> {{ .organization.alias }}({{ .organization.name }})</li>
{{- end }}
<li><strong>Event Time:</strong> {{ .event.creationTimestamp }}</li>
</ul>

{{ if .event.annotations }}
<h3>Annotations:</h3>
<ul>
{{ range $key, $value := .event.annotations }}
	<li><strong>{{ $key }}:</strong> {{ $value }}</li>
{{ end }}
</ul>
{{ end }}

<h3>Message:</h3>
<p>{{ .event.messsge }}</p>

<p>For more information, please check the system dashboard or contact support.</p>`,
}

type Language string

const (
	LanguageZhCN       Language = "zh-CN"
	LanguageEnUS       Language = "en-US"
	LanguageFollowUser Language = "follow-user"
)

func UserLanguage(user *auth.UserProfile, defaultLanguage Language) Language {
	switch defaultLanguage {
	case LanguageZhCN, LanguageEnUS:
		return defaultLanguage
	case LanguageFollowUser:
		if slices.Contains(user.Languages, string(LanguageZhCN)) {
			return LanguageZhCN
		}
		switch user.State {
		case "CN":
			return LanguageZhCN
		}
	}
	return LanguageEnUS
}

type MessageTemplate interface {
	Template(ctx context.Context, user *auth.UserProfile, defaultLanguage Language, raw RawMessage) (*SimpleMessage, error)
}

type TitleContentTemplate struct {
	Title   string
	Content string
	Details string
}

type I18nTemplate map[events.Reason]map[Language]TitleContentTemplate

func NewI18nMessageTemplates(config I18nTemplate) (*I18nMessageTemplates, error) {
	return NewI18nMessageTemplatesWithDefault(config, DefaultTemplateConfig)
}

func NewI18nMessageTemplatesWithDefault(config I18nTemplate, def TitleContentTemplate) (*I18nMessageTemplates, error) {
	templates := map[events.Reason]map[Language]HtmlMessageTemplate{}
	for reason, languages := range config {
		for language, item := range languages {
			tpl, err := NewHtmlMessageTemplate(item)
			if err != nil {
				return nil, fmt.Errorf("invalid template for %s %s: %w", reason, language, err)
			}
			if _, ok := templates[reason]; !ok {
				templates[reason] = map[Language]HtmlMessageTemplate{}
			}
			templates[reason][language] = tpl
		}
	}
	deftemplate, err := NewHtmlMessageTemplate(def)
	if err != nil {
		return nil, fmt.Errorf("invalid default template: %w", err)
	}
	return &I18nMessageTemplates{templates: templates, defaultTemplate: deftemplate}, nil
}

type I18nMessageTemplates struct {
	templates       map[events.Reason]map[Language]HtmlMessageTemplate
	defaultTemplate HtmlMessageTemplate
}

func (m *I18nMessageTemplates) Template(ctx context.Context, user *auth.UserProfile, defaultLanguage Language, raw RawMessage) (*SimpleMessage, error) {
	type TemplateData struct {
		RawMessage `json:",inline"`
		User       *auth.UserProfile `json:"user,omitempty"`
	}
	title, content, details, err := m.Render(UserLanguage(user, defaultLanguage), raw.Event.Reason, TemplateData{RawMessage: raw, User: user})
	if err != nil {
		return nil, err
	}
	simpleMessage := SimpleMessage{
		CreationTimestamp: store.Time{Time: time.Now()},
		Level:             raw.Level,
		Type:              EventDescriptionType(raw.Event),
		Title:             title,
		Content:           content,
		Details:           details,
	}
	return &simpleMessage, nil
}

func (m *I18nMessageTemplates) Render(lang Language, reason events.Reason, data any) (string, string, string, error) {
	if t, ok := m.templates[reason]; ok {
		if render, ok := t[lang]; ok {
			return render.Render(data)
		}
		// use default language
		if render, ok := t[LanguageEnUS]; ok {
			return render.Render(data)
		}
	}
	return m.defaultTemplate.Render(data)
}

func NewHtmlMessageTemplate(item TitleContentTemplate) (HtmlMessageTemplate, error) {
	tpl := HtmlMessageTemplate{}
	if item.Title != "" {
		titletemplate, err := NewTemplate(item.Title)
		if err != nil {
			return HtmlMessageTemplate{}, err
		}
		tpl.title = titletemplate
	}
	if item.Content != "" {
		contenttemplate, err := NewTemplate(item.Content)
		if err != nil {
			return HtmlMessageTemplate{}, err
		}
		tpl.content = contenttemplate
	}
	if item.Details != "" {
		detailstemplate, err := NewTemplate(item.Details)
		if err != nil {
			return HtmlMessageTemplate{}, err
		}
		tpl.details = detailstemplate
	}
	return tpl, nil
}

type HtmlMessageTemplate struct {
	title   *Template
	content *Template
	details *Template
}

// Render render the template and return Title and Content by lang and data
func (t HtmlMessageTemplate) Render(data any) (string, string, string, error) {
	title, err := t.title.Execute(data)
	if err != nil {
		return "", "", "", err
	}
	content, err := t.content.Execute(data)
	if err != nil {
		return "", "", "", err
	}
	if t.details != nil {
		details, err := t.details.Execute(data)
		if err != nil {
			return "", "", "", err
		}
		return title, content, details, nil
	}
	return title, content, "", nil

}

func NewTemplate(tpl string) (*Template, error) {
	// use html template to avoid html injection rather than text/template
	fcMap := sprig.HtmlFuncMap()
	fcMap["formatTimeWithZone"] = formatTimeWithZone
	t := template.New("").Funcs(fcMap).Option("missingkey=zero")
	t, err := t.Parse(tpl)
	if err != nil {
		return nil, err
	}
	return &Template{t: t}, nil
}

func NewTextTemplate(tpl string) (*Template, error) {
	// use html template to avoid html injection rather than text/template
	fcMap := sprig.HtmlFuncMap()
	t := texttemplate.New("").Funcs(fcMap).Option("missingkey=zero")
	t, err := t.Parse(tpl)
	if err != nil {
		return nil, err
	}
	return &Template{t: t}, nil
}

type TemplateExecutor interface {
	Execute(wr io.Writer, data any) error
}

type Template struct {
	t TemplateExecutor
}

func (t Template) Execute(data any) (string, error) {
	datamap, err := toany(data) // convert to json
	if err != nil {
		return "", err
	}
	buf := bytes.NewBuffer(make([]byte, 256))
	buf.Reset()
	if err := t.t.Execute(buf, datamap); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func toany(data any) (any, error) {
	ret := any(nil)
	bytes, err := json.Marshal(data)
	if err != nil {
		return ret, err
	}
	if err := json.Unmarshal(bytes, &ret); err != nil {
		return ret, err
	}
	return ret, nil
}

func formatTimeWithZone(timeFormat string) string {
	t, err := time.Parse(time.RFC3339, timeFormat)
	if err != nil {
		return timeFormat
	}
	loc := base.GetCustomeTimeZone()
	if loc == nil {
		return timeFormat
	}
	return t.In(loc).Format(time.DateTime)
}
