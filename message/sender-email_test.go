package message

import (
	"context"
	"testing"
	"time"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/message/option"
	"xiaoshiai.cn/core/message/send"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/tenant"
)

func TestEmailSender_Send(t *testing.T) {
	ctx := context.Background()
	option := option.NewDefaultEmailOptions()
	option.Address = ""
	option.Username = ""
	option.Password = ""
	option.From = ""

	userinfo := &auth.UserProfile{
		User: auth.User{
			ObjectMeta:  store.ObjectMeta{Name: "zhangsan"},
			DisplayName: "张三",
			Email:       "<EMAIL>",
		},
		// Change state to empty to use english template
		State: "CN",
	}

	es, err := NewEmailSender(option)
	if err != nil {
		t.Fatal(err)
	}
	tests := []struct {
		name     string
		userinfo *auth.UserProfile
		raw      RawMessage
		wantErr  bool
	}{
		{
			name:     "test ticket opened",
			userinfo: userinfo,
			raw: RawMessage{
				Level:        send.MessageLevelInfo,
				Organization: organization.Organization{ObjectMeta: store.ObjectMeta{Name: "demo"}},
				Values: map[string]any{
					"name":              "1234-5678-9012-3456-7890",
					"creationTimestamp": "2023-01-01T00:00:00Z",
					"title":             "My Application is not working",
					"createUser":        "zhangsan",
					"comments": []map[string]string{
						{
							"comment": "My newly created application always pending",
							"user":    "zhangsan",
						},
					},
				},
				Event: events.Event{
					Reason: events.ReasonTicketOpened,
					Object: store.ResourcedObjectReference{
						Resource: "tickets",
						Name:     "1234-5678-9012-3456-7890",
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
					Messsge:           "Some How",
					Annotations: map[string]string{
						"comment":  "This is not a bug",
						"operator": "support-01",
					},
				},
			},
		},
		{
			name:     "test ticket replied",
			userinfo: userinfo,
			raw: RawMessage{
				Level: send.MessageLevelInfo,
				Tenant: tenant.Tenant{
					ObjectMeta: store.ObjectMeta{
						Name:  "demo",
						Alias: "Demo Tenant",
					},
				},
				Organization: organization.Organization{ObjectMeta: store.ObjectMeta{Name: "demo"}},
				Values: map[string]any{
					"name":              "1234-5678-9012-3456-7890",
					"creationTimestamp": "2023-01-01T00:00:00Z",
					"title":             "My Application is not working",
					"createUser":        "zhangsan",
					"comments": []map[string]string{
						{
							"comment": "My newly created application always pending",
							"user":    "zhangsan",
						},
					},
				},
				Event: events.Event{
					Reason: events.ReasonTicketReplied,
					Object: store.ResourcedObjectReference{
						Resource: "tickets",
						Name:     "1234-5678-9012-3456-7890",
						Scopes: []store.Scope{
							{
								Name:     "demo",
								Resource: "tenants",
							},
						},
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
					Messsge:           "Some How",
					Annotations: map[string]string{
						"comment":  "This is not a bug",
						"operator": "support-01",
					},
				},
			},
		},
		{
			name:     "test ticket closed",
			userinfo: userinfo,
			raw: RawMessage{
				Level:        send.MessageLevelInfo,
				Organization: organization.Organization{ObjectMeta: store.ObjectMeta{Name: "demo"}},
				Values: map[string]any{
					"name":              "1234-5678-9012-3456-7890",
					"creationTimestamp": "2023-01-01T00:00:00Z",
					"title":             "My Application is not working",
					"createUser":        "zhangsan",
					"comments": []map[string]string{
						{
							"comment": "My newly created application always pending",
							"user":    "zhangsan",
						},
					},
				},
				Event: events.Event{
					Reason: events.ReasonTickerClosed,
					Object: store.ResourcedObjectReference{
						Resource: "tickets",
						Name:     "1234-5678-9012-3456-7890",
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
					Messsge:           "Some How",
					Annotations: map[string]string{
						"comment":  "This is not a bug",
						"operator": "support-01",
					},
				},
			},
		},
		{
			name:     "license expire soon",
			userinfo: userinfo,
			raw: RawMessage{
				Level:        send.MessageLevelInfo,
				Organization: organization.Organization{ObjectMeta: store.ObjectMeta{Name: "demo"}},
				Values: map[string]any{
					"name":              "1234-5678-9012-3456-7890",
					"creationTimestamp": "2023-01-01T00:00:00Z",
					"for":               "MyAPP",
					"notBefore":         "2023-01-01T00:00:00Z",
					"notAfter":          "2023-03-01T00:00:00Z",
				},
				Event: events.Event{
					Reason: events.LicenseReasonExpireSoon,
					Object: store.ResourcedObjectReference{
						Resource: "license",
						Name:     "1234-5678-9012-3456-7890",
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
					Annotations:       map[string]string{"days": "3"},
				},
			},
		},
		{
			name:     "license expired",
			userinfo: userinfo,
			raw: RawMessage{
				Level:        send.MessageLevelInfo,
				Organization: organization.Organization{ObjectMeta: store.ObjectMeta{Name: "demo"}},
				Values: map[string]any{
					"name":              "1234-5678-9012-3456-7890",
					"creationTimestamp": "2023-01-01T00:00:00Z",
					"for":               "MyAPP",
					"notBefore":         "2023-01-01T00:00:00Z",
					"notAfter":          "2023-03-01T00:00:00Z",
				},
				Event: events.Event{
					Reason: events.LicenseReasonExpired,
					Object: store.ResourcedObjectReference{
						Resource: "license",
						Name:     "1234-5678-9012-3456-7890",
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
				},
			},
		},
		{
			name:     "test wallet recharge",
			userinfo: userinfo,
			raw: RawMessage{
				Level: send.MessageLevelInfo,
				Tenant: tenant.Tenant{
					ObjectMeta: store.ObjectMeta{
						Name:  "demo",
						Alias: "Demo Tenant",
					},
				},
				Event: events.Event{
					Reason: events.ReasonWalletRecharge,
					Object: store.ResourcedObjectReference{
						Resource: "wallet",
						Name:     "demo",
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
					Annotations: map[string]string{
						"delta": "1.00",
					},
				},
				Values: map[string]any{
					"name":              "1234-5678-9012-3456-7890",
					"creationTimestamp": "2023-01-01T00:00:00Z",
					"balance":           10.8989898,
				},
			},
		},
		{
			name:     "test announcement",
			userinfo: userinfo,
			raw: RawMessage{
				Level: send.MessageLevelInfo,
				Event: events.Event{
					Reason: events.AnnouncementReasonCreated,
					Object: store.ResourcedObjectReference{
						Resource: "announcement",
						Name:     "1234-5678-9012-3456-7890",
					},
					CreationTimestamp: store.Time{Time: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)},
				},
				Values: map[string]any{
					"title":             "系统维护通知",
					"content":           "本系统计划于 <strong>2025年12月31日</strong> 进行维护，届时将暂停服务。",
					"creator":           "admin",
					"creationTimestamp": "2023-01-01T00:00:00Z",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := es.Send(ctx, tt.userinfo, tt.raw); (err != nil) != tt.wantErr {
				t.Errorf("EmailSender.Send() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
