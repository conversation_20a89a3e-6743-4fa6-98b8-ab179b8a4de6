package app

import (
	"github.com/spf13/cobra"
	"xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/agent"
)

func NewAgentCommand() *cobra.Command {
	options := agent.DefaultOptions()
	cmd := &cobra.Command{
		Use:                "agent",
		Long:               "Start the ismc agent",
		DisableFlagParsing: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := config.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := config.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return agent.Run(ctx, options)
		},
	}
	config.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}
