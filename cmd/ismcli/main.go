package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"

	"github.com/regclient/regclient/types/descriptor"
	"github.com/regclient/regclient/types/ref"
	"github.com/spf13/cobra"
	"xiaoshiai.cn/common/progress"
	"xiaoshiai.cn/common/version/verflag"
	"xiaoshiai.cn/core/ismc/vmoci"
)

func main() {
	if err := NewCommand().Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

func NewCommand() *cobra.Command {
	cmd := &cobra.Command{
		Long: "vmoci is a tool for publishing virtual machine images to OCI registries.",
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			return cmd.Help()
		},
	}
	cmd.AddCommand(
		NewPushCommand(),
		NewLoginCommand(),
		NewInfoCommand(),
		NewPullCommand(),
		NewListCommand(),
	)
	flags := cmd.Flags()
	verflag.AddFlags(flags)
	return cmd
}

func NewInfoCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "info",
		Short: "Show information about the image",
		Long: `
vmoci is a tool for publishing virtual machine images to OCI registries.

Get information about the image:

	vmoci info example.com/vmimages/my-vm:latest
	
`,
		Args:         cobra.ExactArgs(1),
		SilenceUsage: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			if len(args) == 0 {
				return cmd.Help()
			}
			image := args[0]
			ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
			defer cancel()
			cli, err := vmoci.NewClient(vmoci.Options{})
			if err != nil {
				return err
			}
			info, err := cli.Info(ctx, image)
			if err != nil {
				return err
			}
			return JsonOutPut(info)
		},
	}
	return cmd
}

func NewLoginCommand() *cobra.Command {
	username, password := "", ""

	cmd := &cobra.Command{
		Use:          "login",
		Short:        "Log in to a registry",
		SilenceUsage: true,
		Args:         cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			cli, err := vmoci.NewClient(vmoci.Options{})
			if err != nil {
				return err
			}
			return cli.Login(context.Background(), args[0], username, password)
		},
	}
	flags := cmd.Flags()
	flags.StringVarP(&username, "username", "u", "", "Username")
	flags.StringVarP(&password, "password", "p", "", "Password")
	return cmd
}

type Options struct {
	OS           string
	Architecture string
	OSRelease    string
}

func NewPushCommand() *cobra.Command {
	options := Options{
		OS:           "linux",
		Architecture: "amd64",
	}
	cmd := &cobra.Command{
		Use:   "push",
		Short: `Push a virtual machine image to a registry.`,
		Long: `
vmoci is a tool for publishing virtual machine images to OCI registries.

Publish a vmware image:

	vmoci push vmware-image.vmdk example.com/vmimages/my-vm:latest

Publish a multiple platform images:

	vmoci push --os=linux --os-release='Ubuntu 22.04' --arch=arm64 qemu-image.qcow2 vmware-image.vmdk example.com/vmimages/my-vm:latest

`,
		SilenceUsage: true,
		Args:         cobra.MinimumNArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			if len(args) < 2 {
				return fmt.Errorf("image file and image name are required")
			}
			args, image := args[:len(args)-1], args[len(args)-1]
			imagefiles := []vmoci.ImageFiles{}
			for _, filename := range args {
				imagefiles = append(imagefiles, vmoci.ImageFiles{
					Architecture: options.Architecture,
					OS:           options.OS,
					OSRelease:    options.OSRelease,
					Files:        []string{filename},
				})
			}
			ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
			defer cancel()
			cli, err := vmoci.NewClient(vmoci.Options{})
			if err != nil {
				return err
			}
			return cli.PushImage(ctx, image, vmoci.PushImageOptions{Images: imagefiles, ShowProgress: true})
		},
	}
	flags := cmd.Flags()
	verflag.AddFlags(flags)

	flags.StringVar(&options.OS, "os", options.OS, "os name [linux,windows]")
	flags.StringVar(&options.Architecture, "arch", options.Architecture, "architecture name[arm64,amd64]")
	flags.StringVar(&options.OSRelease, "os-release", options.OSRelease, "os release name, such as 'Ubuntu 22.04'")
	return cmd
}

type PullOptions struct {
	Formats []vmoci.ImageFormat
}

func NewPullCommand() *cobra.Command {
	options := PullOptions{}
	cmd := &cobra.Command{
		Use:          "pull",
		Short:        `Pull a virtual machine image from a registry.`,
		Long:         `Pull a virtual machine image from a registry.`,
		Args:         cobra.MinimumNArgs(1),
		SilenceUsage: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			if len(args) == 0 {
				return fmt.Errorf("image name is required")
			}
			image := args[0]

			ref, err := ref.New(image)
			if err != nil {
				return err
			}
			dest := ref.Repository

			if len(args) > 1 {
				dest = args[1]
			}

			ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
			defer cancel()
			cli, err := vmoci.NewClient(vmoci.Options{})
			if err != nil {
				return err
			}

			os.MkdirAll(dest, 0o755)
			fmt.Printf("Pulling %s to %s\n", image, dest)

			pullToFile := func(ctx context.Context, desc descriptor.Descriptor, filename string, layer io.Reader) error {
				fmt.Printf("Pulling %s\n", filename)
				bar := progress.NewSingleBar(ctx)
				layer = bar.WrapReader(io.NopCloser(layer), filename, desc.Size, "pulling")

				f, err := os.Create(filepath.Join(dest, filename))
				if err != nil {
					return err
				}
				defer f.Close()
				_, err = io.Copy(f, layer)
				return err
			}
			checkLocalExists := func(ctx context.Context, desc descriptor.Descriptor, filename string) (bool, error) {
				stat, err := os.Stat(filepath.Join(dest, filename))
				if err != nil {
					if os.IsNotExist(err) {
						return false, nil
					}
					return false, err
				}
				// check size
				if stat.Size() != desc.Size {
					return false, nil
				}
				fmt.Printf("Skip pulling %s,eixsts\n", filename)
				return true, nil
			}
			return cli.PullImage(ctx, image, vmoci.PullImageOptions{
				Formats:            options.Formats,
				PullConfigCallback: pullToFile,
				CheckLocalExists:   checkLocalExists,
				PullLayerCallback:  pullToFile,
			})
		},
	}
	flags := cmd.Flags()
	flags.StringArrayP("format", "", nil, "image format [qcow2,vmdk,ova,raw]")
	return cmd
}

func NewListCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "Show information about the image",
		Long: `
List all tags of the image:

	vmoci list example.com/vmimages/my-vm
	
`,
		Args:         cobra.ExactArgs(1),
		SilenceUsage: true,
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			if len(args) == 0 {
				return cmd.Help()
			}
			image := args[0]
			ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
			defer cancel()
			cli, err := vmoci.NewClient(vmoci.Options{})
			if err != nil {
				return err
			}
			versions, err := cli.ListVersions(ctx, image)
			if err != nil {
				return err
			}
			return JsonOutPut(versions)
		},
	}
	return cmd
}

func JsonOutPut(data any) error {
	datas, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}
	fmt.Fprintln(os.Stdout, string(datas))
	return nil
}
