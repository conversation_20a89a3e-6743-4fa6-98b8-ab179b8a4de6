package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"xiaoshiai.cn/core/cmd/edge/app"
)

func main() {
	cmd := NewCommand()
	if err := cmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

func NewCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:  "edge",
		Long: "edge all in one executable",
	}
	cmd.AddCommand(
		app.NewServerCommand(),
	)
	return cmd
}
