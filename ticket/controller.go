package ticket

import (
	"context"
	"fmt"
	"time"

	"xiaoshiai.cn/common/store"
)

// 工单模版
type TicketTemplate struct {
	store.ObjectMeta     `json:",inline"`
	ApprovalUsers        []string               `json:"approvalUsers"`        // 审批人列表
	Category             TicketTemplateCategory `json:"category"`             // 模版类别
	HasProcessingTickets bool                   `json:"hasProcessingTickets"` // 是否有处理中的工单
}

type Ticket struct {
	store.ObjectMeta `json:",inline"`
	TemplateName     string                 `json:"templateName"`
	ApprovalUsers    []string               `json:"approvalUsers"`
	Category         TicketTemplateCategory `json:"category"`
	Title            string                 `json:"title"`
	Level            TicketLevel            `json:"level"`
	Comments         []TicketComment        `json:"comments"` // 工单描述，截图等信息
	Creator          string                 `json:"creator"`
	State            TicketStatus           `json:"state"`
}

type TicketComment struct {
	Content     string       `json:"content"`
	User        string       `json:"user"`
	Images      [][]byte     `json:"images"`
	Action      TicketAction `json:"action"`      // 评论的动作
	CommentTime CustomTime   `json:"commentTime"` // 评论的时间
}

type CustomTime struct {
	time.Time
}

func (ct *CustomTime) UnmarshalJSON(data []byte) error {
	if string(data) == `""` {
		ct.Time = time.Now()
		return nil
	}
	var err error
	ct.Time, err = time.Parse(`"2006-01-02T15:04:05Z07:00"`, string(data))
	return err
}

// every second shoud set description and images
type TicketStatus struct {
	CurrentState TicketState `json:"currentState"`
	ApprovalUser string      `json:"approvalUser"`
}

type TicketTemplateCategory string

const (
	TicketTemplateCategoryApp     TicketTemplateCategory = "application"
	TicketTemplateCategoryCluster TicketTemplateCategory = "cluster"
	TicketTemplateCategoryOther   TicketTemplateCategory = "other"
)

type TicketState string

const (
	TicketStateInit       TicketState = "init"       // 初始化
	TicketStateProcessing TicketState = "processing" // 待处理
	TicketStateRejected   TicketState = "rejected"   // 已拒绝
	TicketStateClosed     TicketState = "closed"     // 已关闭
	TicketStateComplete   TicketState = "complete"   // 已完成
)

type TicketAction string

const (
	TicketActionCreate TicketAction = "create" // 创建
	TicketActionClose  TicketAction = "close"  // 关闭
	TicketActionReopen TicketAction = "reopen" // 重开
	TicketActionReject TicketAction = "reject" // 拒接
	TicketActionAccept TicketAction = "accept" // 同意
)

type TicketLevel string

const (
	LevelNormal     TicketLevel = "normal"
	LevelEnmergency TicketLevel = "enmergency"
	LevelDisaster   TicketLevel = "disaster"
)

type TicketFn func(storage store.TransactionStore, ticketName string, srcState, dstState TicketState, comment *TicketComment) error

type TicketService interface {
	ListTicketsByTemplate(ctx context.Context, templateName string, states []TicketState) ([]Ticket, error)
	Execute(ctx context.Context, name string, currentState TicketState, action TicketAction, ticket *Ticket, comment *TicketComment) error
	List(ctx context.Context, list store.ObjectList, opts ...store.ListOption) error
	Create(ctx context.Context, obj store.Object, opts ...store.CreateOption) error
	Update(ctx context.Context, obj store.Object, opts ...store.UpdateOption) error
	Delete(ctx context.Context, obj store.Object, opts ...store.DeleteOption) error
	Get(ctx context.Context, name string, obj store.Object, opts ...store.GetOption) error
}

type ticketService struct {
	storage        store.TransactionStore // mongodb
	transitionMap  map[TicketState]map[TicketAction]TicketState
	transitionFunc TicketFn
}

func NewTicketService(storage store.TransactionStore) TicketService {
	ts := &ticketService{
		storage:        storage,
		transitionFunc: ticketTransitionFunc,
	}
	// 初始化->创建->待处理
	ts.setTransitionMap(TicketStateInit, TicketActionCreate, TicketStateProcessing)
	// 待处理 ->关闭->已关闭
	ts.setTransitionMap(TicketStateProcessing, TicketActionClose, TicketStateClosed)
	// 待处理->拒绝->已拒绝
	ts.setTransitionMap(TicketStateProcessing, TicketActionReject, TicketStateRejected)
	// 已拒绝->重开->待处理
	ts.setTransitionMap(TicketStateRejected, TicketActionReopen, TicketStateProcessing)
	// 已关闭->重开->待处理
	ts.setTransitionMap(TicketStateClosed, TicketActionReopen, TicketStateProcessing)
	// 待处理->同意->待处理/完成
	ts.setTransitionMap(TicketStateProcessing, TicketActionAccept, TicketStateProcessing)
	return ts
}

func (t *ticketService) setTransitionMap(srcState TicketState, action TicketAction, dstState TicketState) {
	transitionMap := t.transitionMap
	if transitionMap == nil {
		transitionMap = make(map[TicketState]map[TicketAction]TicketState)
	}
	if _, ok := transitionMap[srcState]; !ok {
		transitionMap[srcState] = make(map[TicketAction]TicketState)
	}
	if _, ok := transitionMap[srcState][action]; !ok {
		transitionMap[srcState][action] = dstState
	}
	t.transitionMap = transitionMap
}

func (t *ticketService) Execute(ctx context.Context, name string, currentState TicketState, action TicketAction, ticket *Ticket, comment *TicketComment) error {
	transitionMap := t.transitionMap
	actionMap, ok := transitionMap[currentState]
	if !ok {
		return fmt.Errorf("not find current state:%v", currentState)
	}
	dstState, ok := actionMap[action]
	if !ok {
		return fmt.Errorf("not find action:%v", action)
	}
	if ticket != nil {
		if err := t.Create(ctx, ticket); err != nil {
			return err
		}
	}
	return t.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		return t.transitionFunc(t.storage, name, currentState, dstState, comment)
	})
}

func (t *ticketService) List(ctx context.Context, list store.ObjectList, opts ...store.ListOption) error {
	return t.storage.List(ctx, list, opts...)
}

func (t *ticketService) Create(ctx context.Context, obj store.Object, opts ...store.CreateOption) error {
	return t.storage.Create(ctx, obj, opts...)
}

func (t *ticketService) Update(ctx context.Context, obj store.Object, opts ...store.UpdateOption) error {
	return t.storage.Update(ctx, obj, opts...)
}

func (t *ticketService) Delete(ctx context.Context, obj store.Object, opts ...store.DeleteOption) error {
	return t.storage.Delete(ctx, obj, opts...)
}

func (t *ticketService) Get(ctx context.Context, name string, obj store.Object, opts ...store.GetOption) error {
	return t.storage.Get(ctx, name, obj, opts...)
}

func (ts *ticketService) ListTicketsByTemplate(ctx context.Context, templateName string, states []TicketState) ([]Ticket, error) {
	var values []any
	for _, state := range states {
		values = append(values, string(state))
	}
	opt := store.WithFieldRequirements(
		store.Requirement{
			Key:      "state.currentState",
			Operator: store.In,
			Values:   values,
		},
		store.Requirement{
			Key:      "templateName",
			Operator: store.Equals,
			Values:   []any{templateName},
		},
	)
	list := store.List[Ticket]{}
	if err := ts.storage.List(ctx, &list, opt); err != nil {
		return nil, err
	}
	return list.Items, nil
}

// 状态转移函数
func ticketTransitionFunc(storage store.TransactionStore, ticketName string, srcState, dstState TicketState, comment *TicketComment) error {
	ctx := context.Background()
	ticketObj := &Ticket{}
	if err := storage.Get(ctx, ticketName, ticketObj); err != nil {
		return err
	}

	if ticketObj.State.CurrentState == srcState {
		var isReopen bool
		switch ticketObj.State.CurrentState {
		case TicketStateRejected, TicketStateClosed:
			// 重新打开需要评论等信息
			// ticketObj.Comments = ticketObj.Comments[:len(ticketObj.Comments)-1]
			isReopen = true
		}
		if comment != nil {
			ticketObj.Comments = append(ticketObj.Comments, *comment)
		}
		switch dstState {
		case TicketStateProcessing:
			next := findNextNode(ticketObj.ApprovalUsers, ticketObj.State.ApprovalUser)
			if isReopen {
				next = ticketObj.State.ApprovalUser
			}
			ticketObj.State.ApprovalUser = next
			ticketObj.State.CurrentState = TicketStateProcessing
			if next == "" {
				ticketObj.State.CurrentState = TicketStateComplete
			}
		default:
			ticketObj.State.CurrentState = dstState
		}
		return storage.Update(ctx, ticketObj)
	}
	return fmt.Errorf("ticket current state error")
}

func findNextNode(approvalusers []string, userid string) string {
	if userid == "" {
		// 开始流程
		return approvalusers[0]
	}
	for i := range approvalusers {
		node := approvalusers[i]
		if node == userid {
			if i+1 < len(approvalusers) {
				return approvalusers[i+1]
			}
		}
	}
	return ""
}
