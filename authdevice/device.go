package authdevice

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"xiaoshiai.cn/common/crypto"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/useragent"
)

const DeviceIDCookieKey = "device_id"

// UserDevice is the device that the user is using
// if the user is using a browser, the device is the user agent
// if the user is using a mobile app, the device is the device
type UserDevice struct {
	store.ObjectMeta `json:",inline"`
	Type             UserDeviceType
}

type UserDeviceType string

const (
	UserDeviceTypeUnknown UserDeviceType = "unknown"
	UserDeviceTypeBrwoser UserDeviceType = "browser"
	UserDeviceTypeMobile  UserDeviceType = "mobile"
)

func WithUserDevice(ctx context.Context, devid *UserDevice) context.Context {
	return api.SetContextValue(ctx, "device-id", devid)
}

func UserDeviceFromContext(ctx context.Context) *UserDevice {
	return api.GetContextValue[*UserDevice](ctx, "device-id")
}

type UserDeviceOptions struct {
	EncryptionKey string `json:"encryptionKey,omitempty" description:"encryption key for device data, 32 bytes"`
}

func NewDefaultUserDeviceOptions() *UserDeviceOptions {
	return &UserDeviceOptions{}
}

func NewUserDeviceService(options *UserDeviceOptions) (*UserDeviceService, error) {
	encodeKey := options.EncryptionKey
	if len(encodeKey) != 32 {
		return nil, fmt.Errorf("encode key must be 32 bytes long")
	}
	return &UserDeviceService{EncodeKey: encodeKey}, nil
}

// UserDeviceService ensures that the user has a device id cookie
type UserDeviceService struct {
	EncodeKey string
}

var _ api.Filter = api.FilterFunc((&UserDeviceService{}).Filter())

// Process implements api.Filter.
func (s *UserDeviceService) Filter() api.FilterFunc {
	return func(w http.ResponseWriter, r *http.Request, next http.Handler) {
		s.ensureDevice(w, r)
		next.ServeHTTP(w, r)
	}
}

func (s *UserDeviceService) ensureDevice(w http.ResponseWriter, r *http.Request) {
	if dev, _ := s.GetDevice(r); dev != nil {
		return
	}
	device := UserDevice{
		ObjectMeta: store.ObjectMeta{
			Name:        rand.RandomAlphaNumeric(16),
			Description: ParseUserAgentName(r),
		},
		Type: UserDeviceTypeBrwoser,
	}
	encryptedDevice := s.EncodeDeviceID(device)
	// set the cookie
	api.SetCookie(w, DeviceIDCookieKey, encryptedDevice, time.Now().Add(365*24*time.Hour))
}

func (s *UserDeviceService) GetDevice(r *http.Request) (*UserDevice, error) {
	encrypted := api.GetCookie(r, DeviceIDCookieKey)
	return s.DecodeDevice(encrypted)
}

func (s *UserDeviceService) DecodeDevice(encrypted string) (*UserDevice, error) {
	if encrypted == "" {
		return nil, fmt.Errorf("empty device id")
	}
	decoded, err := crypto.AesCbcPKCS7DecryptBase64([]byte(s.EncodeKey), encrypted)
	if err != nil {
		return nil, err
	}
	dev := &UserDevice{}
	if err := json.Unmarshal(decoded, dev); err != nil {
		return nil, err
	}
	return dev, nil
}

func (s *UserDeviceService) EncodeDeviceID(dev UserDevice) string {
	data, _ := json.Marshal(dev)
	encrypted, _ := crypto.AesCbcPKCS7EncryptBase64([]byte(s.EncodeKey), data)
	return string(encrypted)
}

func ParseUserAgentName(r *http.Request) string {
	ua := useragent.Parse(r.UserAgent())
	return fmt.Sprintf("%s-%s %s", ua.Name, ua.Version, ua.OS)
}
