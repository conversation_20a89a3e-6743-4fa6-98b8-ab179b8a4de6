package authz

import (
	"context"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	_ "xiaoshiai.cn/core/rbac"
)

// NewCustomAuthorizer defines the allowed actions and resources for every authenticated user.
// if API is role-based, update [rbac.DefaultTenantRoles] or [rbac.DefaultOrganizationRoles] instead.
func NewCustomAuthorizer(storage store.Store) api.Authorizer {
	return api.NewCustomAuthorizer(
		[]api.CustomAuthorizeNode{
			{
				Actions: []string{"get", "list"},
				Resource: []string{
					"avatars", "avatars:*", // user avatars
					"user-search",            // tenant search add user
					"products", "products:*", // marketplace
					"announcements", "announcements:*", // announcements
					"cluster-summaries", "cluster-summaries:*", // cluster summaries
					"system-clustermetadata", "system-clustermetadata:*", // system cluster metadata
					"clusters:*:metadata", "clusters:*:metadata:*", // cluster metadata
					"clusters:*:privatenode-skus", "clusters:*:privatenode-skus:*", // cluster private node skus
					"products:*:skus", "products:*:skus:*", // product skus
					"products:*:versions", "products:*:versions:*", // product versions
					"tickets:*:pictures:*",
					"ticket-modules",
				},
			},
			{
				Actions: []string{"*"},
				Resource: []string{
					"current:**",                    // user self operations
					"testchannel", "testchannel:**", // test channel
					"notify", // notify websocket
				},
			},
			{
				Actions:  []string{"create"},
				Resource: []string{"tenant-register:*", "tenant-register"}, // tenant register
			},
		},
	)
}

type UnionAuthorizer []api.AuthorizerFunc

func (aa UnionAuthorizer) Authorize(ctx context.Context, user api.UserInfo, a api.Attributes) (api.Decision, string, error) {
	for _, authorizer := range aa {
		decision, reason, err := authorizer(ctx, user, a)
		if err != nil {
			return api.DecisionDeny, reason, err
		}
		if decision != api.DecisionAllow {
			return api.DecisionDeny, reason, nil
		}
	}
	return api.DecisionAllow, "", nil
}
