package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +genclient
// +genclient:nonNamespaced
type Cluster struct {
	metav1.TypeMeta `json:",inline"`
	// Standard object's metadata.
	// +optional
	metav1.ObjectMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Spec defines the behavior of the License.
	// +optional
	Spec ClusterSpec `json:"spec,omitempty" protobuf:"bytes,2,opt,name=spec"`

	// Status describes the current status of a License.
	// +optional
	Status ClusterStatus `json:"status,omitempty" protobuf:"bytes,3,opt,name=status"`
}
type ClusterSpec struct {
	// Kubeconfig is the kube config file content.
	Config string `json:"config" protobuf:"bytes,1,opt,name=config"`
}

// ClusterStatus is information about the current status of a License.
type ClusterStatus struct {
	Version *ClusterVersion `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	// Represents the latest available observations of a License's current state.
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	// +listType=map
	// +listMapKey=type
	Conditions []ClusterCondition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type" protobuf:"bytes,2,rep,name=conditions"`
}

type ClusterVersion struct {
	Version string `json:"version,omitempty" protobuf:"bytes,1,opt,name=version"`
	Vendor  string `json:"vendor,omitempty" protobuf:"bytes,2,opt,name=vendor"`
}

type ClusterConditionType string

const (
	ClusterConditionTypeConfigValid ClusterConditionType = "ConfigValid"
)

// ClusterCondition contains details about state of License.
type ClusterCondition struct {
	// Type of License controller condition.
	Type ClusterConditionType `json:"type" protobuf:"bytes,1,opt,name=type,casttype=LicenseConditionType"`
	// Status of the condition, one of True, False, Unknown.
	Status corev1.ConditionStatus `json:"status" protobuf:"bytes,2,opt,name=status,casttype=ConditionStatus"`
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime"`
	// +optional
	Reason string `json:"reason,omitempty" protobuf:"bytes,5,opt,name=reason"`
	// +optional
	Message string `json:"message,omitempty" protobuf:"bytes,6,opt,name=message"`
}

// LicenseList is a list of Licenses.
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type ClusterList struct {
	metav1.TypeMeta `json:",inline"`
	// Standard list metadata.
	// +optional
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`

	// Items is the list of License objects in the list.
	Items []Cluster `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +k8s:conversion-gen:explicit-from=net/url.Values
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type ClusterProxyOptions struct {
	metav1.TypeMeta `json:",inline"`
	Path            string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}
