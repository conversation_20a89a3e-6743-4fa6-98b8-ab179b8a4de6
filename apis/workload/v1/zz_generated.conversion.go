//go:build !ignore_autogenerated
// +build !ignore_autogenerated

/*
Copyright (C) 2024 The Poxiaoyun Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by conversion-gen. DO NOT EDIT.

package v1

import (
	url "net/url"

	conversion "k8s.io/apimachinery/pkg/conversion"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

func init() {
	localSchemeBuilder.Register(RegisterConversions)
}

// RegisterConversions adds conversion functions to the given scheme.
// Public to allow building arbitrary schemes.
func RegisterConversions(s *runtime.Scheme) error {
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*ClusterProxyOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_ClusterProxyOptions(a.(*url.Values), b.(*ClusterProxyOptions), scope)
	}); err != nil {
		return err
	}
	if err := s.AddGeneratedConversionFunc((*url.Values)(nil), (*VirtualMachineVncOptions)(nil), func(a, b interface{}, scope conversion.Scope) error {
		return Convert_url_Values_To_v1_VirtualMachineVncOptions(a.(*url.Values), b.(*VirtualMachineVncOptions), scope)
	}); err != nil {
		return err
	}
	return nil
}

func autoConvert_url_Values_To_v1_ClusterProxyOptions(in *url.Values, out *ClusterProxyOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["path"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Path, s); err != nil {
			return err
		}
	} else {
		out.Path = ""
	}
	return nil
}

// Convert_url_Values_To_v1_ClusterProxyOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_ClusterProxyOptions(in *url.Values, out *ClusterProxyOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_ClusterProxyOptions(in, out, s)
}

func autoConvert_url_Values_To_v1_VirtualMachineVncOptions(in *url.Values, out *VirtualMachineVncOptions, s conversion.Scope) error {
	// WARNING: Field TypeMeta does not have json tag, skipping.

	if values, ok := map[string][]string(*in)["path"]; ok && len(values) > 0 {
		if err := runtime.Convert_Slice_string_To_string(&values, &out.Path, s); err != nil {
			return err
		}
	} else {
		out.Path = ""
	}
	return nil
}

// Convert_url_Values_To_v1_VirtualMachineVncOptions is an autogenerated conversion function.
func Convert_url_Values_To_v1_VirtualMachineVncOptions(in *url.Values, out *VirtualMachineVncOptions, s conversion.Scope) error {
	return autoConvert_url_Values_To_v1_VirtualMachineVncOptions(in, out, s)
}
