package v1

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +genclient
type VirtualMachine struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              VirtualMachineSpec   `json:"spec,omitempty"`
	Status            VirtualMachineStatus `json:"status,omitempty"`
}

type VirtualMachinePhase string

const (
	VirtualMachinePhaseUnkown  VirtualMachinePhase = "Unkown"
	VirtualMachinePhasePending VirtualMachinePhase = "Pending"
	VirtualMachinePhaseRunning VirtualMachinePhase = "Running"
	VirtualMachinePhaseStopped VirtualMachinePhase = "Stopped"
)

type PowerState string

const (
	PowerStateUnknown   PowerState = "Unknow"
	PowerStateOn        PowerState = "On"
	PowerStateOff       PowerState = "Off"
	PowerStateSuspended PowerState = "Suspended"
)

type VirtualMachineSpec struct {
	// Power is the power state of the virtual machine
	Power PowerState `json:"power,omitempty"`

	// Image is the image to use for the virtual machine
	// an image may have one or more disks
	Image Image `json:"image,omitempty"`

	// Disks is a list of addtional disks to attach to the virtual machine
	// +optional
	// +patchMergeKey=name
	// +patchStrategy=merge,retainKeys
	// +listType=map
	// +listMapKey=name
	Disks []corev1.LocalObjectReference `json:"disks,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`

	// Networks is a list of NetworkClass name to attach to the virtual machine to
	// +optional
	// +patchMergeKey=name
	// +patchStrategy=merge,retainKeys
	// +listType=map
	// +listMapKey=name
	Networks []corev1.LocalObjectReference `json:"networks,omitempty" patchStrategy:"merge,retainKeys" patchMergeKey:"name"`

	// Resources is the resources to allocate to the virtual machine
	Resources corev1.ResourceRequirements `json:"resources"`

	// NodeName is a request to schedule this onto a specific node. If it is non-empty,
	// the scheduler simply schedules this onto that node, assuming that it fits resource
	// requirements.
	// +optional
	NodeName string `json:"nodeName,omitempty"`

	// +optional
	SchedulerName string `json:"schedulerName,omitempty"`

	// NodeSelector is a selector which must be true to fit on a node.
	// Selector which must match a node's labels to be scheduled on that node.
	// +optional
	// +mapType=atomic
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`

	// If specified, the VirtualMachine's tolerations.
	// +optional
	// +listType=atomic
	Tolerations []corev1.Toleration `json:"tolerations,omitempty"`

	// If specified, the VirtualMachine's scheduling constraints
	// +optional
	Affinity *Affinity `json:"affinity,omitempty"`

	// +optional
	OS *VirtualMachineOS `json:"os,omitempty"`
}

// Affinity is a group of affinity scheduling rules.
type Affinity struct {
	// Describes node affinity scheduling rules for the VirtualMachine.
	// +optional
	NodeAffinity *corev1.NodeAffinity `json:"nodeAffinity,omitempty"`
	// Describes VirtualMachine affinity scheduling rules (e.g. co-locate this VirtualMachine in the same node, zone, etc. as some other VirtualMachine(s)).
	// +optional
	VirtualMachineAffinity *corev1.PodAffinity `json:"virtualMachineAffinity,omitempty"`
	// Describes VirtualMachine anti-affinity scheduling rules (e.g. avoid putting this VirtualMachine in the same node, zone, etc. as some other VirtualMachine(s)).
	// +optional
	VirtualMachineAntiAffinity *corev1.PodAntiAffinity `json:"virtualMachineAntiAffinity,omitempty"`
}

// VirtualMachineOS defines the OS parameters of a VirtualMachine.
type VirtualMachineOS struct {
	// Name is the name of the operating system. The currently supported values are linux and windows.
	// Additional value may be defined in future and can be one of:
	// https://github.com/opencontainers/runtime-spec/blob/master/config.md#platform-specific-configuration
	// Clients should expect to handle additional values and treat unrecognized values in this field as os: null
	Name OSName `json:"name"`

	// Architecture is the hardware architecture the guest os.
	// +optional
	Architecture string `json:"architecture,omitempty"`
}

// OSName is the set of OS'es that can be used in OS.
// +enum
type OSName string

// These are valid values for OSName
const (
	Linux   OSName = "linux"
	Windows OSName = "windows"
)

type VirtualMachineInterface struct {
	Name string `json:"name,omitempty" protobuf:"bytes,1,opt,name=name"`
}

type VirtualMachineDisk struct {
	Name                     string `json:"name,omitempty" protobuf:"bytes,1,opt,name=name"`
	VirtualMachineDiskSource `json:",inline"`
}

type VirtualMachineDiskSource struct {
	// Local is the source local disk to use for the disk
	// +optional
	Local *corev1.LocalObjectReference `json:"local,omitempty"`
}

type Image struct {
	// Name is the virtual machine image to use, it a oci image addr.
	Name string `json:"name"`

	// Capacity is the capacity of the image disk, it is used to override the image root disk capacity
	// if this field is empty, the image disk capacity will be used
	// +optional
	Capacity *resource.Quantity `json:"capacity,omitempty"`

	// Image pull policy.
	// One of Always, Never, IfNotPresent.
	// Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
	// Cannot be updated.
	// +optional
	PullPolicy corev1.PullPolicy `json:"pullPolicy,omitempty"`

	// ImagePullSecrets is an optional list of references to secrets in the same namespace to use for pulling any of the images.
	// If specified, these secrets will be passed to individual puller implementations for them to use.
	// +optional
	// +patchMergeKey=name
	// +patchStrategy=merge
	// +listType=map
	// +listMapKey=name
	PullSecrets []corev1.LocalObjectReference `json:"pullSecrets,omitempty" patchStrategy:"merge" patchMergeKey:"name"`
}

type CloudInit struct {
	// UserData contains NoCloud inline cloud-init userdata.
	// + optional
	UserData string `json:"userData,omitempty"`
	// NetworkData contains NoCloud inline cloud-init networkdata.
	// + optional
	NetworkData string `json:"networkData,omitempty"`
}

const (
	VirtualMachineConditionTypeRunning string = "Running"

	// VirtualMachineConditionTypeCreated is the condition type for the VirtualMachine already created.
	VirtualMachineConditionTypeCreated string = "Created"
)

type VirtualMachineStatus struct {
	// Power is the power state of the virtual machine
	Power PowerState `json:"power,omitempty"`

	// The memory usage is the memory working set.
	Usage corev1.ResourceList `json:"usage" protobuf:"bytes,4,rep,name=usage"`

	// The Disk usage is the disk usage.
	DiskUsage corev1.ResourceList `json:"diskUsage" protobuf:"bytes,5,rep,name=diskUsage"`

	// Phase is the current phase of the virtual machine
	Phase VirtualMachinePhase `json:"phase,omitempty"`

	// Message is a human readable message with details about the current phase
	// it can be used to communicate errors or warnings
	Message string `json:"message,omitempty"`

	// +optional
	// LastPowerOffTime represents the last time the virtual machine was powered off
	LastPowerOffTime *metav1.Time `json:"lastPowerOffTime,omitempty"`

	// Resources is the current resource requirements of the virtual machine
	Resources corev1.ResourceRequirements `json:"resources,omitempty"`

	// Conditions
	// +optional
	// +patchMergeKey=type
	// +patchStrategy=merge
	// +listType=map
	// +listMapKey=type
	Conditions []metav1.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type"`

	// IPs holds the IP addresses allocated. may be allocated IPv4 or IPv6.
	// This list is empty if no IPs have been allocated yet.
	// +optional
	// +patchStrategy=merge
	// +patchMergeKey=ip
	// +listType=map
	// +listMapKey=ip
	IPs []IP `json:"ips,omitempty" patchStrategy:"merge" patchMergeKey:"ip"`

	// RFC 3339 date and time at which the object was acknowledged by the Kubelet.
	// This is before the Kubelet pulled the container image(s) for the pod.
	// +optional
	StartTime *metav1.Time `json:"startTime,omitempty"`

	// Image is the name of vm image that the vm is running.
	// The container image is the image used in the PodSpec
	Image string `json:"image"`

	// ImageID is the image ID of the vm's image. The image ID may not
	// match the image used in the PodSpec, as it may have been
	// resolved by the runtime.
	ImageID string `json:"imageID"`
}

type IP struct {
	// IP is the IP address assigned
	IP string `json:"ip,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type VirtualMachineList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	Items           []VirtualMachine `json:"items" protobuf:"bytes,2,rep,name=items"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
// +k8s:conversion-gen:explicit-from=net/url.Values
type VirtualMachineVncOptions struct {
	metav1.TypeMeta `json:",inline"`
	Path            string `json:"path,omitempty" protobuf:"bytes,1,opt,name=path"`
}
