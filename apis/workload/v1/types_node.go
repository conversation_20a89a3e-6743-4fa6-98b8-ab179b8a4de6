package v1

import (
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type Node struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              NodeSpec   `json:"spec,omitempty"`
	Status            NodeStatus `json:"status,omitempty"`
}

type NodeSpec struct {
	// Unschedulable controls node schedulability of new pods. By default, node is schedulable.
	// More info: https://kubernetes.io/docs/concepts/nodes/node/#manual-node-administration
	// +optional
	Unschedulable bool `json:"unschedulable,omitempty" protobuf:"varint,4,opt,name=unschedulable"`
	// If specified, the node's taints.
	// +optional
	// +listType=atomic
	Taints []corev1.Taint `json:"taints,omitempty" protobuf:"bytes,5,opt,name=taints"`
}

type NodeStatus struct {
	// The memory usage is the memory working set.
	Usage corev1.ResourceList `json:"usage" protobuf:"bytes,4,rep,name=usage"`
	// Capacity represents the total resources of a node.
	// +optional
	Capacity corev1.ResourceList `json:"capacity,omitempty"`
	// +optional
	// +patchStrategy=merge
	// +patchMergeKey=ip
	// +listType=map
	// +listMapKey=ip
	IPs []IP `json:"ips,omitempty" patchStrategy:"merge" patchMergeKey:"ip"`

	// Conditions
	// +optional
	// +patchStrategy=merge
	// +patchMergeKey=type
	// +listType=map
	// +listMapKey=type
	Conditions []metav1.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type"`
	Extra      map[string]string  `json:"annotations,omitempty"`
	// NodeInfo
	NodeInfo NodeSystemInfo `json:"nodeInfo,omitempty"`
}

type NodeSystemInfo struct {
	// MachineID reported by the node. For unique machine identification
	MachineID string `json:"machineID,omitempty"`

	// SystemUUID reported by the node. For unique machine identification
	SystemUUID string `json:"systemUUID,omitempty"`

	// Boot ID reported by the node.
	BootID string `json:"bootID,omitempty"`

	// OS
	OS OSInfo `json:"os"`

	// Hardware is the hardware info
	Hardware HardwareInfo `json:"hardware"`

	// Software is the software of vitual machine manager system.
	Software SoftwareInfo `json:"software"`

	// CPU is the cpu info
	CPU CPUInfo `json:"cpu"`
}
type HardwareInfo struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

type CPUInfo struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

type OSInfo struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

type SoftwareInfo struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

const (
	// NodeConditionTypeReady is the condition type of Node is ready.
	NodeConditionTypeReady string = "Ready"
)

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type NodeList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty" protobuf:"bytes,1,opt,name=metadata"`
	Items           []Node `json:"items" protobuf:"bytes,2,rep,name=items"`
}

func IPsFrom(ips []string) []IP {
	var result []IP
	for _, ip := range ips {
		result = append(result, IP{IP: ip})
	}

	return result
}
