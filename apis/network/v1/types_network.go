package v1

import metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type NetworkClass struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`
	Spec              NetworkClassSpec   `json:"spec,omitempty"`
	Status            NetworkClassStatus `json:"status,omitempty"`
}

type NetworkClassSpec struct {
	SecurityGroupID string `json:"securityGroupID,omitempty"`
	VSwitchID       string `json:"vSwitchID,omitempty"`
	NodeName        string `json:"nodeName,omitempty"`
	NetworkName     string `json:"networkName,omitempty"`
}

type NetworkClassStatus struct {
	Ready bool `json:"ready,omitempty"`
	// Conditions
	// +optional
	// +patchStrategy=merge
	// +patchMergeKey=type
	// +listType=map
	// +listMapKey=type
	Conditions []metav1.Condition `json:"conditions,omitempty" patchStrategy:"merge" patchMergeKey:"type"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object
type NetworkClassList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []NetworkClass `json:"items"`
}

const (
	// NetworkConditionTypeReady is the condition type of Node is ready.
	NetworkConditionTypeReady string = "Ready"
)
