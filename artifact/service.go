package artifact

import (
	"context"
	"fmt"
	"io"
	"log"
	"strconv"
	"strings"
	"time"

	"helm.sh/helm/v3/pkg/chart"
	"helm.sh/helm/v3/pkg/chart/loader"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/harbor"
	"xiaoshiai.cn/common/oci"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

type OCIOptions struct {
	Host             string `json:"host,omitempty"`
	Username         string `json:"username,omitempty"`
	Password         string `json:"password,omitempty"`
	ChartsRepository string `json:"chartsRepository,omitempty"`
}

func NewDefaultOCIOptions() *OCIOptions {
	return &OCIOptions{
		Host:             "registry.example.com",
		Username:         "",
		Password:         "",
		ChartsRepository: "apps",
	}
}

type RegistryAccountOptions struct {
	Pull    bool
	Push    bool
	Expires time.Time
}

type ChartsProvider interface {
	EnsureProject(ctx context.Context, appname string, public bool) error
	GetChartReadme(ctx context.Context, appname, version string) ([]byte, error)
	DownloadChart(ctx context.Context, appname, version string) (*chart.Chart, error)
	ExistsChart(ctx context.Context, appname, version string) (bool, error)
	GetRegistryAccount(ctx context.Context, projectname, accountname string) (*RegistryAccount, error)
	RemoveRegistryAccount(ctx context.Context, projectname, accountname string) error
	GenerateRegistryAccount(ctx context.Context, projectname, accountname string, options RegistryAccountOptions) (*RegistryAccount, error)
	EnableRegistryAccount(ctx context.Context, projectname, accountname string, enabled bool) error
}

type RegistryAccount struct {
	Host     string `json:"host"`
	Username string `json:"username"`
	Password string `json:"password"`
}

func NewArtifactService(ctx context.Context, o *OCIOptions) (*ArtifactService, error) {
	harborcli, err := harbor.NewClient(&harbor.Options{Addr: o.Host, Username: o.Username, Passwd: o.Password})
	if err != nil {
		return nil, err
	}
	oci, err := oci.NewOCIArtifacts([]oci.OCICredential{{Host: o.Host, Username: o.Username, Password: o.Password}})
	if err != nil {
		return nil, err
	}
	// ensure apps project exists
	s := &ArtifactService{
		Harbor:        harborcli,
		RegistryHost:  o.Host,
		OCI:           oci,
		ChartsProject: o.ChartsRepository,
	}
	return s, nil
}

var _ ChartsProvider = &ArtifactService{}

type ArtifactService struct {
	Harbor        *harbor.Client
	OCI           *oci.OCIArtifacts
	RegistryHost  string
	ChartsProject string
}

type Tag struct {
	Name              string    `json:"name"`
	CreationTimestamp time.Time `json:"creationTimestamp"`
}

func searchToQ(key, search string) string {
	if search == "" {
		return ""
	}
	return fmt.Sprintf("%s=~%s", key, search)
}

func (a *ArtifactService) ListTags(ctx context.Context, project, repository string, options api.ListOptions) (*api.Page[Tag], error) {
	list, err := a.Harbor.ListArtifacts(ctx, project, repository, harbor.ListArtifactOptions{
		CommonOptions: harbor.CommonOptions{
			Q:        searchToQ("tags", options.Search),
			Sort:     options.Sort,
			Page:     options.Page,
			PageSize: options.Size,
		},
		GetArtifactOptions: harbor.GetArtifactOptions{WithTag: true},
	})
	if err != nil {
		return nil, err
	}
	tags := make([]Tag, 0, len(list.Items))
	for _, art := range list.Items {
		for _, tag := range art.Tags {
			tags = append(tags, Tag{
				Name:              tag.Name,
				CreationTimestamp: tag.PushTime,
			})
		}
	}
	return &api.Page[Tag]{
		Total: int64(list.Total),
		Items: tags,
		Page:  int64(options.Page),
		Size:  int64(options.Size),
	}, nil
}

func (a *ArtifactService) RemoveArtifact(ctx context.Context, project string, repository, reference string) error {
	return a.Harbor.DeleteArtifact(ctx, project, repository, reference)
}

func (a *ArtifactService) UploadHelmChart(ctx context.Context, project, repository string, r io.Reader) error {
	return a.OCI.UploadHelmChart(ctx, a.fullimg(project, repository), repository, r)
}

type Repository struct {
	store.ObjectMeta `json:",inline"`
	Repository       string `json:"repository"`
	ArtifacrtCount   int    `json:"artifactCount"`
	PullCount        int    `json:"pullCount"`
}

func (a *ArtifactService) ListRepositories(ctx context.Context, project string, options api.ListOptions) (*api.Page[Repository], error) {
	list, err := a.Harbor.ListRepositories(ctx, project, harbor.ListRepositoryOptions{
		CommonOptions: harbor.CommonOptions{
			Sort:     options.Sort,
			Q:        searchToQ("name", options.Search),
			Page:     options.Page,
			PageSize: options.Size,
		},
	})
	if err != nil {
		return nil, err
	}
	items := make([]Repository, 0, len(list.Items))

	for _, repo := range list.Items {
		items = append(items, Repository{
			ObjectMeta: store.ObjectMeta{
				Name:              strings.TrimPrefix(repo.Name, project+"/"),
				Description:       repo.Description,
				CreationTimestamp: store.Time{Time: repo.CreationTime},
			},
			Repository:     repo.Name,
			ArtifacrtCount: repo.ArtifacrtCount,
			PullCount:      repo.PullCount,
		})
	}
	return &api.Page[Repository]{
		Total: int64(list.Total),
		Items: items,
		Page:  int64(options.Page),
		Size:  int64(options.Size),
	}, nil
}

type Artifact struct {
	store.ObjectMeta  `json:",inline"`
	Repository        string              `json:"repository"`
	Type              string              `json:"type"`
	Digest            string              `json:"digest"`
	MediaType         string              `json:"mediaType"`
	ManifestMediaType string              `json:"manifestMediaType"`
	ArtifactType      string              `json:"artifactType"`
	Tags              []Tag               `json:"tags"`
	ExtraAttrs        map[string]any      `json:"extraAttrs"`
	Readme            []byte              `json:"readme"`
	References        []ArtifactReference `json:"references"`
	Size              int64               `json:"size"`
}

type ArtifactReference struct {
	Annotations map[string]string         `json:"annotations"`
	Platform    ArtifactReferencePlatform `json:"platform"`
	Digest      string                    `json:"digest"`
}

type ArtifactReferencePlatform struct {
	OSFeatures   []string `json:"osFeatures"`
	Architecture string   `json:"architecture"`
	OS           string   `json:"os"`
	Variant      string   `json:"variant"`
}

func ArtifacrtFromHarbor(art harbor.Artifacrt) Artifact {
	return Artifact{
		ObjectMeta: store.ObjectMeta{
			Name:              art.Digest,
			CreationTimestamp: store.Time{Time: art.PushTime},
		},
		Repository:        art.RepositoryName,
		Digest:            art.Digest,
		Type:              art.Type,
		MediaType:         art.MediaType,
		ManifestMediaType: art.ManifestMediaType,
		ArtifactType:      art.ArtifactType,
		ExtraAttrs:        art.ExtraAttrs,
		Size:              art.Size,
		Tags: func(tags []harbor.Tag) []Tag {
			ret := make([]Tag, 0, len(tags))
			for _, tag := range tags {
				ret = append(ret, Tag{
					Name:              tag.Name,
					CreationTimestamp: tag.PushTime,
				})
			}
			return ret
		}(art.Tags),
		References: func(refs []harbor.Reference) []ArtifactReference {
			ret := make([]ArtifactReference, 0, len(refs))
			for _, ref := range refs {
				ret = append(ret, ArtifactReference{
					Annotations: ref.Annotations,
					Platform: ArtifactReferencePlatform{
						OSFeatures:   ref.Platform.OSFeatures,
						Architecture: ref.Platform.Architecture,
						OS:           ref.Platform.OS,
						Variant:      ref.Platform.Variant,
					},
					Digest: ref.ChildDigest,
				})
			}
			return ret
		}(art.References),
	}
}

func (a *ArtifactService) ListRepositoryArtifacts(ctx context.Context,
	project string, repository string, options api.ListOptions,
) (*api.Page[Artifact], error) {
	list, err := a.Harbor.ListArtifacts(ctx, project, repository, harbor.ListArtifactOptions{
		CommonOptions: harbor.CommonOptions{
			Q:        searchToQ("tags", options.Search),
			Sort:     options.Sort,
			Page:     options.Page,
			PageSize: options.Size,
		},
	})
	if err != nil {
		return nil, err
	}
	items := make([]Artifact, 0, len(list.Items))
	for _, art := range list.Items {
		items = append(items, ArtifacrtFromHarbor(art))
	}
	return &api.Page[Artifact]{
		Total: int64(list.Total),
		Items: items,
		Page:  int64(options.Page),
		Size:  int64(options.Size),
	}, nil
}

func (a *ArtifactService) GetArtifactDetails(ctx context.Context, project, repository, reference string) (*Artifact, error) {
	art, err := a.Harbor.GetArtifact(ctx, project, repository, reference, harbor.GetArtifactOptions{})
	if err != nil {
		return nil, err
	}
	ret := ArtifacrtFromHarbor(*art)
	if art.Type == "CHART" {
		readme, err := a.Harbor.GetAddition(ctx, repository, repository, reference, harbor.AdditionTypeReadmeMd)
		if err != nil {
			log.Printf("failed to get readme: %v", err)
		}
		ret.Readme = readme
	}
	return &ret, nil
}

func (a *ArtifactService) fullimg(project, repository string) string {
	return a.RegistryHost + "/" + project + "/" + repository
}

func (a *ArtifactService) EnsureProject(ctx context.Context, projectname string, public bool) error {
	autoscan := "true"
	exists, err := a.Harbor.GetProject(ctx, projectname)
	if err != nil {
		if !harbor.IsErrorCode(err, harbor.ErrorCodeNotFound) {
			return err
		}
		// create project
		create := harbor.ApplyProject{
			Name:     projectname,
			Public:   public,
			Metadata: harbor.ProjectMetadata{AutoScan: autoscan},
		}
		if err := a.Harbor.CreateProject(ctx, create); err != nil {
			return err
		}
	} else {
		// exists, update public status
		desired := strconv.FormatBool(public)
		if exists.Metadata.Public != desired {
			exists.Metadata.Public = desired
			exists.Metadata.AutoScan = autoscan
			if err := a.Harbor.UpdateProject(ctx, exists); err != nil {
				return err
			}
		}
	}
	return nil
}

// OnMarketApplication implements market.ChartsProvider.
func (a *ArtifactService) GenerateRegistryAccount(ctx context.Context, projectname, accountname string, options RegistryAccountOptions) (*RegistryAccount, error) {
	exists, err := a.Harbor.GetProjectRobotAccountByName(ctx, projectname, accountname)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
	} else {
		// if exists, delete it, cause we can't get the password
		// and we need to create a new one
		if err := a.Harbor.DeleteProjectRobotAccount(ctx, projectname, exists.ID); err != nil {
			return nil, err
		}
	}
	access := []harbor.RobotPermissionAccess{}
	if options.Pull {
		access = append(access, harbor.RobotPermissionAccess{Resource: harbor.PermissionResourceRepository, Action: harbor.PermissionActionPull})
	}
	if options.Push {
		access = append(access, harbor.RobotPermissionAccess{Resource: harbor.PermissionResourceRepository, Action: harbor.PermissionActionPush})
	}
	durationdays := -1
	if !options.Expires.IsZero() {
		durationdays = max(int(options.Expires.Sub(time.Now()).Hours()/24), 1)
	}
	create := harbor.CreateRobot{
		Name:        accountname,
		Description: "auto generated robot account",
		Level:       harbor.PermissionLevelProject,
		Duration:    durationdays,
		Permissions: []harbor.RobotPermission{
			{
				Kind:      harbor.PermissionKindProject,
				Namespace: projectname,
				Access:    access,
			},
		},
	}
	// create an new account
	account, err := a.Harbor.CreateRobotAccount(ctx, create)
	if err != nil {
		return nil, err
	}
	ret := &RegistryAccount{
		Host:     a.RegistryHost,
		Username: account.Name,
		Password: account.Secret,
	}
	return ret, nil
}

func (a *ArtifactService) EnableRegistryAccount(ctx context.Context, projectname, accountname string, enabled bool) error {
	account, err := a.Harbor.GetProjectRobotAccountByName(ctx, projectname, accountname)
	if err != nil {
		return err
	}
	if account.Disable != enabled {
		return nil
	}
	account.Disable = !enabled
	return a.Harbor.UpdateRobotAccount(ctx, account)
}

// GetRegistryAccount implements ChartsProvider.
func (a *ArtifactService) GetRegistryAccount(ctx context.Context, projectname string, accountname string) (*RegistryAccount, error) {
	exists, err := a.Harbor.GetProjectRobotAccountByName(ctx, projectname, accountname)
	if err != nil {
		return nil, err
	}
	return &RegistryAccount{Host: a.RegistryHost, Username: exists.Name, Password: exists.Secret}, nil
}

func (a *ArtifactService) RemoveRegistryAccount(ctx context.Context, projectname, accountname string) error {
	exists, err := a.Harbor.GetProjectRobotAccountByName(ctx, projectname, accountname)
	if err != nil {
		if harbor.IsErrorCode(err, harbor.ErrorCodeNotFound) {
			return nil
		}
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if err := a.Harbor.DeleteProjectRobotAccount(ctx, projectname, exists.ID); err != nil {
		if harbor.IsErrorCode(err, harbor.ErrorCodeNotFound) {
			return nil
		}
		return err
	}
	return nil
}

// ExistsChart implements market.ChartsProvider.
func (a *ArtifactService) ExistsChart(ctx context.Context, chart string, version string) (bool, error) {
	return a.OCI.ExistsTag(ctx, a.fullimg(a.ChartsProject, chart), version)
}

func (a *ArtifactService) GetChart(ctx context.Context, project, version string) (*Artifact, error) {
	return a.GetArtifactDetails(ctx, a.ChartsProject, project, version)
}

// GetChartReadme implements market.ChartsProvider.
func (a *ArtifactService) GetChartReadme(ctx context.Context, project string, version string) ([]byte, error) {
	return a.Harbor.GetAddition(ctx, a.ChartsProject, project, version, harbor.AdditionTypeReadmeMd)
}

func (a *ArtifactService) RemoveChart(ctx context.Context, project, version string) error {
	return a.RemoveArtifact(ctx, a.ChartsProject, project, version)
}

func (a *ArtifactService) UploadChart(ctx context.Context, project string, r io.Reader) error {
	return a.UploadHelmChart(ctx, a.ChartsProject, project, r)
}

func (a *ArtifactService) ListCharts(ctx context.Context, project string, options api.ListOptions) (*api.Page[Tag], error) {
	return a.ListTags(ctx, a.ChartsProject, project, options)
}

func (a *ArtifactService) DownloadChart(ctx context.Context, project, version string) (*chart.Chart, error) {
	files, err := a.OCI.DownloadChart(ctx, a.fullimg(a.ChartsProject, project), version)
	if err != nil {
		return nil, err
	}
	return loader.LoadFiles(files)
}
