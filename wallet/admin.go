package wallet

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/tenant"
)

type WalletWithTenantInfo struct {
	Wallet `json:",inline"`
	Tenant *tenant.Tenant `json:"tenant"`
}

func (a *API) ListWallets(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		walletlist, err := a.WalletService.List(ctx, api.GetListOptions(r))
		if err != nil {
			return nil, err
		}
		tenantlist := &store.List[tenant.Tenant]{}
		if err := a.Store.List(ctx, tenantlist); err != nil {
			log.Error(err, "failed to list wallet tenants")
			return nil, err
		}
		tenantmap := map[string]tenant.Tenant{}
		for _, tenant := range tenantlist.Items {
			tenantmap[tenant.Name] = tenant
		}
		items := make([]WalletWithTenantInfo, 0, len(walletlist.Items))
		for _, wallet := range walletlist.Items {
			info := WalletWithTenantInfo{Wallet: wallet}
			if tenant, ok := tenantmap[wallet.Name]; ok {
				info.Tenant = &tenant
			}
			items = append(items, info)
		}
		return store.List[WalletWithTenantInfo]{
			Items: items,
			Total: walletlist.Total,
			Page:  walletlist.Page,
			Size:  walletlist.Size,
		}, nil
	})
}

type AdminRechartOptions struct {
	Amount base.Price `json:"amount"`
	Note   string     `json:"note"`
}

func (a *API) AdminRechargeWallet(w http.ResponseWriter, r *http.Request) {
	a.onWallet(w, r, func(ctx context.Context, wallet string) (any, error) {
		options := AdminRechartOptions{}
		if err := api.Body(r, &options); err != nil {
			return nil, err
		}
		if options.Note == "" {
			return nil, errors.NewBadRequest("note is required for recharging")
		}
		rechartopt := RechargeOptions{
			Amount:    options.Amount,
			Operator:  api.AuthenticateFromContext(ctx).User.Name,
			Summary:   options.Note,
			PaymentID: NewPaymentID(),
		}
		return a.WalletService.Recharge(ctx, wallet, rechartopt)
	})
}

func NewPaymentID() string {
	return time.Now().Format("20060102150405") + rand.RandomNumeric(12)
}

// ListRecords implements WalletService.
func (a *API) ListBills(w http.ResponseWriter, r *http.Request) {
	a.onWallet(w, r, func(ctx context.Context, wallet string) (any, error) {
		options := ListTransactionsOptions{
			ListOptions: api.GetListOptions(r),
			Start:       api.Query(r, "start", time.Now().Add(-time.Hour*24)),
			End:         api.Query(r, "end", time.Time{}),
		}
		return a.WalletService.ListTransactions(ctx, wallet, options)
	})
}

func (a *API) onWallet(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, wallet string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		wallet := api.Path(r, "wallet", "")
		if wallet == "" {
			return nil, errors.NewBadRequest("wallet is required")
		}
		return fn(ctx, wallet)
	})
}

func (a *API) adminWalletGroup() api.Group {
	return api.NewGroup("/wallets").
		Route(
			api.GET("").
				Operation("list wallets").
				To(a.ListWallets).
				Param(api.PageParams...).
				Response(store.List[WalletWithTenantInfo]{}),

			api.POST("/{wallet}:recharge").
				Operation("recharge wallet").
				To(a.AdminRechargeWallet).
				Param(api.BodyParam("recharge", AdminRechartOptions{})),

			api.GET("/{wallet}/bills").
				Operation("list wallet bills").
				To(a.ListBills).
				Param(api.PageParams...).
				Param(
					api.QueryParam("start", "").Format("date-time").Optional(),
					api.QueryParam("end", "").Format("date-time").Optional(),
				).
				Response(store.List[WalletTransaction]{}),
		)
}
