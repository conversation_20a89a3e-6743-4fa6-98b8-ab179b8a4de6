package wallet

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/base"
)

func NewAPI(base base.API, walletService WalletService) *API {
	return &API{
		API:           base,
		WalletService: walletService,
	}
}

type API struct {
	base.API
	WalletService WalletService
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Wallet").
		SubGroup(
			a.adminWalletGroup(),
			a.tenantWalletGroup(),
			a.tenantOrganizationWalletGroup(),
			a.tenantTransactionGroup(),
		)
}
