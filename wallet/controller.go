package wallet

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/tenant"
)

func NewTenantWalletController(storage store.Store, waWalletService WalletService) (*controller.Controller, error) {
	rec := &TenantWalletReconciler{
		Client:        storage,
		walletService: waWalletService,
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("tenant-wallets"))
	c := controller.NewController("tenant-wallets", br).
		Watch(
			controller.NewStoreSource(storage, &tenant.Tenant{}),
		)
	return c, nil
}

type TenantWalletReconciler struct {
	Client        store.Store
	walletService WalletService
}

func (r *TenantWalletReconciler) Initialize(ctx context.Context) error {
	wallets, err := r.walletService.List(ctx, api.ListOptions{})
	if err != nil {
		return err
	}
	tenantlist := &store.List[tenant.Tenant]{}
	if err := r.Client.List(ctx, tenantlist); err != nil {
		return err
	}
	tenantmap := map[string]tenant.Tenant{}
	for _, tenant := range tenantlist.Items {
		tenantmap[tenant.Name] = tenant
	}
	var errs []error
	for _, wallet := range wallets.Items {
		if _, ok := tenantmap[wallet.Name]; !ok {
			if wallet.Balance.IsPositive() {
				if err := r.walletService.SetDisabled(ctx, wallet.Name, true); err != nil {
					log.Error(err, "failed to disable wallet", "wallet", wallet.Name)
					errs = append(errs, err)
				}
				continue
			}
			log.Info("remove wallet", "wallet", wallet.Name)
			if err := r.walletService.Remove(ctx, wallet.Name); err != nil {
				log.Error(err, "failed to remove wallet", "wallet", wallet.Name)
				errs = append(errs, err)
			}
		}
	}
	return errors.NewAggregate(errs)
}

func (r *TenantWalletReconciler) Sync(ctx context.Context, obj *tenant.Tenant) (controller.Result, error) {
	return controller.Result{}, r.sync(ctx, obj)
}

func (r *TenantWalletReconciler) sync(ctx context.Context, obj *tenant.Tenant) error {
	wallet, err := r.walletService.Get(ctx, obj.Name)
	if err != nil {
		if errors.IsNotFound(err) {
			log.Info("init wallet for tenant", "tenant", obj.Name)
			return r.walletService.InitWallet(ctx, obj.Name, base.NewPrice(0))
		}
	}
	_ = wallet
	return nil
}

func (r *TenantWalletReconciler) Remove(ctx context.Context, obj *tenant.Tenant) (controller.Result, error) {
	return controller.Result{}, r.remove(ctx, obj)
}

func (r *TenantWalletReconciler) remove(ctx context.Context, obj *tenant.Tenant) error {
	wallet, err := r.walletService.Get(ctx, obj.Name)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
	}
	if wallet.Balance.IsPositive() {
		return nil
	}
	if err := r.walletService.Remove(ctx, obj.Name); err != nil {
		return err
	}
	return nil
}
