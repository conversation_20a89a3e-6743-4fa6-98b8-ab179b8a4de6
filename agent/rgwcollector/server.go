package rgwcollector

import (
	"context"
	"net/http"
	"sync"
	"time"

	"github.com/ceph/go-ceph/rgw/admin"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/agent/rgwcollector/pkg"
)

const RGWCollectorPath = "/rgw/metrics"

var _ prometheus.Collector = &centerRGWCollector{}

type centerRGWCollector struct {
	ctx             context.Context
	storage         store.Store
	api             *admin.API
	lastCollectTime time.Time
	collectors      map[string]pkg.Collector
}

func NewCenterRGWCollector(ctx context.Context, storage store.Store, api *admin.API) *centerRGWCollector {
	c := &centerRGWCollector{
		ctx:             ctx,
		storage:         storage,
		api:             api,
		lastCollectTime: time.Unix(0, 0),
		collectors:      make(map[string]pkg.Collector),
	}
	for key, valueFn := range pkg.Factories {
		cli, err := valueFn(ctx, &pkg.Clients{
			RGWAdminAPI: api,
			Store:       storage,
		})
		if err != nil {
			log.FromContext(ctx).Error(err, "Failed to init collector", "Name", key)
			continue
		}
		c.collectors[key] = cli
	}
	return c
}

func (a *centerRGWCollector) Collector(w http.ResponseWriter, r *http.Request) {
	registry := prometheus.NewRegistry()
	registry.MustRegister(a)
	h := promhttp.HandlerFor(registry, promhttp.HandlerOpts{
		ErrorHandling: promhttp.ContinueOnError,
	})
	h.ServeHTTP(w, r)
}

func (a *centerRGWCollector) Group() api.Group {
	return api.
		NewGroup("").
		Tag("RGW").
		Route(
			api.GET(RGWCollectorPath).
				Operation("Center Collector RGW Metrics").
				To(a.Collector),
		)
}

// Collect implements prometheus.Collector.
func (a *centerRGWCollector) Collect(ch chan<- prometheus.Metric) {
	metricsCh := make(chan prometheus.Metric)
	wg := sync.WaitGroup{}
	wg.Add(1)
	go func() {
		for metric := range metricsCh {
			ch <- metric
		}
		wg.Done()
	}()

	wgCol := sync.WaitGroup{}
	wgCol.Add(len(a.collectors))
	for name, coll := range a.collectors {
		go func(name string, coll pkg.Collector) {
			begin := time.Now()
			err := coll.Update(metricsCh)
			duration := time.Since(begin)
			var success float64

			if err != nil {
				log.FromContext(a.ctx).Error(err, "Collector failed", "name", name)
				success = 0
			} else {
				success = 1
			}
			metricsCh <- prometheus.MustNewConstMetric(scrapeDurationDesc, prometheus.GaugeValue, duration.Seconds(), name)
			metricsCh <- prometheus.MustNewConstMetric(scrapeSuccessDesc, prometheus.GaugeValue, success, name)
			wgCol.Done()
		}(name, coll)
	}
	wgCol.Wait()
	a.lastCollectTime = time.Now()
	close(metricsCh)
	wg.Wait()
}

// Describe implements prometheus.Collector.
func (a *centerRGWCollector) Describe(ch chan<- *prometheus.Desc) {
	ch <- scrapeDurationDesc
	ch <- scrapeSuccessDesc
}

var (
	scrapeDurationDesc = prometheus.NewDesc(
		prometheus.BuildFQName("ceph", "scrape", "collector_duration_seconds"),
		"Duration of a collector scrape.",
		[]string{"collector"},
		nil,
	)
	scrapeSuccessDesc = prometheus.NewDesc(
		prometheus.BuildFQName("ceph", "scrape", "collector_success"),
		"Whether a collector succeeded.",
		[]string{"collector"},
		nil,
	)
)
