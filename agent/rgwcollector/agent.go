package rgwcollector

import (
	"net/http"
	"net/url"

	"k8s.io/apimachinery/pkg/util/proxy"
	"xiaoshiai.cn/common/rest/api"
	libproxy "xiaoshiai.cn/common/rest/proxy"
	"xiaoshiai.cn/core/agent/constants"
)

func NewRGWCollector(edgetransport http.RoundTripper) *RGWCollector {
	return &RGWCollector{EdgeTransport: edgetransport}
}

type RGWCollector struct {
	EdgeTransport http.RoundTripper
}

func (a *RGWCollector) CollectorHandler(w http.ResponseWriter, r *http.Request) {
	target := &url.URL{
		Scheme:   "http", // current use http not http3(https)
		Host:     constants.ISMCEdgeAddr,
		Path:     RGWCollectorPath,
		RawQuery: r.URL.RawQuery,
	}
	handler := proxy.NewUpgradeAwareHandler(target, a.EdgeTransport, true, false, libproxy.ErrorResponser{})
	handler.ServeHTTP(w, r)
}

func (a *RGWCollector) Group() api.Group {
	return api.
		NewGroup("").
		Tag("RGW").
		Route(
			api.GET(RGWCollectorPath).
				Operation("Collector RGW Metrics").
				To(a.CollectorHandler),
		)
}
