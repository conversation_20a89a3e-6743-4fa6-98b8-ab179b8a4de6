package pkg

import (
	"context"

	"github.com/ceph/go-ceph/rgw/admin"
	"github.com/prometheus/client_golang/prometheus"
	"xiaoshiai.cn/common/store"
)

var _ Collector = &RGWUserQuota{}

func init() {
	Factories["rgw_user_quota"] = NewRGWUserQuota
}
func NewRGWUserQuota(ctx context.Context, c *Clients) (Collector, error) {
	return &RGWUserQuota{
		ctx:     ctx,
		api:     c.RGWAdminAPI,
		storage: c.Store,
	}, nil
}

type RGWUserQuota struct {
	ctx     context.Context
	api     *admin.API
	storage store.Store
	current *prometheus.Desc
}

func (c *RGWUserQuota) Update(ch chan<- prometheus.Metric) error {
	// Get the "admin" user
	users, err := c.api.GetUsers(c.ctx)
	if err != nil {
		return err
	}

	// Iterate over users to get quota
	for _, user := range *users {
		userQuota, err := c.api.GetUserQuota(c.ctx, admin.QuotaSpec{
			UID: user,
		})
		if err != nil {
			return err
		}
		if userQuota.Enabled == nil {
			continue
		}
		labels := map[string]string{
			"uid": user,
		}

		c.current = prometheus.NewDesc(
			prometheus.BuildFQName(Namespace, "rgw", "user_quota_max_size_kb"),
			"RGW User Quota max size KiB",
			nil, labels)
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*userQuota.MaxSizeKb))

		c.current = prometheus.NewDesc(
			prometheus.BuildFQName(Namespace, "rgw", "user_quota_max_objects"),
			"RGW User Quota max objects",
			nil, labels)
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*userQuota.MaxObjects))
	}

	return nil
}
