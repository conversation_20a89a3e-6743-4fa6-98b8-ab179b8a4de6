package pkg

import (
	"context"

	"github.com/ceph/go-ceph/rgw/admin"
	"github.com/prometheus/client_golang/prometheus"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/oss"
	"xiaoshiai.cn/core/tenant"
)

var _ Collector = &RGWBuckets{}

func init() {
	Factories["rgw_buckets"] = NewRGWBuckets
}
func NewRGWBuckets(ctx context.Context, c *Clients) (Collector, error) {
	return &RGWBuckets{
		ctx:     ctx,
		api:     c.RGWAdminAPI,
		storage: c.Store,
	}, nil
}

type RGWBuckets struct {
	ctx     context.Context
	api     *admin.API
	storage store.Store
	current *prometheus.Desc
}

func (c *RGWBuckets) getBucketMetrics(tenant, org, bucketName string, ch chan<- prometheus.Metric) error {
	bucketInfo, err := c.api.GetBucketInfo(c.ctx, admin.Bucket{
		Bucket: bucketName,
	})
	if err != nil {
		return err
	}

	labels := map[string]string{
		"bucket":       bucketName,
		"uid":          bucketInfo.Owner,
		"tenant":       tenant,
		"organization": org,
	}

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_size"),
		"RGW Bucket Size",
		nil, labels)
	if bucketInfo.Usage.RgwMain.Size == nil {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, 0.0)
	} else {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*bucketInfo.Usage.RgwMain.Size))
	}

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_size_kb"),
		"RGW Bucket Size actual",
		nil, labels)
	if bucketInfo.Usage.RgwMain.SizeKb == nil {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, 0.0)
	} else {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*bucketInfo.Usage.RgwMain.SizeKb))
	}

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_size_kb_actual"),
		"RGW Bucket Size KiB actual",
		nil, labels)
	if bucketInfo.Usage.RgwMain.SizeKbActual == nil {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, 0.0)
	} else {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*bucketInfo.Usage.RgwMain.SizeKbActual))
	}

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_size_kb_utilized"),
		"RGW Bucket Size KiB utilized",
		nil, labels)
	if bucketInfo.Usage.RgwMain.SizeKbUtilized == nil {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, 0.0)
	} else {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*bucketInfo.Usage.RgwMain.SizeKbUtilized))
	}

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_num_objects"),
		"RGW Bucket Num Objects",
		nil, labels)
	if bucketInfo.Usage.RgwMain.NumObjects == nil {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, 0.0)
	} else {
		ch <- prometheus.MustNewConstMetric(
			c.current, prometheus.GaugeValue, float64(*bucketInfo.Usage.RgwMain.NumObjects))
	}

	if bucketInfo.BucketQuota.Enabled == nil || !*bucketInfo.BucketQuota.Enabled {
		return nil
	}

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_quota_max_size_kb"),
		"RGW Bucket Quota Max Size KiB",
		nil, labels)
	ch <- prometheus.MustNewConstMetric(
		c.current, prometheus.GaugeValue, float64(*bucketInfo.BucketQuota.MaxSizeKb))

	c.current = prometheus.NewDesc(
		prometheus.BuildFQName(Namespace, "rgw", "bucket_quota_max_objects"),
		"RGW Bucket Quota Max Objects",
		nil, labels)
	ch <- prometheus.MustNewConstMetric(
		c.current, prometheus.GaugeValue, float64(*bucketInfo.BucketQuota.MaxObjects))
	return nil
}

func (c *RGWBuckets) Update(ch chan<- prometheus.Metric) error {
	allTenants := &store.List[tenant.Tenant]{}
	if err := c.storage.List(c.ctx, allTenants); err != nil {
		return err
	}
	for _, item := range allTenants.Items {
		tenantName := item.Name
		allorgs := &store.List[organization.Organization]{}
		if err := c.storage.Scope(store.Scope{
			Resource: "tenants",
			Name:     tenantName,
		}).List(c.ctx, allorgs); err != nil {
			return err
		}
		for _, orgItem := range allorgs.Items {
			organizationName := orgItem.Name
			orgStorage := c.storage.Scope(store.Scope{
				Resource: "tenants",
				Name:     tenantName,
			}, store.Scope{Resource: "organizations", Name: organizationName})

			allOss := &store.List[oss.ObjectStorage]{}
			if err := orgStorage.List(c.ctx, allOss); err != nil {
				return err
			}
			for _, bucket := range allOss.Items {
				bucketName := bucket.Name
				if err := c.getBucketMetrics(tenantName, organizationName, bucketName, ch); err != nil {
					log.FromContext(c.ctx).Error(err, "Failed to get bucket metrics", "Bucket", bucketName, "Tenant", tenantName)
				}
			}
		}
	}
	return nil
}
