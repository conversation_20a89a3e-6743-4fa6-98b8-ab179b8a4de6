package pkg

import (
	"context"
	"time"

	"github.com/ceph/go-ceph/rgw/admin"
	"github.com/prometheus/client_golang/prometheus"
)

func init() {
	Factories["rgw_usage"] = NewRGWUsage
}

var _ Collector = &RGWUsage{}

type RGWUsage struct {
	ctx     context.Context
	api     *admin.API
	current *prometheus.Desc
}

func NewRGWUsage(ctx context.Context, c *Clients) (Collector, error) {
	return &RGWUsage{
		ctx: ctx,
		api: c.RGWAdminAPI,
	}, nil
}

// Update implements Collector.
func (r *RGWUsage) Update(ch chan<- prometheus.Metric) error {
	now := time.Now()
	start := now.AddDate(0, 0, -1).Format(time.DateTime)
	usage, err := r.api.GetUsage(r.ctx, admin.Usage{
		Start:  start,
		End:    now.Format(time.DateTime),
		UserID: "admin",
	})
	if err != nil {
		return err
	}
	labelsKey := []string{"bucket", "category"}
	for _, entry := range usage.Entries {
		for _, bucket := range entry.Buckets {
			bucketName := bucket.Bucket
			if bucketName == "-" {
				continue
			}
			for _, item := range bucket.Categories {
				r.current = prometheus.NewDesc(
					prometheus.BuildFQName(Namespace, "rgw", "bucket_usage_bytes_sent"),
					"Total bytes sent by user and bucket",
					labelsKey, nil)
				ch <- prometheus.MustNewConstMetric(
					r.current, prometheus.GaugeValue, float64(item.BytesSent), bucketName, item.Category)

				r.current = prometheus.NewDesc(
					prometheus.BuildFQName(Namespace, "rgw", "bucket_usage_bytes_received"),
					"Total bytes received by user and bucket",
					labelsKey, nil)
				ch <- prometheus.MustNewConstMetric(
					r.current, prometheus.GaugeValue, float64(item.BytesReceived), bucketName, item.Category)

				r.current = prometheus.NewDesc(
					prometheus.BuildFQName(Namespace, "rgw", "bucket_usage_ops_total"),
					"Total operations by user and bucket",
					labelsKey, nil)
				ch <- prometheus.MustNewConstMetric(
					r.current, prometheus.GaugeValue, float64(item.Ops), bucketName, item.Category)

				r.current = prometheus.NewDesc(
					prometheus.BuildFQName(Namespace, "rgw", "bucket_usage_ops_successful_total"),
					"Total successful operations by user and bucket",
					labelsKey, nil)
				ch <- prometheus.MustNewConstMetric(
					r.current, prometheus.GaugeValue, float64(item.SuccessfulOps), bucketName, item.Category)

			}
		}
	}
	return nil
}
