package pkg

import (
	"context"

	"github.com/ceph/go-ceph/rgw/admin"
	"github.com/prometheus/client_golang/prometheus"
	"xiaoshiai.cn/common/store"
)

const Namespace = "ceph"

type Clients struct {
	RGWAdminAPI *admin.API
	Store       store.Store
}

type Collector interface {
	Update(chan<- prometheus.Metric) error
}

type NewCollectorFunc func(context.Context, *Clients) (Collector, error)

var Factories map[string]NewCollectorFunc = map[string]NewCollectorFunc{}
