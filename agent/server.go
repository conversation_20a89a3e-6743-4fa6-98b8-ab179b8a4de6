package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/ceph/go-ceph/rgw/admin"
	"github.com/google/uuid"
	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/agent/alert"
	"xiaoshiai.cn/core/agent/constants"
	"xiaoshiai.cn/core/agent/rgwcollector"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/edge/tunnel"
	"xiaoshiai.cn/core/edge/tunnel/transport/websocket"
	"xiaoshiai.cn/core/oss"
)

type ServerOptions struct {
	// Server is the central edge server
	// on multi replicas ismc, the agents is connected to serval ismc servers.
	// one ismc server do not know the agents connected to other ismc servers.
	// so every ismc server are connected to a central edge server.
	// central edge server know all the agents connected to all ismc servers.
	Server   string `json:"server,omitempty"`
	DeviceID string `json:"deviceID,omitempty" description:"device id of self"`
}

func NewDefaultServerOptions() *ServerOptions {
	return &ServerOptions{
		Server:   "ws://bob-edgeserver:80/devices-connect",
		DeviceID: constants.ISMCEdgeAddr,
	}
}

func NewTunnel(ctx context.Context, deviceID string) (*EdgeTunnel, error) {
	tunnelManager, err := tunnel.NewManager(ctx, deviceID, &tunnel.Options{})
	if err != nil {
		return nil, err
	}
	return &EdgeTunnel{Tunnel: tunnelManager}, nil
}

type EdgeTunnel struct {
	Tunnel        *tunnel.DefaultManager
	httpTransport http.RoundTripper
}

// GetConfig implements cluster.EdgeClientConfigGetter.
func (s *EdgeTunnel) GetConfig(addr string) *httpclient.ClientConfig {
	// it cache the http transport to reuse the connections
	if s.httpTransport == nil {
		s.httpTransport = tunnel.HttpTransport(s.Tunnel)
	}
	return &httpclient.ClientConfig{
		Server:       &url.URL{Scheme: "http", Host: addr}, // current use http not http3(https)
		RoundTripper: s.httpTransport,
		DialContext:  s.Tunnel.DialContext, // for websocket dial
	}
}

func (d *EdgeTunnel) ConnectUpstream(ctx context.Context, wsserver string, addroverride string, callback func(tunnel.Interface)) error {
	return tunnel.Retry(ctx, func(ctx context.Context) error {
		dev, err := websocket.Open(ctx, wsserver, nil)
		if err != nil {
			log.Error(err, "failed to open tcp connection to edge server")
			return err
		}
		return d.Tunnel.Connect(ctx, dev, tunnel.ConnectOptions{
			IsUpstream:            true,
			AuthenticatorOverride: tunnel.AlwaysAllowAuthenticator{},
			// ismc server use a random address to connect to edge server
			// so the edge server can identify every ismc server
			LocalAddressOverride:   addroverride,
			RouteAdvertiseInterval: 1 * time.Minute,
			IgnoreRouteAdvertise:   true, // do not receive route advertise from upstream
			ConnectedCallback:      callback,
		})
	})
}

func NewServer(ctx context.Context, store store.Store, mongo store.Store, options *ServerOptions) (*Server, error) {
	tunnelManager, err := NewTunnel(ctx, options.DeviceID)
	if err != nil {
		return nil, err
	}
	server := &Server{
		Tunnel:     tunnelManager,
		Store:      store,
		MongoStore: mongo,
		Options:    options,
	}
	tunnelManager.Tunnel.Authn = &tunnel.TokenAuthenticator{GetTokenFunc: server.getTokenFromID}
	tunnelManager.Tunnel.Eventer = tunnel.EventerFunc(server.onDevice)
	return server, nil
}

type Server struct {
	Options    *ServerOptions
	Store      store.Store
	MongoStore store.Store
	Tunnel     *EdgeTunnel
}

func (s *Server) deviceConnect(w http.ResponseWriter, r *http.Request) {
	websocket.HandlerConnect(w, r, func(ctx context.Context, dev tunnel.Device) error {
		return s.Tunnel.Tunnel.Connect(ctx, dev, tunnel.ConnectOptions{})
	})
}

func (d *Server) onDevice(event tunnel.Event) {
	ctx := context.Background()
	if err := d.onDeviceEvent(ctx, event); err != nil {
		log.Error(err, "failed to handle device event")
	}
}

func (d *Server) onDeviceEvent(ctx context.Context, event tunnel.Event) error {
	switch event.Kind {
	case tunnel.EventKindConnected, tunnel.EventKindDisconnected:
		connected := event.Kind == tunnel.EventKindConnected
		log.Info("device connected changed", "device", event.Addr, "connected", connected)

		clusterlist := &store.List[cluster.Cluster]{}
		if err := d.Store.List(ctx, clusterlist, store.WithSubScopes()); err != nil {
			return err
		}
		for _, cluster := range clusterlist.Items {
			if cluster.Edge.DeviceID != event.Addr {
				continue
			}
			patchstatus := map[string]any{
				"connected": connected,
			}
			if connected {
				patchstatus["lastConnectedTimestamp"] = time.Now().Format(time.RFC3339)
			} else {
				patchstatus["lastDisconnectedTimestamp"] = time.Now().Format(time.RFC3339)
			}
			patchdata, err := json.Marshal(map[string]any{"status": patchstatus})
			if err != nil {
				return fmt.Errorf("failed to marshal cluster status: %w", err)
			}
			patch := store.RawPatch(store.PatchTypeMergePatch, patchdata)
			if err := d.Store.Scope(cluster.Scopes...).Status().Patch(ctx, &cluster, patch); err != nil {
				return fmt.Errorf("failed to patch cluster status: %w", err)
			}
		}
	}
	return nil
}

func (d *Server) BuildEdgeAPI(ctx context.Context, ossOpt *oss.ObjectStorageOptions) (*api.API, error) {
	var groups = []api.Group{}
	alertapi := alert.CenterAlertReceiver{
		Store:      d.Store,
		MongoStore: d.MongoStore,
	}
	groups = append(groups, alertapi.Group())
	if ossOpt.Enabled {
		adm, err := admin.New(ossOpt.Address, ossOpt.AccessKey, ossOpt.SecretKey, &http.Client{Timeout: time.Minute})
		if err != nil {
			return nil, err
		}
		rgwcollectorapi := rgwcollector.NewCenterRGWCollector(ctx, d.Store, adm)
		groups = append(groups, rgwcollectorapi.Group())
	}
	return api.New().Group(groups...), nil
}

func (d *Server) getTokenFromID(ctx context.Context, devid string) (string, error) {
	clusterlist := &store.List[cluster.Cluster]{}
	if err := d.Store.List(ctx, clusterlist,
		store.WithSubScopes(),
		store.WithFieldRequirements(store.RequirementEqual("edge.deviceID", devid))); err != nil {
		return "", err
	}
	for _, cluster := range clusterlist.Items {
		if cluster.Edge.DeviceID == devid {
			return cluster.Edge.Token, nil
		}
	}
	return "", errors.NewUnauthorized("device not registered")
}

func (d *Server) connectUpstream(ctx context.Context) error {
	randaddr := "ismc-" + uuid.NewString()
	return d.Tunnel.ConnectUpstream(ctx, d.Options.Server, randaddr, nil)
}

func (d *Server) Run(ctx context.Context, ossOpt *oss.ObjectStorageOptions) error {
	// edge handler is the api handler for edge server
	// it called by edge agent
	edgehandler, err := d.BuildEdgeAPI(ctx, ossOpt)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return tunnel.ServeHTTP(ctx, d.Tunnel.Tunnel, edgehandler.Build())
	})
	eg.Go(func() error {
		return d.connectUpstream(ctx)
	})
	return eg.Wait()
}

func NewConnectAPI(server *Server) api.Group {
	return api.
		NewGroup("").
		Tag("agent").
		Route(
			api.GET("/devices-connect").
				Operation("agent connect websocket endpoint").
				To(server.deviceConnect),
		)
}
