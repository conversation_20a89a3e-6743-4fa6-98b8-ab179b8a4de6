package agent

import (
	"context"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/core/agent/alert"
	"xiaoshiai.cn/core/agent/collector"
	"xiaoshiai.cn/core/agent/proxy"
	"xiaoshiai.cn/core/agent/rgwcollector"
	"xiaoshiai.cn/core/agent/statistics"
	"xiaoshiai.cn/core/agent/terminal"
	"xiaoshiai.cn/core/edge/tunnel"
	"xiaoshiai.cn/core/ismc/rest"
)

func BuildTunnelAPI(ctx context.Context, deps *Dependencies) (*api.API, error) {
	proxyapi, err := proxy.NewAPI(deps.RestConfig)
	if err != nil {
		return nil, err
	}
	terminalapi := terminal.NewAPI()
	cloudapi := rest.NewAPI(deps.CloudProvider)
	statisticsapi := statistics.NewAPI(deps.Client)

	api := api.
		New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			proxyapi.Group(),
			terminalapi.Group(),
			cloudapi.Group(),
			statisticsapi.Group(),
		)
	return api, nil
}

func BuildLocalAPI(ctx context.Context, deps *Dependencies) (*api.API, error) {
	alertreveiverapi, err := alert.NewAgentAlertReciver(tunnel.HttpTransport(deps.Tunnel))
	if err != nil {
		return nil, err
	}
	rgwcollectorapi := rgwcollector.NewRGWCollector(tunnel.HttpTransport(deps.Tunnel))
	proxyapi, err := proxy.NewAPI(deps.RestConfig)
	if err != nil {
		return nil, err
	}
	metricscollector := collector.NewMetricsCollector(deps.CloudProvider)
	cloudapi := rest.NewAPI(deps.CloudProvider)
	statisticsapi := statistics.NewAPI(deps.Client)
	return api.
		New().
		Plugin(
			api.VersionPlugin{Version: version.Get()},
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Group(
			rgwcollectorapi.Group(),
			alertreveiverapi.Group(),
			api.NewGroup("").
				Filter(
					api.LoggingFilter(log.FromContext(ctx)),
					api.NewCORSFilter(),
				).
				SubGroup(
					metricscollector.Group(),
					cloudapi.Group(),
					statisticsapi.Group(),
					proxyapi.Group(),
				),
		), nil
}
