package terminal

import (
	"net/http"

	"github.com/gorilla/websocket"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/ws"
)

type API struct{}

func NewAPI() *API {
	return &API{}
}

func (s *API) Exec(w http.ResponseWriter, r *http.Request) {
	// prepare websocket
	ctx := r.Context()
	// perfer bash if available

	tty := api.Query(r, "tty", true)

	const defaultPerferBashCommand = "[ -x /bin/bash ] && exec /bin/bash"
	commands := []string{"/bin/sh", "-c", api.Query(r, "command", defaultPerferBashCommand)}

	upgrader := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
	wsc, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		api.Error(w, err)
		return
	}
	defer wsc.Close()

	defer func() {
		wsc.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
	}()

	stream := ws.NewExecStream(ctx, wsc)
	if err := Exec(ctx, commands, stream, stream, stream, tty, stream.ResizeEvent); err != nil {
		wsc.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseInternalServerErr, err.Error()))
	}
}

func (s *API) Group() api.Group {
	return api.
		NewGroup("/terminal").
		Route(
			api.GET("/exec").To(s.Exec),
		)
}
