package agent

import (
	"context"
	cryptorand "crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"fmt"
	"math"
	"math/big"
	"time"

	"golang.org/x/sync/errgroup"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/cluster"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/core/edge/tunnel"
	"xiaoshiai.cn/core/edge/tunnel/transport/websocket"
	cloud "xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers"
)

type Options struct {
	// Listen local address to listen
	Listen     string             `json:"listen,omitempty"`
	ISMC       *ISMCOptions       `json:"ismc,omitempty"`
	Cloud      *providers.Options `json:"cloud,omitempty"`
	Kubernetes *KubernetesOptions `json:"kubernetes,omitempty"`
}

func (o *Options) Validate() error {
	errs := []error{}
	if o.ISMC != nil {
		if o.ISMC.DeviceID == "" {
			errs = append(errs, fmt.Errorf("edge.deviceID is required"))
		}
	}
	return errors.NewAggregate(errs)
}

func DefaultOptions() *Options {
	p := providers.NewDefaultOptions()
	return &Options{
		Listen:     ":8080",
		ISMC:       NewDefaultEdgeOptions(),
		Cloud:      p,
		Kubernetes: &KubernetesOptions{Enabled: true},
	}
}

type KubernetesOptions struct {
	Enabled bool `json:"enabled,omitempty"`
}

type ISMCOptions struct {
	// Token is use to authenticate the device
	Token string `json:"token,omitempty"`
	// DeviceID is the unique identifier of the device
	DeviceID string `json:"deviceID,omitempty"`
	// Server is the address of the central server
	Server string `json:"server,omitempty"`
}

func NewDefaultEdgeOptions() *ISMCOptions {
	return &ISMCOptions{
		Token:    "",
		DeviceID: "",
		Server:   "ws://ismc.example.com/devices-connect",
	}
}

func Run(ctx context.Context, options *Options) error {
	deps, err := BuildDependencies(ctx, options)
	if err != nil {
		return err
	}
	tunnelapi, err := BuildTunnelAPI(ctx, deps)
	if err != nil {
		return err
	}
	localapi, err := BuildLocalAPI(ctx, deps)
	if err != nil {
		return err
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return tunnel.ServeHTTP(ctx, deps.Tunnel, tunnelapi.Build())
	})
	eg.Go(func() error {
		return RunTunnelConnectRetry(ctx, deps)
	})
	eg.Go(func() error {
		return localapi.Serve(ctx, options.Listen)
	})
	eg.Go(func() error {
		return pprof.Run(ctx)
	})
	return eg.Wait()
}

func RunTunnelConnectRetry(ctx context.Context, deps *Dependencies) error {
	return tunnel.Retry(ctx, func(ctx context.Context) error {
		return RunTunnelConnect(ctx, deps)
	})
}

func RunTunnelConnect(ctx context.Context, deps *Dependencies) error {
	log.FromContext(ctx).Info("connecting to ismc server", "server", deps.Options.ISMC.Server)
	dev, err := websocket.Open(ctx, deps.Options.ISMC.Server, nil)
	if err != nil {
		return err
	}
	return deps.Tunnel.Connect(ctx, dev, tunnel.ConnectOptions{Token: deps.Options.ISMC.Token, IsUpstream: true})
}

type Dependencies struct {
	Options       *Options
	Tunnel        tunnel.Manager
	CloudProvider cloud.Provider
	TLSConfig     *tls.Config
	RestConfig    *rest.Config
	Client        client.Client
}

func BuildDependencies(ctx context.Context, options *Options) (*Dependencies, error) {
	tunnelManager, err := tunnel.NewManager(ctx, options.ISMC.DeviceID, &tunnel.Options{
		Eventer: tunnel.LoggerEventer{},
	})
	if err != nil {
		return nil, err
	}
	cloud, err := providers.NewProvider(ctx, options.Cloud)
	if err != nil {
		return nil, err
	}
	tlscfg, err := NewSelfSignedTLSConfig()
	if err != nil {
		return nil, err
	}

	deps := &Dependencies{
		Tunnel:        tunnelManager,
		TLSConfig:     tlscfg,
		CloudProvider: cloud,
		Options:       options,
	}

	if options.Kubernetes.Enabled {
		restconfig, err := DefaultClientConfig().ClientConfig()
		if err != nil {
			return nil, fmt.Errorf("failed to get kubernetes rest config: %w", err)
		}
		// set default QPS and Burst
		restconfig.QPS = 1000
		restconfig.Burst = 2000

		cluster, err := cluster.New(restconfig)
		if err != nil {
			return nil, err
		}
		go cluster.Start(ctx)

		deps.RestConfig = restconfig
		deps.Client = cluster.GetClient()
	}
	return deps, nil
}

func DefaultClientConfig() clientcmd.ClientConfig {
	return clientcmd.NewNonInteractiveDeferredLoadingClientConfig(
		clientcmd.NewDefaultClientConfigLoadingRules(), nil)
}

// NewSelfSignedCACert creates a CA certificate
func NewSelfSignedTLSConfig() (*tls.Config, error) {
	rsakey, err := rsa.GenerateKey(cryptorand.Reader, 2048)
	if err != nil {
		return nil, err
	}
	// Create a self-signed certificate
	now := time.Now().UTC()
	// returns a uniform random value in [0, max-1), then add 1 to serial to make it a uniform random value in [1, max).
	serial, err := cryptorand.Int(cryptorand.Reader, new(big.Int).SetInt64(math.MaxInt64-1))
	if err != nil {
		return nil, err
	}
	serial = new(big.Int).Add(serial, big.NewInt(1))
	tmpl := x509.Certificate{
		SerialNumber:          serial,
		Subject:               pkix.Name{CommonName: "self-signed"},
		DNSNames:              []string{"localhost"},
		NotBefore:             now.Add(-24 * time.Hour),
		NotAfter:              now.Add(10 * 365 * 24 * time.Hour),
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		BasicConstraintsValid: true,
		IsCA:                  true,
	}
	certDER, err := x509.CreateCertificate(cryptorand.Reader, &tmpl, &tmpl, &rsakey.PublicKey, rsakey)
	if err != nil {
		return nil, err
	}
	return &tls.Config{Certificates: []tls.Certificate{{Certificate: [][]byte{certDER}, PrivateKey: rsakey}}}, nil
}
