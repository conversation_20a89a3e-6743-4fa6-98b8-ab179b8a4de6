package alert

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"slices"
	"strings"

	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/observerability"
	"xiaoshiai.cn/core/observerability/alertchannel"
)

const AlertReceiverPath = "/alert/receive"

type CenterAlertReceiver struct {
	Store      store.Store
	MongoStore store.Store
}

type SendFunc func(p url.Values, al alertchannel.AlertProxyMessage, platform alertchannel.PlatformType) error

func (a *CenterAlertReceiver) ReceiveHandler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	alert := &alertchannel.AlertProxyWebhookAlert{}
	if err := api.Body(r, alert); err != nil {
		api.Error(w, err)
		return
	}
	query := r.URL.Query()
	ptype := query.Get("type")
	var proxy alertchannel.AlertProxy
	switch alertchannel.ChannelType(ptype) {
	case alertchannel.TypeEmail:
		proxy = &alertchannel.Email{}
	case alertchannel.TypeDingding:
		proxy = &alertchannel.Dingding{}
	case alertchannel.TypeFeishu:
		proxy = &alertchannel.Feishu{}
	case alertchannel.TypeWebhook:
		proxy = &alertchannel.Webhook{}
	case alertchannel.TypeMSTeams:
		proxy = &alertchannel.MSTeams{}

	default:
		api.Error(w, fmt.Errorf("unknown proxy type"))
		return
	}
	for _, alert := range alert.Alerts {
		start := alert.StartsAt.In(base.GetCustomeTimeZone())
		alert.StartsAt = &start
		end := alert.EndsAt.In(base.GetCustomeTimeZone())
		alert.EndsAt = &end
		if msg, ok := alert.Annotations[alertchannel.MessageAnnotationsKey]; ok {
			// 为 " 转义
			alert.Annotations[alertchannel.MessageAnnotationsKey] = strings.ReplaceAll(msg, `"`, `\"`)
		}
		if err := a.recordAlertToHistory(ctx, r.URL.Query(), alert, proxy.DoRequest, alertchannel.ChannelType(ptype)); err != nil {
			api.Error(w, err)
			return
		}
	}
	api.Success(w, nil)
}

func (a *CenterAlertReceiver) recordAlertToHistory(ctx context.Context, params url.Values, alert alertchannel.AlertProxyMessage, fn SendFunc, channelType alertchannel.ChannelType) error {
	//该告警的原始规则名称
	originalertname := alert.Labels[alertchannel.AlertOriginNameLabel]
	if originalertname == "" {
		return nil
	}
	appname := alert.Labels[alertchannel.AlertScopeApplication]
	clustername := alert.Labels[alertchannel.AlertScopeCluster]
	tenantname := alert.Labels[alertchannel.AlertScopeTenant]
	organizationname := alert.Labels[alertchannel.AlertScopeOrganization]

	mongoScope := observerability.AlertRecordScope{}
	storage := a.Store
	recordname := alert.Fingerprint
	if tenantname != "" {
		obj := &store.Unstructured{}
		obj.SetResource("tenants")
		if err := storage.Get(ctx, tenantname, obj); err != nil {
			if liberrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		mongoScope.Tenant = tenantname
		storage = storage.Scope(store.Scope{Resource: "tenants", Name: tenantname})
		if clustername != "" {
			// 自有集群
			obj := &store.Unstructured{}
			obj.SetResource("clusters")
			if err := storage.Get(ctx, clustername, obj); err != nil {
				if liberrors.IsNotFound(err) {
					return nil
				}
				return err
			}
			mongoScope.Cluster = clustername
			storage = storage.Scope(store.Scope{Resource: "clusters", Name: clustername})
		} else if organizationname != "" {
			obj := &store.Unstructured{}
			obj.SetResource("organizations")
			if err := storage.Get(ctx, organizationname, obj); err != nil {
				if liberrors.IsNotFound(err) {
					return nil
				}
				return err
			}
			mongoScope.Organization = organizationname
			storage = storage.Scope(store.Scope{Resource: "organizations", Name: organizationname})
			if appname != "" {
				obj := &store.Unstructured{}
				obj.SetResource("applications")
				if err := storage.Get(ctx, appname, obj); err != nil {
					if liberrors.IsNotFound(err) {
						return nil
					}
					return err
				}
				mongoScope.Application = appname
				storage = storage.Scope(store.Scope{Resource: "applications", Name: appname})
			}
		}
	} else if clustername != "" {
		obj := &store.Unstructured{}
		obj.SetResource("clusters")
		if err := storage.Get(ctx, clustername, obj); err != nil {
			if liberrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		mongoScope.Cluster = clustername
		storage = storage.Scope(store.Scope{Resource: "clusters", Name: clustername})
	}
	originalertrule := &observerability.AlertRule{}
	if err := storage.Get(ctx, originalertname, originalertrule); err != nil {
		if liberrors.IsNotFound(err) {
			return nil
		}
		return err
	}
	//是webhook
	if channelType == alertchannel.TypeWebhook {
		hasWebhookReceiver := slices.ContainsFunc(originalertrule.Receivers, func(a *observerability.AlertReceiver) bool {
			return a.AlertChannel.ChannelType == alertchannel.TypeWebhook
		})
		if !hasWebhookReceiver || len(originalertrule.Receivers) != 1 {
			return nil
		}
	}
	// 发送失败，不需要记录历史
	plate := alertchannel.PlatformTypeBOB
	if clustername != "" && tenantname == "" {
		plate = alertchannel.PlatformTypeISMC
	}
	var errMessage string
	if err := fn(params, alert, plate); err != nil {
		log.FromContext(ctx).Info("send alert information",
			"alertname", originalertname,
			"tenant", tenantname, "organization", organizationname,
			"cluster", clustername, "instance", appname)
		errMessage = err.Error()
	}

	var (
		templateGroup      string
		templateExpression = originalertrule.Expr
	)
	if originalertrule.PromqlGenerator != nil {
		templateGroup = originalertrule.PromqlGenerator.TemplateGroupResource
		templateExpression = originalertrule.PromqlGenerator.TemplateShowName
	}

	ar := &observerability.AlertRecord{
		ObjectMeta: store.ObjectMeta{
			Name: recordname,
		},
	}
	rs := observerability.GetAlertRecordFields(mongoScope)
	if err := a.MongoStore.Get(ctx, recordname, ar, store.WithGetFieldRequirements(rs...)); err != nil {
		if !liberrors.IsNotFound(err) {
			return err
		}
		ar.AlertRecordScope = mongoScope
		ar.TemplateGroup = templateGroup
		ar.TemplateExpression = templateExpression
		ar.Labels = alert.Labels
		ar.Record = alert
		ar.SendErrorMessage = errMessage
		ar.Count = 1
		return a.MongoStore.Create(ctx, ar)
	}
	ar.Labels = make(map[string]string)
	for k, v := range alert.Labels {
		ar.Labels[k] = v
	}
	ar.TemplateGroup = templateGroup
	ar.TemplateExpression = templateExpression
	ar.Count += 1
	ar.Record = alert
	ar.SendErrorMessage = errMessage
	return a.MongoStore.Update(ctx, ar)
}

func (a *CenterAlertReceiver) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Alert").
		Route(
			api.POST(AlertReceiverPath).To(a.ReceiveHandler),
		)
}
