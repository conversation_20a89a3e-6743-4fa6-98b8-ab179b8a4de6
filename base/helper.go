package base

import (
	"context"
	"encoding/json"
	"fmt"
	"hash/fnv"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/controller"
	mongodbqueue "xiaoshiai.cn/common/queue/mongodb"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/store/mongo"
)

func RandName(prefix string) string {
	suf := rand.RandomAlphaNumeric(8)
	if prefix == "" {
		return suf
	}
	return fmt.Sprintf("%s-%s", prefix, suf)
}

func FirstOrEmpty(s []string) string {
	if len(s) > 0 {
		return s[0]
	}
	return ""
}

func Redirect(w http.ResponseWriter, r *http.Request) {
	var queryPart string
	if len(r.URL.RawQuery) > 0 {
		queryPart = "?" + r.URL.RawQuery
	}
	http.Redirect(w, r, r.URL.Path+"/"+queryPart, http.StatusMovedPermanently)
}

// GetNamespaceFromTenantOrganization
// Deprecated: use namespace in app.cluster.namespace
func GetNamespaceFromTenantOrganization(tenant, org string) string {
	return tenant + "-" + org
}

func TenantOrganizationFromScopes(scopes ...store.Scope) (string, string) {
	org, tenant := "", ""
	for _, scope := range scopes {
		switch scope.Resource {
		case "organizations":
			org = scope.Name
		case "tenants":
			tenant = scope.Name
		}
	}
	return tenant, org
}

//	ReferenceToName converts a cluster reference to a name
//
// tenant cluster 	-> tenant.cluster
// cluster 			-> cluster
func ReferenceToName(cluster store.ObjectReference) string {
	names := []string{}
	for _, scope := range cluster.Scopes {
		names = append(names, scope.Name)
	}
	names = append(names, cluster.Name)
	return strings.Join(names, ".")
}

type ScopeHandler[T store.Object] struct {
	On                                            func(ctx context.Context, obj T) (controller.Result, error)
	OnTenant                                      func(ctx context.Context, tenant string, obj T) (controller.Result, error)
	OnCluster                                     func(ctx context.Context, cluster string, obj T) (controller.Result, error)
	OnTenantCluster                               func(ctx context.Context, tenant, cluster string, obj T) (controller.Result, error)
	OnTenantWorkspace                             func(ctx context.Context, tenant, workspace string, obj T) (controller.Result, error)
	OnTenantWorkspaceApplication                  func(ctx context.Context, tenant, workspace, app string, obj T) (controller.Result, error)
	OnTenantOrganization                          func(ctx context.Context, tenant, org string, obj T) (controller.Result, error)
	OnTenantOrganizationApplication               func(ctx context.Context, tenant, org, app string, obj T) (controller.Result, error)
	OnTenantOrganizationApplicationSubApplication func(ctx context.Context, tenant, org, app, subapp string, obj T) (controller.Result, error)
}

func DispatchOnScopes[T store.Object](ctx context.Context, obj T, handler ScopeHandler[T]) (controller.Result, error) {
	scopes := obj.GetScopes()
	if len(scopes) == 0 {
		if handler.On != nil {
			return handler.On(ctx, obj)
		}
		return controller.Result{}, nil
	}
	scope, scopes := scopes[0], scopes[1:]
	switch scope.Resource {
	case "tenants":
		tenantname := scope.Name
		if len(scopes) == 0 {
			if handler.OnTenant != nil {
				return handler.OnTenant(ctx, tenantname, obj)
			}
			return controller.Result{}, nil

		}
		scope, scopes = scopes[0], scopes[1:]
		switch scope.Resource {
		case "clusters":
			clusterName := scope.Name
			if len(scopes) == 0 {
				if handler.OnTenantCluster != nil {
					return handler.OnTenantCluster(ctx, tenantname, clusterName, obj)
				}
				return controller.Result{}, nil
			}
		case "workspaces":
			workspace := scope.Name
			if len(scopes) == 0 {
				if handler.OnTenantWorkspace != nil {
					return handler.OnTenantWorkspace(ctx, tenantname, workspace, obj)
				}
				return controller.Result{}, nil
			}
		case "organizations":
			orgname := scope.Name
			if len(scopes) == 0 {
				if handler.OnTenantOrganization != nil {
					return handler.OnTenantOrganization(ctx, tenantname, orgname, obj)
				}
				return controller.Result{}, nil
			}
			scope, scopes = scopes[0], scopes[1:]
			switch scope.Resource {
			case "applications":
				appname := scope.Name
				if len(scopes) == 0 {
					if handler.OnTenantOrganizationApplication != nil {
						return handler.OnTenantOrganizationApplication(ctx, tenantname, orgname, appname, obj)
					}
					return controller.Result{}, nil
				}
				scope, scopes = scopes[0], scopes[1:]
				switch scope.Resource {
				case "applications":
					subappname := scope.Name
					if len(scopes) == 0 {
						if handler.OnTenantOrganizationApplicationSubApplication != nil {
							return handler.OnTenantOrganizationApplicationSubApplication(ctx, tenantname, orgname, appname, subappname, obj)
						}
						return controller.Result{}, nil
					}
				}
			case "workspaces":
				workspace := scope.Name
				if len(scopes) == 0 {
					if handler.OnTenantWorkspace != nil {
						return handler.OnTenantWorkspace(ctx, tenantname, workspace, obj)
					}
					return controller.Result{}, nil
				}
				scope, scopes = scopes[0], scopes[1:]
				switch scope.Resource {
				case "applications":
					appname := scope.Name
					if len(scopes) == 0 {
						if handler.OnTenantWorkspaceApplication != nil {
							return handler.OnTenantWorkspaceApplication(ctx, tenantname, orgname, appname, obj)
						}
						return controller.Result{}, nil
					}
				}
			}
		}
	case "clusters":
		clusterName := scope.Name
		if handler.OnCluster != nil {
			return handler.OnCluster(ctx, clusterName, obj)
		}
		return controller.Result{}, nil
	}
	return controller.Result{}, nil
}

func NewAllApplicationGroup(subgroup ...api.Group) api.Group {
	return api.NewGroup("").
		SubGroup(
			// tenant applications
			NewTenantGroup("applications/{application}").
				SubGroup(subgroup...),
			// organization applications
			NewTenantOrganizationGroup("applications/{application}").
				SubGroup(subgroup...),
			// organization applications subapplications
			NewTenantOrganizationGroup("applications/{application}/subapplications/{subapplication}/").
				SubGroup(subgroup...),
		)
}

func MaxResourceList(total corev1.ResourceList, add corev1.ResourceList) {
	ResourceListCollect(total, add, func(_ corev1.ResourceName, into *resource.Quantity, val resource.Quantity) {
		if into.Cmp(val) < 0 {
			*into = val
		}
	})
}

func AddResourceList(total corev1.ResourceList, add corev1.ResourceList) {
	ResourceListCollect(total, add, func(_ corev1.ResourceName, into *resource.Quantity, val resource.Quantity) {
		into.Add(val)
	})
}

func SubResourceList(total corev1.ResourceList, sub corev1.ResourceList) {
	ResourceListCollect(total, sub, func(_ corev1.ResourceName, into *resource.Quantity, val resource.Quantity) {
		into.Sub(val)
	})
}

func MultiplyResourceList(total corev1.ResourceList, factor int64) {
	ResourceListCollect(total, total, func(_ corev1.ResourceName, into *resource.Quantity, _ resource.Quantity) {
		into.Mul(factor)
	})
}

type ResourceListCollectFunc func(corev1.ResourceName, *resource.Quantity, resource.Quantity)

func ResourceListCollect(into, vals ResourceList, collect ResourceListCollectFunc) ResourceList {
	for resourceName, quantity := range vals {
		lastQuantity := into[resourceName].DeepCopy()
		collect(resourceName, &lastQuantity, quantity)
		into[resourceName] = lastQuantity
	}
	return into
}

func ListOptionsToStoreListOptions(opts api.ListOptions) []store.ListOption {
	listOpts := []store.ListOption{}
	if opts.Size > 0 {
		listOpts = append(listOpts, store.WithPageSize(opts.Page, opts.Size))
	}
	if opts.Sort != "" {
		listOpts = append(listOpts, store.WithSort(opts.Sort))
	}
	if opts.Search != "" {
		listOpts = append(listOpts, store.WithSearch(opts.Search))
	}
	return listOpts
}

func InjectAttrName(ctx context.Context, name string) {
	attributes := api.AttributesFromContext(ctx)
	if len(attributes.Resources) == 0 {
		return
	}
	attributes.Resources[len(attributes.Resources)-1].Name = name
}

type ReQueueError struct {
	After time.Duration
}

func (r ReQueueError) Error() string {
	return fmt.Sprintf("retry after %s", r.After)
}

func ReQueue(after time.Duration) error {
	return ReQueueError{After: after}
}

func UnwrapReQueueError(err error) (controller.Result, error) {
	if err == nil {
		return controller.Result{}, nil
	}
	if requeue, ok := err.(ReQueueError); ok {
		return controller.Result{
			Requeue:      true,
			RequeueAfter: requeue.After,
		}, nil
	}
	return controller.Result{}, err
}

type ScheduleRunner struct {
	RunEveryHourFunc  func(ctx context.Context) error
	RunEveryDayFunc   func(ctx context.Context) error
	RunEveryWeekFunc  func(ctx context.Context) error
	RunEveryMonthFunc func(ctx context.Context) error

	// CustomFuncs is a map of cron spec to function. The function will be called when the cron spec matches.
	CustomFuncs map[string]func(ctx context.Context) error
}

func (s *ScheduleRunner) Run(ctx context.Context) error {
	cron := cron.New()
	// Run every hour
	if s.RunEveryHourFunc != nil {
		cron.AddFunc("0 * * * *", func() {
			s.RunEveryHourFunc(ctx)
		})
	}
	// Run every day
	if s.RunEveryDayFunc != nil {
		cron.AddFunc("0 0 * * *", func() {
			s.RunEveryDayFunc(ctx)
		})
	}
	// Run every week
	if s.RunEveryWeekFunc != nil {
		cron.AddFunc("0 0 * * 1", func() {
			s.RunEveryWeekFunc(ctx)
		})
	}
	// Run every month
	if s.RunEveryMonthFunc != nil {
		cron.AddFunc("0 0 1 * *", func() {
			s.RunEveryMonthFunc(ctx)
		})
	}
	for spec, fn := range s.CustomFuncs {
		cron.AddFunc(spec, func() {
			fn(ctx)
		})
	}
	go func() {
		<-ctx.Done()
		stopctx := cron.Stop()
		// Wait for all jobs to finish
		<-stopctx.Done()
	}()
	go cron.Run()
	<-ctx.Done()
	stopctx := cron.Stop()
	<-stopctx.Done()
	return nil
}

type Cycle struct {
	From time.Time `json:"from,omitempty"`
	To   time.Time `json:"to,omitempty"`
}

func (c Cycle) Key() string {
	return fmt.Sprintf("%s-%s", c.From.Format(time.RFC3339), c.To.Format(time.RFC3339))
}

func (c Cycle) Duration() time.Duration {
	return c.To.Sub(c.From)
}

func CalcHistoryCyclesFromNow(maxHistory, resolution time.Duration) []Cycle {
	if maxHistory == 0 {
		return []Cycle{}
	}
	now := time.Now()
	return CalcCycles(now.Add(-maxHistory), now, resolution)
}

func CalcCycles(from, to time.Time, resolution time.Duration) []Cycle {
	from, to = from.Truncate(resolution), to.Truncate(resolution)
	cycles := []Cycle{}
	for from.Before(to) {
		cycles = append(cycles, Cycle{From: from, To: from.Add(resolution)})
		from = from.Add(resolution)
	}
	return cycles
}

var timeZone = os.Getenv("CTZ")

func GetCustomeTimeZone() *time.Location {
	var offset int
	switch timeZone {
	case "UTC-12":
		offset = -12 * 3600
	case "UTC-11":
		offset = -11 * 3600
	case "UTC-10":
		offset = -10 * 3600
	case "UTC-9:30":
		offset = -9*3600 - 30*60
	case "UTC-9":
		offset = -9 * 3600
	case "UTC-8":
		offset = -8 * 3600
	case "UTC-7":
		offset = -7 * 3600
	case "UTC-6":
		offset = -6 * 3600
	case "UTC-5":
		offset = -5 * 3600
	case "UTC-4":
		offset = -4 * 3600
	case "UTC-3:30":
		offset = -3*3600 - 30*60
	case "UTC-3":
		offset = -3 * 3600
	case "UTC-2":
		offset = -2 * 3600
	case "UTC-1":
		offset = -1 * 3600
	case "UTC", "UTC+0", "":
		offset = 0
	case "UTC+1":
		offset = 1 * 3600
	case "UTC+2":
		offset = 2 * 3600
	case "UTC+3":
		offset = 3 * 3600
	case "UTC+4":
		offset = 4 * 3600
	case "UTC+5":
		offset = 5 * 3600
	case "UTC+5:30":
		offset = 5*3600 + 30*60
	case "UTC+5:45":
		offset = 5*3600 + 45*60
	case "UTC+6":
		offset = 6 * 3600
	case "UTC+6:30":
		offset = 6*3600 + 30*60
	case "UTC+7":
		offset = 7 * 3600
	case "UTC+8":
		offset = 8 * 3600
	case "UTC+8:45":
		offset = 8*3600 + 45*60
	case "UTC+9":
		offset = 9 * 3600
	case "UTC+9:30":
		offset = 9*3600 + 30*60
	case "UTC+10":
		offset = 10 * 3600
	case "UTC+10:30":
		offset = 10*3600 + 30*60
	case "UTC+11":
		offset = 11 * 3600
	case "UTC+12":
		offset = 12 * 3600
	case "UTC+12:45":
		offset = 12*3600 + 45*60
	case "UTC+13":
		offset = 13 * 3600
	case "UTC+14":
		offset = 14 * 3600
	}
	return time.FixedZone(timeZone, offset)
}

func ComputeHash(values ...any) uint32 {
	hasher := fnv.New32a()
	hasher.Reset()
	for _, value := range values {
		data, err := json.Marshal(value)
		if err != nil {
			fmt.Fprintf(hasher, "%#v", value)
		} else {
			hasher.Write(data)
		}
		hasher.Write([]byte{0})
	}
	return hasher.Sum32()
}

func ComputeHashToHex(values ...any) string {
	return SafeEncodeString(fmt.Sprint(ComputeHash(values...)))
}

const alphanums = "bcdfghjklmnpqrstvwxz2456789"

func SafeEncodeString(s string) string {
	r := make([]byte, len(s))
	for i, b := range []rune(s) {
		r[i] = alphanums[(int(b) % len(alphanums))]
	}
	return string(r)
}

func Def[T comparable](val, def T) T {
	var empty T
	if val == empty {
		return def
	}
	return val
}

func NewQueue(store *mongo.MongoStorage, name string) *mongodbqueue.MongoQueue {
	collection := store.Database().Collection(name)
	return mongodbqueue.NewMongoDBQueue(collection, &mongodbqueue.MongoQueueOptions{CheckInterval: 1 * time.Minute})
}

type SafeSlice[T any] struct {
	sync.Mutex
	s []T
}

func (s *SafeSlice[T]) Append(items ...T) {
	s.Lock()
	defer s.Unlock()
	s.s = append(s.s, items...)
}

func (s *SafeSlice[T]) Get() []T {
	s.Lock()
	defer s.Unlock()
	return s.s
}
