package base

import (
	"context"
	stderrors "errors"
	"net/http"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

type API struct {
	Store store.Store
}

func NewAPI(store store.Store, scopePathVarNames ...string) API {
	return API{Store: store}
}

func (a *API) On(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	On(w, r, func(ctx context.Context) (any, error) {
		return fn(ctx, a.Store)
	})
}

func On(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		val, err := fn(ctx)
		err = ConvertError(err)
		return val, err
	})
}

func ConvertError(err error) error {
	if err == nil {
		return nil
	}
	// convert k8s error to common error
	if status, ok := err.(apierrors.APIStatus); ok || stderrors.As(err, &status) {
		metastatus := status.Status()
		return &errors.Status{
			Status:  metastatus.Status,
			Code:    metastatus.Code,
			Message: metastatus.Message,
			Reason:  errors.StatusReason(metastatus.Reason),
		}
	}
	return err
}

func (a *API) OnCurrentUser(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, storage store.Store, user string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		userinfo := api.AuthenticateFromContext(ctx)
		if userinfo.User.Name == "" {
			return nil, errors.NewUnauthorized("require login")
		}
		return f(ctx, storage, userinfo.User.Name)
	})
}

func ScopeTenant(tenant string) store.Scope {
	return store.Scope{Resource: "tenants", Name: tenant}
}

func ScopeOrganization(organization string) store.Scope {
	return store.Scope{Resource: "organizations", Name: organization}
}

func NewTenantGroup(subresource string) api.Group {
	return api.NewGroup("/tenants/{tenant}/" + subresource)
}

func (a *API) OnTemplateGroup(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		templateGroup := api.Path(r, "templategroup", "")
		if templateGroup == "" {
			return nil, errors.NewBadRequest("templateGroup name is required")
		}
		obj := &store.Unstructured{}
		obj.SetResource("alertruletemplategroups")
		if err := storage.Get(ctx, templateGroup, obj); err != nil {
			return nil, err
		}
		templateGroupStorage := storage.Scope(store.Scope{Resource: "alertruletemplategroups", Name: templateGroup})
		return fn(ctx, templateGroupStorage)
	})
}

func (a *API) OnTemplateGroupResource(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store) (any, error)) {
	a.OnTemplateGroup(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		resource := api.Path(r, "resource", "")
		if resource == "" {
			return nil, errors.NewBadRequest("resource name is required")
		}
		obj := &store.Unstructured{}
		obj.SetResource("alertruletemplateresourcegroups")
		if err := storage.Get(ctx, resource, obj); err != nil {
			return nil, err
		}
		storage = storage.Scope(store.Scope{Resource: "alertruletemplateresourcegroups", Name: resource})
		return fn(ctx, storage)
	})
}

func (a *API) OnTenant(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		tenant := api.Path(r, "tenant", "")
		if tenant == "" {
			return "", errors.NewBadRequest("tenant name is required")
		}
		obj := &store.Unstructured{}
		obj.SetResource("tenants")
		if err := storage.Get(ctx, tenant, obj); err != nil {
			return nil, err
		}
		tenantStorage := storage.Scope(ScopeTenant(tenant))
		return fn(ctx, tenantStorage, tenant)
	})
}

func ScopeTenantOrganization(tenant, organization string) []store.Scope {
	return []store.Scope{ScopeTenant(tenant), {Resource: "organizations", Name: organization}}
}

func NewTenantOrganizationGroup(subresource string) api.Group {
	return NewTenantGroup("organizations/{organization}/" + subresource)
}

func NewTenantWorkspaceGroup(subresource string) api.Group {
	if true {
		// currently workspace is same as organization
		return NewTenantOrganizationGroup(subresource)
	}
	return NewTenantGroup("workspaces/{workspace}/" + subresource)
}

func NewTenantOrganizationApplicationGroup(subresource string) api.Group {
	return NewTenantOrganizationGroup("applications/{application}/" + subresource)
}

func NewProductGroup(subresource string) api.Group {
	return api.NewGroup("/products/{product}/" + subresource)
}

func (a *API) OnTenantWorkspace(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, workspace string) (any, error)) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		workspace := api.Path(r, "workspace", "")
		if workspace == "" {
			return nil, errors.NewBadRequest("workspace name is required")
		}
		storage = storage.Scope(ScopeWorkspace(workspace))
		return fn(ctx, storage, tenant, workspace)
	})
}

func (a *API) OnTenantOrganization(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, organization string) (any, error)) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		organization := api.Path(r, "organization", "")
		if organization == "" {
			return nil, errors.NewBadRequest("organization name is required")
		}
		storage = storage.Scope(store.Scope{Resource: "organizations", Name: organization})
		return fn(ctx, storage, tenant, organization)
	})
}

func (a *API) OnTenantOrTenantOrganization(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, storage store.Store, tenant, organization string) (any, error),
) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		organization := api.Path(r, "organization", "")
		if organization == "" {
			return fn(ctx, storage, tenant, "")
		}
		storage = storage.Scope(ScopeOrganization(organization))
		return fn(ctx, storage, tenant, organization)
	})
}

// OnTenantOrTenantOrganization
// application can be created both in tenant and tenant organization
func (a *API) OnTenantOrTenantOrganizationScopes(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, scopes []store.Scope) (any, error),
) {
	a.OnTenant(w, r, func(ctx context.Context, _ store.Store, tenant string) (any, error) {
		scopes := []store.Scope{ScopeTenant(tenant)}
		if org := api.Path(r, "organization", ""); org != "" {
			scopes = append(scopes, ScopeOrganization(org))
		}
		return fn(ctx, scopes)
	})
}

func (a *API) OnPosiibleApplicationScopes(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, scopes []store.Scope) (any, error),
) {
	a.OnTenantOrTenantOrganizationScopes(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		if app := api.Path(r, "application", ""); app != "" {
			// list subapplications
			scopes = append(scopes, ScopeApplication(app))
		}
		return fn(ctx, scopes)
	})
}

func NewApplicationGroup(subresource string) api.Group {
	return NewTenantOrganizationGroup("applications/{application}/" + subresource)
}

// OnApplication
// appref is the reference of the application
// it should be a tenant organization application or a subapplication(tenant organization application scoeps)
// appscopestorage is the storage under the application scope
func (a *API) OnApplication(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error)) {
	a.OnTenantOrTenantOrganizationScopes(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		application := api.Path(r, "application", "")
		if application == "" {
			return nil, errors.NewBadRequest("application name is required")
		}
		appref := store.ObjectReference{Name: application, Scopes: scopes}
		storage := a.Store.Scope(scopes...)
		storage = storage.Scope(ScopeApplication(application))

		// under subapplications
		if subappname := api.Path(r, "subapplication", ""); subappname != "" {
			storage = storage.Scope(ScopeApplication(subappname))
			appref.Scopes = append(appref.Scopes, ScopeApplication(application))
			appref.Name = subappname
		}

		return fn(ctx, appref, storage)
	})
}

func (a *API) OnApplicationInstance(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, workspace, name string) (any, error)) {
}

func NewClusterGroup(subresource string) api.Group {
	return api.NewGroup("/clusters/{cluster}/" + subresource)
}

func NewTenantClusterGroup(subresource string) api.Group {
	return NewTenantGroup("/clusters/{cluster}/" + subresource)
}

func ScopeCluster(cluster string) store.Scope {
	return store.Scope{Resource: "clusters", Name: cluster}
}

func ScopeApplication(app string) store.Scope {
	return store.Scope{Resource: "applications", Name: app}
}

func ScopeWorkspace(workspace string) store.Scope {
	return store.Scope{Resource: "workspaces", Name: workspace}
}

func (a *API) OnCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, cluster string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		cluster := api.Path(r, "cluster", "")
		if cluster == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		storage = storage.Scope(ScopeCluster(cluster))
		return fn(ctx, storage, cluster)
	})
}

func (a *API) OnTenantCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, cluster string) (any, error)) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		cluster := api.Path(r, "cluster", "")
		if cluster == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		storage = storage.Scope(ScopeCluster(cluster))
		return fn(ctx, storage, tenant, cluster)
	})
}

func NewProductsGroup(subresource string) api.Group {
	return api.NewGroup("/products/{product}/" + subresource)
}

func NewAdminProductsGroup(subresource string) api.Group {
	return api.NewGroup("/admin-products/{product}/" + subresource)
}

func (a *API) OnProduct(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, name string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		name := api.Path(r, "product", "")
		if name == "" {
			return nil, errors.NewBadRequest("market product name is required")
		}
		storage = storage.Scope(store.Scope{Resource: "products", Name: name})
		return fn(ctx, storage, name)
	})
}

type ScopeVar struct {
	Resource    string
	PathVarName string
}

func (a *API) OnScope(w http.ResponseWriter, r *http.Request, scopeVars []ScopeVar, fn func(ctx context.Context, storage store.Store, scopes []store.Scope) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		scopes := make([]store.Scope, 0, len(scopeVars))

		for _, val := range scopeVars {
			value := api.Path(r, val.PathVarName, "")
			if value == "" {
				return nil, errors.NewBadRequest(val.PathVarName + " is required")
			}
			scopes = append(scopes, store.Scope{Resource: val.Resource, Name: value})
		}
		return fn(ctx, storage.Scope(scopes...), scopes)
	})
}

func OnSystemOrTenantOrTenantOrganizationRequirements(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, reqs store.Requirements) (any, error),
) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		reqs := store.Requirements{}
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			reqs = append(reqs, store.RequirementEqual("tenant", tenant))
		}
		if org := api.Path(r, "organization", ""); org != "" {
			reqs = append(reqs, store.RequirementEqual("organization", org))
		}
		return fn(ctx, reqs)
	})
}

func OnSystemOrTenantOrTenantOrganization(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, opttenant, optorg string) (any, error),
) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return fn(ctx, api.Path(r, "tenant", ""), api.Path(r, "organization", ""))
	})
}

func NewCurrentGroup() api.Group {
	return api.NewGroup("/current")
}

func OnCurrentUser(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, user string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		userinfo := api.AuthenticateFromContext(ctx)
		if userinfo.User.Name == "" {
			return nil, errors.NewBadRequest("require login")
		}
		return fn(ctx, userinfo.User.Name)
	})
}
