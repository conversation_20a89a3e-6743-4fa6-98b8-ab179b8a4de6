package tenant

import (
	"context"
	"net/http"
	"slices"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/rbac"
)

type UserTenantRole struct {
	Tenant `json:",inline"`
	Role   string `json:"role"`
}

func (a *API) ListCurentUserTenants(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		// check isSuperAdmin admin user
		isSuperAdmin, err := IsSuperAdminUser(ctx, storage, username)
		if err != nil {
			return nil, err
		}
		if isSuperAdmin {
			tenants := &store.List[Tenant]{}
			if err := storage.List(ctx, tenants); err != nil {
				return nil, err
			}
			// has all tenants admin
			ret := []UserTenantRole{}
			for _, tenant := range tenants.Items {
				ret = append(ret, UserTenantRole{Tenant: tenant, Role: rbac.RoleAdmin})
			}
			return ret, nil
		} else {
			// search all tenants
			tenants := &store.List[Tenant]{}
			if err := storage.List(ctx, tenants); err != nil {
				return nil, err
			}
			ret := []UserTenantRole{}
			for _, item := range tenants.Items {
				userrole := &rbac.UserRole{}
				if err := storage.Scope(base.ScopeTenant(item.Name)).Get(ctx, username, userrole); err != nil {
					if errors.IsNotFound(err) {
						continue
					}
					return nil, err
				}
				role := UserTenantRole{
					Tenant: item,
					Role: func() string {
						if len(userrole.Roles) == 0 {
							return ""
						}
						return userrole.Roles[0]
					}(),
				}
				ret = append(ret, role)
			}
			return ret, nil
		}
	})
}

func IsSuperAdminUser(ctx context.Context, storage store.Store, username string) (bool, error) {
	// check is admin user
	userrole := &rbac.UserRole{}
	if err := storage.Get(ctx, username, userrole); err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}
	return slices.Contains(userrole.Roles, rbac.RoleAdmin), nil
}

func (a *API) currentTenantsGroup() api.Group {
	return api.
		NewGroup("/current").
		SubGroup(
			api.NewGroup("/tenants").
				Route(
					api.GET("").
						Doc("list current user tenants").
						To(a.ListCurentUserTenants).
						Response([]UserTenantRole{})),
		)
}
