package tenant

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/rbac"
)

type RegisterTenantRequest struct {
	Name        string `json:"name"`
	<PERSON><PERSON>       string `json:"alias,omitempty"`
	Description string `json:"description"`
	Email       string `json:"email,omitempty"`
	Phone       string `json:"phone,omitempty"`
	Avatar      string `json:"avatar,omitempty"`
	Lang        string `json:"lang,omitempty"`
}

func (a *API) RegisterTenant(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, _ store.Store) (any, error) {
		obj := &RegisterTenantRequest{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		newtenant := &Tenant{
			ObjectMeta: store.ObjectMeta{
				Name:        obj.Name,
				Alias:       obj.<PERSON>,
				Description: obj.Description,
			},
			Enabled: true,
			Email:   obj.Email,
			Phone:   obj.Phone,
			Avatar:  obj.Avatar,
			Lang:    obj.<PERSON>,
		}
		if err := base.ValidateRestrictName(obj.Name); err != nil {
			return nil, err
		}
		if err := a.checkTenant(ctx, newtenant, true); err != nil {
			return nil, err
		}
		if err := a.Store.Create(r.Context(), newtenant); err != nil {
			return nil, err
		}
		// add the user who created the tenant to the tenant's admin role
		userinfo := api.AuthenticateFromContext(ctx).User
		if username := userinfo.Name; username != "" {
			if err := rbac.AddUserToScope(ctx, a.Store, []store.Scope{base.ScopeTenant(obj.Name)}, username, rbac.RoleAdmin); err != nil {
				return nil, err
			}
		}
		return obj, nil
	})
}

func (a *API) registerTenantsGroup() api.Group {
	return api.
		NewGroup("/tenant-register").
		Tag("Tenant").
		Route(
			api.POST("").
				Doc("Register an tenant").
				To(a.RegisterTenant).
				Param(api.BodyParam("tenant", RegisterTenantRequest{})).
				Response(Tenant{}),
		)
}
