package tenant

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/rbac"
)

func NewTenantController(storage store.Store, info cluster.CloudInfoGetter) (*controller.Controller, error) {
	rec := &TenantController{
		Client:   storage,
		Clusters: info,
	}
	br := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("tenants-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("tenants", br).Watch(
		controller.NewStoreSource(storage, &Tenant{}),
	)
	return c, nil
}

var _ controller.Reconciler[*Tenant] = &TenantController{}

type TenantController struct {
	Client   store.Store
	Clusters cluster.CloudInfoGetter
}

// Remove implements Reconciler.
func (t *TenantController) Remove(ctx context.Context, obj *Tenant) (controller.Result, error) {
	return controller.Result{}, nil
}

// Sync implements Reconciler.
func (t *TenantController) Sync(ctx context.Context, obj *Tenant) (controller.Result, error) {
	if err := t.syncPredefinedTenantRoles(ctx, obj); err != nil {
		return controller.Result{}, err
	}
	obj.Status.Phase = TenantPhaseReady
	return controller.Result{}, nil
}

func (t *TenantController) syncPredefinedTenantRoles(ctx context.Context, obj *Tenant) error {
	conditionStorage := t.Client.Scope(obj.Scopes...).Scope(base.ScopeTenant(obj.GetName()))
	return rbac.InitRoles(ctx, conditionStorage, rbac.DefaultTenantRoles, true)
}
