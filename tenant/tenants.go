package tenant

import (
	"context"
	"fmt"
	"net/http"
	"slices"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/rbac"
)

type Tenant struct {
	store.ObjectMeta `json:",inline"`
	Enabled          bool         `json:"enabled"`
	Email            string       `json:"email,omitempty"`
	Phone            string       `json:"phone,omitempty"`
	Avatar           string       `json:"avatar,omitempty"`
	Lang             string       `json:"lang,omitempty"`
	Status           TenantStatus `json:"status,omitempty"`
}

type TenantStatus struct {
	Phase      string                       `json:"phase,omitempty"`
	Message    string                       `json:"message,omitempty"`
	Conditions []controller.StatusCondition `json:"conditions,omitempty"`
}

const (
	TenantPhaseReady = "Ready"
)

type TenantWithUsers struct {
	Tenant    `json:",inline"`
	UserCount int      `json:"userCount"`
	Users     []string `json:"users,omitempty"`
}

func NewAPI(base base.API, recoreder events.Recorder) *API {
	return &API{
		API:       base,
		recoreder: recoreder,
	}
}

type API struct {
	base.API
	recoreder events.Recorder
}

func (a *API) ListTenants(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		list := &store.List[Tenant]{}
		if _, err := base.GenericList(r, a.Store, list); err != nil {
			return nil, err
		}
		withusers := []TenantWithUsers{}
		for _, tenant := range list.Items {
			usercnt, err := storage.Scope(base.ScopeTenant(tenant.Name)).Count(ctx, &rbac.UserRole{})
			if err != nil {
				log.Error(err, "count user role error")
			}
			withusers = append(withusers, TenantWithUsers{
				Tenant:    tenant,
				UserCount: usercnt,
			})
		}
		return store.List[TenantWithUsers]{Items: withusers, Total: list.Total, Page: list.Page, Size: list.Size}, nil
	})
}

func (a *API) GetTenant(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		return base.GenericGet(r, a.Store, &Tenant{}, tenant)
	})
}

func (a *API) CreateTenant(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, _ store.Store) (any, error) {
		obj := &Tenant{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if err := base.ValidateRestrictName(obj.Name); err != nil {
			return nil, err
		}
		if err := a.checkTenant(ctx, obj, true); err != nil {
			return nil, err
		}
		if err := a.Store.Create(r.Context(), obj); err != nil {
			return nil, err
		}
		return obj, nil
	})
}

func (a *API) UpdateTenant(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, tenantname string) (any, error) {
		if tenantname == "" {
			return nil, fmt.Errorf("name is required")
		}
		obj := &Tenant{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if objname := obj.GetName(); objname != "" && objname != tenantname {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, tenantname)
		}
		if err := a.checkTenant(ctx, obj, false); err != nil {
			return nil, err
		}
		obj.SetName(tenantname)
		obj.SetResourceVersion(0)
		if err := a.Store.Update(r.Context(), obj); err != nil {
			return nil, err
		}
		obj.ObjectMeta.Scopes = append(obj.ObjectMeta.Scopes, store.Scope{Resource: "tenants", Name: tenantname})
		a.recoreder.EventNoAggregate(ctx, obj, events.TenantOrganizationRefresh, "Tenant Information Changed")
		return obj, nil
	})
}

func (a *API) checkTenant(ctx context.Context, tenant *Tenant, creation bool) error {
	excludes := []string{}
	if !creation {
		excludes = append(excludes, tenant.Name)
	}
	// check tenant email
	if email := tenant.Email; email != "" {
		ok, err := a.existsTenantByField(ctx, "email", email, excludes...)
		if err != nil {
			return err
		}
		if ok {
			return errors.NewConflict("tenant", tenant.Name, fmt.Errorf("tenant with email %s already exists", email))
		}
	}
	// check tenant phone
	if phone := tenant.Phone; phone != "" {
		ok, err := a.existsTenantByField(ctx, "phone", phone, excludes...)
		if err != nil {
			return err
		}
		if ok {
			return errors.NewConflict("tenant", tenant.Name, fmt.Errorf("tenant with phone %s already exists", phone))
		}
	}
	return nil
}

func (a *API) existsTenantByField(ctx context.Context, field, value string, excludes ...string) (bool, error) {
	tenantlist := store.List[Tenant]{}
	listoptions := []store.ListOption{
		store.WithFieldRequirements(store.RequirementEqual(field, value)),
	}
	if err := a.Store.List(ctx, &tenantlist, listoptions...); err != nil {
		return false, err
	}
	ret := []Tenant{}
	for _, tenant := range tenantlist.Items {
		if !slices.Contains(excludes, tenant.Name) {
			ret = append(ret, tenant)
		}
	}
	return len(ret) > 0, nil
}

func (a *API) DeleteTenant(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		org := &store.Unstructured{}
		org.SetResource("organizations")

		cnt, err := a.Store.Scope(base.ScopeTenant(tenant)).Count(ctx, org)
		if err != nil {
			return nil, err
		}
		if cnt > 0 {
			return nil, errors.NewBadRequest(fmt.Sprintf("cannot delete tenant %q, it has %d organizations", tenant, cnt))
		}
		return base.GenericDelete(r, a.Store, &Tenant{}, tenant,
			store.WithDeletePropagation(store.DeletePropagationForeground))
	})
}

// EnableTenant
func (a *API) EnableTenant(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		tenantobj := &Tenant{ObjectMeta: store.ObjectMeta{Name: tenant}}
		patch := store.RawPatch(store.PatchTypeMergePatch, []byte(`{"enabled":true}`))
		if err := a.Store.Patch(ctx, tenantobj, patch); err != nil {
			return nil, err
		}
		return tenantobj, nil
	})
}

func (a *API) DisableTenant(w http.ResponseWriter, r *http.Request) {
	a.onTenant(w, r, func(ctx context.Context, tenant string) (any, error) {
		tenantobj := &Tenant{ObjectMeta: store.ObjectMeta{Name: tenant}}
		patch := store.RawPatch(store.PatchTypeMergePatch, []byte(`{"enabled":false}`))

		if err := a.Store.Patch(ctx, tenantobj, patch); err != nil {
			return nil, err
		}
		return tenantobj, nil
	})
}

func (a *API) onTenant(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tanent string) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		tenant := api.Path(r, "tenant", "")
		if tenant == "" {
			return "", errors.NewBadRequest("tenant name is required")
		}
		return fn(ctx, tenant)
	})
}

func (a *API) tenantsGroup() api.Group {
	return api.
		NewGroup("/tenants").
		Route(
			api.GET("").
				Doc("List tenants").
				To(a.ListTenants).Param(base.PageParams...).Response(store.List[TenantWithUsers]{}),
			api.POST("").
				Doc("Create tenant").
				To(a.CreateTenant).Param(api.BodyParam("tenant", Tenant{})).Response(Tenant{}),
			api.GET("/{tenant}").
				Doc("Get tenant").
				To(a.GetTenant).Response(Tenant{}),
			api.PUT("/{tenant}").
				Doc("Update tenant").
				To(a.UpdateTenant).Param(api.BodyParam("tenant", Tenant{})).Response(Tenant{}),
			api.DELETE("/{tenant}").
				Doc("Delete tenant").
				To(a.DeleteTenant),
			api.POST("/{tenant}:enable").
				Doc("Enable tenant").
				To(a.EnableTenant),
			api.POST("/{tenant}:disable").
				Doc("Disable tenant").
				To(a.DisableTenant),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Tenant").
		SubGroup(
			a.tenantsGroup(),
			a.currentTenantsGroup(),
			a.registerTenantsGroup(),
		)
}
