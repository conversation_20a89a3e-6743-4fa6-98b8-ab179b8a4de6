package audit

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
)

var _ api.AuditSink = &DummySink{}

type DummySink struct{}

// Save implements api.AuditSink.
func (d DummySink) Save(log *api.AuditLog) error {
	return nil
}

func BuildAuditSink(ctx context.Context, storage store.Store, options *AuditOptions) (api.AuditSink, error) {
	if !options.Enabled {
		return DummySink{}, nil
	}
	auditsink := NewStorageAuditSink(storage)
	auditsink = api.NewCachedAuditSink(ctx, auditsink, api.DefaultAuditLogCacheSize)
	return auditsink, nil
}

var _ api.AuditSink = &StorageAuditSink{}

func NewStorageAuditSink(storage store.Store) api.AuditSink {
	return &StorageAuditSink{Store: storage}
}

type StorageAuditSink struct {
	Store store.Store
}
type Audit struct {
	store.ObjectMeta `json:",inline"`
	api.AuditLog     `json:",inline"`
	Tenant           string `json:"tenant,omitempty"`
	Organization     string `json:"organization,omitempty"`
}

func init() {
	mongo.GlobalObjectsScheme.Register(&Audit{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"action"},
			{"resourceType"},
			{"username"},
			{"startTime"},
			{"tenant"},
		},
	})
}

// Save implements api.AuditSink.
func (s *StorageAuditSink) Save(log *api.AuditLog) error {
	if log.Request.Method == http.MethodGet {
		return nil
	}
	scopes := []store.Scope{}
	for _, scope := range log.Parents {
		scopes = append(scopes, store.Scope{
			Resource: scope.Resource,
			Name:     scope.Name,
		})
	}
	audit := Audit{
		AuditLog: *log,
		ObjectMeta: store.ObjectMeta{
			// use unixnano as name to make sure it's unique
			Name: strconv.FormatInt(time.Now().UnixNano(), 10),
		},
	}
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	return s.Store.Scope(scopes...).Create(ctx, &audit)
}
