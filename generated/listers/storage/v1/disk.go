/*
Copyright (C) 2024 The Poxiaoyun Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by lister-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/listers"
	"k8s.io/client-go/tools/cache"
	v1 "xiaoshiai.cn/core/apis/storage/v1"
)

// DiskLister helps list Disks.
// All objects returned here must be treated as read-only.
type DiskLister interface {
	// List lists all Disks in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.Disk, err error)
	// Disks returns an object that can list and get Disks.
	Disks(namespace string) DiskNamespaceLister
	DiskListerExpansion
}

// diskLister implements the DiskLister interface.
type diskLister struct {
	listers.ResourceIndexer[*v1.Disk]
}

// NewDiskLister returns a new DiskLister.
func NewDiskLister(indexer cache.Indexer) DiskLister {
	return &diskLister{listers.New[*v1.Disk](indexer, v1.Resource("disk"))}
}

// Disks returns an object that can list and get Disks.
func (s *diskLister) Disks(namespace string) DiskNamespaceLister {
	return diskNamespaceLister{listers.NewNamespaced[*v1.Disk](s.ResourceIndexer, namespace)}
}

// DiskNamespaceLister helps list and get Disks.
// All objects returned here must be treated as read-only.
type DiskNamespaceLister interface {
	// List lists all Disks in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*v1.Disk, err error)
	// Get retrieves the Disk from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*v1.Disk, error)
	DiskNamespaceListerExpansion
}

// diskNamespaceLister implements the DiskNamespaceLister
// interface.
type diskNamespaceLister struct {
	listers.ResourceIndexer[*v1.Disk]
}
