/*
Copyright (C) 2024 The Poxiaoyun Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
	v1 "xiaoshiai.cn/core/apis/storage/v1"
)

// FakeDisks implements DiskInterface
type FakeDisks struct {
	Fake *FakeStorageV1
	ns   string
}

var disksResource = v1.SchemeGroupVersion.WithResource("disks")

var disksKind = v1.SchemeGroupVersion.WithKind("Disk")

// Get takes name of the disk, and returns the corresponding disk object, and an error if there is any.
func (c *FakeDisks) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.Disk, err error) {
	emptyResult := &v1.Disk{}
	obj, err := c.Fake.
		Invokes(testing.NewGetActionWithOptions(disksResource, c.ns, name, options), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1.Disk), err
}

// List takes label and field selectors, and returns the list of Disks that match those selectors.
func (c *FakeDisks) List(ctx context.Context, opts metav1.ListOptions) (result *v1.DiskList, err error) {
	emptyResult := &v1.DiskList{}
	obj, err := c.Fake.
		Invokes(testing.NewListActionWithOptions(disksResource, disksKind, c.ns, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1.DiskList{ListMeta: obj.(*v1.DiskList).ListMeta}
	for _, item := range obj.(*v1.DiskList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested disks.
func (c *FakeDisks) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchActionWithOptions(disksResource, c.ns, opts))

}

// Create takes the representation of a disk and creates it.  Returns the server's representation of the disk, and an error, if there is any.
func (c *FakeDisks) Create(ctx context.Context, disk *v1.Disk, opts metav1.CreateOptions) (result *v1.Disk, err error) {
	emptyResult := &v1.Disk{}
	obj, err := c.Fake.
		Invokes(testing.NewCreateActionWithOptions(disksResource, c.ns, disk, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1.Disk), err
}

// Update takes the representation of a disk and updates it. Returns the server's representation of the disk, and an error, if there is any.
func (c *FakeDisks) Update(ctx context.Context, disk *v1.Disk, opts metav1.UpdateOptions) (result *v1.Disk, err error) {
	emptyResult := &v1.Disk{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateActionWithOptions(disksResource, c.ns, disk, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1.Disk), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeDisks) UpdateStatus(ctx context.Context, disk *v1.Disk, opts metav1.UpdateOptions) (result *v1.Disk, err error) {
	emptyResult := &v1.Disk{}
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceActionWithOptions(disksResource, "status", c.ns, disk, opts), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1.Disk), err
}

// Delete takes name of the disk and deletes it. Returns an error if one occurs.
func (c *FakeDisks) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(disksResource, c.ns, name, opts), &v1.Disk{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeDisks) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	action := testing.NewDeleteCollectionActionWithOptions(disksResource, c.ns, opts, listOpts)

	_, err := c.Fake.Invokes(action, &v1.DiskList{})
	return err
}

// Patch applies the patch and returns the patched disk.
func (c *FakeDisks) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.Disk, err error) {
	emptyResult := &v1.Disk{}
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceActionWithOptions(disksResource, c.ns, name, pt, data, opts, subresources...), emptyResult)

	if obj == nil {
		return emptyResult, err
	}
	return obj.(*v1.Disk), err
}
