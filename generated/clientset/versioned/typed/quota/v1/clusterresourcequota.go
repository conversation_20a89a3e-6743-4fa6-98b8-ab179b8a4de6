/*
Copyright (C) 2024 The Poxiaoyun Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
	v1 "xiaoshiai.cn/core/apis/quota/v1"
	scheme "xiaoshiai.cn/core/generated/clientset/versioned/scheme"
)

// ClusterResourceQuotasGetter has a method to return a ClusterResourceQuotaInterface.
// A group's client should implement this interface.
type ClusterResourceQuotasGetter interface {
	ClusterResourceQuotas() ClusterResourceQuotaInterface
}

// ClusterResourceQuotaInterface has methods to work with ClusterResourceQuota resources.
type ClusterResourceQuotaInterface interface {
	Create(ctx context.Context, clusterResourceQuota *v1.ClusterResourceQuota, opts metav1.CreateOptions) (*v1.ClusterResourceQuota, error)
	Update(ctx context.Context, clusterResourceQuota *v1.ClusterResourceQuota, opts metav1.UpdateOptions) (*v1.ClusterResourceQuota, error)
	// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
	UpdateStatus(ctx context.Context, clusterResourceQuota *v1.ClusterResourceQuota, opts metav1.UpdateOptions) (*v1.ClusterResourceQuota, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.ClusterResourceQuota, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.ClusterResourceQuotaList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ClusterResourceQuota, err error)
	ClusterResourceQuotaExpansion
}

// clusterResourceQuotas implements ClusterResourceQuotaInterface
type clusterResourceQuotas struct {
	*gentype.ClientWithList[*v1.ClusterResourceQuota, *v1.ClusterResourceQuotaList]
}

// newClusterResourceQuotas returns a ClusterResourceQuotas
func newClusterResourceQuotas(c *QuotaV1Client) *clusterResourceQuotas {
	return &clusterResourceQuotas{
		gentype.NewClientWithList[*v1.ClusterResourceQuota, *v1.ClusterResourceQuotaList](
			"clusterresourcequotas",
			c.RESTClient(),
			scheme.ParameterCodec,
			"",
			func() *v1.ClusterResourceQuota { return &v1.ClusterResourceQuota{} },
			func() *v1.ClusterResourceQuotaList { return &v1.ClusterResourceQuotaList{} }),
	}
}
