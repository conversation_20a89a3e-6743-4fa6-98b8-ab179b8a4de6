package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	libconfig "xiaoshiai.cn/common/config"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/rest"
)

func main() {
	if err := NewISMCCmd().Execute(); err != nil {
		fmt.Println(err.Error())
		os.Exit(1)
	}
}

func NewISMCCmd() *cobra.Command {
	options := rest.DefaultOptions()
	cmd := &cobra.Command{
		Use:   "ismc",
		Short: "Start ismc api",
		RunE: func(cmd *cobra.Command, args []string) error {
			if err := libconfig.Parse(cmd.Flags()); err != nil {
				return err
			}
			ctx := libconfig.SetupSignalContext()
			ctx = log.NewContext(ctx, log.DefaultLogger)

			return rest.Run(ctx, options)
		},
	}
	libconfig.RegisterFlags(cmd.Flags(), "", options)
	return cmd
}
