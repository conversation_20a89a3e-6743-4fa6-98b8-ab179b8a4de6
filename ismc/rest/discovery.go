package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) GetDiscoveryVersion(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.GetVersion(ctx)
	})
}

func (a API) discoveryGroup() api.Group {
	return api.
		NewGroup("/discovery").
		Tag("DISCOVERY").
		Route(
			api.GET("/version").To(a.GetDiscoveryVersion).
				Operation("get discovery info").
				Response(common.Version{}),
		)
}
