package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	cloud "xiaoshiai.cn/core/ismc/common"
)

func (a API) GetSystemStatistics(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider cloud.Provider) (any, error) {
		return provider.GetSystemStatistics(ctx)
	})
}

func (a API) statisticsGroup() api.Group {
	return api.NewGroup("/statistics").
		Route(
			api.GET("").To(a.GetSystemStatistics).
				Operation("get system statistics").
				Response(cloud.SystemStatistics{}),
		)
}
