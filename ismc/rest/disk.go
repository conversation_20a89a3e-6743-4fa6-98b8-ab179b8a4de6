package rest

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListDiskClasses(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return ListDiskClass(r, provider)
	})
}

func ListDiskClass(r *http.Request, p common.Provider) (common.List[common.DiskClass], error) {
	return p.ListDiskClass(r.Context(), common.ListDiskClassOptions{
		ListOptions:   GetListOptions(r),
		ResourceGroup: api.Query(r, ResourceGroup, ""),
		Zone:          api.Query(r, "zone", ""),
	})
}

func (a API) GetDiskClass(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetDiskClass(ctx, id)
	})
}

func (a API) ListDisks(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListDisks(ctx, common.ListDiskOptions{
			ListOptions:    GetListOptions(r),
			Name:           api.Query(r, "name", ""),
			Zone:           api.Query(r, "zone", ""),
			ResourceGroup:  api.Query(r, ResourceGroup, ""),
			VirtualMachine: api.Query(r, "virtualMachine", ""),
		})
	})
}

func (a API) GetDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetDisk(ctx, id)
	})
}

func (a API) CreateDisk(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		disk := &common.Disk{}
		if err := api.Body(r, disk); err != nil {
			return nil, err
		}
		desc, err := provider.CreateDisk(ctx, disk, common.CreateDiskOptions{
			Wait: api.Query(r, "wait", false),
		})
		if err != nil {
			return nil, err
		}
		return desc, nil
	})
}

func (a API) UpdateDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		disk := &common.Disk{}
		if err := api.Body(r, disk); err != nil {
			return nil, err
		}
		if disk.ID != id {
			return nil, ErrIDNotEqual
		}
		if err := provider.UpdateDisk(ctx, disk); err != nil {
			return nil, err
		}
		return disk, nil
	})
}

func (a API) DeleteDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		if err := provider.DeleteDisk(ctx, id, common.DeleteDiskOptions{
			Wait: api.Query(r, "wait", false),
		}); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a API) ListDiskClusters(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListDiskClusters(ctx, common.ListDiskClusterOptions{
			ListOptions:  GetListOptions(r),
			Zone:         api.Query(r, "zone", ""),
			StorageClass: api.Query(r, "storageClass", ""),
		})
	})
}

func (a API) GetDiskCluster(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetDiskCluster(ctx, id)
	})
}

func (a API) DeleteDiskCluster(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		if err := provider.DeleteDiskCluster(ctx, id); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a API) ReInitalizeDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		if err := provider.ReInitalizeDisk(ctx, id); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

type ResizeDiskRequest struct {
	Size resource.Quantity `json:"size,omitempty"`
}

func (a API) ResizeDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		resizereq := &ResizeDiskRequest{}
		if err := api.Body(r, resizereq); err != nil {
			return nil, err
		}
		if err := provider.ResizeDisk(ctx, id, resizereq.Size, common.ResizeDiskOptions{Wait: api.Query(r, "wait", false)}); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a API) diskGroups() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			api.NewGroup("/diskclasses").
				Route(
					api.GET("").To(a.ListDiskClasses).
						Param(api.PageParams...).
						Param(api.QueryParam("zone", "").Optional()).
						Operation("list disk classes").
						Response(common.List[common.DiskClass]{}),

					api.GET("/{id}").
						To(a.GetDiskClass).
						Operation("get disk class").
						Response(common.DiskClass{}),
				),

			api.NewGroup("/diskclusters").
				Route(
					api.GET("").To(a.ListDiskClusters).
						Param(api.PageParams...).
						Param(api.QueryParam("zone", "").Optional()).
						Operation("list disk clusters").
						Response(common.List[common.DiskCluster]{}),

					api.GET("/{id}").
						To(a.GetDiskCluster).
						Operation("get disk cluster").
						Response(common.DiskCluster{}),

					api.DELETE("/{id}").
						To(a.DeleteDiskCluster).
						Operation("delete disk cluster"),
				),
			api.NewGroup("/disks").
				Route(
					api.GET("").To(a.ListDisks).
						Param(api.PageParams...).
						Param(api.QueryParam("zone", "").Optional()).
						Param(api.QueryParam("resourceGroup", "").Optional()).
						Param(api.QueryParam("name", "filter by disk name, exact match").Optional()).
						Param(api.QueryParam("virtualMachine", "filter disk used by vm").Optional()).
						Operation("list disks").
						Response(common.List[common.Disk]{}),

					api.GET("/{id}").
						To(a.GetDisk).
						Param(api.QueryParam("zone", "").Optional()).
						Param(api.QueryParam("resourceGroup", "").Optional()).
						Operation("get disk").
						Response(common.Disk{}),

					api.POST("").
						To(a.CreateDisk).
						Param(api.BodyParam("disk", common.Disk{})).
						Param(api.QueryParam("resourceGroup", "")).
						Operation("create disk").
						Response(common.Disk{}),

					api.PUT("/{id}").
						To(a.UpdateDisk).
						Param(api.BodyParam("disk", common.Disk{})).
						Param(api.QueryParam("resourceGroup", "")).
						Operation("update disk").
						Response(common.Disk{}),

					api.POST("/{id}:reinitialize").
						To(a.ReInitalizeDisk).
						Operation("reinitialize disk"),

					api.POST("/{id}:resize").
						To(a.ResizeDisk).
						Param(
							api.BodyParam("body", ResizeDiskRequest{}),
						).
						Operation("resize disk"),

					api.DELETE("/{id}").
						To(a.DeleteDisk).
						Operation("delete disk"),
				),
		)
}
