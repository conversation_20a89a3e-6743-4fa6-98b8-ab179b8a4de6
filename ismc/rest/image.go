package rest

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListImages(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return ListImages(r, provider, "")
	})
}

func ListImages(r *http.Request, p common.Provider, defaultPhase common.ImagePhase) (common.List[common.Image], error) {
	return p.ListImages(r.Context(), common.ListImageOptions{
		ListOptions:   GetListOptions(r),
		Name:          api.Query(r, "name", ""),
		ResourceGroup: api.Query(r, "resourceGroup", ""),
		Source:        common.ImageSource(api.Query(r, "source", "")),
		Phase:         common.ImagePhase(api.Query(r, "phase", defaultPhase)),
		Zone:          api.Query(r, "zone", ""),
	})
}

func (a API) GetImage(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetImage(ctx, id)
	})
}

func (a API) UpdateImage(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		image := common.Image{}
		if err := api.Body(r, &image); err != nil {
			return nil, err
		}
		if image.ID != id {
			return nil, ErrIDNotEqual
		}
		if err := provider.UpdateImage(ctx, &image); err != nil {
			return nil, err
		}
		return image, nil
	})
}

func (a API) DeleteImage(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		if err := provider.DeleteImage(ctx, id); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a API) ImportImage(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		mr, err := r.MultipartReader()
		if err != nil {
			return nil, err
		}
		image := common.Image{}

		configpart, err := mr.NextPart()
		if err != nil {
			return nil, err
		}
		if err := json.NewDecoder(configpart).Decode(&image); err != nil {
			return nil, err
		}
		filepart, err := mr.NextPart()
		if err != nil {
			return nil, err
		}
		filename := filepart.FileName()
		if filename == "" {
			return nil, errors.NewBadRequest("invalid request")
		}
		contentlen, _ := strconv.ParseInt(filepart.Header.Get("Content-Length"), 10, 64)
		file := common.FileContent{
			FileName: filename,
			// wrap the filepart reader to noop closer to avoid race on http2 close and defer close.
			Content:       io.NopCloser(filepart),
			ContentLength: contentlen,
			ContentType:   filepart.Header.Get("Content-Type"),
		}
		return provider.ImportImage(ctx, &image, file)
	})
}

func (a API) imageGroup() api.Group {
	return api.
		NewGroup("/images").
		Route(
			api.GET("").
				To(a.ListImages).
				Operation("list images").
				Param(api.PageParams...).
				Param(
					api.QueryParam("resourceGroup", "").Optional(),
					api.QueryParam("source", "").In(
						common.ImageSourcePublic,
						common.ImageSourceCustom,
						common.ImageSourceShared,
						common.ImageSourceCommunity,
					),
				).
				Response(common.List[common.Image]{}),

			api.GET("/{id}").
				To(a.GetImage).
				Param(api.QueryParam("resourceGroup", "").Optional()).
				Operation("get image").
				Response(common.Image{}),

			api.PUT("/{id}").
				To(a.UpdateImage).
				Param(api.QueryParam("resourceGroup", "").Optional()).
				Operation("update image").
				Param(api.BodyParam("image", common.Image{})),

			api.DELETE("/{id}").
				To(a.DeleteImage).
				Param(api.QueryParam("resourceGroup", "").Optional()).
				Operation("delete image"),

			api.POST("").
				To(a.ImportImage).
				Param(api.QueryParam("resourceGroup", "")).
				Operation("import image"),
		)
}
