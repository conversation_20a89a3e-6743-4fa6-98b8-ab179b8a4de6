package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListVirtualNetworks(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return ListVirtualNetworks(r, provider)
	})
}

func ListVirtualNetworks(r *http.Request, p common.Provider) (common.List[common.VirtualNetwork], error) {
	return p.ListVirtualNetworks(r.Context(), common.ListVirtualNetworkOptions{ListOptions: GetListOptions(r)})
}

func (a API) CreateVirtualNetwork(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		vn := &common.VirtualNetwork{}
		if err := api.Body(r, vn); err != nil {
			return nil, err
		}
		return provider.CreateVirtualNetwork(ctx, vn)
	})
}

func (a API) UpdateVirtualNetwork(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		vn := &common.VirtualNetwork{}
		if err := api.Body(r, vn); err != nil {
			return nil, err
		}
		if vn.ID == "" || vn.ID != id {
			return nil, errors.NewBadRequest("id in body is not equal to id in path")
		}
		if err := provider.UpdateVirtualNetwork(ctx, vn); err != nil {
			return nil, err
		}
		return vn, nil
	})
}

func (a API) GetVirtualNetwork(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetVirtualNetwork(ctx, id)
	})
}

func (a API) DeleteVirtualNetwork(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		if err := provider.DeleteVirtualNetwork(ctx, id); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a API) ListVirtualSwitches(w http.ResponseWriter, r *http.Request) {
	a.onNetwork(w, r, func(ctx context.Context, provider common.Provider, network string) (any, error) {
		return ListVirtualSubnetworks(r, provider, network)
	})
}

func ListVirtualSubnetworks(r *http.Request, p common.Provider, networkid string) (common.List[common.VirtualSubnetwork], error) {
	return p.ListVirtualSubnetwork(r.Context(), networkid, common.ListVirtualSubnetworkOptions{
		ListOptions:   GetListOptions(r),
		Zone:          api.Query(r, "zone", ""),
		ResourceGroup: api.Query(r, "resourceGroup", ""),
	})
}

func (a API) GetVirtualSwitch(w http.ResponseWriter, r *http.Request) {
	a.onNetwork(w, r, func(ctx context.Context, provider common.Provider, network string) (any, error) {
		id := api.Path(r, "subnetwork", "")
		if id == "" {
			return nil, errors.NewBadRequest("subnetwork is required")
		}
		return provider.GetVirtualSubnetwork(ctx, network, id)
	})
}

func (a API) CreateVirtualSwitch(w http.ResponseWriter, r *http.Request) {
	a.onNetwork(w, r, func(ctx context.Context, provider common.Provider, network string) (any, error) {
		vs := &common.VirtualSubnetwork{}
		if err := api.Body(r, vs); err != nil {
			return nil, err
		}
		desc, err := provider.CreateVirtualSubnetwork(ctx, network, vs)
		if err != nil {
			return nil, err
		}
		return desc, nil
	})
}

func (a API) UpdateVirtualSwitch(w http.ResponseWriter, r *http.Request) {
	a.onNetwork(w, r, func(ctx context.Context, provider common.Provider, network string) (any, error) {
		id := api.Path(r, "subnetwork", "")
		if id == "" {
			return nil, errors.NewBadRequest("subnetwork is required")
		}
		vs := &common.VirtualSubnetwork{}
		if err := api.Body(r, vs); err != nil {
			return nil, err
		}
		if vs.ID != id {
			return nil, ErrIDNotEqual
		}
		if err := provider.UpdateVirtualSubnetwork(ctx, network, vs); err != nil {
			return nil, err
		}
		return vs, nil
	})
}

func (a API) DeleteVirtualSwitch(w http.ResponseWriter, r *http.Request) {
	a.onNetwork(w, r, func(ctx context.Context, provider common.Provider, network string) (any, error) {
		id := api.Path(r, "subnetwork", "")
		if id == "" {
			return nil, errors.NewBadRequest("subnetwork is required")
		}
		if err := provider.DeleteVirtualSubnetwork(ctx, network, id); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a API) ListVirtualSwitchesAllocations(w http.ResponseWriter, r *http.Request) {
	a.onNetwork(w, r, func(ctx context.Context, provider common.Provider, network string) (any, error) {
		vs := api.Path(r, "subnetwork", "")
		if vs == "" {
			return nil, errors.NewBadRequest("subnetwork is required")
		}
		return provider.ListVirtualSubnetworkAllocations(ctx, network, vs, common.ListVirtualSwitchAllocationOptions{ListOptions: GetListOptions(r)})
	})
}

func (a API) onNetwork(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, provider common.Provider, network string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		provider, err := a.GetProvider(r)
		if err != nil {
			return nil, err
		}
		network := api.Path(r, "network", "")
		if network == "" {
			return nil, errors.NewBadRequest("network name is required")
		}
		return fn(ctx, provider, network)
	})
}

func (a API) networkGroup() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			api.NewGroup("/networks").
				Route(
					api.GET("").To(a.ListVirtualNetworks).
						Param(api.PageParams...).
						Operation("list networks").
						Response(common.List[common.VirtualNetwork]{}),

					api.POST("").To(a.CreateVirtualNetwork).
						Operation("create network").
						Param(
							api.QueryParam("resourceGroup", ""),
							api.BodyParam("network", common.VirtualNetwork{}),
						).
						Response(common.VirtualNetwork{}),

					api.PUT("/{id}").To(a.UpdateVirtualNetwork).
						Operation("update network").
						Param(api.BodyParam("network", common.VirtualNetwork{})).
						Response(common.VirtualNetwork{}),

					api.GET("/{id}").To(a.GetVirtualNetwork).
						Operation("get network").
						Response(common.VirtualNetwork{}),

					api.DELETE("/{id}").To(a.DeleteVirtualNetwork).
						Operation("delete network").
						Response(common.VirtualNetwork{}),
				),
			api.NewGroup("/networks/{network}/subnetworks").
				Route(
					api.GET("").To(a.ListVirtualSwitches).
						Param(api.PageParams...).
						Operation("list subnetworks").
						Response(common.List[common.VirtualSubnetwork]{}),

					api.POST("").To(a.CreateVirtualSwitch).
						Operation("create subnetwork").
						Param(
							api.QueryParam("resourceGroup", ""),
							api.BodyParam("switch", common.VirtualSubnetwork{}),
						).
						Response(common.VirtualSubnetwork{}),

					api.GET("/{subnetwork}").To(a.GetVirtualSwitch).
						Operation("get subnetwork").
						Response(common.VirtualSubnetwork{}),

					api.PUT("/{subnetwork}").To(a.UpdateVirtualSwitch).
						Operation("update subnetwork").
						Response(common.VirtualSubnetwork{}),

					api.DELETE("/{subnetwork}").To(a.DeleteVirtualSwitch).
						Operation("delete subnetwork"),

					api.GET("/{subnetwork}/allocations").To(a.ListVirtualSwitchesAllocations).
						Param(api.PageParams...).
						Operation("list allocations").
						Response(common.List[common.VirtualSubnetworkAllocation]{}),
				),
		)
}
