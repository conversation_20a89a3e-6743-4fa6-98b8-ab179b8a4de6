package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListVirtualMachines(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListVirtualMachines(ctx, common.ListVirtualMachineOptions{
			ListOptions:   GetListOptions(r),
			ResourceGroup: api.Query(r, "resourceGroup", ""),
			Zone:          api.Query(r, "zone", ""),
			Name:          api.Query(r, "name", ""),
			Tags:          api.Query(r, "tags", []string{}),
		})
	})
}

func (a API) CreateVirtualMachine(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		vm := &common.VirtualMachine{}
		if err := api.Body(r, vm); err != nil {
			return nil, err
		}
		opt := common.CreateVirtualMachineOptions{
			Wait: api.Query(r, "wait", false),
		}
		desc, err := provider.CreateVirtualMachine(ctx, vm, opt)
		if err != nil {
			return nil, err
		}
		return desc, nil
	})
}

func (a API) GetVirtualMachine(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetVirtualMachine(ctx, id)
	})
}

func (a API) UpdateVirtualMachine(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		vm := &common.VirtualMachine{}
		if err := api.Body(r, vm); err != nil {
			return nil, err
		}
		if vm.ID != id {
			return nil, errors.NewBadRequest("id in body does not match id in path")
		}
		if err := provider.UpdateVirtualMachine(ctx, vm); err != nil {
			return nil, err
		}
		return vm, nil
	})
}

func (a API) DeleteVirtualMachine(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return nil, provider.DeleteVirtualMachine(ctx, id, common.DeleteVirtualMachineOptions{
			Wait:      api.Query(r, "wait", false),
			Force:     api.Query(r, "force", false),
			WithDisks: api.Query(r, "withDisks", false),
		})
	})
}

func (a API) GetVirtualMachineConnectionInfo(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetVirtualMachineRemoteConnectionInfo(ctx, id)
	})
}

func (a API) VNC(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		id := api.Path(r, "id", "")
		if id == "" {
			return nil, errors.NewBadRequest("id is required")
		}
		reqpath := api.Path(r, "path", "")
		provider.VNCVirtualMachine(w, r, id, reqpath)
		return nil, nil
	})
}

type SetPower struct {
	Power common.PowerAction `json:"power,omitempty"`
	Hard  bool               `json:"hard,omitempty"`
}

func (a API) SetVirtualMachinePower(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return SetVirtualMachinePower(r, provider, id)
	})
}

func SetVirtualMachinePower(r *http.Request, p common.Provider, id string) (any, error) {
	req := &SetPower{}
	if err := api.Body(r, req); err != nil {
		return nil, err
	}
	return nil, p.SetVirtualMachinePower(r.Context(), id, req.Power, common.VirtualMachinePowerOptions{Hard: req.Hard})
}

func (a API) ListVirtualMachineDisks(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.ListVirtualMachineDisks(ctx, id, common.ListVirtualMachineDiskOptions{
			ListOptions: GetListOptions(r),
		})
	})
}

func (a API) AttachVirtualMachineDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		disk := common.AttachVirtualMachineDisk{}
		if err := api.Body(r, &disk); err != nil {
			return nil, err
		}
		return nil, provider.AttachVirtualMachineDisk(ctx, id, common.AttachVirtualMachineDiskOptions{
			Disk: disk,
			Wait: api.Query(r, "wait", false),
		})
	})
}

func (a API) DetachVirtualMachineDisk(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		diskid := api.Path(r, "diskid", "")
		return nil, provider.DetachVirtualMachineDisk(ctx, id, diskid, common.DetachVirtualMachineDiskOptions{
			Wait: api.Query(r, "wait", false),
		})
	})
}

func (a API) ListVirtualMachineNetworkInterfaces(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.ListVirtualMachineNetworkInterfaces(ctx, id, common.ListVirtualMachineNetworkOptions{
			ListOptions: GetListOptions(r),
		})
	})
}

func (a API) AttachVirtualMachineNetworkInterface(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		nic := common.VirtualMachineInterfaceRef{}
		if err := api.Body(r, &nic); err != nil {
			return nil, err
		}
		return nil, provider.AttachVirtualMachineNetworkInterface(ctx, id,
			common.AttachVirtualMachineNetworkInterfaceOptions{
				Interface: nic,
				Wait:      api.Query(r, "wait", false),
			})
	})
}

func (a API) DetachVirtualMachineNetworkInterface(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		nicid := api.Path(r, "interfaceid", "")
		return nil, provider.DetachVirtualMachineNetworkInterface(ctx, id, nicid, common.DetachVirtualMachineNetworkInterfaceOptions{
			Wait: api.Query(r, "wait", false),
		})
	})
}

func (a API) ChangeVirtualMachineInstanceType(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		instancetype := common.VirtualMachineInstanceTypeRef{}
		if err := api.Body(r, &instancetype); err != nil {
			return nil, err
		}
		return nil, provider.ChangeVirtualMachineInstanceType(ctx, id,
			common.ChangeVirtualMachineInstanceTypeOptions{
				InstanceType: instancetype,
				Force:        api.Query(r, "force", false),
				Wait:         api.Query(r, "wait", false),
			})
	})
}

func (a API) ChangeVirtualMachineCloudInit(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		cloudinit := common.CloudInit{}
		if err := api.Body(r, &cloudinit); err != nil {
			return nil, err
		}
		return nil, provider.ChangeVirtualMachineCloudInit(ctx, id, common.ResetVirtualMachineCloudInitOptions{
			CloudInit: cloudinit,
			Wait:      api.Query(r, "wait", false),
		})
	})
}

func (a API) ReInstallVirtualMachine(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		options := common.ReInstallVirtualMachineOptions{}
		if err := api.Body(r, &options); err != nil {
			return nil, err
		}
		return nil, provider.ReInstallVirtualMachine(ctx, id, options)
	})
}

func (a API) ChangeVirtualMachineImage(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		options := common.ChangeVirtualMachineImageOptions{}
		if err := api.Body(r, &options); err != nil {
			return nil, err
		}
		return nil, provider.ChangeVirtualMachineImage(ctx, id, options)
	})
}

func (a API) ResetVirtualMachinePassword(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		options := common.ResetVirtualMachinePasswordOptions{}
		if err := api.Body(r, &options); err != nil {
			return nil, err
		}
		return nil, provider.ResetVirtualMachinePassword(ctx, id, options)
	})
}

func (a API) vmGroup() api.Group {
	return api.
		NewGroup("/virtualmachines").
		Route(
			api.GET("").To(a.ListVirtualMachines).
				Param(api.PageParams...).
				Param(api.QueryParam("resourceGroup", "").Optional()).
				Param(api.QueryParam("zone", "").Optional()).
				Param(api.QueryParam("name", "filter by name extarctly").Optional()).
				Operation("list virtual machines").
				Response(common.List[common.VirtualMachine]{}),

			api.POST("").To(a.CreateVirtualMachine).
				Param(api.BodyParam("virtualMachine", common.VirtualMachine{})).
				Operation("create virtual machine").
				Response(common.VirtualMachine{}),

			api.GET("/{id}").To(a.GetVirtualMachine).
				Operation("get virtual machine").
				Response(common.VirtualMachine{}),

			api.GET("/{id}/remoteconnectioninfo").To(a.GetVirtualMachineConnectionInfo).
				Operation("get virtual machine connection info").
				Response(common.RemoteConnectionInfo{}),

			api.PUT("/{id}").To(a.UpdateVirtualMachine).
				Param(api.BodyParam("virtualMachine", common.VirtualMachine{})).
				Operation("update virtual machine").
				Response(common.VirtualMachine{}),

			api.DELETE("/{id}").
				To(a.DeleteVirtualMachine).
				Param(api.QueryParam("wait", "wait operation finished").Format("bool").Optional()).
				Param(api.QueryParam("force", "force delete").Format("bool").Optional()).
				Param(api.QueryParam("withDisks", "delete disks with vm").Format("bool").Optional()).
				Operation("delete virtual machine"),

			api.GET("/{id}/vnc").
				To(a.VNC).
				Operation("redirect to vnc/"),

			api.POST("/{id}/power").
				To(a.SetVirtualMachinePower).
				Param(api.BodyParam("power", SetPower{}).Optional()).
				Operation("set virtual machine power"),

			api.Any("/{id}/vnc/{path}*").
				To(a.VNC).
				Operation("get virtual machine").
				Response(common.VirtualMachine{}),

			api.GET("/{id}/disks").
				To(a.ListVirtualMachineDisks).
				Param(api.PageParams...).
				Operation("list virtual machine disks").
				Response(common.List[common.Disk]{}),

			api.POST("/{id}/disks").
				To(a.AttachVirtualMachineDisk).
				Operation("attach disk to virtual machine").
				Param(
					api.BodyParam("disk", common.AttachVirtualMachineDisk{}),
					api.QueryParam("wait", "wait operation finished").Format("bool").Optional(),
				),

			api.DELETE("/{id}/disks/{diskid}").
				To(a.DetachVirtualMachineDisk).
				Operation("detach disk from virtual machine").
				Param(
					api.QueryParam("wait", "wait operation finished").Format("bool").Optional(),
				),

			api.GET("/{id}/interfaces").
				To(a.ListVirtualMachineNetworkInterfaces).
				Param(api.PageParams...).
				Operation("list virtual machine network interfaces").
				Response(common.List[common.NetworkInterface]{}),

			api.POST("/{id}/interfaces").
				To(a.AttachVirtualMachineNetworkInterface).
				Operation("attach network interface to virtual machine").
				Param(
					api.BodyParam("interface", common.VirtualMachineInterfaceRef{}),
					api.QueryParam("wait", "wait operation finished").Format("bool").Optional(),
				),

			api.DELETE("/{id}/interfaces/{interfaceid}").
				To(a.DetachVirtualMachineNetworkInterface).
				Operation("detach network interface from virtual machine").
				Param(
					api.QueryParam("wait", "wait operation finished").Format("bool").Optional(),
				),

			api.PUT("/{id}/instancetype").
				To(a.ChangeVirtualMachineInstanceType).
				Operation("change virtual machine instance type").
				Param(
					api.BodyParam("instancetype", common.VirtualMachineInstanceTypeRef{}),
				),

			api.PUT("/{id}/cloudinit").
				To(a.ChangeVirtualMachineCloudInit).
				Operation("reset virtual machine cloud init").
				Param(
					api.BodyParam("cloudinit", common.CloudInit{}),
					api.QueryParam("wait", "wait operation finished").Format("bool").Optional(),
				),

			api.POST("/{id}:reinstall").
				To(a.ReInstallVirtualMachine).
				Operation("reinstall virtual machine").
				Param(
					api.BodyParam("options", common.ReInstallVirtualMachineOptions{}),
				),

			api.POST("/{id}:change-image").
				To(a.ChangeVirtualMachineImage).
				Operation("change virtual machine image").
				Param(
					api.BodyParam("options", common.ChangeVirtualMachineImageOptions{}),
				),

			api.POST("/{id}:reset-password").
				To(a.ResetVirtualMachinePassword).
				Operation("reset virtual machine password").
				Param(
					api.BodyParam("password", common.ResetVirtualMachinePasswordOptions{}),
				),
		)
}
