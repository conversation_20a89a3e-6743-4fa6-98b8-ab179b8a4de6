package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (p API) ListOrWatchTasks(w http.ResponseWriter, r *http.Request) {
	p.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		if watch := api.Query(r, "watch", false); !watch {
			return provider.ListTasks(ctx, common.ListTaskOptions{
				ListOptions:  GetListOptions(r),
				ResourceType: api.Query(r, "resourceType", ""),
			})
		}
		log := log.FromContext(ctx)

		encoder, err := api.NewStreamEncoderFromRequest[common.WatchEvent[common.Task]](w, r)
		if err != nil {
			return nil, err
		}
		defer encoder.Close()

		onEvent := func(ctx context.Context, event common.WatchEvent[common.Task]) error {
			if err := encoder.Encode("tasks", event); err != nil {
				log.Error(err, "failed to write event through sse")
				return err
			}
			return nil
		}
		options := common.WactchTaskOptions{
			ResourceType: api.Query(r, "resourceType", ""),
			Size:         api.Query(r, "size", 0),
			Sort:         api.Query(r, "sort", ""),
			Phase:        common.TaskPhase(api.Query(r, "phase", "")),
		}
		if err := provider.WactchTasks(ctx, onEvent, options); err != nil {
			log.Error(err, "failed to watch tasks")
			encoder.SendError(err)
			return nil, err
		}
		return nil, nil
	})
}

func (p API) GetTask(w http.ResponseWriter, r *http.Request) {
	p.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetTask(ctx, id)
	})
}

func (p API) RemoveTask(w http.ResponseWriter, r *http.Request) {
	p.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return nil, provider.RemoveTask(ctx, id)
	})
}

func (p API) RestartTask(w http.ResponseWriter, r *http.Request) {
	p.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return nil, provider.RestartTask(ctx, id)
	})
}

func (p API) StopTask(w http.ResponseWriter, r *http.Request) {
	p.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return nil, provider.StopTask(ctx, id)
	})
}

func (p API) taskGroup() api.Group {
	return api.
		NewGroup("/tasks").
		Route(
			api.GET("").
				Operation("list tasks").
				Param(PageParams...).
				Param(api.QueryParam("resourceType", "resource type").Optional()).
				Param(api.QueryParam("phase", "task phase").Optional()).
				Param(api.QueryParam("watch", "watch tasks").Optional()).
				Param(api.QueryParam("protocol", "connect protocol").In("sse", "websocket").Optional()).
				Response(common.List[common.Task]{}).
				To(p.ListOrWatchTasks),

			api.GET("/{id}").
				Operation("get task").
				Response(common.Task{}).
				To(p.GetTask),

			api.DELETE("/{id}").
				Operation("remove task").
				To(p.RemoveTask),

			api.POST("/{id}:restart").
				Operation("restart task").
				To(p.RestartTask),

			api.POST("/{id}:stop").
				Operation("stop task").
				To(p.StopTask),
		)
}
