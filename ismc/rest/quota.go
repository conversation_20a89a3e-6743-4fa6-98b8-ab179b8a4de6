package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) GetQuota(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.GetQuota(ctx)
	})
}

func (a API) quotaGroup() api.Group {
	return api.
		NewGroup("/quota").
		Route(
			api.GET("").To(a.GetQuota).
				Operation("get quota").
				Response(common.Quota{}),
		)
}
