package rest

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers"
)

var ErrIDNotEqual = errors.NewBadRequest("id in body is not equal to id in path")

const ResourceGroup = "resourceGroup"

const CloudPathPrefix = "/v1/cloud"

func Run(ctx context.Context, opt *Options) error {
	provider, err := providers.NewProvider(ctx, opt.Provider)
	if err != nil {
		return fmt.Errorf("init provider: %w", err)
	}
	return NewAPI(provider).Run(ctx, opt.Listen)
}

var PageParams = []api.Param{
	api.QueryParam("limit", "size limit").Optional(),
	api.QueryParam("size", "size limit").Optional(),
	api.QueryParam("page", "page number").Optional(),
	api.QueryParam("search", "Search string for searching").Optional(),
	api.QueryParam("sort", "Sort string for sorting").In("name", "name-", "time", "time-").Optional(),
	api.QueryParam("continue", "Continue token for pagination").Optional(),
}

func GetListOptions(r *http.Request) common.ListOptions {
	return common.ListOptions{
		Page:     api.Query(r, "page", 0), // 0 means no pagination
		Size:     api.Query(r, "size", 0), // 0 means no limit
		Search:   api.Query(r, "search", ""),
		Sort:     api.Query(r, "sort", ""),
		Continue: api.Query(r, "continue", ""),
	}
}

func NewAPI(provider common.Provider) *API {
	return &API{
		GetProvider: func(r *http.Request) (common.Provider, error) {
			return provider, nil
		},
	}
}

type API struct {
	GetProvider func(r *http.Request) (common.Provider, error)
}

func (a API) onID(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, provider common.Provider, id string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		id := api.Path(r, "id", "")
		if id == "" {
			return nil, errors.NewBadRequest("id is required")
		}
		provider, err := a.GetProvider(r)
		if err != nil {
			return nil, err
		}
		return fn(ctx, provider, id)
	})
}

func (a API) on(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, provider common.Provider) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		provider, err := a.GetProvider(r)
		if err != nil {
			return nil, err
		}
		return fn(ctx, provider)
	})
}

func (a API) CloudGroup(prefix string) api.Group {
	return api.
		NewGroup(prefix).
		Tag("Cloud").
		SubGroup(
			a.zonesGroup(),
			a.vmGroup(),
			a.diskGroups(),
			a.networkGroup(),
			a.imageGroup(),
			a.instanceTypeGroup(),
			a.discoveryGroup(),
			a.quotaGroup(),
			a.statisticsGroup(),
			a.resourceGroupGroup(),
			a.metricsGroup(),
			a.securityGroupGroup(),
			a.taskGroup(),
			a.hostGroup(),
		)
}

func (a API) Group() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			a.CloudGroup(CloudPathPrefix),
		)
}

func (a API) Run(ctx context.Context, listen string) error {
	return api.New().
		Plugin(
			api.HealthCheckPlugin{},
			api.NewAPIDocPlugin("/docs", nil),
		).
		Filter(
			api.LoggingFilter(log.FromContext(ctx)),
			api.NewCORSFilter(),
		).
		Group(
			a.Group(),
		).
		Serve(ctx, listen)
}
