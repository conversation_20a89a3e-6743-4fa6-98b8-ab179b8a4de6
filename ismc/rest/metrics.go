package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListVirtualMachineMetrics(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListVirtualMachineMetrics(ctx, common.ListVirtualMachineMetricsOptions{
			ListOptions:   GetListOptions(r),
			ResourceGroup: api.Query(r, "resourceGroup", ""),
			Zone:          api.Query(r, "zone", ""),
		})
	})
}

func (a API) GetVirtualMachineMetrics(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetVirtualMachineMetrics(ctx, id)
	})
}

func (a API) ListZoneMetrics(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListZoneMetrics(ctx)
	})
}

func (a API) GetZoneMetrics(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetZoneMetrics(ctx, id)
	})
}

func (a API) ListDiskMetrics(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListDiskMetrics(ctx)
	})
}

func (a API) GetDiskMetrics(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetDiskMetrics(ctx, id)
	})
}

func (a API) ListHostMetrics(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListHostMetrics(ctx)
	})
}

func (a API) GetHostMetrics(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetHostMetrics(ctx, id)
	})
}

func (a API) metricsGroup() api.Group {
	return api.NewGroup("").
		Route(
			api.GET("/virtualmachine-metrics").
				Response(common.List[common.VirtualMachineMetrics]{}).
				To(a.ListVirtualMachineMetrics),

			api.GET("/virtualmachine-metrics/{id}").
				Response(common.VirtualMachineMetrics{}).
				To(a.GetVirtualMachineMetrics),

			api.GET("/zone-metrics").
				Response(common.List[common.ZoneMetrics]{}).
				To(a.ListZoneMetrics),

			api.GET("/zone-metrics/{id}").
				Response(common.ZoneMetrics{}).
				To(a.GetZoneMetrics),

			api.GET("/disk-metrics").
				Response(common.List[common.DiskMetrics]{}).
				To(a.ListDiskMetrics),

			api.GET("/disk-metrics/{id}").
				Response(common.DiskMetrics{}).
				To(a.GetDiskMetrics),

			api.GET("/host-metrics").
				Response(common.List[common.HostMetrics]{}).
				To(a.ListHostMetrics),

			api.GET("/host-metrics/{id}").
				Response(common.HostMetrics{}).
				To(a.GetHostMetrics),
		)
}
