package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListResourceGroups(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		return provider.ListResourceGroups(ctx, common.ListResourceGroupOptions{
			ListOptions: GetListOptions(r),
			Name:        api.Query(r, "name", ""),
		})
	})
}

func (a API) GetResourceGroup(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetResourceGroup(ctx, id)
	})
}

func (a API) DeleteResourceGroup(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return nil, provider.DeleteResourceGroup(ctx, id)
	})
}

func (a API) CreateResourceGroup(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		resourceGroup := &common.ResourceGroup{}
		if err := api.Body(r, resourceGroup); err != nil {
			return nil, err
		}
		return provider.CreateResourceGroup(ctx, resourceGroup)
	})
}

func (a API) UpdateResourceGroup(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		resourceGroup := &common.ResourceGroup{}
		if err := api.Body(r, resourceGroup); err != nil {
			return nil, err
		}
		if resourceGroup.ID != id {
			return nil, ErrIDNotEqual
		}
		return resourceGroup, nil
	})
}

func (a API) resourceGroupGroup() api.Group {
	return api.NewGroup("/resourcegroups").
		Route(
			api.GET("").
				To(a.ListResourceGroups).
				Operation("list resource groups").
				Param(api.PageParams...).
				Param(api.QueryParam("name", "filter by name(extraly match)").Optional()).
				Response(common.List[common.ResourceGroup]{}),

			api.POST("").
				To(a.CreateResourceGroup).
				Operation("create resource group").
				Param(api.BodyParam("resourceGroup", common.ResourceGroup{})).
				Response(common.ResourceGroup{}),

			api.GET("/{id}").
				To(a.GetResourceGroup).
				Operation("get resource group").
				Response(common.ResourceGroup{}),

			api.DELETE("/{id}").
				To(a.DeleteResourceGroup).
				Operation("delete resource group"),

			api.PUT("/{id}").
				To(a.UpdateResourceGroup).
				Param(api.BodyParam("resourceGroup", common.ResourceGroup{})).
				Operation("update resource group").
				Response(common.ResourceGroup{}),
		)
}
