package rest

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

func (a API) ListHosts(w http.ResponseWriter, r *http.Request) {
	a.on(w, r, func(ctx context.Context, provider common.Provider) (any, error) {
		listOption := GetListOptions(r)
		return provider.ListHosts(ctx, common.ListHostOptions{
			ListOptions: listOption,
		})
	})
}

func (a API) GetHost(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		return provider.GetHost(ctx, id)
	})
}

func (a API) UpdateHost(w http.ResponseWriter, r *http.Request) {
	a.onID(w, r, func(ctx context.Context, provider common.Provider, id string) (any, error) {
		host := &common.Host{}
		if err := api.Body(r, host); err != nil {
			return nil, err
		}
		if host.ID != id {
			return nil, ErrIDNotEqual
		}
		return nil, provider.UpdateHost(ctx, id, host)
	})
}

func (a API) hostGroup() api.Group {
	return api.
		NewGroup("/hosts").
		Route(
			api.GET("").To(a.ListHosts).
				Param(api.PageParams...).
				Operation("list hosts").
				Response(common.List[common.Host]{}),

			api.GET("/{id}").To(a.GetHost).
				Operation("get host").
				Response(common.Host{}),

			api.PUT("/{id}").To(a.UpdateHost).
				Param(
					api.BodyParam("host", common.Host{}),
				).
				Operation("update host").
				Response(nil),
		)
}
