package vmoci

import (
	"context"

	"github.com/regclient/regclient/types/ref"
)

type ImagerVersion struct {
	Name string
}

func (c *Client) ListVersions(ctx context.Context, image string) ([]ImagerVersion, error) {
	imgref, err := ref.New(image)
	if err != nil {
		return nil, err
	}
	tags, err := c.regcli.TagList(ctx, imgref)
	if err != nil {
		return nil, err
	}
	var versions []ImagerVersion
	for _, tag := range tags.Tags {
		versions = append(versions, ImagerVersion{Name: tag})
	}
	return versions, nil
}
