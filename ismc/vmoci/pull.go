package vmoci

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"path/filepath"
	"slices"

	"github.com/regclient/regclient/types/descriptor"
	"github.com/regclient/regclient/types/manifest"
	"github.com/regclient/regclient/types/mediatype"
	"github.com/regclient/regclient/types/ref"
)

type (
	PullLayerFunc        func(ctx context.Context, desc descriptor.Descriptor, filename string, layer io.Reader) error
	CheckLayerExistsFunc func(ctx context.Context, desc descriptor.Descriptor, filename string) (bool, error)
)

type PullImageOptions struct {
	Formats            []ImageFormat
	PullConfigCallback PullLayerFunc
	CheckLocalExists   func(ctx context.Context, desc descriptor.Descriptor, filename string) (bool, error)
	PullLayerCallback  PullLayerFunc
}

func (c *Client) PullImage(ctx context.Context, image string, options PullImageOptions) error {
	imgref, err := ref.New(image)
	if err != nil {
		return err
	}
	mani, err := c.regcli.ManifestGet(ctx, imgref)
	if err != nil {
		return err
	}
	index, ok := mani.(manifest.Indexer)
	if !ok {
		return c.pull(ctx, imgref, mani, options)
	}
	manifests, err := index.GetManifestList()
	if err != nil {
		return err
	}
	for _, d := range manifests {
		if d.MediaType != mediatype.OCI1Manifest {
			continue
		}
		if len(options.Formats) != 0 {
			if d.Annotations == nil || !slices.Contains(options.Formats, ImageFormat(d.Annotations[AnnotationDiskFormat])) {
				continue
			}
		}
		maniref := imgref
		maniref.Digest = d.Digest.String()
		thismanifest, err := c.regcli.ManifestGet(ctx, maniref)
		if err != nil {
			return err
		}
		return c.pull(ctx, imgref, thismanifest, options)
	}
	return fmt.Errorf("no suitable manifest found")
}

func (c *Client) pull(ctx context.Context, reference ref.Ref, mani manifest.Manifest, opt PullImageOptions) error {
	imager, ok := mani.(manifest.Imager)
	if !ok {
		return fmt.Errorf("not a imager manifest: %s", mani.GetDescriptor().MediaType)
	}
	if opt.PullConfigCallback != nil {
		desc, err := imager.GetConfig()
		if err != nil {
			return err
		}
		filename := "config.json"
		if err := c.pullLayerFile(ctx, reference, desc, filename, opt.CheckLocalExists, opt.PullConfigCallback); err != nil {
			return err
		}
	}
	layers, err := imager.GetLayers()
	if err != nil {
		return err
	}
	for idx, layer := range layers {
		filename := fmt.Sprintf("disk-%d", idx)
		if layer.Annotations != nil && layer.Annotations[AnnotationDiskFileName] != "" {
			filename = layer.Annotations[AnnotationDiskFileName]
		}
		if err := c.pullLayerFile(ctx, reference, layer, filename, opt.CheckLocalExists, opt.PullLayerCallback); err != nil {
			return err
		}
	}
	return nil
}

func (c *Client) pullLayerFile(ctx context.Context, reference ref.Ref, d descriptor.Descriptor, filename string, existsFunc CheckLayerExistsFunc, layerFunc PullLayerFunc) error {
	// check local exists
	if existsFunc != nil {
		exists, err := existsFunc(ctx, d, filename)
		if err != nil {
			return err
		}
		if exists {
			return nil
		}
	}
	if layerFunc == nil {
		return nil
	}
	br, err := c.regcli.BlobGet(ctx, reference, d)
	if err != nil {
		return err
	}
	defer br.Close()
	return layerFunc(ctx, br.GetDescriptor(), filename, br)
}

type FileContent struct {
	Filename      string
	ContentLength int64
	ContentType   string
	GetContent    func(ctx context.Context) (io.ReadCloser, error)
}

type PullImageFileOptions struct {
	// filter by supported image formats, such as "ova" "raw"
	// it returns the first suitable image
	Formats []ImageFormat
	// filter by architecture, such as "amd64" "arm64"
	Arch string
}

type ImageConfigWithContent struct {
	Config VirtualMachineConfig
	File   FileContent
}

func (c *Client) PullImageFile(ctx context.Context, image string, options PullImageFileOptions) (*ImageConfigWithContent, error) {
	imgref, err := ref.New(image)
	if err != nil {
		return nil, err
	}
	mani, err := c.regcli.ManifestGet(ctx, imgref)
	if err != nil {
		return nil, err
	}
	index, ok := mani.(manifest.Indexer)
	if !ok {
		return c.pullImageFile(ctx, imgref, mani)
	}
	manifests, err := index.GetManifestList()
	if err != nil {
		return nil, err
	}
	for _, d := range manifests {
		if d.MediaType != mediatype.OCI1Manifest {
			continue
		}
		if len(options.Formats) != 0 {
			if d.Annotations == nil || !slices.Contains(options.Formats, ImageFormat(d.Annotations[AnnotationDiskFormat])) {
				continue
			}
		}
		if options.Arch != "" && (d.Platform == nil || options.Arch != d.Platform.Architecture) {
			continue
		}
		maniref := imgref
		maniref.Digest = d.Digest.String()
		thismanifest, err := c.regcli.ManifestGet(ctx, maniref)
		if err != nil {
			return nil, err
		}
		return c.pullImageFile(ctx, imgref, thismanifest)
	}
	return nil, fmt.Errorf("no suitable image format found")
}

func (c *Client) pullImageFile(ctx context.Context, reference ref.Ref, mani manifest.Manifest) (*ImageConfigWithContent, error) {
	imager, ok := mani.(manifest.Imager)
	if !ok {
		return nil, fmt.Errorf("not a imager manifest: %s", mani.GetDescriptor().MediaType)
	}
	desc, err := imager.GetConfig()
	if err != nil {
		return nil, err
	}
	br, err := c.regcli.BlobGet(ctx, reference, desc)
	if err != nil {
		return nil, err
	}
	imgconfig := VirtualMachineConfig{}
	if err := json.NewDecoder(br).Decode(&imgconfig); err != nil {
		return nil, err
	}
	layers, err := imager.GetLayers()
	if err != nil {
		return nil, err
	}
	for idx, d := range layers {
		filename := fmt.Sprintf("disk-%d", idx)
		if d.Annotations != nil && d.Annotations[AnnotationDiskFileName] != "" {
			filename = d.Annotations[AnnotationDiskFileName]
		}
		file := c.layerAsImportContent(ctx, reference, d, filename)
		return &ImageConfigWithContent{Config: imgconfig, File: file}, nil
	}
	return nil, fmt.Errorf("no layers found")
}

func (c *Client) layerAsImportContent(_ context.Context, reference ref.Ref, d descriptor.Descriptor, filename string) FileContent {
	return FileContent{
		Filename:      filename,
		ContentLength: d.Size,
		ContentType:   mime.TypeByExtension(filepath.Ext(filename)),
		GetContent: func(ctx context.Context) (io.ReadCloser, error) {
			return c.regcli.BlobGet(ctx, reference, d)
		},
	}
}
