package vmoci

import (
	"context"
	"fmt"
	"io"

	"github.com/regclient/regclient/types/ref"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	cloud "xiaoshiai.cn/core/ismc/common"
)

type ImportImageInfo struct {
	ImageName     string
	ResourceGroup string
	Zone          string
	// for private registry
	Image    string
	Username string
	Password string
}

func ImportImageFromRegistryToCloud(ctx context.Context, imginfo ImportImageInfo, imageformats []ImageFormat, provider cloud.Provider) (*cloud.Descripter, error) {
	if imginfo.ImageName == "" || imginfo.Image == "" {
		return nil, fmt.Errorf("invalid image info: %v", imginfo)
	}
	imgref, err := ref.New(imginfo.Image)
	if err != nil {
		return nil, err
	}
	cli, err := NewClient(Options{Auths: []AuthOptions{
		{Hostname: imgref.Registry, Username: imginfo.Username, Password: imginfo.Password},
	}})
	if err != nil {
		return nil, err
	}

	mainfile, err := cli.PullImageFile(ctx, imginfo.Image, PullImageFileOptions{Formats: imageformats})
	if err != nil {
		return nil, fmt.Errorf("pull image %s failed: %w", imginfo.Image, err)
	}
	newimg := &cloud.Image{
		Descripter: cloud.Descripter{
			Name:        imginfo.ImageName,
			Annotations: mainfile.Config.Annotations,
			Labels:      mainfile.Config.Labels,
		},
		BootMode: func() cloud.BootMode {
			for _, bootmode := range mainfile.Config.BootModes {
				return cloud.BootMode(bootmode)
			}
			return cloud.BootModeBIOS
		}(),
		OS:           cloud.OSType(mainfile.Config.OS),
		OSVersion:    mainfile.Config.OSRelease,
		Features:     mainfile.Config.Features,
		Architecture: cloud.Architecture(mainfile.Config.Architecture),
	}
	content, err := mainfile.File.GetContent(ctx)
	if err != nil {
		return nil, fmt.Errorf("get image content failed: %w", err)
	}
	defer content.Close()

	log := log.FromContext(ctx).WithValues("image", imginfo.Image)

	progresscontent := io.NopCloser(common.NewSimpleProgressReader(content, mainfile.File.ContentLength, log))

	newimgfile := cloud.FileContent{
		FileName:      mainfile.File.Filename,
		Content:       progresscontent,
		ContentType:   mainfile.File.ContentType,
		ContentLength: mainfile.File.ContentLength,
	}
	desc, err := provider.ImportImage(ctx, newimg, newimgfile)
	if err != nil {
		return nil, fmt.Errorf("import image %s failed: %w", imginfo.Image, err)
	}
	return desc, nil
}
