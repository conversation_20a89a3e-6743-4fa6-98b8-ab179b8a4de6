package vmoci

import (
	"time"

	"github.com/regclient/regclient"
	"github.com/regclient/regclient/config"
	"github.com/regclient/regclient/scheme/reg"
)

const PullPushConcurrency = 3

const (
	ConfigMediaType = "application/vnd.virtualmachine-disk.config.v1+json"

	AnnotationDiskFormat   = "cn.xiaoshiai.ismc.disk.format"
	AnnotationDiskFileName = "cn.xiaoshiai.ismc.disk.filename"
	AnnotationCreated      = "org.opencontainers.image.created"
)

type ImageFormat string

const (
	// vmware
	ImageFormatVMDK ImageFormat = "vmdk"
	// qemu
	ImageFormatQCOW2 ImageFormat = "qcow2"
	// img
	ImageFormatIMG ImageFormat = "img"
	// cloud image
	ImageFormatOVA ImageFormat = "ova"
	// hyperv
	ImageFormatVHD  ImageFormat = "vhd"
	ImageFormatVHDX ImageFormat = "vhdx"
	// raw
	ImageFormatRaw ImageFormat = "raw"
	// iso
	ImageFormatISO ImageFormat = "iso"
)

var KnownExtFormatMediaType = map[ImageFormat]string{
	ImageFormatVMDK:  "application/vnd.virtual-machine.disk.v1.vmdk",
	ImageFormatQCOW2: "application/vnd.virtual-machine.disk.v1.qcow2",
	ImageFormatIMG:   "application/vnd.virtual-machine.disk.v1.img",
	ImageFormatOVA:   "application/vnd.virtual-machine.disk.v1.ova",
	ImageFormatVHD:   "application/vnd.virtual-machine.disk.v1.vhd",
	ImageFormatVHDX:  "application/vnd.virtual-machine.disk.v1.vhdx",
	ImageFormatRaw:   "application/vnd.virtual-machine.disk.v1.raw",
	ImageFormatISO:   "application/vnd.virtual-machine.disk.v1.iso",
}

type VirtualMachineConfig struct {
	Annotations  map[string]string `json:"annotations,omitempty"`
	Labels       map[string]string `json:"labels,omitempty"`
	OS           string            `json:"os,omitempty"`
	OSRelease    string            `json:"osRelease,omitempty"`
	Features     []string          `json:"features,omitempty"`
	BootModes    []string          `json:"bootModes,omitempty"` // e.g. bios, uefi
	Architecture string            `json:"architecture,omitempty"`
	Created      time.Time         `json:"created,omitempty"`
	Ports        []Port            `json:"ports,omitempty"`
	Volumes      []Volume          `json:"volumes,omitempty"`
}

type Port struct {
	Name     string `json:"name,omitempty"`
	Port     int    `json:"port,omitempty"`
	Protocol string `json:"protocol,omitempty"`
}

type Volume struct {
	Name string `json:"name,omitempty"`
	Path string `json:"path,omitempty"`
}

type Client struct {
	regcli *regclient.RegClient
}

type Options struct {
	// Auths is a map of registry to auth options
	Auths []AuthOptions
}

type AuthOptions struct {
	Hostname string
	Username string
	Password string
}

func NewClient(opt Options) (*Client, error) {
	regopts := []reg.Opts{
		reg.WithCertDirs([]string{"/etc/docker/certs.d"}),
	}
	for _, auth := range opt.Auths {
		reghost := config.HostNew()
		reghost.Hostname = auth.Hostname
		reghost.User = auth.Username
		reghost.Pass = auth.Password
		reghost.TLS = config.TLSInsecure
		regopts = append(regopts, reg.WithConfigHosts([]*config.Host{reghost}))
	}
	regcli := regclient.New(
		regclient.WithDockerCreds(),
		regclient.WithRegOpts(regopts...),
	)
	return &Client{regcli: regcli}, nil
}
