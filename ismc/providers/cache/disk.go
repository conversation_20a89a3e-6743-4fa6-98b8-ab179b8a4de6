package cache

import (
	"context"
	"slices"
	"sync"
	"sync/atomic"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.DiskOperation = &DiskOperation{}

func NewDiskOperation(diskOperation common.DiskOperation, expires time.Duration) *DiskOperation {
	return &DiskOperation{
		DiskOperation: diskOperation,
		ExpiredCache: NewExpiredCache(expires, func(ctx context.Context) ([]common.Disk, error) {
			list, err := diskOperation.ListDisks(ctx, common.ListDiskOptions{})
			if err != nil {
				return nil, err
			}
			return list.Items, nil
		}),
	}
}

type DiskOperation struct {
	ExpiredCache  *ExpiredCache[common.Disk]
	DiskOperation common.DiskOperation
}

// CreateDisk implements common.DiskOperation.
func (d *DiskOperation) CreateDisk(ctx context.Context, disk *common.Disk, options common.CreateDiskOptions) (*common.Descripter, error) {
	desc, err := d.DiskOperation.CreateDisk(ctx, disk, options)
	if err != nil {
		return desc, err
	}
	d.ExpiredCache.Refresh()
	return desc, nil
}

// DeleteDisk implements common.DiskOperation.
func (d *DiskOperation) DeleteDisk(ctx context.Context, id string, options common.DeleteDiskOptions) error {
	if err := d.DiskOperation.DeleteDisk(ctx, id, options); err != nil {
		return err
	}
	d.ExpiredCache.Remove(id)
	return nil
}

// GetDisk implements common.DiskOperation.
func (d *DiskOperation) GetDisk(ctx context.Context, id string) (*common.Disk, error) {
	disk, err := d.DiskOperation.GetDisk(ctx, id)
	if err != nil {
		return nil, err
	}
	return disk, nil
}

// ListDisks implements common.DiskOperation.
func (d *DiskOperation) ListDisks(ctx context.Context, options common.ListDiskOptions) (common.List[common.Disk], error) {
	if options.VirtualMachine != "" {
		return d.DiskOperation.ListDisks(ctx, options)
	}
	disks := d.ExpiredCache.List()
	ret := common.List[common.Disk]{}
	for _, disk := range disks {
		if options.Name != "" && disk.Name != options.Name {
			continue
		}
		if options.ResourceGroup != "" && disk.ResourceGroup != options.ResourceGroup {
			continue
		}
		if options.Zone != "" && disk.Zone != options.Zone {
			continue
		}
		ret.Items = append(ret.Items, disk)
	}
	return common.PageList(ret.Items, options.ListOptions), nil
}

// ReInitalizeDisk implements common.DiskOperation.
func (d *DiskOperation) ReInitalizeDisk(ctx context.Context, id string) error {
	return d.DiskOperation.ReInitalizeDisk(ctx, id)
}

// ResizeDisk implements common.DiskOperation.
func (d *DiskOperation) ResizeDisk(ctx context.Context, id string, size resource.Quantity, options common.ResizeDiskOptions) error {
	return d.DiskOperation.ResizeDisk(ctx, id, size, options)
}

// UpdateDisk implements common.DiskOperation.
func (d *DiskOperation) UpdateDisk(ctx context.Context, disk *common.Disk) error {
	return d.DiskOperation.UpdateDisk(ctx, disk)
}

type ExpiredCache[T NamedValue] struct {
	ListFunc    func(ctx context.Context) ([]T, error)
	Expires     time.Duration
	lastupdate  time.Time
	updating    atomic.Bool
	mu          sync.RWMutex
	initialized bool
	cachedItems []T
}

type NamedValue interface {
	GetID() string
}

func NewExpiredCache[T NamedValue](expires time.Duration, listfunc func(ctx context.Context) ([]T, error)) *ExpiredCache[T] {
	if expires <= 0 {
		expires = time.Hour
	}
	return &ExpiredCache[T]{
		Expires:  expires,
		ListFunc: listfunc,
	}
}

func (e *ExpiredCache[T]) List() []T {
	e.waitInitialized()
	if expired := time.Since(e.lastupdate) > e.Expires; expired {
		e.Refresh()
	}
	return e.cachedItems
}

func (e *ExpiredCache[T]) waitInitialized() {
	if e.initialized {
		return
	}
	e.mu.Lock()
	defer e.mu.Unlock()
	if e.initialized {
		return
	}
	list, err := e.dolist()
	if err != nil {
		log.Error(err, "failed to initialize cache")
		return
	}
	e.cachedItems = list
	e.lastupdate = time.Now()
	e.initialized = true
}

func (e *ExpiredCache[T]) Remove(key string) {
	e.mu.Lock()
	defer e.mu.Unlock()

	for i, item := range e.cachedItems {
		if item.GetID() == key {
			e.cachedItems = slices.Delete(e.cachedItems, i, i+1)
			break
		}
	}
}

func (e *ExpiredCache[T]) Refresh() {
	// if already updating, return
	if e.updating.CompareAndSwap(true, false) {
		return
	}
	defer e.updating.Store(false)

	list, err := e.dolist()
	if err != nil {
		log.Error(err, "failed to refresh cache")
		return
	}
	e.mu.Lock()
	defer e.mu.Unlock()
	e.cachedItems = list
	e.lastupdate = time.Now()
}

func (e *ExpiredCache[T]) dolist() ([]T, error) {
	// refresh the cache
	listctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	return e.ListFunc(listctx)
}
