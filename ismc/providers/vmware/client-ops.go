package vmware

import (
	"context"
	"net/http"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/mo"
)

type Network struct {
	ResourceID string `json:"resource_id"`
	Type       string `json:"type"`
	Network    string `json:"network"`
}

func (vc *Client) ListAllNetworks(ctx context.Context) ([]Network, error) {
	refs, err := vc.Finder.NetworkList(ctx, "/*/network/*")
	if err != nil {
		return nil, err
	}
	var networks []Network
	baseElements := []string{"summary", "name"}
	for _, ref := range refs {
		switch ref.Reference().Type {
		case "DistributedVirtualPortgroup":
			obj := object.NewDistributedVirtualPortgroup(vc.VimClient.Client, ref.Reference())
			cr, err := FindReferenceInto[*object.DistributedVirtualPortgroup, mo.DistributedVirtualPortgroup](ctx, vc.VimClient, obj, baseElements)
			if err != nil {
				return nil, err
			}
			networks = append(networks, Network{
				ResourceID: cr.Reference().Value,
				Network:    cr.Name,
				Type:       cr.Reference().Type,
			})
		case "DistributedVirtualSwitch":
			obj := object.NewDistributedVirtualSwitch(vc.VimClient.Client, ref.Reference())
			cr, err := FindReferenceInto[*object.DistributedVirtualSwitch, mo.DistributedVirtualSwitch](ctx, vc.VimClient, obj, append(baseElements, "config"))
			if err != nil {
				return nil, err
			}
			networks = append(networks, Network{
				ResourceID: cr.Reference().Value,
				Network:    cr.Name,
				Type:       cr.Reference().Type,
			})

		case "Network":
			obj := object.NewNetwork(vc.VimClient.Client, ref.Reference())
			cr, err := FindReferenceInto[*object.Network, mo.Network](ctx, vc.VimClient, obj, baseElements)
			if err != nil {
				return nil, err
			}
			networks = append(networks, Network{
				ResourceID: cr.Reference().Value,
				Network:    cr.Name,
				Type:       cr.Reference().Type,
			})
		}
	}
	return networks, nil
}

type VMwareVirtualMachineListItem struct {
	VM            string `json:"vm"`
	Name          string `json:"name"`
	PowerState    string `json:"power_state"`
	CPUCount      int    `json:"cpu_count"`
	MemorySizeMiB int    `json:"memory_size_MiB"`
}

type VMwareVirtualMachine struct {
	Name               string                           `json:"name"`
	Placement          VMwareVirtualMachinePlacement    `json:"placement"`
	HardwareVersion    string                           `json:"hardware_version"`
	Boot               VMwareVirtualMachineBoot         `json:"boot"`
	GuestOS            string                           `json:"guest_OS"`
	InstantCloneFrozen bool                             `json:"instant_clone_frozen"`
	Memory             VMwareVirtualMachineMemory       `json:"memory"`
	CPU                VMwareVirtualMachineCPU          `json:"cpu"`
	Disks              []VMwareVirtualMachineDisk       `json:"disks"`
	Nics               []VMwareVirtualMachineNIC        `json:"nics"`
	Cdroms             []VMwareVirtualMachineDevice     `json:"cdroms"`
	Floppies           []VMwareVirtualMachineDevice     `json:"floppies"`
	ParallelPorts      []VMwareVirtualMachineDevice     `json:"parallel_ports"`
	SataAdapters       []VMwareVirtualMachinePCI        `json:"sata_adapters"`
	ScsiAdapters       []VMwareVirtualMachinePCI        `json:"scsi_adapters"`
	PowerState         string                           `json:"power_state"`
	Identity           VMwareVirtualMachineIdentity     `json:"identity"`
	NvmeAdapters       []VMwareVirtualMachinePCI        `json:"nvme_adapters"`
	SerialPorts        []VMwareVirtualMachineDevice     `json:"serial_ports"`
	BootDevices        []VMwareVirtualMachineBootDevice `json:"boot_devices"`
	Hardware           VMwareVirtualMachineHardware     `json:"hardware"`
}

type VMwareVirtualMachineBootDevice struct {
	Type string `json:"type"`
}

type VMwareVirtualMachinePlacement struct {
	Folder       string `json:"folder"`
	ResourcePool string `json:"resource_pool"`
	Cluster      string `json:"cluster"`
	Host         string `json:"host"`
	Datastore    string `json:"datastore"`
}

type VMwareVirtualMachineHardware struct {
	UpgradePolicy string `json:"upgrade_policy"`
	UpgradeStatus string `json:"upgrade_status"`
	Version       string `json:"version"`
}

type VMwareVirtualMachineBoot struct {
	Delay           int    `json:"delay"`
	EFILegacyBoot   bool   `json:"efi_legacy_boot"`
	RetryDelay      int    `json:"retry_delay"`
	EnterSetupMode  bool   `json:"enter_setup_mode"`
	NetworkProtocol string `json:"network_protocol"`
	Type            string `json:"type"`
	Retry           bool   `json:"retry"`
}

type VMwareVirtualMachineNIC struct {
	Type                    string                             `json:"type"`
	UPTCompatibilityEnabled bool                               `json:"upt_compatibility_enabled"`
	StartConnected          bool                               `json:"start_connected"`
	PCI_SlotNumber          int                                `json:"pci_slot_number"`
	Backing                 VMwareVirtualMachineNetworkBacking `json:"backing"`
	MACAddress              string                             `json:"mac_address"`
	MACType                 string                             `json:"mac_type"`
	AllowGuestControl       bool                               `json:"allow_guest_control"`
	WakeOnLANEnabled        bool                               `json:"wake_on_lan_enabled"`
	Label                   string                             `json:"label"`
	State                   string                             `json:"state"`
}

type VMwareVirtualMachineNetworkBacking struct {
	Type            string `json:"type"`
	Network         string `json:"network"`
	NetworkName     string `json:"network_name"`
	DistributedPort string `json:"distributed_port"`
}

type VMwareVirtualMachineIdentity struct {
	Name         string `json:"name"`
	InstanceUUID string `json:"instance_uuid"`
	BiosUUID     string `json:"bios_uuid"`
}

type VMwareVirtualMachineCPU struct {
	HotRemoveEnabled bool `json:"hot_remove_enabled"`
	Count            int  `json:"count"`
	HotAddEnabled    bool `json:"hot_add_enabled"`
	CoresPerSocket   int  `json:"cores_per_socket"`
}

type VMwareVirtualMachinePCI struct {
	Type          string `json:"type"`
	Bus           int    `json:"bus"`
	Unit          int    `json:"unit"`
	PCISlotNumber int    `json:"pci_slot_number"`
	Label         string `json:"label"`
	Sharing       string `json:"sharing"`
}

type VMwareVirtualMachineDisk struct {
	Type     string                            `json:"type"`
	SCSI     VMwareVirtualMachinePCI           `json:"scsi"`
	SATA     VMwareVirtualMachinePCI           `json:"sata"`
	NVME     VMwareVirtualMachinePCI           `json:"nvme"`
	Backing  VMwareVirtualMachineDeviceBacking `json:"backing"`
	Label    string                            `json:"label"`
	Capacity int                               `json:"capacity"`
	NewVMDK  VMwareVirtualMachineNewVMDK       `json:"new_vmdk"`
}

type VMwareVirtualMachineNewVMDK struct {
	Name          string                            `json:"name"`
	Capacity      int64                             `json:"capacity"`
	StoragePolicy VMwareVirtualMachineStoragePolicy `json:"storage_policy"`
}

type VMwareVirtualMachineStoragePolicy struct {
	Policy string `json:"policy"`
}

type VMwareVirtualMachineMemory struct {
	HotAddIncrementSizeMiB int64 `json:"hot_add_increment_size_MiB"`
	SizeMiB                int64 `json:"size_MiB"`
	HotAddEnabled          bool  `json:"hot_add_enabled"`
	HotAddLimitMiB         int64 `json:"hot_add_limit_MiB"`
}

type VMwareVirtualMachineDevice struct {
	StartConnected    bool                              `json:"start_connected"`
	Type              string                            `json:"type"`
	Backing           VMwareVirtualMachineDeviceBacking `json:"backing"`
	AllowGuestControl bool                              `json:"allow_guest_control"`
	Label             string                            `json:"label"`
	State             string                            `json:"state"`
	SATA              VMwareVirtualMachinePCIBUS        `json:"sata"`
	IDE               VMwareVirtualMachinePCIBUS        `json:"ide"`
	// serial
	YieldOnPoll bool `json:"yield_on_poll"`
}

type VMwareVirtualMachinePCIBUS struct {
	Bus  int `json:"bus"`
	Unit int `json:"unit"`
}

type VMwareVirtualMachineDeviceBacking struct {
	Type             string `json:"type"`
	DeviceAccessType string `json:"device_access_type"`
	HostDevice       string `json:"host_device"`
	// disk
	VMDKFile string `json:"vmdk_file"`
	// cdrom
	ISOFile string `json:"iso_file"`
	// floppy
	ImageFile string `json:"image_file"`
	// serial
	Pipe            string `json:"pipe"`
	NoRxLoss        bool   `json:"no_rx_loss"`
	NetworkLocation string `json:"network_location"`
	Proxy           string `json:"proxy"`
}

// https://developer.broadcom.com/xapis/vsphere-automation-api/v6.5%20-%20v7.0.0/vcenter/rest/vcenter/vm/vm/get/
func (c *Client) RestGetVirtualMachine(ctx context.Context, vm string) (VMwareVirtualMachineListItem, error) {
	vmListItem := VMwareVirtualMachineListItem{}
	if err := c.RestClient.Do(ctx, c.RestClient.Resource("/vcenter/vm/"+vm).Request(http.MethodGet), &vmListItem); err != nil {
		return vmListItem, IgnoreNotFound(err)
	}
	return vmListItem, nil
}

func (c *Client) RestCreateVirtualMachine(ctx context.Context, vm VMwareVirtualMachine) error {
	return c.RestClient.Do(ctx, c.RestClient.Resource("/vcenter/vm").Request(http.MethodPost, vm), nil)
}

func (c *Client) RestDeleteVirtualMachine(ctx context.Context, vm string) error {
	return c.RestClient.Do(ctx, c.RestClient.Resource("/vcenter/vm/"+vm).Request(http.MethodDelete), nil)
}

// https://developer.broadcom.com/xapis/vsphere-automation-api/v6.5%20-%20v7.0.0/vcenter/rest/vcenter/vm/vm/console/tickets/post/
// Create Vm Console Tickets
func (c *Client) RestCreateVirtualMachineConsoleTickets(ctx context.Context, vm string) (string, error) {
	req := c.RestClient.Resource("/vcenter/vm/"+vm+"/console/tickets").Request(http.MethodPost, map[string]any{
		"spec": map[string]any{
			"type": "WEBMKS",
		},
	})
	type Ticket struct {
		Ticket string `json:"ticket"`
	}
	ticket := Ticket{}
	if err := c.RestClient.Do(ctx, req, &ticket); err != nil {
		return "", IgnoreNotFound(err)
	}
	return ticket.Ticket, nil
}

type RestDatastore struct {
	// Datastore is the identifier for the datastore, e.g., "datastore-5076"
	Datastore string `json:"datastore"`
	Name      string `json:"name"`
	// Type is the type of the datastore, e.g., VMFS, NFS, etc.
	// This field is optional and may not be present in all responses.
	// If not present, it is assumed to be an empty string.
	Type string `json:"type"`
	// FreeSpace is the free space in bytes
	FreeSpace int64 `json:"free_space"`
	// Capacity is the total capacity in bytes
	Capacity int64 `json:"capacity"`
}

func (c *Client) RestListDatastore(ctx context.Context, dcid string) ([]RestDatastore, error) {
	req := c.RestClient.Resource("/vcenter/datastore")
	if dcid != "" {
		req = req.WithParam("filter.datacenters.1", dcid)
	}
	datastores := []RestDatastore{}
	if err := c.RestClient.Do(ctx, req.Request(http.MethodGet), &datastores); err != nil {
		return nil, err
	}
	return datastores, nil
}
