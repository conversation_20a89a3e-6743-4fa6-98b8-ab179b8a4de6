package vmware

import (
	"context"
	"fmt"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/property"
	"github.com/vmware/govmomi/task"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/log"
)

func (c *ObjectCache) runTasks(ctx context.Context) error {
	log.FromContext(ctx).Info("start cache task collector")
	taskManager := task.NewManager(c.cli.VimClient.Client)
	root := c.cli.VimClient.ServiceContent.RootFolder
	collector, err := taskManager.CreateCollectorForTasks(ctx, types.TaskFilterSpec{
		Entity: &types.TaskFilterSpecByEntity{
			Entity:    root,
			Recursion: types.TaskFilterSpecRecursionOptionChildren,
		},
	})
	if err != nil {
		return err
	}
	// nolint errcheck
	defer collector.Destroy(ctx)

	lastpage, err := collector.LatestPage(ctx)
	if err != nil {
		return fmt.Errorf("get latest page: %w", err)
	}
	lastestPageKeys := make(map[string]struct{})
	for _, taskInfo := range lastpage {
		lastestPageKeys[taskKey(taskInfo)] = struct{}{}
	}
	newpc, err := property.DefaultCollector(c.cli.VimClient.Client).Create(ctx)
	if err != nil {
		return fmt.Errorf("create property collector: %w", err)
	}
	spec := types.PropertyFilterSpec{
		ObjectSet: []types.ObjectSpec{
			{
				Obj: collector.Reference(),
			},
		},
		PropSet: []types.PropertySpec{
			{
				Type:    collector.Reference().Type,
				PathSet: []string{"latestPage"},
			},
		},
	}
	// latestPage update contains all items in latestPage it fixed max 10 items.
	// when an event happens, 9 items are same as before, only one item is new.
	// so we must find the new item in latestPage.
	return property.WaitForUpdatesEx(ctx, newpc, &property.WaitFilter{CreateFilter: types.CreateFilter{Spec: spec}}, func(ou []types.ObjectUpdate) bool {
		for _, update := range ou {
			for _, change := range update.ChangeSet {
				if change.Name == "latestPage" {
					arrayOfTasks, ok := change.Val.(types.ArrayOfTaskInfo)
					if !ok {
						log.Error(fmt.Errorf("invalid task type: %T", change.Val), "process task updates")
						continue
					}
					thispagekeys := make(map[string]struct{})
					for _, task := range arrayOfTasks.TaskInfo {
						taskcacheid := taskKey(task)
						thispagekeys[taskcacheid] = struct{}{}
						if _, ok := lastestPageKeys[taskcacheid]; ok {
							// this task is already handled, skip it
							continue
						}
						// this is a new task, handle it
						if err := c.onTaskInfo(ctx, task); err != nil {
							log.FromContext(ctx).Error(err, "handle task info", "task", task.DescriptionId)
						}
					}
					lastestPageKeys = thispagekeys
				}
			}
		}
		return false
	})
}

func taskKey(taskInfo types.TaskInfo) string {
	return fmt.Sprintf("%s/%s", taskInfo.Key, taskInfo.State)
}

func (c *ObjectCache) onTaskInfo(ctx context.Context, taskInfo types.TaskInfo) error {
	log := log.FromContext(ctx)
	if taskInfo.State != types.TaskInfoStateSuccess {
		return nil
	}
	switch taskInfo.DescriptionId {
	case "VirtualDiskManager.createVirtualDisk":
		log.Info("created virtual disk", "task", taskInfo.DescriptionId, "result", taskInfo.Result)
		dspathstr, ok := taskInfo.Result.(string)
		if !ok {
			return nil
		}
		dspath := object.DatastorePath{}
		if !dspath.FromString(dspathstr) {
			return fmt.Errorf("invalid datastore path: %s", dspathstr)
		}
		if dspath.Datastore == "" {
			// datastore is not specified, we cannot handle this
			log.Info("create virtual disk without datastore", "path", dspathstr)
			return nil
		}
		// task events happens to quick, add to queue is a good way to handle this
		if true {
			// notify cache to refresh the datastore files
			c.Notify(types.ManagedObjectReference{Type: TypeDatastoreFile})
			return nil
		}
		// we need to know datastore id
		datastoreref, err := c.getDatastoreByName(ctx, dspath.Datastore)
		if err != nil {
			return fmt.Errorf("get filepath datastore %s: %w", dspath.Datastore, err)
		}
		return c.refreshDatastoreFiles(ctx, datastoreref.Value, []string{dspath.Path})
	case "VirtualDiskManager.deleteVirtualDisk":
		log.Info("task delete virtual disk", "task", taskInfo.DescriptionId, "result", taskInfo.Result)
		// there is no file path in task info, we cannot handle this
		return nil
	}
	return nil
}

func (c *ObjectCache) getDatastoreByName(ctx context.Context, name string) (*types.ManagedObjectReference, error) {
	datastores, err := CacheToList(c, TypeDatastore, func(mo mo.Datastore) bool {
		return mo.Name == name
	})
	if err != nil {
		return nil, err
	}
	if len(datastores) > 0 {
		ref := datastores[0].Reference()
		return &ref, nil
	}
	// find from api
	obj, err := c.cli.Finder.Datastore(ctx, name)
	if err != nil {
		return nil, fmt.Errorf("find datastore %s: %w", name, err)
	}
	ref := obj.Reference()
	return &ref, nil
}
