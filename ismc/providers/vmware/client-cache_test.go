package vmware

import (
	"context"
	"flag"
	"fmt"
	"testing"

	"k8s.io/klog/v2"
	"xiaoshiai.cn/common/log"
)

func Test_NewObjectCache(t *testing.T) {
	c := SetupTestClient(t)

	// 初始化 klog
	klog.InitFlags(nil)
	_ = flag.Set("v", "5")
	flag.Parse()

	ctx := context.Background()
	ctx = log.NewContext(ctx, log.DefaultLogger)

	cache := NewDefaultObjectCache(c)

	if err := cache.Run(ctx); err != nil {
		t.Fatalf("failed to list objects: %v", err)
	}
}

func Test_ListDataStoreFiles(t *testing.T) {
	c := SetupTestClient(t)

	// 初始化 klog
	klog.InitFlags(nil)
	_ = flag.Set("v", "5")
	flag.Parse()

	ctx := context.Background()
	ctx = log.NewContext(ctx, log.DefaultLogger)

	pattern := []string{
		"abab/abab.vmdk",
	}
	items, err := ListDataStoreDiskFiles(ctx, c.VimClient.Client, "datastore-5076", pattern)
	if err != nil {
		t.Fatalf("failed to list datastore files: %v", err)
	}
	for _, item := range items {
		fmt.Printf("Found file: %s, size: %d, type: %s\n", item.Value, item.FileInfo.FileSize, item.Type)
	}
}
