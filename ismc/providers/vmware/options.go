package vmware

type Options struct {
	Address    string `json:"address,omitempty" description:"vcenter address"`
	Username   string `json:"username,omitempty" description:"vcenter username"`
	Password   string `json:"password,omitempty" description:"vcenter password"`
	Datacenter string `json:"datacenter,omitempty" description:"vcenter datacenter"`
	Proxy      string `json:"proxy,omitempty" description:"http proxy address"`
	Debug      bool   `json:"debug,omitempty" description:"enable debug mode"`
}

func NewDefaultOptions() *Options {
	return &Options{
		Address:    "",
		Username:   "<EMAIL>",
		Password:   "",
		Datacenter: "Datacenter",
	}
}
