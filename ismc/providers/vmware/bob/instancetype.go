package bob

import (
	"context"
	"fmt"

	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

const (
	KB = 1024
	MB = 1024 * KB
	GB = 1024 * MB
)

var _ common.InstanceTypeOperation = &Provider{}

var BuiltInInstanceTypes = []common.InstanceType{
	{
		Descripter: common.Descripter{
			ID:   "s1.tiny",
			Name: "s1.tiny",
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    resource.MustParse("1"),
			common.ResourceMemory: resource.MustParse("1Gi"),
		},
		Architecture: common.ArchitectureAmd64,
		SupportedBootModes: []common.BootMode{
			common.BootModeBIOS,
		},
	},
	{
		Descripter: common.Descripter{
			ID:   "s1.small",
			Name: "s1.small",
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    resource.MustParse("1"),
			common.ResourceMemory: resource.MustParse("2Gi"),
		},
		Architecture: common.ArchitectureAmd64,
	},
	{
		Descripter: common.Descripter{
			ID:   "s1.medium",
			Name: "s1.medium",
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    resource.MustParse("2"),
			common.ResourceMemory: resource.MustParse("4Gi"),
		},
		Architecture: common.ArchitectureAmd64,
	},
	{
		Descripter: common.Descripter{
			ID:   "s1.large",
			Name: "s1.large",
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    resource.MustParse("4"),
			common.ResourceMemory: resource.MustParse("8Gi"),
		},
		Architecture: common.ArchitectureAmd64,
	},
	{
		Descripter: common.Descripter{
			ID:   "s1.xlarge",
			Name: "s1.xlarge",
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    resource.MustParse("8"),
			common.ResourceMemory: resource.MustParse("16Gi"),
		},
		Architecture: common.ArchitectureAmd64,
	},
	{
		Descripter: common.Descripter{
			ID:   "s1.2xlarge",
			Name: "s1.2xlarge",
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    resource.MustParse("16"),
			common.ResourceMemory: resource.MustParse("32Gi"),
		},
		Architecture: common.ArchitectureAmd64,
	},
}

// ListInstanceTypes implements ismc.InstanceTypeOperation.
func (p *Provider) ListInstanceTypes(ctx context.Context, options common.ListInstanceTypeOptions) (common.List[common.InstanceType], error) {
	var items []common.InstanceType
	items = append(items, BuiltInInstanceTypes...)
	if options.Vm != "" {
		// 由于有些虚拟机有cpu或内存限制，所以需要根据虚拟机的配置返回可变更的规格
		vmInfo, err := vmware.FromResourceToMo[mo.VirtualMachine](ctx, p.Client.VimClient, options.Vm, TypeVirtualMachine, []string{"config", "runtime"})
		if err != nil {
			return common.List[common.InstanceType]{}, err
		}
		if vmInfo.Runtime.PowerState == types.VirtualMachinePowerStatePoweredOn {
			memMin := int64(vmInfo.Config.Hardware.MemoryMB) * 1024 * 1024
			memMax := vmInfo.Config.HotPlugMemoryLimit * 1024 * 1024
			items = findAvaiableMemoryInstanceTypeBaseOnHost(memMin, memMax)
		}
		if vmInfo.Runtime.Host != nil {
			hostsystem, err := p.getHost(ctx, vmInfo.Runtime.Host.Value, []string{"summary"})
			if err != nil {
				return common.List[common.InstanceType]{}, err
			}
			summary := hostsystem.Summary
			if hardware := summary.Hardware; hardware != nil {
				cpuCores := int64(hardware.NumCpuThreads)
				for idx := 0; idx < len(items); {
					item := items[idx]
					cpu := item.Resources[common.ResourceCPU]
					if cpu.Value() > cpuCores {
						items = append(items[:idx], items[idx+1:]...)
					} else {
						idx++
					}
				}
			}
		}

	}
	return common.PageList(items, options.ListOptions), nil
}

func findAvaiableMemoryInstanceTypeBaseOnHost(min, max int64) []common.InstanceType {
	var avaiable []common.InstanceType
	for _, ele := range BuiltInInstanceTypes {
		memSize := ele.Resources[common.ResourceMemory]
		if min <= memSize.Value() && max >= memSize.Value() {
			avaiable = append(avaiable, ele)
		}
	}
	return avaiable
}

// CreateInstanceType implements ismc.InstanceTypeOperation.
func (p *Provider) CreateInstanceType(ctx context.Context, instanceType *common.InstanceType) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteInstanceType implements ismc.InstanceTypeOperation.
func (p *Provider) DeleteInstanceType(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// UpdateInstanceType implements ismc.InstanceTypeOperation.
func (p *Provider) UpdateInstanceType(ctx context.Context, instanceType *common.InstanceType) error {
	return common.ErrUnsupported
}

func (p *Provider) GetInstanceType(ctx context.Context, id string) (*common.InstanceType, error) {
	for _, t := range BuiltInInstanceTypes {
		if t.ID == id {
			return &t, nil
		}
	}
	return nil, errors.NewNotFound("instancetype", id)
}

func (p *Provider) findInstanceType(harware types.VirtualHardware) string {
	cpu := *resource.NewQuantity(int64(harware.NumCPU), resource.DecimalSI)
	memory := *resource.NewQuantity(int64(harware.MemoryMB)*MB, resource.BinarySI)
	for _, t := range BuiltInInstanceTypes {
		if t.Resources[common.ResourceCPU].Equal(cpu) && t.Resources[common.ResourceMemory].Equal(memory) {
			return t.ID
		}
	}
	return fmt.Sprintf("%dc-%dg", harware.NumCPU, harware.MemoryMB/1024)
}
