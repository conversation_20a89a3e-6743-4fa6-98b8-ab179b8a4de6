package bob

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.StatisticsOperation = &Provider{}

// GetSystemStatistics implements common.Provider.
func (p *Provider) GetSystemStatistics(ctx context.Context) (*common.SystemStatistics, error) {
	hosts, err := p.listHosts(ctx, "name", "datastore")
	if err != nil {
		return nil, err
	}
	vms, err := p.listvms(ctx, "name", "config")
	if err != nil {
		return nil, err
	}
	networks, err := p.listNetworks(ctx)
	if err != nil {
		return nil, err
	}
	diskclusters, err := p.listDiskClusters(ctx, "")
	if err != nil {
		return nil, err
	}
	disks, err := p.listdisks(ctx, "", "")
	if err != nil {
		return nil, err
	}
	// Calculate storage total and used
	storagetotal, storageused := resource.NewQuantity(0, resource.BinarySI), resource.NewQuantity(0, resource.BinarySI)
	for _, diskcluster := range diskclusters {
		storagetotal.Add(diskcluster.Status.Total)
		storageused.Add(diskcluster.Status.Used)
	}
	stat := &common.SystemStatistics{
		ZoneCount:           len(hosts),
		HostCount:           len(hosts),
		DiskCount:           len(disks),
		VirtualMachineCount: len(vms),
		VirtualNetworkCount: len(networks),
		DiskClusterCount:    len(diskclusters),
		StorageUsed:         *storageused,
		StorageTotal:        *storagetotal,
	}
	return stat, nil
}
