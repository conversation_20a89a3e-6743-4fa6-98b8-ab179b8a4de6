package bob

import (
	"context"
	"fmt"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

var _ common.HostOperation = &Provider{}

// GetHost implements common.Provider.
func (p *Provider) GetHost(ctx context.Context, id string) (*common.Host, error) {
	hostsystems, err := p.getHost(ctx, id, []string{"name", "summary"})
	if err != nil {
		return nil, err
	}
	zone := p.ConvertHost(ctx, hostsystems)
	return &zone, nil
}

// ListHosts implements common.Provider.
func (p *Provider) ListHosts(ctx context.Context, options common.ListHostOptions) (common.List[common.Host], error) {
	list := common.List[common.Host]{Items: []common.Host{}}
	hostsystems, err := p.listHosts(ctx, "name", "summary", "config")
	if err != nil {
		return list, err
	}
	// convert host to zone config.product.ostype
	var hosts []common.Host
	for _, host := range hostsystems {
		hosts = append(hosts, p.ConvertHost(ctx, host))
	}
	return common.PageList(hosts, options.ListOptions), nil
}

// UpdateHost implements common.Provider.
func (p *Provider) UpdateHost(ctx context.Context, hostname string, host *common.Host) error {
	hostsystem, err := p.getHost(ctx, hostname, []string{"name", "runtime"})
	if err != nil {
		return err
	}
	if hostsystem.Runtime.InMaintenanceMode != host.Maintenance {
		// update maintenance mode
	}
	return nil
}

func (p *Provider) ConvertHost(ctx context.Context, host mo.HostSystem) common.Host {
	zone := common.Host{
		Descripter: common.Descripter{
			ID:   host.Self.Value,
			Name: host.Name,
			UID:  host.Self.ServerGUID,
		},
		Status: common.HostStatus{
			Addtional: map[string]string{},
			Capacity:  map[common.ResourceType]resource.Quantity{},
			Used:      map[common.ResourceType]resource.Quantity{},
		},
	}
	if config := host.Config; config != nil {
		zone.Status.OS = config.Product.OsType
	}
	summary := host.Summary
	// power
	if runtime := summary.Runtime; runtime != nil {
		zone.Maintenance = runtime.InMaintenanceMode
		zone.Status.BootTime = runtime.BootTime
		switch runtime.ConnectionState {
		case types.HostSystemConnectionStateConnected:
			switch runtime.PowerState {
			case types.HostSystemPowerStatePoweredOn:
				zone.Status.PowerState = common.PowerStateOn
			case types.HostSystemPowerStatePoweredOff:
				zone.Status.PowerState = common.PowerStateOff
			case types.HostSystemPowerStateStandBy:
				zone.Status.PowerState = common.PowerStateSuspended
			case types.HostSystemPowerStateUnknown:
				zone.Status.PowerState = common.PowerStateUnknown
			}
		case types.HostSystemConnectionStateDisconnected:
			zone.Status.PowerState = common.PowerStateUnknown
		case types.HostSystemConnectionStateNotResponding:
			zone.Status.PowerState = common.PowerStateUnknown
		}
	}
	// hardware
	if hardware := summary.Hardware; hardware != nil {
		zone.Status.Capacity[common.ResourceCPU] = *resource.NewQuantity(int64(hardware.NumCpuThreads), resource.DecimalSI)
		zone.Status.Capacity[common.ResourceMemory] = *resource.NewQuantity(int64(hardware.MemorySize), resource.BinarySI)
		zone.Status.CPU.Model = hardware.CpuModel
	}
	// quick stats
	{
		zone.Status.Used[common.ResourceMemory] = *resource.NewQuantity(int64(summary.QuickStats.OverallMemoryUsage)*MB, resource.BinarySI)
		if hardware := summary.Hardware; hardware != nil {
			// OverallCpuUsage is in MHz, NumCpuThreads is the number of threads, CpuMhz is the MHz of each thread
			// so the formula is OverallCpuUsage / (NumCpuThreads * CpuMhz)
			usedCores := float64(summary.QuickStats.OverallCpuUsage) / (float64(hardware.NumCpuThreads) * float64(hardware.CpuMhz))
			zone.Status.Used[common.ResourceCPU] = *resource.NewScaledQuantity(int64(usedCores*1000), resource.Milli)
		}
	}
	// additional
	if product := summary.Config.Product; product != nil {
		zone.Status.Software = common.SoftwareInfo{
			Name:    product.FullName,
			Version: product.Version,
			Vendor:  product.Vendor,
		}
		zone.Status.Addtional["product"] = product.FullName
		zone.Status.Addtional["vendor"] = product.Vendor
		zone.Status.Addtional["version"] = product.Version
	}
	zone.Status.IPs = append(zone.Status.IPs, summary.ManagementServerIp)
	return zone
}

func (p *Provider) getHost(ctx context.Context, hostid string, attr []string) (mo.HostSystem, error) {
	hostref := types.ManagedObjectReference{Type: TypeHostSystem, Value: hostid}
	hostsys, err := vmware.FindReferenceInto[mo.Reference, mo.HostSystem](ctx, p.Client.VimClient, hostref, attr)
	if err != nil {
		return mo.HostSystem{}, WrapError(err, "host", hostid)
	}
	return hostsys, nil
}

func (p *Provider) listHosts(ctx context.Context, attr ...string) ([]mo.HostSystem, error) {
	refs, err := p.Client.Finder.HostSystemList(ctx, fmt.Sprintf("/%s/host/*", p.DC.Name()))
	if err != nil {
		return nil, err
	}
	return vmware.FindReferenceSliceInto[*object.HostSystem, mo.HostSystem](ctx, p.Client.VimClient, refs, attr)
}
