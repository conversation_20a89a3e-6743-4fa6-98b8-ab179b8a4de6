package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/providers/vmware"
)

func SetupTestProvider(t *testing.T) *Provider {
	options := vmware.Options{
		Address:    "*************",
		Username:   "<EMAIL>",
		Datacenter: "Datacenter",
		Password:   "",
	}
	client, err := vmware.NewClient(context.Background(), options)
	if err != nil {
		t.Fatalf("failed to create client: %v", err)
	}
	provider, err := NewProvider(context.Background(), client)
	if err != nil {
		t.Fatal(err)
	}
	return provider
}

func TestNewProvider(t *testing.T) {
	p := SetupTestProvider(t)
	t.Log(p)
}
