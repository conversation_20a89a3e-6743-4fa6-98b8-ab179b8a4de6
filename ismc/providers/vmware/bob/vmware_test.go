package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/providers/vmware"
)

func SetupTestProvider(t *testing.T) *Provider {
	options := vmware.Options{
		Address:  "*************",
		Username: "<EMAIL>",
		Password: "",
	}
	client, err := vmware.NewClient(context.Background(), options)
	if err != nil {
		t.Fatalf("failed to create client: %v", err)
	}
	provider, err := NewProvider(context.Background(), client, "Datacenter")
	if err != nil {
		t.<PERSON>al(err)
	}
	return provider
}

func TestNewProvider(t *testing.T) {
	p := SetupTestProvider(t)
	t.Log(p)
}
