package bob

import (
	"context"
	"testing"

	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/core/ismc/common"
)

func TestListInstanceTypes(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	ts, err := p.ListInstanceTypes(ctx, common.ListInstanceTypeOptions{
		Vm: "vm-8046",
		ListOptions: common.ListOptions{
			Size: 500,
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	for _, tt := range ts.Items {
		t.Log(tt)
	}
	t.Log("len:", len(ts.Items))
}

func TestGetResourceSupportMetrics(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	ref, err := p.Client.FromMOID(ctx, "vm-5081", "VirtualMachine")
	if err != nil {
		t.Fatal(err)
	}
	supportMetrics, err := p.getResourceSupportMetrics(ctx, ref.Reference())
	if err != nil {
		t.Fatal(err)
	}
	for k, v := range supportMetrics.toMap() {
		t.Log(k, "--->", v)
	}
	t.Log("finished")
}

func TestResourceGetMetricsV1(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	ref, err := p.Client.FromMOID(ctx, "vm-8007", "VirtualMachine")
	if err != nil {
		t.Fatal(err)
	}
	mrs, err := p.GetResourcesMetrics(ctx, ref.Reference(), []string{"mem.usage.average"}, "")
	if err != nil {
		t.Fatal(err)
	}

	t.Log(mrs)
}

func TestListResourceMetrics(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)

	vms, err := p.listvms(ctx, "name")
	if err != nil {
		t.Fatal(err)
		return
	}
	entities := make([]types.ManagedObjectReference, 0, len(vms))
	for _, vm := range vms {
		entities = append(entities, vm.Reference())
	}

	mrs, err := p.ListResourcesMetrics(ctx, []string{"mem.usage.average"}, entities)
	if err != nil {
		t.Fatal(err)
	}
	for _, mr := range mrs {
		t.Log(mr)
	}
	t.Log("finished")
}

func TestListDiskMetrics(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	diskMetrics, err := p.ListDiskMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}
	for i := range diskMetrics {
		t.Log(*diskMetrics[i])
	}
	t.Log("finished")
}

func TestListHostsystemMetrics(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	hostMetrics, err := p.ListHostMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}
	for _, h := range hostMetrics {
		t.Log(*h)
	}
}

func TestListVirtualMachineMetrics(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	vmMetrics, err := p.ListVirtualMachineMetrics(ctx, common.ListVirtualMachineMetricsOptions{})
	if err != nil {
		t.Fatal(err)
	}
	for _, vm := range vmMetrics {
		t.Log(*vm)
	}
	t.Log("finished")
}
