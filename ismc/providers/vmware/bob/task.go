package bob

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/vmware/govmomi/event"
	"github.com/vmware/govmomi/task"
	"github.com/vmware/govmomi/vim25/methods"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
)

type taskCollector struct {
	history      *task.HistoryCollector
	eventManager *event.Manager
	initSend     []types.TaskInfo
	watchChan    chan struct{}
	update<PERSON>han   chan types.TaskInfo
	list         bool
}

// Collect implements collector.
func (t *taskCollector) Collect(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			tasks, err := t.history.ReadNextTasks(ctx, 100)
			if err != nil {
				log.FromContext(ctx).Error(err, "read next tasks")
				return
			}
			select {
			case <-t.watchChan:
				for _, task := range tasks {
					t.update<PERSON>han <- task
				}
				time.Sleep(time.Second)
			default:
				if len(tasks) == 0 {
					close(t.watch<PERSON>han)
					if t.list {
						return
					}
					continue
				}
				t.initSend = append(t.initSend, tasks...)
			}
		}
	}
}

// WactchTasks implements common.Provider.
func (p *Provider) WactchTasks(ctx context.Context, on common.OnTaskEvent, opts common.WactchTaskOptions) error {
	col, err := p.newTaskCollector(ctx, nil, false)
	if err != nil {
		return err
	}
	defer func() {
		if err := col.history.Destroy(context.Background()); err != nil {
			log.Error(err, "destory task history")
		}
	}()
	go col.Collect(ctx)
	// send init task
	select {
	case <-col.watchChan:
		sort.Slice(col.initSend, func(i, j int) bool {
			return col.initSend[i].StartTime.After(*col.initSend[j].StartTime)
		})
		if opts.Size > 0 && len(col.initSend) > opts.Size {
			col.initSend = col.initSend[:opts.Size]
		}
		for _, task := range col.initSend {
			if err := on(ctx, common.WatchEvent[common.Task]{
				Type: common.WatchEventTypeAdd,
				Data: col.ParseTask(task),
			}); err != nil {
				return err
			}
		}
	case <-ctx.Done():
		return nil
	}
	for {
		select {
		case <-ctx.Done():
			return nil
		case e := <-col.updateChan:
			if err := on(ctx, common.WatchEvent[common.Task]{
				Type: common.WatchEventTypeAdd,
				Data: col.ParseTask(e),
			}); err != nil {
				return err
			}
		}
	}
}

func (t *taskCollector) getSubEvents(eventChainId int32) ([]common.SubTask, error) {
	events, err := t.eventManager.QueryEvents(context.Background(), types.EventFilterSpec{
		EventChainId: eventChainId,
	})
	if err != nil {
		return nil, err
	}
	var subTasks []common.SubTask
	for _, e := range events {
		ev := e.GetEvent()
		if ev == nil {
			continue
		}
		subTasks = append(subTasks, common.SubTask{
			ID:             fmt.Sprintf("%d", ev.Key),
			StartTimestamp: &ev.CreatedTime,
			Message:        ev.FullFormattedMessage,
		})
	}
	return subTasks, nil
}
func (t *taskCollector) ParseTask(task types.TaskInfo) common.Task {
	tk := common.Task{
		Descripter: common.Descripter{
			ID:   task.Key,
			Name: taskName(task),
			UID:  task.ActivationId,
		},
		Type: taskName(task),
		Status: common.TaskStatus{
			Progress: int(task.Progress),
		},
	}
	switch task.State {
	case types.TaskInfoStateQueued:
		tk.Status.Phase = common.TaskPhaseQueued
	case types.TaskInfoStateRunning:
		tk.Status.Phase = common.TaskPhaseRunning
	case types.TaskInfoStateSuccess:
		tk.Status.Phase = common.TaskPhaseSuccess
		tk.Status.Progress = 100
	case types.TaskInfoStateError:
		tk.Status.Phase = common.TaskPhaseFailed
		if task.Error != nil {
			tk.Status.Message = strings.TrimSuffix(task.Error.LocalizedMessage, ".")
		}

	default:
		tk.Status.Phase = common.TaskPhase(task.State)
	}
	if task.Description != nil {
		tk.Description = task.Description.Message
	}
	if task.StartTime != nil {
		tk.CreationTimestamp = *task.StartTime
		tk.Status.StartTimestamp = task.StartTime
	}
	if task.CompleteTime != nil {
		tk.Status.EndTimestamp = task.CompleteTime
	}
	if task.Entity != nil {
		tk.ServiceType = task.Entity.Type
		tk.Status.Resources = append(tk.Status.Resources, common.TaskResource{
			ID:       task.Entity.Value,
			Resource: task.Entity.Type,
		})
	}
	// 磁盘操作没有Entity
	if tk.ServiceType == "" {
		if strings.Contains(tk.Name, "VirtualDisk") {
			tk.ServiceType = "VirtualDisk"
		}
	}
	subs, err := t.getSubEvents(task.EventChainId)
	if err != nil {
		log.Error(err, "get sub events failed")
	} else {
		tk.Status.SubTasks = subs
	}
	return tk
}

func (p *Provider) newTaskCollector(ctx context.Context, ref *types.ManagedObjectReference, list bool) (*taskCollector, error) {
	c := p.Client.VimClient.Client
	m := task.NewManager(c)
	now, err := methods.GetCurrentTime(ctx, c) // vCenter server time (UTC)
	if err != nil {
		return nil, err
	}
	if ref == nil {
		ref = &c.ServiceContent.RootFolder
	}
	filter := types.TaskFilterSpec{
		Entity: &types.TaskFilterSpecByEntity{
			Entity:    *ref,
			Recursion: types.TaskFilterSpecRecursionOptionAll,
		},
		Time: &types.TaskFilterSpecByTime{
			TimeType:  types.TaskFilterSpecTimeOptionStartedTime,
			BeginTime: types.NewTime(now.Add(-2 * 24 * time.Hour)),
		},
	}
	collector, err := m.CreateCollectorForTasks(ctx, filter)
	if err != nil {
		return nil, err
	}
	return &taskCollector{
		history:      collector,
		watchChan:    make(chan struct{}),
		updateChan:   make(chan types.TaskInfo, 10),
		eventManager: event.NewManager(c),
		list:         list,
	}, nil
}

// ListTasks implements common.Provider.
func (p *Provider) ListTasks(ctx context.Context, opts common.ListTaskOptions) (common.List[common.Task], error) {
	col, err := p.newTaskCollector(ctx, nil, true)
	if err != nil {
		return common.List[common.Task]{}, err
	}
	defer func() {
		if err := col.history.Destroy(context.Background()); err != nil {
			log.Error(err, "destory task history")
		}
	}()
	var tasks []common.Task
	go col.Collect(ctx)
	// send init task
	select {
	case <-col.watchChan:
		sort.Slice(col.initSend, func(i, j int) bool {
			return col.initSend[i].StartTime.After(*col.initSend[j].StartTime)
		})
		if opts.Size > 0 && len(col.initSend) > opts.Size {
			col.initSend = col.initSend[:opts.Size]
		}
		for _, task := range col.initSend {
			newtask := col.ParseTask(task)
			if opts.ResourceType != "" && newtask.ResourceType != opts.ResourceType {
				continue
			}
			if opts.Phase != "" && newtask.Status.Phase != opts.Phase {
				continue
			}
			tasks = append(tasks, newtask)
		}
	case <-ctx.Done():
		return common.List[common.Task]{}, nil
	}
	return common.PageList(tasks, opts.ListOptions), nil
}

func taskName(info types.TaskInfo) string {
	name := strings.TrimSuffix(info.Name, "_Task")
	switch name {
	case "":
		return info.DescriptionId
	case "Destroy", "Rename":
		return info.Entity.Type + "." + name
	default:
		return name
	}
}

// GetTask implements common.Provider.
func (p *Provider) GetTask(ctx context.Context, id string) (*common.Task, error) {
	return nil, common.ErrUnsupported
}

// RemoveTask implements common.Provider.
func (p *Provider) RemoveTask(ctx context.Context, id string) error {
	return nil
}

// RestartTask implements common.Provider.
func (p *Provider) RestartTask(ctx context.Context, id string) error {
	return nil
}

// StopTask implements common.Provider.
func (p *Provider) StopTask(ctx context.Context, id string) error {
	return nil
}

// 获取虚拟机的资源的事件
// func (p *Provider) listVMResourceEvents(ctx context.Context, ref types.ManagedObjectReference, eventTypes []string) error {
// 	eventManager := event.NewManager(p.Client.VimClient.Client)
// 	return eventManager.Events(ctx, []types.ManagedObjectReference{ref}, 1000, false, false,
// 		func(mor types.ManagedObjectReference, be []types.BaseEvent) error {
// 			for _, e := range be {
// 				if e.GetEvent() == nil {
// 					continue
// 				}
// 				// switch e.(type) {
// 				// case *types.VmPoweredOnEvent:
// 				// case *types.VmStartingEvent:
// 				// case *types.VmReconfiguredEvent:
// 				// case *types.VmCreatedEvent:
// 				// case *types.VmMacAssignedEvent:
// 				// case *types.VmUuidAssignedEvent:
// 				// case *types.VmInstanceUuidAssignedEvent:
// 				// case *types.VmBeingCreatedEvent:
// 				// 	// reconfigure
// 				// }
// 				//types.VmReconfiguredEvent

// 				event, ok := e.(*types.VmBeingCreatedEvent)
// 				if !ok {
// 					continue
// 				}
// 				fmt.Println("[", event.CreatedTime, "]", strings.TrimSpace(event.FullFormattedMessage), "-------")
// 			}
// 			return nil
// 		}, eventTypes...)
// }
