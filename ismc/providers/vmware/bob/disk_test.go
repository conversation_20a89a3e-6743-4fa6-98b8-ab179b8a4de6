package bob

import (
	"context"
	"encoding/json"
	"testing"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
)

func TestGetMetrics(t *testing.T) {
	p := SetupTestProvider(t)
	ctx := context.Background()
	metrics, err := p.ListDiskMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}

	// metrics, err := p.ListVirtualMachineMetrics(ctx, common.ListVirtualMachineMetricsOptions{})
	// if err != nil {
	// 	t.Fatal(err)
	// }
	for _, m := range metrics {
		t.Log(*m)
	}
}

func TestListDisks(t *testing.T) {
	p := SetupTestProvider(t)
	ctx := context.Background()

	got, err := p.GetVirtualMachine(ctx, "vm-5081")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(*got)
}

func TestDeleteDisk(t *testing.T) {
	p := SetupTestProvider(t)
	ctx := context.Background()

	if err := p.DeleteDisk(ctx, "[data-ssd] disks/test-tt/test-disk-1.vmdk", common.DeleteDiskOptions{}); err != nil {
		t.Errorf("Provider.CreateDisk() error = %v", err)
		return
	}
	t.Log("success")
}

func TestCreateDisk(t *testing.T) {
	p := SetupTestProvider(t)
	ctx := context.Background()

	group := "test-tt"

	newdisk := &common.Disk{
		Descripter: common.Descripter{
			Name:          "test-disk-1",
			ResourceGroup: group,
		},
		Size: resource.MustParse("10Gi"),
	}
	if _, err := p.CreateDisk(ctx, newdisk, common.CreateDiskOptions{}); err != nil {
		t.Errorf("Provider.CreateDisk() error = %v", err)
		return
	}
	t.Log(newdisk.ID)
}

func TestGetDisk(t *testing.T) {
	p := SetupTestProvider(t)
	ctx := context.Background()

	disk, err := p.GetDisk(ctx, "[data-ssd] disks/test-tt/test-disk-1.vmdk")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(*disk)
}

func TestProvider_ListDisks(t *testing.T) {
	p := SetupTestProvider(t)
	ctx := context.Background()

	group := "test-tt"

	rg, err := p.GetResourceGroup(ctx, group)
	if err != nil {
		if errors.IsNotFound(err) {
			rg = &common.ResourceGroup{
				Descripter: common.Descripter{
					Name: group,
				},
			}
			if _, err := p.CreateResourceGroup(ctx, rg); err != nil {
				t.Errorf("Provider.CreateResourceGroup() error = %v", err)
				return
			}
		} else {
			t.Errorf("Provider.GetResourceGroup() error = %v", err)
			return
		}
	}
	got, err := p.ListDisks(ctx, common.ListDiskOptions{})
	if err != nil {
		t.Errorf("Provider.ListDisks() error = %v", err)
		return
	}
	jsonbytes, _ := json.MarshalIndent(got, "", "  ")
	t.Logf("Provider.ListDisks() = %s", jsonbytes)

	grouplist, err := p.ListDisks(ctx, common.ListDiskOptions{ResourceGroup: rg.ID})
	if err != nil {
		t.Errorf("Provider.ListDisks() error = %v", err)
		return
	}
	jsonbytes, _ = json.MarshalIndent(grouplist, "", "  ")
	t.Logf("Provider.ListDisks() = %s", jsonbytes)

	for _, disk := range grouplist.Items {
		if err := p.DeleteDisk(ctx, disk.ID, common.DeleteDiskOptions{}); err != nil {
			t.Errorf("Provider.DeleteDisk() error = %v", err)
			return
		}
	}
	newdisk := &common.Disk{
		Descripter: common.Descripter{
			Name:          "test-disk",
			ResourceGroup: group,
		},
		Size: resource.MustParse("10Gi"),
	}
	if _, err := p.CreateDisk(ctx, newdisk, common.CreateDiskOptions{}); err != nil {
		t.Errorf("Provider.CreateDisk() error = %v", err)
		return
	}
	existdisk, err := p.GetDisk(ctx, newdisk.ID)
	if err != nil {
		t.Errorf("Provider.GetDisk() error = %v", err)
		return
	}
	// change disk size
	existdisk.Size = resource.MustParse("20Gi")
	if err := p.UpdateDisk(ctx, existdisk); err != nil {
		t.Errorf("Provider.UpdateDisk() error = %v", err)
		return
	}
	if err := p.DeleteDisk(ctx, existdisk.ID, common.DeleteDiskOptions{}); err != nil {
		t.Errorf("Provider.DeleteDisk() error = %v", err)
		return
	}
}
