package bob

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.QuotaOperation = &Provider{}

// GetQuota implements common.Provider.
func (p *Provider) GetQuota(ctx context.Context) (*common.Quota, error) {
	zones, err := p.ListHosts(ctx, common.ListHostOptions{})
	if err != nil {
		return nil, err
	}
	quota := common.Quota{
		Hard: map[common.ResourceType]resource.Quantity{},
	}
	for _, zone := range zones.Items {
		AddResourceList(quota.Hard, zone.Status.Capacity)
	}
	return &quota, nil
}

func AddResourceList(total, add map[common.ResourceType]resource.Quantity) {
	for k, v := range add {
		if val, ok := total[k]; ok {
			val.Add(v)
			total[k] = val
		} else {
			total[k] = v
		}
	}
}
