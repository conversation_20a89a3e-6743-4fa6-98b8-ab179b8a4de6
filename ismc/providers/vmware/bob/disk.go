package bob

import (
	"context"
	"fmt"
	"path"
	"path/filepath"
	"slices"
	"strings"
	"time"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"golang.org/x/exp/rand"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

const (
	TypeDatacenter = "Datacenter"
	TypeDatastore  = "Datastore"
	TypeDisk       = "Disk"
)

const (
	DiskClassDefault = "default"
)

/*
磁盘在 vmware 上没有tag可以标记
为了区分不同的资源组，使用不同的文件夹来存放磁盘文件
创建的虚拟机也会放置到对应的资源组文件夹下。
*/
var _ common.DiskOperation = &Provider{}

func (p *Provider) listDiskFiles(ctx context.Context, zone string) ([]vmware.DatastoreFile, error) {
	var limiteddatastores []mo.Datastore
	if zone != "" {
		listDatastores, err := p.listDatastores(ctx, zone)
		if err != nil {
			return nil, err
		}
		limiteddatastores = listDatastores
	}
	filterFunc := func(datastorefile vmware.DatastoreFile) bool {
		// is disk file
		if !strings.HasSuffix(datastorefile.Value, ".vmdk") {
			return false
		}
		// limit by zone
		if len(limiteddatastores) > 0 && !slices.ContainsFunc(limiteddatastores, func(ds mo.Datastore) bool {
			return ds.Self.Value == datastorefile.DataStore
		}) {
			return false
		}
		return true
	}
	disklist, err := vmware.CacheToList(p.Cache, vmware.TypeDatastoreFile, filterFunc)
	if err != nil {
		return nil, err
	}
	return disklist, nil
}

func (p *Provider) listdisks(ctx context.Context, group, zone string) ([]common.Disk, error) {
	disklist, err := p.listDiskFiles(ctx, zone)
	if err != nil {
		return nil, err
	}
	mounts, err := p.listMountsInfo(ctx)
	if err != nil {
		log.V(5).Error(err, "failed to list mounts info")
	}
	// filter disks by zone and resource group
	// zone = HostSystem
	// resourceGroup = Folder
	disks := make([]common.Disk, 0, len(disklist))
	for _, item := range disklist {
		if !strings.HasSuffix(item.Value, ".vmdk") {
			continue
		}
		dspath := DSPathFromString(item.Value)
		// ignore content library
		if strings.HasPrefix(dspath.Path, "contentlib-") {
			continue
		}
		// ignore vCenter Server
		if strings.HasPrefix(dspath.Path, "VMware vCenter Server/") {
			continue
		}
		ds := mo.Datastore{
			ManagedEntity: mo.ManagedEntity{
				ExtensibleManagedObject: mo.ExtensibleManagedObject{
					Self: types.ManagedObjectReference{
						Type:  vmware.TypeDatastore,
						Value: item.DataStore,
					},
				},
				Name: item.DataStore,
			},
		}
		// complete more information from cache
		if dsp := p.getdsFromCache(item.DataStore); dsp != nil {
			ds = *dsp
		}
		disk := p.datastorefileToDisk(item, dspath, ds, mounts)
		// skip if disk is not in the specified resource group
		if group != "" && disk.ResourceGroup != group {
			continue
		}
		disks = append(disks, disk)
	}
	return disks, nil
}

func (p *Provider) datastorefileToDisk(item vmware.DatastoreFile, dspath object.DatastorePath, ds mo.Datastore, mounts map[string][]string) common.Disk {
	disk := common.Disk{
		Descripter: common.Descripter{
			ID:                vmware.EncodeID(item.Value),
			Name:              path.Base(dspath.Path),
			ResourceGroup:     GroupFromDsPath(dspath.Path),
			CreationTimestamp: ptr.Deref(item.FileInfo.Modification, time.Time{}),
			Annotations:       map[string]string{"filepath": dspath.Path},
		},
		Zone:        getDsZone(ds),
		DiskCluster: ds.Name,
		DiskClass:   getDsClass(ds),
		Status: common.DiskStatus{
			Phase:          "available",
			Used:           *resource.NewQuantity(int64(item.FileInfo.FileSize), resource.BinarySI),
			AvailableZones: p.getDsAvailableZones(ds),
		},
	}
	if diskinfo := item.VmDiskFileInfo; diskinfo != nil {
		disk.Size = *resource.NewQuantity(int64(diskinfo.CapacityKb)*1024, resource.BinarySI)
		disk.Status.Device = diskinfo.DiskType
	}
	if mountvms, exist := mounts[item.Value]; exist {
		disk.Status.Phase = "in-use"
		mounts := make([]common.DiskMountInfo, len(mountvms))
		for i, mount := range mountvms {
			mounts[i] = common.DiskMountInfo{VirtualMachine: mount}
		}
		disk.Status.Mounts = mounts
	}
	return disk
}

// 获取所有虚拟机挂载的磁盘ID
func (p *Provider) listMountsInfo(ctx context.Context) (map[string][]string, error) {
	mounts := make(map[string][]string)
	vms, err := p.listvms(ctx, "config", "name", "layout.disk")
	if err != nil {
		return nil, err
	}
	for _, vm := range vms {
		if vm.Layout == nil {
			continue
		}
		if vm.Config.BootOptions != nil && len(vm.Config.BootOptions.BootOrder) > 0 {
			for _, device := range vm.Config.BootOptions.BootOrder {
				if disk, ok := device.(*types.VirtualMachineBootOptionsBootableDiskDevice); ok {
					fmt.Printf("Boot disk key: %d\n", disk.DeviceKey)
					break
				}
			}
		}
		for _, disk := range vm.Layout.Disk {
			for _, fi := range disk.DiskFile {
				mounts[fi] = append(mounts[fi], vm.Name)
			}
		}
	}
	return mounts, nil
}

// ListDisks implements ismc.DiskOperation.
func (p *Provider) ListDisks(ctx context.Context, options common.ListDiskOptions) (common.List[common.Disk], error) {
	if options.VirtualMachine != "" {
		disks, err := p.listVirtualMachineDisks(ctx, options.VirtualMachine)
		if err != nil {
			return common.List[common.Disk]{}, vmware.IgnoreNotFound(err)
		}
		return common.PageList(disks, options.ListOptions), nil
	}
	list := common.List[common.Disk]{Items: []common.Disk{}}
	items, err := p.listdisks(ctx, options.ResourceGroup, options.Zone)
	if err != nil {
		return list, err
	}
	return common.PageList(items, options.ListOptions), nil
}

func DsPath(dsname string, elem ...string) *object.DatastorePath {
	return (&object.DatastorePath{Datastore: dsname, Path: path.Join(elem...)})
}

const DefautlDisksPath = "disks"

func newDiskDSPath(ds mo.Datastore, diskname string, resourceGroup string) (*object.DatastorePath, string) {
	if diskname == "" {
		diskname = "noname-disk" // default disk name
	}
	if !strings.HasSuffix(diskname, ".vmdk") {
		diskname += ".vmdk"
	}
	return GroupedDsPath(ds.Name, resourceGroup, diskname), diskname
}

func GroupedDsPath(dsname string, group string, elem ...string) *object.DatastorePath {
	if group == "" {
		return DsPath(dsname, append([]string{DefautlDisksPath}, elem...)...)
	}
	return DsPath(dsname, append([]string{DefautlDisksPath, group}, elem...)...)
}

func GroupFromDsPath(path string) string {
	dspath := object.DatastorePath{}
	if !dspath.FromString(path) {
		return ""
	}
	paths := strings.Split(dspath.Path, "/")
	if len(paths) < 2 {
		return ""
	}
	if paths[0] != DefautlDisksPath {
		return ""
	}
	return paths[1]
}

func (p *Provider) GetFileManager(ds string) *object.DatastoreFileManager {
	dsref := object.NewDatastore(p.Client.VimClient.Client, types.ManagedObjectReference{Type: TypeDatastore, Value: ds})
	return dsref.NewFileManager(p.DC, true)
}

// CreateDisk implements ismc.DiskOperation.
func (p *Provider) CreateDisk(ctx context.Context, disk *common.Disk, options common.CreateDiskOptions) (*common.Descripter, error) {
	resourceGroup := disk.ResourceGroup
	// check resource group exists
	if err := p.Groups.CheckGroupExists(ctx, resourceGroup); err != nil {
		return nil, err
	}
	if disk.Name == "" {
		return nil, errors.NewBadRequest("disk name is required")
	}
	var datastore *mo.Datastore
	if disk.DiskCluster != "" {
		thisds, err := p.getds(ctx, disk.DiskCluster)
		if err != nil {
			return nil, errors.NewBadRequest(fmt.Sprintf("datastore %s: %s", disk.DiskCluster, err.Error()))
		}
		datastore = thisds
	} else {
		datastores, err := p.listDatastores(ctx, disk.Zone)
		if err != nil {
			return nil, err
		}
		// filter datastores by disk class
		if disk.DiskClass != "" {
			datastores = slices.DeleteFunc(datastores, func(ds mo.Datastore) bool {
				dsclass := getDsClass(ds)
				return dsclass != "" && dsclass != disk.DiskClass
			})
		}
		if len(datastores) == 0 {
			return nil, errors.NewBadRequest("no datastore available")
		}
		randdatastore := datastores[rand.Intn(len(datastores))]
		datastore = &randdatastore
	}
	log.Info("CreateDisk", "datastore", datastore.Name, "disk", disk.Name)
	return p.createDisk(ctx, *datastore, disk)
}

func (p *Provider) createDisk(ctx context.Context, ds mo.Datastore, disk *common.Disk) (*common.Descripter, error) {
	target, diskname := newDiskDSPath(ds, disk.Name, disk.ResourceGroup)
	// make parent directory
	dcref := p.DC
	if err := object.
		NewFileManager(p.Client.VimClient.Client).
		MakeDirectory(ctx,
			DsPath(target.Datastore, filepath.Dir(target.Path)).String(), dcref, true); err != nil {
		return nil, err
	}
	// create disk
	diskspec := &types.FileBackedVirtualDiskSpec{
		CapacityKb: int64(disk.Size.Value()) / 1024,
		VirtualDiskSpec: types.VirtualDiskSpec{
			DiskType:    string(types.VirtualDiskTypeThin),
			AdapterType: string(types.VirtualDiskAdapterTypeLsiLogic),
		},
	}
	task, err := object.
		NewVirtualDiskManager(p.Client.VimClient.Client).
		CreateVirtualDisk(ctx, target.String(), dcref, diskspec)
	if err != nil {
		return nil, err
	}
	result, err := task.WaitForResult(ctx)
	if err != nil {
		if vmware.IsTaskFileAlreadyExistsError(err) {
			return nil, errors.NewAlreadyExists("disk", disk.Name)
		}
		return nil, err
	}
	if result.Error != nil {
		return nil, errors.NewBadRequest(result.Error.LocalizedMessage)
	}
	diskID := vmware.EncodeID(target.String())
	// fill disk info
	disk.ID = diskID
	disk.DiskCluster = ds.Reference().Value
	disk.Zone = getDsZone(ds)

	dsc := &common.Descripter{
		ID:            diskID,
		Name:          diskname,
		ResourceGroup: disk.ResourceGroup,
	}
	return dsc, nil
}

func getDsZone(ds mo.Datastore) string {
	// if a disk available in multiple hosts, return empty
	if len(ds.Host) != 1 {
		return ""
	}
	return ds.Host[0].Key.Value
}

func (p *Provider) getDsAvailableZones(ds mo.Datastore) []common.ObjectRef {
	ret := make([]common.ObjectRef, 0, len(ds.Host))
	for _, host := range ds.Host {
		zoneref := common.ObjectRef{
			ID: host.Key.Value,
		}
		if host := p.getHostFromCache(host.Key.Value); host != nil {
			zoneref.Name = host.Name
		}
		ret = append(ret, zoneref)
	}
	return ret
}

func getDsClass(ds mo.Datastore) string {
	return ds.Summary.Type
}

// DeleteDisk implements ismc.DiskOperation.
func (p *Provider) DeleteDisk(ctx context.Context, id string, options common.DeleteDiskOptions) error {
	diskID := vmware.DecodeID(id)
	vdm := object.NewVirtualDiskManager(p.Client.VimClient.Client)
	task, err := vdm.DeleteVirtualDisk(ctx, diskID, p.DC)
	if err != nil {
		return vmware.IgnoreNotFound(err)
	}
	if err := task.Wait(ctx); err != nil {
		if vmware.IsTaskFileNotFoundError(err) {
			return errors.NewNotFound("disk", id)
		}
		return err
	}
	// remove in cache
	p.Cache.Remove(types.ManagedObjectReference{Type: vmware.TypeDatastoreFile, Value: diskID})
	return nil
}

func (p *Provider) getDatastoreFile(_ context.Context, filepath string) (*vmware.DatastoreFile, error) {
	dsfile, err := vmware.CacheToGet[*vmware.DatastoreFile](p.Cache, types.ManagedObjectReference{
		Type: vmware.TypeDatastoreFile, Value: filepath,
	})
	if err != nil {
		// TODO: fallback to search datastore
		return nil, err
	}
	return dsfile, nil
}

// GetDisk implements ismc.DiskOperation.
func (p *Provider) GetDisk(ctx context.Context, id string) (*common.Disk, error) {
	dsfile, err := p.getDatastoreFile(ctx, vmware.DecodeID(id))
	if err != nil {
		return nil, err
	}
	dspath := DSPathFromString(dsfile.Value)
	ds := mo.Datastore{
		ManagedEntity: mo.ManagedEntity{
			ExtensibleManagedObject: mo.ExtensibleManagedObject{
				Self: types.ManagedObjectReference{
					Type:  vmware.TypeDatastore,
					Value: dsfile.DataStore,
				},
			},
			Name: dspath.Datastore,
		},
	}
	// complete more information from cache
	if dsp := p.getdsFromCache(dsfile.DataStore); dsp != nil {
		ds = *dsp
	}
	mounts, err := p.listMountsInfo(ctx)
	if err != nil {
		log.V(5).Error(err, "failed to list mounts info")
	}
	disk := p.datastorefileToDisk(*dsfile, dspath, ds, mounts)
	return &disk, nil
}

// UpdateDisk implements ismc.DiskOperation.
func (p *Provider) UpdateDisk(ctx context.Context, disk *common.Disk) error {
	existsdisk, err := p.GetDisk(ctx, disk.ID)
	if err != nil {
		return err
	}
	ds := existsdisk.DiskCluster
	if cmp := disk.Size.Cmp(existsdisk.Size); cmp != 0 {
		if cmp < 0 {
			return errors.NewBadRequest("disk size can not be shrinked")
		}
		// resize disk
		if err := p.resizeDisk(ctx, ds, vmware.DecodeID(disk.ID), existsdisk.Size, disk.Size); err != nil {
			return err
		}
	}
	return nil
}

// ResizeDisk implements common.DiskOperation.
func (p *Provider) ResizeDisk(ctx context.Context, id string, size resource.Quantity, options common.ResizeDiskOptions) error {
	existsdisk, err := p.GetDisk(ctx, id)
	if err != nil {
		return err
	}
	ds := existsdisk.DiskCluster
	return p.resizeDisk(ctx, ds, vmware.DecodeID(id), existsdisk.Size, size)
}

// 使用原始ID
func (p *Provider) resizeDisk(ctx context.Context, ds, id string, oldsize, newsize resource.Quantity) error {
	switch cmp := newsize.Cmp(oldsize); cmp {
	case 0:
		return nil
	case -1:
		return errors.NewBadRequest("disk size can not be shrinked")
	}
	// resize disk
	fm := p.GetFileManager(ds)
	newsizekb := int64(newsize.Value()) / 1024
	task, err := fm.VirtualDiskManager.ExtendVirtualDisk(ctx, id, fm.Datacenter, newsizekb, nil)
	if err != nil {
		return err
	}
	return task.Wait(ctx)
}

// ReInitalizeDisk implements common.DiskOperation.
func (p *Provider) ReInitalizeDisk(ctx context.Context, id string) error {
	return nil
}
