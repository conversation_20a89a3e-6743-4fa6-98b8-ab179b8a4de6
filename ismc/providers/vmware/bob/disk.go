package bob

import (
	"context"
	"fmt"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/task"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"golang.org/x/exp/rand"
	"golang.org/x/sync/errgroup"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

const (
	TypeDatacenter = "Datacenter"
	TypeDatastore  = "Datastore"
	TypeDisk       = "Disk"
)

const (
	DiskClassDefault = "default"
)

/*
磁盘在 vmware 上没有tag可以标记
为了区分不同的资源组，使用不同的文件夹来存放磁盘文件
创建的虚拟机也会放置到对应的资源组文件夹下。
*/
var _ common.DiskOperation = &Provider{}

const EnableDiskCache = true

func (p *Provider) listOriginDisksWithCache(zone, resourceGroup string) ([]DiskWrapper, error) {
	if p.diskCache == nil {
		return p.listOrigindisks(context.Background(), zone, resourceGroup)
	}
	ret := make([]DiskWrapper, 0)
	cachedlist := p.diskCache.List()
	for _, item := range cachedlist {
		if zone != "" && item.Zone != zone {
			continue
		}
		if resourceGroup != "" && item.ResourceGroup != resourceGroup {
			continue
		}
		ret = append(ret, item)
	}
	return ret, nil
}

func (p *Provider) listAllOriginDisks(ctx context.Context) ([]DiskWrapper, error) {
	return p.listOrigindisks(ctx, "", "")
}

// 获取原始磁盘文件信息
func (p *Provider) listOrigindisks(ctx context.Context, zone, resourceGroup string) ([]DiskWrapper, error) {
	datastores, err := p.listDatastores(ctx, zone)
	if err != nil {
		return nil, err
	}
	list := base.SafeSlice[DiskWrapper]{}
	eg := errgroup.Group{}
	for _, ds := range datastores {
		eg.Go(func() error {
			fullsearchpath := GroupedDsPath(ds.Name, resourceGroup).Path
			vmdisks, err := p.searchDatastoreDisks(ctx, ds, fullsearchpath, true, true, "*.vmdk")
			if err != nil {
				return err
			}
			list.Append(vmdisks...)
			return nil
		})
	}
	_ = eg.Wait()
	return list.Get(), nil
}

func (p *Provider) listdisks(ctx context.Context, zone, resourceGroup string) ([]common.Disk, error) {
	originDisks, err := p.listOriginDisksWithCache(zone, resourceGroup)
	if err != nil {
		return nil, err
	}
	mounts, err := p.listMountsInfo(ctx)
	if err != nil {
		return nil, err
	}
	return parseVmwareDisksToDisks(originDisks, mounts), nil
}

// 获取所有虚拟机挂载的磁盘ID
func (p *Provider) listMountsInfo(ctx context.Context) (map[string][]string, error) {
	startTime := time.Now()
	defer func() {
		log.Info("listMountsInfo", "cost", time.Since(startTime))
	}()
	mounts := make(map[string][]string)
	vms, err := p.listvms(ctx, "config", "name", "layout.disk")
	if err != nil {
		return nil, err
	}
	for _, vm := range vms {
		if vm.Layout == nil {
			continue
		}
		if vm.Config.BootOptions != nil && len(vm.Config.BootOptions.BootOrder) > 0 {
			for _, device := range vm.Config.BootOptions.BootOrder {
				if disk, ok := device.(*types.VirtualMachineBootOptionsBootableDiskDevice); ok {
					fmt.Printf("Boot disk key: %d\n", disk.DeviceKey)
					break
				}
			}
		}
		for _, disk := range vm.Layout.Disk {
			for _, fi := range disk.DiskFile {
				mounts[fi] = append(mounts[fi], vm.Name)
			}
		}
	}
	return mounts, nil
}

// ListDisks implements ismc.DiskOperation.
func (p *Provider) ListDisks(ctx context.Context, options common.ListDiskOptions) (common.List[common.Disk], error) {
	if options.VirtualMachine != "" {
		disks, err := p.listVirtualMachineDisks(ctx, options.VirtualMachine)
		if err != nil {
			return common.List[common.Disk]{}, vmware.IgnoreNotFound(err)
		}
		return common.PageList(disks, options.ListOptions), nil
	}
	list := common.List[common.Disk]{Items: []common.Disk{}}
	items, err := p.listdisks(ctx, options.Zone, options.ResourceGroup)
	if err != nil {
		return list, err
	}
	return common.PageList(items, options.ListOptions), nil
}

func DsPath(dsname string, elem ...string) *object.DatastorePath {
	return (&object.DatastorePath{Datastore: dsname, Path: path.Join(elem...)})
}

const DefautlDisksPath = "disks"

func GroupedDsPath(dsname string, group string, elem ...string) *object.DatastorePath {
	if group == "" {
		return DsPath(dsname, elem...)
	}
	return DsPath(dsname, append([]string{DefautlDisksPath, group}, elem...)...)
}

func GroupFromDsPath(path string) string {
	dspath := object.DatastorePath{}
	if !dspath.FromString(path) {
		return ""
	}
	paths := strings.Split(dspath.Path, "/")
	if len(paths) < 2 {
		return ""
	}
	if paths[0] != DefautlDisksPath {
		return ""
	}
	return paths[1]
}

func (p *Provider) GetFileManager(ds string) *object.DatastoreFileManager {
	dsref := object.NewDatastore(p.Client.VimClient.Client, types.ManagedObjectReference{Type: TypeDatastore, Value: ds})
	return dsref.NewFileManager(p.DC, true)
}

// CreateDisk implements ismc.DiskOperation.
func (p *Provider) CreateDisk(ctx context.Context, disk *common.Disk, options common.CreateDiskOptions) (*common.Descripter, error) {
	resourceGroup := disk.ResourceGroup
	// check resource group exists
	if err := p.Groups.EnsureGroup(ctx, resourceGroup); err != nil {
		return nil, err
	}

	if disk.Name == "" {
		return nil, errors.NewBadRequest("disk name is required")
	}
	datastores, err := p.listDatastores(ctx, disk.Zone)
	if err != nil {
		return nil, err
	}
	// filter datastores by disk class
	if disk.DiskClass != "" {
		// TODO: filter datastores by disk class
	}

	if len(datastores) == 0 {
		return nil, errors.NewBadRequest("no datastore available")
	}
	dt := datastores[rand.Intn(len(datastores))]
	log.Info("CreateDisk", "datastore", dt.Name, "disk", disk.Name)
	return p.createDisk(ctx, dt, disk)
}

func (p *Provider) createDisk(ctx context.Context, ds mo.Datastore, disk *common.Disk) (*common.Descripter, error) {
	diskname := disk.Name
	if diskname == "" {
		return nil, errors.NewBadRequest("disk name is required")
	}
	if !strings.HasSuffix(diskname, ".vmdk") {
		diskname += ".vmdk"
	}
	target := GroupedDsPath(ds.Name, disk.ResourceGroup, diskname)
	// make parent directory
	dcref := p.DC
	if err := object.
		NewFileManager(p.Client.VimClient.Client).
		MakeDirectory(ctx,
			DsPath(target.Datastore, filepath.Dir(target.Path)).String(), dcref, true); err != nil {
		return nil, err
	}
	// create disk
	diskspec := &types.FileBackedVirtualDiskSpec{
		CapacityKb: int64(disk.Size.Value()) / 1024,
		VirtualDiskSpec: types.VirtualDiskSpec{
			DiskType:    string(types.VirtualDiskTypeThin),
			AdapterType: string(types.VirtualDiskAdapterTypeLsiLogic),
		},
	}
	task, err := object.
		NewVirtualDiskManager(p.Client.VimClient.Client).
		CreateVirtualDisk(ctx, target.String(), dcref, diskspec)
	if err != nil {
		return nil, err
	}
	result, err := task.WaitForResult(ctx)
	if err != nil {
		if isTaskFileAlreadyExistsError(err) {
			return nil, errors.NewAlreadyExists("disk", disk.Name)
		}
		return nil, err
	}
	if result.Error != nil {
		return nil, errors.NewBadRequest(result.Error.LocalizedMessage)
	}
	diskID := vmware.EncodeID(target.String())
	// fill disk info
	disk.ID = diskID
	disk.DiskCluster = ds.Reference().Value
	disk.Zone = getDsZone(ds)

	dsc := &common.Descripter{
		ID:            diskID,
		Name:          diskname,
		ResourceGroup: disk.ResourceGroup,
	}
	return dsc, nil
}

func getDsZone(ds mo.Datastore) string {
	for _, host := range ds.Host {
		if host.Key.Value != "" {
			return host.Key.Value
		}
	}
	return ""
}

func getDsClass(ds mo.Datastore) string {
	return ds.Summary.Type
}

func isTaskFileNotFounError(err error) bool {
	return isTaskFaultType[*types.FileNotFound](err)
}

func isTaskFileAlreadyExistsError(err error) bool {
	return isTaskFaultType[*types.FileAlreadyExists](err)
}

func isTaskFaultType[T types.BaseMethodFault](err error) bool {
	if taskerr, ok := err.(task.Error); ok {
		_, ok := taskerr.Fault().(T)
		return ok
	}
	return false
}

func (p *Provider) listDatastores(ctx context.Context, optionalzone string) ([]mo.Datastore, error) {
	fields := []string{"name", "summary", "host"}
	if optionalzone != "" {
		host, err := p.getHost(ctx, optionalzone, []string{"name", "datastore"})
		if err != nil {
			return nil, err
		}
		return vmware.FindReferenceSliceInto[types.ManagedObjectReference, mo.Datastore](
			ctx, p.Client.VimClient, host.Datastore, fields)
	}
	refs, err := p.Client.Finder.DatastoreList(ctx, fmt.Sprintf("/%s/datastore/*", p.DC.Name()))
	if err != nil {
		return nil, err
	}
	return vmware.FindReferenceSliceInto[*object.Datastore, mo.Datastore](ctx, p.Client.VimClient, refs, fields)
}

// DeleteDisk implements ismc.DiskOperation.
func (p *Provider) DeleteDisk(ctx context.Context, id string, options common.DeleteDiskOptions) error {
	diskID := vmware.DecodeID(id)
	dspath := object.DatastorePath{}
	if !dspath.FromString(diskID) {
		return errors.NewBadRequest(fmt.Sprintf("invalid disk name %s", diskID))
	}
	dcref := p.DC
	vdm := object.NewVirtualDiskManager(p.Client.VimClient.Client)
	infos, err := vdm.QueryVirtualDiskInfo(ctx, diskID, dcref, false)
	if err != nil {
		if vmware.IsNotFound(err) {
			return nil
		}
		return err
	}
	if len(infos) == 0 {
		return nil
	}
	task, err := vdm.DeleteVirtualDisk(ctx, diskID, dcref)
	if err != nil {
		return vmware.IgnoreNotFound(err)
	}
	if err := task.Wait(ctx); err != nil {
		return err
	}
	if p.diskCache != nil {
		p.diskCache.Remove(diskID)
	}
	return nil
}

// 该id使用原始id,路径的方式
func (p *Provider) getDisk(ctx context.Context, id string, ignoreVC bool) ([]DiskWrapper, error) {
	dspath := object.DatastorePath{}
	if !dspath.FromString(id) {
		return nil, errors.NewBadRequest(fmt.Sprintf("invalid disk name %s", id))
	}
	ref, err := p.Client.Finder.Datastore(ctx, fmt.Sprintf("/%s/datastore/%s", p.DC.Name(), dspath.Datastore))
	if err != nil {
		return nil, err
	}
	datastore, err := vmware.FindReferenceInto[*object.Datastore, mo.Datastore](ctx, p.Client.VimClient, ref, []string{"name", "host"})
	if err != nil {
		return nil, err
	}
	dir, file := path.Split(dspath.Path)
	return p.searchDatastoreDisks(ctx, datastore, dir, false, ignoreVC, file)
}

// GetDisk implements ismc.DiskOperation.
func (p *Provider) GetDisk(ctx context.Context, id string) (*common.Disk, error) {
	list, err := p.getDisk(ctx, vmware.DecodeID(id), true)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, errors.NewNotFound("disk", id)
	}
	mounts, err := p.listMountsInfo(ctx)
	if err != nil {
		return nil, err
	}
	disk := parseVmwareDisksToDisks(list, mounts)[0]
	return &disk, nil
}

type DiskWrapper struct {
	*types.VmDiskFileInfo
	Folder        string
	Zone          string
	DiskCluster   string
	DiskClass     string
	ResourceGroup string
}

var _ common.ID = &DiskWrapper{}

func (d DiskWrapper) GetID() string {
	diskid := d.Folder + " " + d.Path
	if strings.HasSuffix(d.Folder, "/") {
		diskid = d.Folder + d.Path
	}
	return diskid
}

func parseVmwareDisksToDisks(vmdisks []DiskWrapper, mounts map[string][]string) []common.Disk {
	var disks []common.Disk
	for _, vmd := range vmdisks {
		diskid := vmd.Folder + " " + vmd.Path
		if strings.HasSuffix(vmd.Folder, "/") {
			diskid = vmd.Folder + vmd.Path
		}
		disk := common.Disk{
			Descripter: common.Descripter{
				ID:            vmware.EncodeID(diskid),
				Name:          vmd.Path,
				ResourceGroup: vmd.ResourceGroup,
			},
			Zone:        vmd.Zone,
			Size:        *resource.NewQuantity(int64(vmd.CapacityKb)*1024, resource.BinarySI),
			DiskCluster: vmd.DiskCluster,
			DiskClass:   vmd.DiskClass,
			Status: common.DiskStatus{
				Device: vmd.DiskType,
				Used:   *resource.NewQuantity(int64(vmd.FileSize), resource.BinarySI),
			},
		}
		var creationTimestamp time.Time
		if vmd.Modification != nil {
			creationTimestamp = *vmd.Modification
		}
		disk.CreationTimestamp = creationTimestamp
		disk.Status.Phase = "available"
		if mountvms, exist := mounts[diskid]; exist {
			disk.Status.Phase = "in-use"
			mounts := make([]common.DiskMountInfo, len(mountvms))
			for i, mount := range mountvms {
				mounts[i] = common.DiskMountInfo{
					VirtualMachine: mount,
				}
			}
			disk.Status.Mounts = mounts
		}
		disks = append(disks, disk)
	}
	return disks
}

// getDatastoreFileInfo 获取数据存储中文件的信息
func (p *Provider) getDatastoreFileInfo(ctx context.Context, filepath string) (*types.FileInfo, error) {
	dpath := new(object.DatastorePath)
	if !dpath.FromString(filepath) {
		return nil, fmt.Errorf("invalid path name %s", filepath)
	}
	dstore, err := p.Client.Finder.Datastore(ctx, fmt.Sprintf("/%s/datastore/%s", p.DC.Name(), dpath.Datastore))
	if err != nil {
		return nil, err
	}
	base, err := dstore.Stat(ctx, dpath.Path)
	if err != nil {
		return nil, err
	}
	return base.GetFileInfo(), nil
}

func (p *Provider) searchDatastoreDisks(ctx context.Context, ds mo.Datastore, searchpath string, recursive, ignoreVC bool, patterns ...string) ([]DiskWrapper, error) {
	startTime := time.Now()
	defer func() {
		log.FromContext(ctx).V(5).Info("searchDatastoreDisks", "cost", time.Since(startTime), "datastore", ds.Name, "searchpath", searchpath, "recursive", recursive, "patterns", patterns)
	}()
	var disks []DiskWrapper
	browser, err := object.NewDatastore(p.Client.VimClient.Client, ds.Reference()).Browser(ctx)
	if err != nil {
		return nil, err
	}
	spec := &types.HostDatastoreBrowserSearchSpec{
		Query: []types.BaseFileQuery{
			&types.VmDiskFileQuery{
				Details: &types.VmDiskFileQueryFlags{
					DiskType:        true,
					CapacityKb:      true,
					HardwareVersion: true,
					DiskExtents:     ptr.To(true),
					Thin:            ptr.To(true),
					Encryption:      ptr.To(true),
				},
			},
		},
		Details: &types.FileQueryFlags{
			FileType:     true,
			FileSize:     true,
			Modification: true,
			FileOwner:    ptr.To(true),
		},
		MatchPattern: patterns,
	}
	fullsearchpath := DsPath(ds.Name, searchpath).String()
	var t *object.Task
	if recursive {
		newtask, err := browser.SearchDatastoreSubFolders(ctx, fullsearchpath, spec)
		if err != nil {
			return nil, err
		}
		t = newtask
	} else {
		newtask, err := browser.SearchDatastore(ctx, fullsearchpath, spec)
		if err != nil {
			return nil, err
		}
		t = newtask
	}
	result, err := t.WaitForResult(ctx)
	if err != nil {
		if isTaskFileNotFounError(err) {
			return nil, nil
		}
		return nil, err
	}
	if result.Error != nil {
		return nil, errors.NewBadRequest(result.Error.LocalizedMessage)
	}
	var resultlist []types.HostDatastoreBrowserSearchResults
	switch typedresult := result.Result.(type) {
	case types.HostDatastoreBrowserSearchResults:
		resultlist = append(resultlist, typedresult)
	case types.ArrayOfHostDatastoreBrowserSearchResults:
		resultlist = typedresult.HostDatastoreBrowserSearchResults
	}
	for _, r := range resultlist {
		// ignore contentlib
		if strings.HasPrefix(r.FolderPath, DsPath(ds.Name, "contentlib-").String()) && ignoreVC {
			continue
		}
		// ignore vCenter Server
		if strings.HasPrefix(r.FolderPath, DsPath(ds.Name, "VMware vCenter Server/").String()) && ignoreVC {
			continue
		}
		for _, f := range r.File {
			switch file := f.(type) {
			case *types.VmDiskFileInfo:
				disks = append(disks, DiskWrapper{
					VmDiskFileInfo: file,
					Folder:         r.FolderPath,
					Zone:           getDsZone(ds),
					DiskCluster:    ds.Name,
					DiskClass:      getDsClass(ds),
					ResourceGroup:  GroupFromDsPath(r.FolderPath),
				})
			}
		}
	}
	return disks, nil
}

// UpdateDisk implements ismc.DiskOperation.
func (p *Provider) UpdateDisk(ctx context.Context, disk *common.Disk) error {
	existsdisk, err := p.GetDisk(ctx, disk.ID)
	if err != nil {
		return err
	}
	ds := existsdisk.DiskCluster
	if cmp := disk.Size.Cmp(existsdisk.Size); cmp != 0 {
		if cmp < 0 {
			return errors.NewBadRequest("disk size can not be shrinked")
		}
		// resize disk
		if err := p.resizeDisk(ctx, ds, vmware.DecodeID(disk.ID), existsdisk.Size, disk.Size); err != nil {
			return err
		}
	}
	return nil
}

// ResizeDisk implements common.DiskOperation.
func (p *Provider) ResizeDisk(ctx context.Context, id string, size resource.Quantity, options common.ResizeDiskOptions) error {
	existsdisk, err := p.GetDisk(ctx, id)
	if err != nil {
		return err
	}
	ds := existsdisk.DiskCluster
	return p.resizeDisk(ctx, ds, vmware.DecodeID(id), existsdisk.Size, size)
}

// 使用原始ID
func (p *Provider) resizeDisk(ctx context.Context, ds, id string, oldsize, newsize resource.Quantity) error {
	switch cmp := newsize.Cmp(oldsize); cmp {
	case 0:
		return nil
	case -1:
		return errors.NewBadRequest("disk size can not be shrinked")
	}
	// resize disk
	fm := p.GetFileManager(ds)
	newsizekb := int64(newsize.Value()) / 1024
	task, err := fm.VirtualDiskManager.ExtendVirtualDisk(ctx, id, fm.Datacenter, newsizekb, nil)
	if err != nil {
		return err
	}
	return task.Wait(ctx)
}

// ReInitalizeDisk implements common.DiskOperation.
func (p *Provider) ReInitalizeDisk(ctx context.Context, id string) error {
	return nil
}
