package bob

import (
	"context"
	"io/fs"
	"sync"
	"time"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/performance"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/vim25/methods"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/cache"
	"xiaoshiai.cn/core/ismc/providers/vmware"
	"xiaoshiai.cn/core/ismc/providers/vmware/console"
)

var _ common.Provider = &Provider{}

type Provider struct {
	// Datacenter  types.ManagedObjectReference // default region
	DC          *object.Datacenter
	Client      *vmware.Client
	Groups      *ResourceGroupManager
	Contents    *ContentLibray
	PerfManager *performance.Manager

	ConsoleStatisFS fs.FS // static files for console

	BuiltInInstanceTypes []common.InstanceType

	tagsCache   common.KVCache[types.ManagedObjectReference, []tags.Tag]
	diskCache   *cache.ExpiredCache[DiskWrapper]
	fieldsCache *CustomFieldsManager

	hostCapCache common.KVCache[types.ManagedObjectReference, types.HostHardwareSummary]
}

func NewProvider(ctx context.Context, client *vmware.Client, datacentername string) (*Provider, error) {
	datacenterRef, err := client.Finder.Datacenter(ctx, datacentername)
	if err != nil {
		return nil, err
	}
	group, err := NewResourceGroupManager(client, datacenterRef)
	if err != nil {
		return nil, err
	}
	contents, err := NewCachedContentLibrary(client)
	if err != nil {
		return nil, err
	}
	tagCache, err := newTagsCache(ctx, client, group.defaultCategoryID)
	if err != nil {
		return nil, err
	}
	fieldsCache, err := NewCachedCustomFieldsManager(ctx, client)
	if err != nil {
		return nil, err
	}
	p := &Provider{
		Client:               client,
		DC:                   datacenterRef,
		Groups:               group,
		Contents:             contents,
		ConsoleStatisFS:      console.GetStaticFS(),
		PerfManager:          performance.NewManager(client.VimClient.Client),
		BuiltInInstanceTypes: BuiltInInstanceTypes,
		tagsCache:            tagCache,
		fieldsCache:          fieldsCache,
	}
	hostCapCache, err := newHostCapCache(ctx, p)
	if err != nil {
		return nil, err
	}
	p.hostCapCache = hostCapCache

	if EnableDiskCache {
		p.diskCache = cache.NewExpiredCache(10*time.Second, p.listAllOriginDisks)
	}
	return p, nil
}

func newTagsCache(ctx context.Context, client *vmware.Client, tagcategory string) (common.KVCache[types.ManagedObjectReference, []tags.Tag], error) {
	onRefresh := func(ctx context.Context) (map[types.ManagedObjectReference][]tags.Tag, error) {
		tagids, err := client.TagManager.ListTagsForCategory(ctx, tagcategory)
		if err != nil {
			return nil, err
		}
		tagsmap := make(map[string]tags.Tag, len(tagids))
		for _, tagid := range tagids {
			tag, err := client.TagManager.GetTag(ctx, tagid)
			if err != nil {
				log.Error(err, "failed to get tag", "tagid", tagid)
				return nil, err
			}
			tagsmap[tag.ID] = *tag
		}
		tagedobjcts, err := client.TagManager.ListAttachedObjectsOnTags(ctx, tagids)
		if err != nil {
			log.Error(err, "failed to get tag objects")
			return nil, err
		}
		result := make(map[types.ManagedObjectReference][]tags.Tag)
		for _, item := range tagedobjcts {
			for _, objid := range item.ObjectIDs {
				key := objid.Reference()
				tag, ok := tagsmap[item.TagID]
				if !ok {
					continue
				}
				if exists, ok := result[key]; ok {
					result[key] = append(exists, tag)
				} else {
					result[key] = []tags.Tag{tag}
				}
			}
		}
		return result, nil
	}
	onMiss := func(ctx context.Context, id types.ManagedObjectReference) ([]tags.Tag, error) {
		tags, err := client.TagManager.GetAttachedTags(ctx, id)
		if err != nil {
			log.Error(err, "failed to get tags")
			return nil, err
		}
		return tags, nil
	}
	options := common.CacheOptions[types.ManagedObjectReference, []tags.Tag]{
		OnMiss:    onMiss,
		OnRefresh: onRefresh,
	}
	return common.NewMemKVCache(ctx, options)
}

func newHostCapCache(ctx context.Context, client *Provider) (common.KVCache[types.ManagedObjectReference, types.HostHardwareSummary], error) {
	onRefresh := func(ctx context.Context) (map[types.ManagedObjectReference]types.HostHardwareSummary, error) {
		tagids, err := client.listHosts(ctx, "name", "summary", "config")
		if err != nil {
			return nil, err
		}
		kvs := make(map[types.ManagedObjectReference]types.HostHardwareSummary, len(tagids))
		for _, host := range tagids {
			kvs[host.Reference()] = *host.Summary.Hardware
		}
		return kvs, nil
	}
	onMiss := func(ctx context.Context, id types.ManagedObjectReference) (types.HostHardwareSummary, error) {
		host, err := client.getHost(ctx, id.Value, []string{"name", "summary", "config"})
		if err != nil {
			log.Error(err, "failed to get host", "host", id.Value)
			return types.HostHardwareSummary{}, err
		}
		return *host.Summary.Hardware, nil
	}
	options := common.CacheOptions[types.ManagedObjectReference, types.HostHardwareSummary]{
		OnMiss:    onMiss,
		OnRefresh: onRefresh,
	}
	return common.NewMemKVCache(ctx, options)
}

func WrapError(err error, resource, name string) error {
	if vmware.IsNotFound(err) {
		return errors.NewNotFound(resource, name)
	}
	if vmware.IsAlreadyExists(err) {
		return errors.NewAlreadyExists(resource, name)
	}
	return errors.NewBadRequest(err.Error())
}

func NewCachedCustomFieldsManager(ctx context.Context, client *vmware.Client) (*CustomFieldsManager, error) {
	fm, err := object.GetCustomFieldsManager(client.VimClient.Client)
	if err != nil {
		return nil, err
	}
	man := &CustomFieldsManager{
		cache: make(map[string]types.CustomFieldDef),
		mu:    sync.RWMutex{},
		fm:    fm,
	}
	if err := man.Refresh(ctx); err != nil {
		return nil, err
	}
	return man, nil
}

type CustomFieldsManager struct {
	cache map[string]types.CustomFieldDef
	mu    sync.RWMutex
	fm    *object.CustomFieldsManager
}

func (m *CustomFieldsManager) CustomValuesToLabels(customvalues []types.BaseCustomFieldValue) map[string]string {
	labels := make(map[string]string)
	for _, cv := range customvalues {
		cfs, ok := cv.(*types.CustomFieldStringValue)
		if !ok {
			continue
		}
		if cfs.Key == 0 {
			continue
		}
		key := m.GetKeyFromID(cfs.Key)
		if key == "" {
			continue
		}
		labels[key] = cfs.Value
	}
	return labels
}

func (m *CustomFieldsManager) SetKeyValue(ctx context.Context, ref types.ManagedObjectReference, key string, value string) error {
	fieldDef, err := m.GetKeyIDOrCreate(ctx, key)
	if err != nil {
		return err
	}
	if fieldDef.Key == 0 {
		return errors.NewBadRequest("failed to create custom field")
	}
	req := types.SetField{
		This:   m.fm.Reference(),
		Entity: ref,
		Key:    fieldDef.Key,
		Value:  value,
	}
	if _, err = methods.SetField(ctx, m.fm.Client(), &req); err != nil {
		return err
	}
	return nil
}

func (m *CustomFieldsManager) GetKeyFromID(id int32) string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	for _, fd := range m.cache {
		if fd.Key == id {
			return fd.Name
		}
	}
	return ""
}

func (m *CustomFieldsManager) GetKeyIDOrCreate(ctx context.Context, key string) (types.CustomFieldDef, error) {
	m.mu.RLock()
	cached, ok := m.cache[key]
	m.mu.RUnlock()
	if ok {
		return cached, nil
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	cached, ok = m.cache[key]
	if ok {
		return cached, nil
	}
	m.Refresh(ctx)
	// Try to find the key again
	cached, ok = m.cache[key]
	if ok {
		return cached, nil
	}
	res, err := methods.AddCustomFieldDef(ctx, m.fm.Client(), &types.AddCustomFieldDef{This: m.fm.Reference(), Name: key})
	if err != nil {
		return types.CustomFieldDef{}, err
	}
	m.cache[key] = res.Returnval
	return res.Returnval, nil
}

func (m *CustomFieldsManager) Refresh(ctx context.Context) error {
	fds, err := m.fm.Field(ctx)
	if err != nil {
		return err
	}
	for _, fd := range fds {
		m.cache[fd.Name] = fd
	}
	return nil
}
