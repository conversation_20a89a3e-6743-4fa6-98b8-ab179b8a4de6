package bob

import (
	"context"
	"fmt"
	"io/fs"
	"strconv"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/performance"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
	"xiaoshiai.cn/core/ismc/providers/vmware/console"
)

var _ common.Provider = &Provider{}

type Provider struct {
	DC                   *object.Datacenter
	Client               *vmware.Client
	Groups               *TagManager
	Contents             *ContentLibray
	PerfManager          *performance.Manager
	ConsoleStatisFS      fs.FS // static files for console
	BuiltInInstanceTypes []common.InstanceType
	Fields               *CustomFieldsManager
	Cache                *vmware.ObjectCache
}

func NewProvider(ctx context.Context, client *vmware.Client) (*Provider, error) {
	objectsCache := vmware.NewDefaultObjectCache(client)
	group, err := NewResourceGroupManager(ctx, client, objectsCache, client.Datacenter)
	if err != nil {
		return nil, err
	}
	contents, err := NewCachedContentLibrary(ctx, client, objectsCache, group)
	if err != nil {
		return nil, err
	}
	fields, err := NewCachedCustomFieldsManager(ctx, client, objectsCache)
	if err != nil {
		return nil, err
	}
	go func() {
		if err := objectsCache.Run(ctx); err != nil {
			log.FromContext(ctx).Error(err, "run object cache")
			return
		}
	}()

	if err := objectsCache.WaitSync(ctx); err != nil {
		return nil, fmt.Errorf("wait for object cache sync: %w", err)
	}
	p := &Provider{
		Client:               client,
		DC:                   client.Datacenter,
		Groups:               group,
		Contents:             contents,
		ConsoleStatisFS:      console.GetStaticFS(),
		PerfManager:          performance.NewManager(client.VimClient.Client),
		BuiltInInstanceTypes: BuiltInInstanceTypes,
		Fields:               fields,
		Cache:                objectsCache,
	}
	return p, nil
}

func WrapError(err error, resource, name string) error {
	if vmware.IsNotFound(err) {
		return errors.NewNotFound(resource, name)
	}
	if vmware.IsAlreadyExists(err) {
		return errors.NewAlreadyExists(resource, name)
	}
	return errors.NewBadRequest(err.Error())
}

func NewCachedCustomFieldsManager(ctx context.Context, client *vmware.Client, cache *vmware.ObjectCache) (*CustomFieldsManager, error) {
	fm, err := object.GetCustomFieldsManager(client.VimClient.Client)
	if err != nil {
		return nil, err
	}
	man := &CustomFieldsManager{
		fm:    fm,
		cache: cache,
	}
	return man, nil
}

type CustomFieldsManager struct {
	cache *vmware.ObjectCache
	fm    *object.CustomFieldsManager
}

func (m *CustomFieldsManager) CustomValuesToLabels(customvalues []types.BaseCustomFieldValue) map[string]string {
	labels := make(map[string]string)
	for _, cv := range customvalues {
		cfs, ok := cv.(*types.CustomFieldStringValue)
		if !ok {
			continue
		}
		if cfs.Key == 0 {
			continue
		}
		key := m.GetKeyFromID(cfs.Key)
		if key == "" {
			continue
		}
		labels[key] = cfs.Value
	}
	return labels
}

func (m *CustomFieldsManager) SetKeyValue(ctx context.Context, ref types.ManagedObjectReference, key string, value string) error {
	fieldDef, err := m.GetOrCreateByName(ctx, key)
	if err != nil {
		return err
	}
	if fieldDef.Key == 0 {
		return errors.NewBadRequest("failed to create custom field")
	}
	return m.fm.Set(ctx, ref, fieldDef.Key, value)
}

func (m *CustomFieldsManager) GetKeyFromID(id int32) string {
	ref := types.ManagedObjectReference{
		Type:  vmware.TypeCustomField,
		Value: strconv.Itoa(int(id)),
	}
	field, err := vmware.CacheToGet[*vmware.CustomField](m.cache, ref)
	if err != nil {
		return ""
	}
	return field.CustomFieldDef.Name
}

func (m *CustomFieldsManager) GetOrCreateByName(ctx context.Context, name string) (types.CustomFieldDef, error) {
	fields, _ := vmware.CacheToList(m.cache, vmware.TypeCustomField, func(cf vmware.CustomField) bool {
		return cf.CustomFieldDef.Name == name
	})
	if len(fields) > 0 {
		return fields[0].CustomFieldDef, nil
	}
	cf, err := m.fm.Add(ctx, name, "", nil, nil)
	if err != nil {
		return types.CustomFieldDef{}, err
	}
	field := vmware.CustomField{
		ManagedObjectReference: types.ManagedObjectReference{
			Type:  vmware.TypeCustomField,
			Value: strconv.Itoa(int(cf.Key)),
		},
		CustomFieldDef: *cf,
	}
	return field.CustomFieldDef, nil
}

func (m *CustomFieldsManager) GetByName(ctx context.Context, name string) (*types.CustomFieldDef, error) {
	fields, _ := vmware.CacheToList(m.cache, vmware.TypeCustomField, func(cf vmware.CustomField) bool {
		return cf.CustomFieldDef.Name == name
	})
	if len(fields) > 0 {
		return &fields[0].CustomFieldDef, nil
	}
	field, err := m.fm.Field(ctx, )
	if err != nil {
		return nil, err
	}
	for _, cf := range field {
		if cf.Name == name {
			// cache the field for later use
			m.cache.Set(
				&vmware.CustomField{
				ManagedObjectReference: types.ManagedObjectReference{
					Type:  vmware.TypeCustomField,
					Value: strconv.Itoa(int(cf.Key)),
				},
				CustomFieldDef: cf,
			})
			return &cf, nil
		}
	}
	return nil, errors.NewNotFound("custom field", name)
}