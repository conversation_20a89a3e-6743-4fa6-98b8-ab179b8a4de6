package bob

import (
	"context"

	"xiaoshiai.cn/core/ismc/common"
)

var _ common.SecurityGroupOperation = &Provider{}

// AddSecurityGroupRule implements ismc.SecurityGroupOperation.
func (p *Provider) AddSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return common.ErrUnsupported
}

// CreateSecurityGroup implements ismc.SecurityGroupOperation.
func (p *Provider) CreateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteSecurityGroup implements ismc.SecurityGroupOperation.
func (p *Provider) DeleteSecurityGroup(ctx context.Context, name string) error {
	return common.ErrUnsupported
}

// DeleteSecurityGroupRule implements ismc.SecurityGroupOperation.
func (p *Provider) DeleteSecurityGroupRule(ctx context.Context, securityGroup string, ruleName string) error {
	return common.ErrUnsupported
}

// GetSecurityGroup implements ismc.SecurityGroupOperation.
func (p *Provider) GetSecurityGroup(ctx context.Context, name string) (*common.SecurityGroup, error) {
	return nil, common.ErrUnsupported
}

// ListSecurityGroupRules implements ismc.SecurityGroupOperation.
func (p *Provider) ListSecurityGroupRules(ctx context.Context, securityGroup string, optios common.ListSecurityGroupRuleOptions) (common.List[common.SecurityGroupRule], error) {
	return common.List[common.SecurityGroupRule]{}, nil
}

// ListSecurityGroups implements ismc.SecurityGroupOperation.
func (p *Provider) ListSecurityGroups(ctx context.Context, options common.ListSecurityGroupOptions) (common.List[common.SecurityGroup], error) {
	return common.List[common.SecurityGroup]{}, nil
}

// UpdateSecurityGroup implements ismc.SecurityGroupOperation.
func (p *Provider) UpdateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) error {
	return common.ErrUnsupported
}

// UpdateSecurityGroupRule implements ismc.SecurityGroupOperation.
func (p *Provider) UpdateSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return common.ErrUnsupported
}
