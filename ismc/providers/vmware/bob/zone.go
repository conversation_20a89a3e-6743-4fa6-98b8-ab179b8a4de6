package bob

import (
	"context"

	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/core/ismc/common"
)

const TypeHostSystem = "HostSystem"

var _ common.ZoneOperation = &Provider{}

// ListZones implements ismc.RegionOperation.
func (p *Provider) ListZones(ctx context.Context, option common.ListZoneOptions) (common.List[common.Zone], error) {
	list := common.List[common.Zone]{Items: []common.Zone{}}
	hostsystems, err := p.listHosts(ctx, "name", "summary")
	if err != nil {
		return list, err
	}
	// convert host to zone
	zones := make([]common.Zone, 0, len(hostsystems))
	for _, host := range hostsystems {
		zones = append(zones, ConvertHostToZone(host))
	}
	return common.PageList(zones, option.ListOptions), nil
}

// GetZone implements ismc.RegionOperation.
func (p *Provider) GetZone(ctx context.Context, id string) (*common.Zone, error) {
	hostsystems, err := p.getHost(ctx, id, []string{"name", "summary"})
	if err != nil {
		return nil, err
	}
	zone := ConvertHostToZone(*hostsystems)
	return &zone, nil
}

func ConvertHostToZone(host mo.HostSystem) common.Zone {
	zone := common.Zone{
		Descripter: common.Descripter{
			ID:   host.Self.Value,
			Name: host.Name,
			UID:  host.Self.ServerGUID,
		},
		Status: common.ZoneStatus{
			Ready:     true,
			Addtional: map[string]string{},
		},
	}
	// ready
	if runtime := host.Summary.Runtime; runtime != nil {
		if runtime.InMaintenanceMode ||
			runtime.ConnectionState != types.HostSystemConnectionStateConnected ||
			runtime.PowerState != types.HostSystemPowerStatePoweredOn {
			zone.Status.Ready = false
		}
	}
	return zone
}
