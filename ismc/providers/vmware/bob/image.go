package bob

import (
	"context"
	"fmt"
	"math"
	"net/url"
	"slices"
	"strconv"
	"time"

	"github.com/vmware/govmomi/ovf"
	"github.com/vmware/govmomi/vapi/library"
	"github.com/vmware/govmomi/vim25/soap"
	"github.com/vmware/govmomi/vim25/types"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

var _ common.ImageOperation = &Provider{}

// ListImages implements ismc.ImageOperation.
func (p *Provider) ListImages(ctx context.Context, options common.ListImageOptions) (common.List[common.Image], error) {
	list := common.List[common.Image]{Items: []common.Image{}}
	images, err := p.Contents.ListContenLibraryImages(ctx, string(options.Source), options.Name)
	if err != nil {
		return list, err
	}
	if options.Phase != "" {
		images = slices.DeleteFunc(images, func(img common.Image) bool {
			return img.Status.Phase != options.Phase
		})
	}
	return common.PageList(images, options.ListOptions), nil
}

// DeleteImage implements ismc.ImageOperation.
func (p *Provider) DeleteImage(ctx context.Context, id string) error {
	return p.Contents.DeleteContenLibraryImage(ctx, id)
}

// ExistsImage implements ismc.ImageOperation.
func (p *Provider) GetImage(ctx context.Context, id string) (*common.Image, error) {
	return p.Contents.GetContenLibraryImage(ctx, id)
}

// UpdateImage implements ismc.ImageOperation.
func (p *Provider) UpdateImage(ctx context.Context, image *common.Image) error {
	return p.Contents.UpdateContenLibraryImage(ctx, image)
}

// ImportImage implements ismc.ImageOperation.
func (p *Provider) ImportImage(ctx context.Context, image *common.Image, file common.FileContent) (*common.Descripter, error) {
	return p.Contents.ImportImage(ctx, image, file)
}

func NewCachedContentLibrary(ctx context.Context, client *vmware.Client, cache *vmware.ObjectCache, resourceGroup *TagManager) (*ContentLibray, error) {
	c := &ContentLibray{
		client:        client,
		cache:         cache,
		resourceGroup: resourceGroup,
	}
	if err := c.initDefaultContentLibrary(ctx); err != nil {
		return nil, err
	}
	return c, nil
}

// ContentLibray manages content libraries in VMware.
// resourceGroup == [tags.Tag]
// image source == [library.Library]
type ContentLibray struct {
	client        *vmware.Client
	cache         *vmware.ObjectCache
	resourceGroup *TagManager
}

/*
创建默认的内容库
*/
func (p *ContentLibray) initDefaultContentLibrary(ctx context.Context) error {
	libman := library.NewManager(p.client.RestClient)
	libraries, err := libman.GetLibraries(ctx)
	if err != nil {
		return err
	}
	libraryMaps := make(map[string]library.Library, len(libraries))
	for _, library := range libraries {
		libraryMaps[library.Name] = library
	}
	builtInLibraries := []string{
		string(common.ImageSourcePublic),
		string(common.ImageSourceCustom),
		string(common.ImageSourceShared),
		string(common.ImageSourceCommunity),
	}
	for _, element := range builtInLibraries {
		if lib, ok := libraryMaps[element]; ok {
			libmo := &vmware.ContentLibrary{
				ManagedObjectReference: types.ManagedObjectReference{
					Type:  vmware.TypeContentLibrary,
					Value: lib.ID,
				},
				Library: lib,
			}
			p.cache.Set(libmo)
			continue
		}
		log.Info("creating content library", "name", element)
		dsref, err := p.client.FindMaxFreeDatastoreReference(ctx)
		if err != nil {
			return err
		}
		newlib := library.Library{
			Name: element,
			Type: "LOCAL",
			Storage: []library.StorageBacking{
				{DatastoreID: dsref.Value, Type: "DATASTORE"},
			},
		}
		libid, err := libman.CreateLibrary(ctx, newlib)
		if err != nil {
			return err
		}
		log.Info("created content library", "name", element, "id", libid)
		lib, err := libman.GetLibraryByID(ctx, libid)
		if err != nil {
			return err
		}
		libmo := &vmware.ContentLibrary{
			ManagedObjectReference: types.ManagedObjectReference{
				Type:  vmware.TypeContentLibrary,
				Value: libid,
			},
			Library: *lib,
		}
		p.cache.Set(libmo)
	}
	return nil
}

func (p *ContentLibray) ListContenLibraryImages(ctx context.Context, source, name string) ([]common.Image, error) {
	contentLibraries, err := p.listLibraryItems(ctx, source, name)
	if err != nil {
		return nil, fmt.Errorf("list content library items: %w", err)
	}
	items := make([]common.Image, 0, len(contentLibraries))
	for _, cl := range contentLibraries {
		items = append(items, p.ConvertLibraryItemToImage(cl.Item, cl.OVF))
	}
	return items, nil
}

func (p *ContentLibray) listLibraryItems(_ context.Context, source, name string) ([]vmware.ContentLibraryItem, error) {
	filterFunc := func(cl vmware.ContentLibraryItem) bool {
		// check name matches
		if name != "" && cl.Item.Name != name {
			return false
		}
		// check if the source matches content library name
		if source != "" {
			libref := types.ManagedObjectReference{
				Type:  vmware.TypeContentLibrary,
				Value: cl.Item.LibraryID,
			}
			lib, _ := vmware.CacheToGet[*vmware.ContentLibrary](p.cache, libref)
			if lib == nil || lib.Library.Name != string(source) {
				return false
			}
		}
		return true
	}
	contentLibraries, err := vmware.CacheToList(p.cache, vmware.TypeContentLibraryItem, filterFunc)
	if err != nil {
		return nil, err
	}
	return contentLibraries, nil
}

func (p *ContentLibray) UpdateContenLibraryImage(ctx context.Context, img *common.Image) error {
	exists, err := p.GetContenLibraryImage(ctx, img.Name)
	if err != nil {
		return err
	}
	// only update description
	if exists.Description != img.Description {
		// update description will change the item in cache
		// exists is a pointer to the cached item, so we can update it directly
		exists.Description = img.Description
		update := &library.Item{Name: exists.Name, Description: ptr.To(img.Description)}
		if err := p.client.LibraryManager.UpdateLibraryItem(ctx, update); err != nil {
			return err
		}
	}
	return nil
}

func (p *ContentLibray) DeleteContenLibraryImage(ctx context.Context, id string) error {
	if err := p.client.LibraryManager.DeleteLibraryItem(ctx, &library.Item{ID: id}); err != nil {
		return err
	}
	p.cache.Delete(types.ManagedObjectReference{Type: vmware.TypeContentLibraryItem, Value: id})
	return nil
}

func (p *ContentLibray) GetContenLibraryImage(ctx context.Context, id string) (*common.Image, error) {
	contentitem, err := vmware.CacheToGet[*vmware.ContentLibraryItem](p.cache, types.ManagedObjectReference{Type: vmware.TypeContentLibraryItem, Value: id})
	if err != nil {
		if !vmware.IsNotFound(err) {
			return nil, WrapError(err, "image", id)
		}
		// fallback to get library item from API
		item, err := vmware.GetContentLibraryItem(ctx, p.client, id, "")
		if err != nil {
			return nil, WrapError(err, "image", id)
		}
		p.cache.Set(item)
		contentitem = item
	}
	img := p.ConvertLibraryItemToImage(contentitem.Item, contentitem.OVF)
	return &img, nil
}

func (p *ContentLibray) ConvertLibraryItemToImage(item library.Item, ovfdata *ovf.Envelope) common.Image {
	img := common.Image{
		Descripter: common.Descripter{
			ID:          item.ID,
			Name:        item.Name,
			Description: ptr.Deref(item.Description, ""),
		},
		Source: p.sourceFromContentLib(item.LibraryID),
		Status: common.ImageStatus{
			Phase: common.ImagePhaseReady,
			Size:  *resource.NewQuantity(int64(item.Size), resource.BinarySI),
		},
	}
	if item.Size == 0 {
		img.Status.Phase = common.ImagePhasePending
	}
	if creationtime := item.CreationTime; creationtime != nil {
		img.CreationTimestamp = *creationtime
	}
	if ovfdata != nil {
		completeFromOVF(&img, ovfdata)
	}
	return img
}

func completeFromOVF(img *common.Image, ovfdata *ovf.Envelope) {
	if ovfdata.Disk != nil {
		var minSize int64 = math.MaxInt64
		for _, eachDisk := range ovfdata.Disk.Disks {
			diskSize, err := strconv.ParseInt(eachDisk.Capacity, 10, 64)
			if err != nil {
				continue
			}
			if diskSize <= minSize {
				minSize = diskSize
			}
		}
		img.MinDisk = *resource.NewQuantity(minSize, resource.BinarySI)
	}
	if ovfdata.VirtualSystem != nil {
		vm := ovfdata.VirtualSystem
		if len(vm.VirtualHardware) > 0 {
			items := vm.VirtualHardware[0]
			for _, item := range items.Item {
				if item.Description != nil && *item.Description == "Memory Size" {
					img.MinMemory = *resource.NewQuantity(int64(*item.VirtualQuantity)*MB, resource.BinarySI)
					break
				}
			}
		}
		if len(vm.OperatingSystem) > 0 {
			ops := vm.OperatingSystem[0]
			if ops.OSType != nil {
				img.OS = common.OSType(*ops.OSType)
			}
			if ops.Description != nil {
				img.Architecture = common.Architecture(*ops.Description)
			}
		}
	}
}

func (p *ContentLibray) sourceFromContentLib(id string) common.ImageSource {
	lib, _ := vmware.CacheToGet[*vmware.ContentLibrary](p.cache, types.ManagedObjectReference{Type: vmware.TypeContentLibrary, Value: id})
	return common.ImageSource(lib.Library.Name)
}

func (p *ContentLibray) ImportImage(ctx context.Context, image *common.Image, file common.FileContent) (*common.Descripter, error) {
	log := log.FromContext(ctx).WithValues("image", image.Name)
	libitem, err := p.GetOrCreateEmptyLibraryItem(ctx, image.Name)
	if err != nil {
		return nil, err
	}
	log.Info("importing image")
	sessionID, err := p.client.LibraryManager.CreateLibraryItemUpdateSession(ctx, library.Session{LibraryItemID: libitem.ID})
	if err != nil {
		return nil, err
	}
	log.Info("sessionID:", sessionID)
	keepalivectx, cancel := context.WithCancel(ctx)
	defer cancel()

	// keepalive upload session in case of long time upload
	go p.keepaliveUploadSession(keepalivectx, sessionID)

	if err := p.uploadFile(ctx, sessionID, file); err != nil {
		log.Error(err, "failed to import file", "file", file.FileName)
		if err := p.client.LibraryManager.CancelLibraryItemUpdateSession(ctx, sessionID); err != nil {
			log.Info("failed to complete library item update session", err)
		}
		if err := p.client.LibraryManager.DeleteLibraryItemUpdateSession(ctx, sessionID); err != nil {
			log.Info("failed to complete library item update session", err)
		}
		return nil, err
	}

	log.V(3).Info("completing library item update session")
	if err := p.client.LibraryManager.CompleteLibraryItemUpdateSession(ctx, sessionID); err != nil {
		log.Info("failed to complete library item update session: ", err)
		return nil, err
	}
	log.Info("imported image")

	desc := &common.Descripter{
		ID:   libitem.ID,
		Name: libitem.Name,
	}
	return desc, nil
}

func (p *ContentLibray) uploadFile(ctx context.Context, sessionID string, file common.FileContent) error {
	log := log.FromContext(ctx).WithValues("file", file.FileName)
	log.Info("uploading file")

	content := file.Content
	defer content.Close()

	info := library.UpdateFile{
		Name:       file.FileName,
		SourceType: "PUSH",
		Size:       file.ContentLength,
	}
	update, err := p.client.LibraryManager.AddLibraryItemFile(ctx, sessionID, info)
	if err != nil {
		return err
	}
	if update.UploadEndpoint == nil {
		return fmt.Errorf("no upload endpoint found for %s", file.FileName)
	}
	u, err := url.Parse(update.UploadEndpoint.URI)
	if err != nil {
		return err
	}
	upload := &soap.Upload{
		Type:          def(file.ContentType, "application/octet-stream"),
		Method:        "PUT",
		ContentLength: file.ContentLength,
	}
	progresscontent := common.NewSimpleProgressReader(content, file.ContentLength, log)
	if err := p.client.RestClient.Upload(ctx, progresscontent, u, upload); err != nil {
		return err
	}
	log.Info("uploaded file")
	return nil
}

func def(use, def string) string {
	if use != "" {
		return use
	}
	return def
}

func (p *ContentLibray) keepaliveUploadSession(ctx context.Context, sessionid string) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			_ = p.client.LibraryManager.KeepAliveLibraryItemUpdateSession(ctx, sessionid)
		}
	}
}

// EnsureImageLibraryItem return library item to image name
// if not found, create new library item,默认是ImageSourceCustom内容库
func (p *ContentLibray) GetOrCreateEmptyLibraryItem(ctx context.Context, name string) (*library.Item, error) {
	defaultImageSource := string(common.ImageSourceCustom)
	contentLibraries, err := p.listLibraryItems(ctx, defaultImageSource, name)
	if err != nil {
		return nil, fmt.Errorf("get default content library %s: %w", defaultImageSource, err)
	}
	if len(contentLibraries) > 0 {
		for _, item := range contentLibraries {
			if item.Item.Name == name {
				shallow := item.Item
				return &shallow, nil
			}
		}
	}
	// if not found, create new library item
	contentlib, err := p.getLibrary(ctx, defaultImageSource)
	if err != nil {
		return nil, fmt.Errorf("get content library %s: %w", defaultImageSource, err)
	}
	newitem := library.Item{
		LibraryID:   contentlib.Library.ID,
		Name:        name,
		Type:        "ovf",
		Description: &name,
	}
	newid, err := p.client.LibraryManager.CreateLibraryItem(ctx, newitem)
	if err != nil {
		return nil, err
	}
	item, err := p.client.LibraryManager.GetLibraryItem(ctx, newid)
	if err != nil {
		return nil, fmt.Errorf("get library item %s: %w", newid, err)
	}
	contentlibmo := &vmware.ContentLibraryItem{
		ManagedObjectReference: types.ManagedObjectReference{
			Type: vmware.TypeContentLibraryItem, Value: newid,
		},
		Item: *item,
	}
	p.cache.Set(contentlibmo)
	log.FromContext(ctx).Info("created new content library item", "name", name, "id", newid, "library", contentlib.Library.Name)
	return item, nil
}

func (p *ContentLibray) getLibrary(ctx context.Context, name string) (*vmware.ContentLibrary, error) {
	filterFunc := func(cl vmware.ContentLibrary) bool {
		return cl.Library.Name == name && cl.Library.Type == "LOCAL"
	}
	contentlibraries, err := vmware.CacheToList(p.cache, vmware.TypeContentLibrary, filterFunc)
	if err != nil {
		return nil, fmt.Errorf("get content library %s: %w", name, err)
	}
	if len(contentlibraries) == 0 {
		// fallback to get library from API
		lib, err := p.client.LibraryManager.GetLibraryByName(ctx, name)
		if err != nil {
			return nil, fmt.Errorf("get content library %s: %w", name, err)
		}
		contentlibmo := &vmware.ContentLibrary{
			ManagedObjectReference: types.ManagedObjectReference{
				Type:  vmware.TypeContentLibrary,
				Value: lib.ID,
			},
			Library: *lib,
		}
		p.cache.Set(contentlibmo)
		return contentlibmo, nil
	}
	shallow := contentlibraries[0]
	return &shallow, nil
}
