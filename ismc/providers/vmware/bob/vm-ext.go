package bob

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/types"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

// govc device.ls -vm $name
// AttachVirtualMachineNetworkInterface implements common.Provider.

// ChangeVirtualMachineImage implements common.Provider.
func (p *Provider) ChangeVirtualMachineImage(ctx context.Context, id string, options common.ChangeVirtualMachineImageOptions) error {
	return nil
}

// ChangeVirtualMachineInstanceType implements common.Provider.
func (p *Provider) ChangeVirtualMachineInstanceType(ctx context.Context, id string, options common.ChangeVirtualMachineInstanceTypeOptions) error {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return err
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return fmt.Errorf("vm not found")
	}
	instanceType, err := p.GetInstanceType(ctx, options.InstanceType.ID)
	if err != nil {
		return err
	}
	cpu := instanceType.Resources[common.ResourceCPU]
	memory := instanceType.Resources[common.ResourceMemory]
	spec := types.VirtualMachineConfigSpec{
		NumCPUs:           int32(cpu.Value()),
		MemoryMB:          int64(memory.Value() / 1024 / 1024),
		NumCoresPerSocket: 1,
		// CpuHotAddEnabled:    types.NewBool(true),
		// MemoryHotAddEnabled: types.NewBool(true),
	}
	task, err := vm.Reconfigure(ctx, spec)
	if err != nil {
		return err
	}
	return task.Wait(ctx)
}

var defaultnetworkAdapter = "e1000"

func (p *Provider) AttachVirtualMachineNetworkInterface(ctx context.Context, id string, options common.AttachVirtualMachineNetworkInterfaceOptions) error {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return err
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return fmt.Errorf("vm not found")
	}
	networkRef, err := p.Client.FromMOID(ctx, options.Inerface.Network, "Network")
	if err != nil {
		return err
	}
	network, ok := networkRef.(object.NetworkReference)
	if !ok {
		return fmt.Errorf("network not found")
	}
	backing, err := network.EthernetCardBackingInfo(ctx)
	if err != nil {
		return err
	}
	device, err := object.EthernetCardTypes().CreateEthernetCard(defaultnetworkAdapter, backing)
	if err != nil {
		return err
	}
	return vm.AddDevice(ctx, device)
}

// ListVirtualMachineNetworkInterfaces implements common.Provider.
func (p *Provider) ListVirtualMachineNetworkInterfaces(ctx context.Context, id string, options common.ListVirtualMachineNetworkOptions) (common.List[common.NetworkInterface], error) {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return common.List[common.NetworkInterface]{}, vmware.IgnoreNotFound(err)
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return common.List[common.NetworkInterface]{}, fmt.Errorf("vm not found")
	}
	devices, err := vm.Device(ctx)
	if err != nil {
		return common.List[common.NetworkInterface]{}, err
	}
	nics := p.parseVmNetworkInterfaces(devices, id)
	return common.PageList(nics, options.ListOptions), nil
}

// DetachVirtualMachineNetworkInterface implements common.Provider.
func (p *Provider) DetachVirtualMachineNetworkInterface(ctx context.Context, id string, nicid string, options common.DetachVirtualMachineNetworkInterfaceOptions) error {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return err
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return fmt.Errorf("vm not found")
	}
	devices, err := vm.Device(ctx)
	if err != nil {
		return err
	}
	for _, device := range devices {
		deviceName := devices.Name(device)
		if deviceName == nicid {
			if err = vm.RemoveDevice(ctx, true, device); err != nil {
				return err
			}
		}
	}
	return nil
}

// ReInstallVirtualMachine implements common.Provider.
func (p *Provider) ReInstallVirtualMachine(ctx context.Context, id string, options common.ReInstallVirtualMachineOptions) error {
	return nil
}

// ChangeVirtualMachineCloudInit implements common.Provider.
func (p *Provider) ChangeVirtualMachineCloudInit(ctx context.Context, id string, options common.ResetVirtualMachineCloudInitOptions) error {
	return nil
}

// ResetVirtualMachinePassword implements common.Provider.
func (p *Provider) ResetVirtualMachinePassword(ctx context.Context, id string, options common.ResetVirtualMachinePasswordOptions) error {
	return nil
}

// AttachVirtualMachineDisk implements common.Provider.
func (p *Provider) AttachVirtualMachineDisk(ctx context.Context, id string, options common.AttachVirtualMachineDiskOptions) error {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return err
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return fmt.Errorf("vm not found")
	}
	devices, err := vm.Device(ctx)
	if err != nil {
		return err
	}
	controller, err := devices.FindDiskController("scsi")
	if err != nil {
		return err
	}
	device := &types.VirtualDisk{
		VirtualDevice: types.VirtualDevice{
			Backing: &types.VirtualDiskFlatVer2BackingInfo{
				DiskMode:        string(types.VirtualDiskModePersistent),
				ThinProvisioned: types.NewBool(true),
				VirtualDeviceFileBackingInfo: types.VirtualDeviceFileBackingInfo{
					FileName: vmware.DecodeID(options.Disk.ID),
				},
			},
		},
	}
	devices.AssignController(device, controller)
	return vm.AddDevice(ctx, device)
}

// DetachVirtualMachineDisk implements common.Provider.
func (p *Provider) DetachVirtualMachineDisk(ctx context.Context, id string, diskid string, options common.DetachVirtualMachineDiskOptions) error {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return err
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return fmt.Errorf("vm not found")
	}
	devices, err := vm.Device(ctx)
	if err != nil {
		return err
	}
	for _, device := range devices {
		switch md := device.(type) {
		case *types.VirtualDisk:
			if b, ok := md.Backing.(types.BaseVirtualDeviceFileBackingInfo); ok {
				if vmware.DecodeID(diskid) == b.GetVirtualDeviceFileBackingInfo().FileName {
					return vm.RemoveDevice(ctx, true, device)
				}
			}
		}
	}
	return nil
}

func (p *Provider) listVirtualMachineDisks(ctx context.Context, id string) ([]common.Disk, error) {
	vmref, err := p.Client.FromMOID(ctx, id, TypeVirtualMachine)
	if err != nil {
		return nil, err
	}
	vm, ok := vmref.(*object.VirtualMachine)
	if !ok {
		return nil, fmt.Errorf("vm not found")
	}
	devices, err := vm.Device(ctx)
	if err != nil {
		return nil, err
	}
	infoDevices := &infoDevices{
		list:    devices,
		Devices: toInfoList(devices),
	}
	attacheds := infoDevices.toAttachedDevice()
	var disks []common.Disk
	for _, attached := range attacheds {
		if attached.Type == "VirtualDisk" {
			var creationTimestamp time.Time
			fi, err := p.getDatastoreFileInfo(ctx, attached.File)
			if err != nil {
				log.Error(err, "failed to get datastore file info")
			} else if fi.Modification != nil {
				creationTimestamp = *fi.Modification
			}
			disk := common.Disk{
				Descripter: common.Descripter{
					ID:   vmware.EncodeID(attached.File),
					Name: findDiskName(attached.File),
					Annotations: map[string]string{
						"Key":        fmt.Sprintf("%d", attached.Key),
						"Controller": attached.Controller,
						"UnitNumber": fmt.Sprintf("%d", attached.UnitNumber),
						"File":       attached.File,
						"Summary":    attached.Summary,
						"Device":     attached.Label,
					},
					CreationTimestamp: creationTimestamp,
				},
				Size:        *resource.NewQuantity(int64(attached.CapacityInKB)*1024, resource.BinarySI),
				DiskCluster: attached.DiskCluster,
				DiskClass:   "VMFS",
				Status: common.DiskStatus{
					Phase: "in-use",
					Mounts: []common.DiskMountInfo{
						{
							VirtualMachine: vm.Name(),
							//MountTime:      creationTimestamp,
						},
					},
				},
			}
			disks = append(disks, disk)
		}
	}
	return disks, nil
}

// ListVirtualMachineDisks implements common.Provider.
func (p *Provider) ListVirtualMachineDisks(ctx context.Context, id string, options common.ListVirtualMachineDiskOptions) (common.List[common.Disk], error) {
	disks, err := p.listVirtualMachineDisks(ctx, id)
	if err != nil {
		return common.List[common.Disk]{}, vmware.IgnoreNotFound(err)
	}
	return common.PageList(disks, options.ListOptions), nil
}

type attachedDevice struct {
	Name           string   `json:"name"`
	Type           string   `json:"type"`
	Label          string   `json:"label"`
	Summary        string   `json:"summary"`
	Key            int32    `json:"key"`
	Devices        []string `json:"devices"`
	Controller     string   `json:"controller"`
	UnitNumber     int32    `json:"unitNumber"`
	Connected      bool     `json:"connected"`
	StartConnected bool     `json:"startConnected"` // 是否虚拟机开机就开始连接设备
	GuestControl   bool     `json:"guestControl"`
	Status         string   `json:"status"`
	MACAddress     string   `json:"macAddress"`
	AddressType    string   `json:"addressType"`
	File           string   `json:"file"`
	Parent         string   `json:"parent"`
	CapacityInKB   int64    `json:"caCapacityInKB"`
	DiskCluster    string   `json:"diskCluster"`
	Direction      string   `json:"direction"`
	ServiceURI     string   `json:"serviceURI"`
	ProxyURI       string   `json:"proxyURI"`
	Protocol       string   `json:"protocol"`
}

type infoDevice struct {
	Name string `json:"name"`
	Type string `json:"type"`
	types.BaseVirtualDevice
}

type infoDevices struct {
	Devices []infoDevice `json:"devices"`
	list    object.VirtualDeviceList
}

func toInfoList(devices object.VirtualDeviceList) []infoDevice {
	var res []infoDevice
	for _, device := range devices {
		res = append(res, infoDevice{
			Name:              devices.Name(device),
			Type:              devices.TypeName(device),
			BaseVirtualDevice: device,
		})
	}
	return res
}

// 获取虚拟机下所有的设备
func (r *infoDevices) toAttachedDevice() []attachedDevice {
	var ads []attachedDevice
	for i := range r.Devices {
		device := r.Devices[i].BaseVirtualDevice
		d := device.GetVirtualDevice()
		info := d.DeviceInfo.GetDescription()
		ad := attachedDevice{
			Name:    r.Devices[i].Name,
			Type:    r.list.TypeName(device),
			Label:   info.Label,
			Summary: info.Summary,
			Key:     d.Key,
		}
		if c, ok := device.(types.BaseVirtualController); ok {
			var attached []string
			for _, key := range c.GetVirtualController().Device {
				attached = append(attached, r.list.Name(r.list.FindByKey(key)))
			}
			ad.Devices = attached
		} else {
			if c := r.list.FindByKey(d.ControllerKey); c != nil {
				ad.Controller = r.list.Name(c)
				if d.UnitNumber != nil {
					ad.UnitNumber = *d.UnitNumber
				}
			}
		}
		if ca := d.Connectable; ca != nil {
			ad.Connected = ca.Connected
			ad.StartConnected = ca.StartConnected
			ad.GuestControl = ca.AllowGuestControl
			ad.Status = ca.Status
		}
		switch md := device.(type) {
		case types.BaseVirtualEthernetCard:
			ad.MACAddress = md.GetVirtualEthernetCard().MacAddress
			ad.AddressType = md.GetVirtualEthernetCard().AddressType
		case *types.VirtualDisk:
			if b, ok := md.Backing.(types.BaseVirtualDeviceFileBackingInfo); ok {
				ad.File = b.GetVirtualDeviceFileBackingInfo().FileName
			}
			ad.CapacityInKB = md.CapacityInKB
			if b, ok := md.Backing.(*types.VirtualDiskFlatVer2BackingInfo); ok {
				if b.Parent != nil {
					ad.Parent = b.Parent.GetVirtualDeviceFileBackingInfo().FileName
				}
				dspath := new(object.DatastorePath)
				if dspath.FromString(ad.File) {
					ad.DiskCluster = dspath.Datastore
				}
			}
		case *types.VirtualSerialPort:
			if b, ok := md.Backing.(*types.VirtualSerialPortURIBackingInfo); ok {
				ad.Direction = b.Direction
				ad.ServiceURI = b.ServiceURI
				ad.ProxyURI = b.ProxyURI
			}
		case *types.VirtualPrecisionClock:
			if b, ok := md.Backing.(*types.VirtualPrecisionClockSystemClockBackingInfo); ok {
				proto := b.Protocol
				if proto == "" {
					proto = string(types.HostDateTimeInfoProtocolPtp)
				}
				ad.Protocol = proto
			}
		}
		ads = append(ads, ad)
	}
	return ads
}

func findDiskName(input string) string {
	reversed := reverseString(input)
	index := strings.IndexAny(reversed, "/ ")
	if index != -1 {
		return input[len(input)-index:]
	}
	return input
}

func reverseString(input string) string {
	runes := []rune(input)
	n := len(runes)
	for i := 0; i < n/2; i++ {
		runes[i], runes[n-1-i] = runes[n-1-i], runes[i]
	}
	return string(runes)
}
