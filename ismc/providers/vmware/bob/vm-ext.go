package bob

import (
	"context"
	"fmt"
	"path"
	"strings"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/types"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

// ChangeVirtualMachineImage implements common.Provider.
func (p *Provider) ChangeVirtualMachineImage(ctx context.Context, id string, options common.ChangeVirtualMachineImageOptions) error {
	return nil
}

// ChangeVirtualMachineInstanceType implements common.Provider.
func (p *Provider) ChangeVirtualMachineInstanceType(ctx context.Context, id string, options common.ChangeVirtualMachineInstanceTypeOptions) error {
	instanceType, err := p.GetInstanceType(ctx, options.InstanceType.ID)
	if err != nil {
		return err
	}
	cpu := instanceType.Resources[common.ResourceCPU]
	memory := instanceType.Resources[common.ResourceMemory]
	spec := types.VirtualMachineConfigSpec{
		NumCPUs:           int32(cpu.Value()),
		MemoryMB:          int64(memory.Value() / 1024 / 1024),
		NumCoresPerSocket: 1,
		// CpuHotAddEnabled:    types.NewBool(true),
		// MemoryHotAddEnabled: types.NewBool(true),
	}
	task, err := p.vmref(id).Reconfigure(ctx, spec)
	if err != nil {
		return err
	}
	return task.Wait(ctx)
}

func (p *Provider) AttachVirtualMachineNetworkInterface(ctx context.Context, id string, options common.AttachVirtualMachineNetworkInterfaceOptions) error {
	return p.addVmNic(ctx, id, options.Interface.Network)
}

func (p *Provider) addVmNic(ctx context.Context, vmid string, networkid string) error {
	backing, err := p.networkref(networkid).EthernetCardBackingInfo(ctx)
	if err != nil {
		return err
	}
	// pass "" to use the default nic type it commonly "e1000"
	device, err := object.EthernetCardTypes().CreateEthernetCard("", backing)
	if err != nil {
		return err
	}
	return p.vmref(vmid).AddDevice(ctx, device)
}

// ListVirtualMachineNetworkInterfaces implements common.Provider.
func (p *Provider) ListVirtualMachineNetworkInterfaces(ctx context.Context, id string, options common.ListVirtualMachineNetworkOptions) (common.List[common.NetworkInterface], error) {
	vm, err := p.getvm(ctx, id, "config.hardware.device", "guest")
	if err != nil {
		return common.List[common.NetworkInterface]{}, err
	}
	nics := parseVmNetworkInterfaces(*vm)
	return common.PageListNoDefault(nics, options.ListOptions), nil
}

// DetachVirtualMachineNetworkInterface implements common.Provider.
func (p *Provider) DetachVirtualMachineNetworkInterface(ctx context.Context, id string, nicid string, options common.DetachVirtualMachineNetworkInterfaceOptions) error {
	vm, _, devices, err := p.getvmDevice(ctx, id)
	if err != nil {
		return err
	}
	for _, device := range devices {
		if devices.Name(device) == nicid {
			if err = vm.RemoveDevice(ctx, true, device); err != nil {
				return err
			}
		}
	}
	return nil
}

// ReInstallVirtualMachine implements common.Provider.
func (p *Provider) ReInstallVirtualMachine(ctx context.Context, id string, options common.ReInstallVirtualMachineOptions) error {
	return nil
}

// ChangeVirtualMachineCloudInit implements common.Provider.
func (p *Provider) ChangeVirtualMachineCloudInit(ctx context.Context, id string, options common.ResetVirtualMachineCloudInitOptions) error {
	return nil
}

// ResetVirtualMachinePassword implements common.Provider.
func (p *Provider) ResetVirtualMachinePassword(ctx context.Context, id string, options common.ResetVirtualMachinePasswordOptions) error {
	return nil
}

// AttachVirtualMachineDisk implements common.Provider.
func (p *Provider) AttachVirtualMachineDisk(ctx context.Context, id string, options common.AttachVirtualMachineDiskOptions) error {
	vm, _, devices, err := p.getvmDevice(ctx, id)
	if err != nil {
		return err
	}
	controller := findAvailableDiskController(devices)
	if controller == nil {
		return fmt.Errorf("no available disk controller found for vm %s", id)
	}
	device := &types.VirtualDisk{
		VirtualDevice: types.VirtualDevice{
			Backing: &types.VirtualDiskFlatVer2BackingInfo{
				DiskMode:        string(types.VirtualDiskModePersistent),
				ThinProvisioned: types.NewBool(true),
				VirtualDeviceFileBackingInfo: types.VirtualDeviceFileBackingInfo{
					FileName: vmware.DecodeID(options.Disk.ID),
				},
			},
		},
	}
	devices.AssignController(device, controller)
	return vm.AddDevice(ctx, device)
}

// like [object.VirtualDeviceList.FindDiskController] but will fall back to all available disk controllers
// if no scsi controller found, return nil
func findAvailableDiskController(devices object.VirtualDeviceList) types.BaseVirtualController {
	// scsi
	if c := devices.PickController((*types.VirtualSCSIController)(nil)); c != nil {
		return c
	}
	// sata
	if c := devices.PickController((*types.VirtualSATAController)(nil)); c != nil {
		return c
	}
	// nvme
	if c := devices.PickController((*types.VirtualNVMEController)(nil)); c != nil {
		return c
	}
	// ide
	if c := devices.PickController((*types.VirtualIDEController)(nil)); c != nil {
		return c
	}
	return nil
}

// DetachVirtualMachineDisk implements common.Provider.
func (p *Provider) DetachVirtualMachineDisk(ctx context.Context, id string, diskid string, options common.DetachVirtualMachineDiskOptions) error {
	vm, _, devices, err := p.getvmDevice(ctx, id)
	if err != nil {
		return err
	}
	for _, device := range devices {
		switch md := device.(type) {
		case *types.VirtualDisk:
			if b, ok := md.Backing.(types.BaseVirtualDeviceFileBackingInfo); ok {
				if vmware.DecodeID(diskid) == b.GetVirtualDeviceFileBackingInfo().FileName {
					return vm.RemoveDevice(ctx, true, device)
				}
			}
		}
	}
	return vmware.NewNotFoundError("disk", diskid)
}

func (p *Provider) listVirtualMachineDisks(ctx context.Context, id string) ([]common.Disk, error) {
	_, vmmo, devices, err := p.getvmDevice(ctx, id)
	if err != nil {
		return nil, err
	}
	type FileBackingDevice struct {
		VirtualDisk     *types.VirtualDisk
		FileBackingInfo *types.VirtualDeviceFileBackingInfo
	}
	var filebackingDevices []FileBackingDevice
	dsFileNames := map[types.ManagedObjectReference][]string{}
	for _, dev := range devices {
		switch typedev := dev.(type) {
		case *types.VirtualDisk:
			if filebacking, ok := typedev.Backing.(types.BaseVirtualDeviceFileBackingInfo); ok {
				filebackingDevices = append(filebackingDevices, FileBackingDevice{
					VirtualDisk:     typedev,
					FileBackingInfo: filebacking.GetVirtualDeviceFileBackingInfo(),
				})
				if ds := filebacking.GetVirtualDeviceFileBackingInfo().Datastore; ds != nil {
					dsFileNames[*ds] = append(dsFileNames[*ds], filebacking.GetVirtualDeviceFileBackingInfo().FileName)
				}
			}
		}
	}
	// list files fileinfo
	// it's expensive to list files one by one use [object.Datastore.Stat]
	// so we list all files in one datastore at once
	dsFileInfos := map[types.ManagedObjectReference]map[string]types.FileInfo{}
	for ds, fileNames := range dsFileNames {
		if len(fileNames) == 0 {
			continue
		}
		matchPatterns := make([]string, 0, len(fileNames))
		for _, file := range fileNames {
			dspath := DSPathFromString(file)
			matchPatterns = append(matchPatterns, dspath.Path)
		}
		fimap, err := p.statDatastoreFiles(ctx, ds.Value, matchPatterns)
		if err != nil {
			log.Error(err, "failed to stat datastore files", "datastore", ds.Value, "files", strings.Join(matchPatterns, ","))
		}
		if len(fimap) == 0 {
			continue
		}
		dsFileInfos[ds] = fimap
	}
	disks := make([]common.Disk, 0, len(filebackingDevices))
	// convert all file disk to disk
	for _, device := range filebackingDevices {
		fileName := device.FileBackingInfo.FileName
		filedspath := DSPathFromString(fileName)
		vdisk := device.VirtualDisk
		disk := common.Disk{
			Descripter: common.Descripter{
				ID:   vmware.EncodeID(fileName),
				Name: findDiskName(fileName),
				Annotations: map[string]string{
					// TODO: can we remove those?
					"Key":        fmt.Sprintf("%d", vdisk.Key),
					"UnitNumber": fmt.Sprintf("%d", vdisk.UnitNumber),
					"File":       fileName,
					"Summary":    vdisk.DeviceInfo.GetDescription().Summary,
					"Device":     vdisk.DeviceInfo.GetDescription().Label,
				},
			},
			Size:        *resource.NewQuantity(int64(vdisk.CapacityInKB)*1024, resource.BinarySI),
			DiskCluster: filedspath.Datastore,
			DiskClass:   "VMFS", // TODO: get from runtime
			Status: common.DiskStatus{
				Phase: "in-use",
				Mounts: []common.DiskMountInfo{
					{
						VirtualMachine: vmmo.Name,
						MountTime:      vmmo.Runtime.BootTime,
					},
				},
			},
		}
		if connectable := device.VirtualDisk.Connectable; connectable != nil {
			if !connectable.Connected {
				disk.Status.Phase = "not-connected"
			}
		}
		// TODO: can we remove this?
		if controller := devices.FindByKey(vdisk.ControllerKey); controller != nil {
			disk.Descripter.Annotations["Controller"] = devices.Name(controller)
		}
		if ds := device.FileBackingInfo.Datastore; ds != nil {
			fimap, ok := dsFileInfos[*ds]
			if ok {
				if fileInfo, ok := fimap[fileName]; ok {
					disk.Descripter.Annotations["FileSize"] = fmt.Sprintf("%d", fileInfo.FileSize)
					if fileInfo.Modification != nil {
						disk.CreationTimestamp = *fileInfo.Modification
					}
				}
			}
		}
		disks = append(disks, disk)
	}
	return disks, nil
}

// DSPathFromString converts a string to a DatastorePath.
// if the string is not a valid DatastorePath,
// it will return a DatastorePath with the Path set to the string.
func DSPathFromString(path string) object.DatastorePath {
	dspath := object.DatastorePath{}
	if !dspath.FromString(path) {
		dspath.Path = path
	}
	return dspath
}

// ListVirtualMachineDisks implements common.Provider.
func (p *Provider) ListVirtualMachineDisks(ctx context.Context, id string, options common.ListVirtualMachineDiskOptions) (common.List[common.Disk], error) {
	disks, err := p.listVirtualMachineDisks(ctx, id)
	if err != nil {
		return common.List[common.Disk]{}, vmware.IgnoreNotFound(err)
	}
	return common.PageListNoDefault(disks, options.ListOptions), nil
}

func findDiskName(input string) string {
	return path.Base(DSPathFromString(input).Path)
}
