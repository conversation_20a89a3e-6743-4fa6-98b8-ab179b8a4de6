package bob

import (
	"context"
	"fmt"
	"slices"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

// ResourceGroupOperation
// 使用tag 来标记资源组
// 创建的资源会被添加到资源组中
// vmware 中有 tagCategory 和 tag 概念
// tagCategory 用来分类 tag
// tag 用来关联至资源
var _ common.ResourceGroupOperation = &Provider{}

const (
	ISMCManaged = "ismc-managed"
)

// UpdateResourceGroup implements common.ResourceGroupOperation.
func (p *Provider) UpdateResourceGroup(ctx context.Context, id string, resourceGroup *common.ResourceGroup) error {
	return nil
}

// CreateResourceGroup implements ismc.ResourceGroupOperation.
func (p *Provider) CreateResourceGroup(ctx context.Context, resourceGroup *common.ResourceGroup) (*common.Descripter, error) {
	return p.Groups.CreateResourceGroup(ctx, resourceGroup)
}

// DeleteResourceGroup implements ismc.ResourceGroupOperation.
func (p *Provider) DeleteResourceGroup(ctx context.Context, id string) error {
	return p.Groups.DeleteResourceGroup(ctx, id)
}

// GetResourceGroup implements ismc.ResourceGroupOperation.
func (p *Provider) GetResourceGroup(ctx context.Context, name string) (*common.ResourceGroup, error) {
	return p.Groups.GetResourceGroup(ctx, name)
}

// ListResourceGroups implements ismc.ResourceGroupOperation.
func (p *Provider) ListResourceGroups(ctx context.Context, options common.ListResourceGroupOptions) (common.List[common.ResourceGroup], error) {
	options.Sort = def(options.Sort, "name")
	list := common.List[common.ResourceGroup]{Items: []common.ResourceGroup{}}
	categories, err := p.Groups.ListResourceGroup(ctx)
	if err != nil {
		return list, err
	}
	return common.PageList(categories, options.ListOptions), nil
}

// TagManager manages resource groups in VMware using tags.
// Resource groups are tags in the specific category.
type TagManager struct {
	cache             *vmware.ObjectCache
	defaultCategoryID string        // resource group tag category ID
	tagmanager        *tags.Manager // tag manager for creating and deleting tags
}

func NewResourceGroupManager(ctx context.Context, client *vmware.Client, cache *vmware.ObjectCache, dc *object.Datacenter) (*TagManager, error) {
	categoryID, err := client.GetOrCreateDefaultTagCategory(ctx)
	if err != nil {
		return nil, err
	}
	rg := &TagManager{
		defaultCategoryID: categoryID,
		tagmanager:        tags.NewManager(client.RestClient),
		cache:             cache,
	}
	tag, err := rg.GetOrCreateTagByName(ctx, ISMCManaged) // Ensure the ISMCManaged tag exists
	if err != nil {
		return nil, fmt.Errorf("ensure ISMC Managed tag exists: %w", err)
	}
	log.FromContext(ctx).Info("ismc managed tag", "id", tag.ID, "name", tag.Name)
	return rg, nil
}

func (c *TagManager) CheckGroupExists(ctx context.Context, group string) error {
	if group == "" {
		return nil
	}
	if _, err := c.GetTagByName(ctx, group); err != nil {
		return WrapError(err, "resource group", group)
	}
	return nil
}

func (c *TagManager) TagIt(ctx context.Context, ref types.ManagedObjectReference, tags ...string) error {
	for _, tag := range tags {
		if tag == "" {
			continue
		}
		tagresource, err := c.GetOrCreateTagByName(ctx, tag)
		if err != nil {
			return err
		}
		if err := c.tagmanager.AttachTag(ctx, tagresource.ID, ref); err != nil {
			return err
		}
	}
	return nil
}

func (c *TagManager) GetOrCreateTagByName(ctx context.Context, name string) (*tags.Tag, error) {
	tag, err := c.GetTagByName(ctx, name)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, WrapError(err, "tag", name)
		}
		// tag not found, create it
		log.V(2).Info("tag not found, creating new tag", "name", name)
		// create tag if not found
		newtag := &tags.Tag{
			Name:        name,
			Description: fmt.Sprintf("Auto-created tag for %s", name),
			CategoryID:  c.defaultCategoryID,
		}
		newid, err := c.tagmanager.CreateTag(ctx, newtag)
		if err != nil {
			return nil, WrapError(err, "tag", name)
		}
		newtag, err = c.tagmanager.GetTag(ctx, newid)
		if err != nil {
			return nil, WrapError(err, "tag", name)
		}
		// update cache
		c.cache.Set(&vmware.Tag{
			ManagedObjectReference: types.ManagedObjectReference{Type: vmware.TypeTag, Value: newtag.ID},
			Tag:                    *newtag,
		})
		return newtag, nil
	}
	return tag, nil
}

func (c *TagManager) GetTagByName(ctx context.Context, name string) (*tags.Tag, error) {
	filterfunc := func(tag vmware.Tag) bool {
		return tag.Tag.Name == name && tag.Tag.CategoryID == c.defaultCategoryID
	}
	taglist, err := vmware.CacheToList(c.cache, vmware.TypeTag, filterfunc)
	if err != nil {
		return nil, err
	}
	if len(taglist) > 0 {
		return &taglist[0].Tag, nil
	}
	// fallback to tag manager if not found in cache
	tag, err := c.tagmanager.GetTag(ctx, name)
	if err != nil {
		return tag, WrapError(err, "resource group", name)
	}
	// update cache
	c.cache.Set(&vmware.Tag{
		ManagedObjectReference: types.ManagedObjectReference{Type: vmware.TypeTag, Value: tag.ID},
		Tag:                    *tag,
	})
	return tag, nil
}

func (c *TagManager) ListResourceWithTags(ctx context.Context, tagnames []string) ([]mo.Reference, error) {
	tags, err := c.listTagsFromNames(ctx, tagnames)
	if err != nil {
		return nil, fmt.Errorf("get tags failed for: %s", err)
	}
	if len(tags) == 0 {
		return nil, nil // no tags found
	}
	tagids := make([]string, 0, len(tags))
	for _, tag := range tags {
		tagids = append(tagids, tag.Tag.ID)
	}
	return c.listResourceWithTags(ctx, tagids)
}

func (c *TagManager) listTagsFromNames(ctx context.Context, tagnames []string) ([]vmware.Tag, error) {
	tags, err := vmware.CacheToList(c.cache, vmware.TypeTag, func(tag vmware.Tag) bool {
		return slices.Contains(tagnames, tag.Tag.Name) && tag.Tag.CategoryID == c.defaultCategoryID
	})
	if err != nil {
		return nil, err
	}
	tagnames = slices.DeleteFunc(tagnames, func(t string) bool {
		return slices.ContainsFunc(tags, func(t2 vmware.Tag) bool {
			return t2.Tag.Name == t
		})
	})
	// if any tag is not found, we need to query it from tag manager
	if len(tagnames) > 0 {
		taglist, err := c.listTags(ctx, tagnames)
		if err != nil {
			return nil, fmt.Errorf("get tags failed for: %s", err)
		}
		for _, tag := range taglist {
			tags = append(tags, vmware.Tag{
				ManagedObjectReference: types.ManagedObjectReference{
					Type: vmware.TypeTag, Value: tag.ID,
				},
				Tag: tag,
			})
		}
	}
	return tags, nil
}

func (c *TagManager) listTags(ctx context.Context, names []string) ([]tags.Tag, error) {
	if len(names) == 0 {
		return nil, nil
	}
	taglist, err := c.tagmanager.GetTags(ctx)
	if err != nil {
		return nil, WrapError(err, "tag", "get tags")
	}
	var result []tags.Tag
	for _, tag := range taglist {
		if slices.Contains(names, tag.Name) {
			result = append(result, tag)
		}
	}
	return result, nil
}

func (c *TagManager) CreateResourceGroup(ctx context.Context, group *common.ResourceGroup) (*common.Descripter, error) {
	tag := &tags.Tag{
		Name:        group.Name,
		Description: group.Description,
		CategoryID:  c.defaultCategoryID,
	}
	newid, err := c.tagmanager.CreateTag(ctx, tag)
	if err != nil {
		return nil, WrapError(err, "resource group", group.Name)
	}
	newtag, err := c.tagmanager.GetTag(ctx, newid) // update cache
	if err != nil {
		return nil, WrapError(err, "resource group", group.Name)
	}
	// update cache
	c.cache.Set(&vmware.Tag{
		ManagedObjectReference: types.ManagedObjectReference{Type: vmware.TypeTag, Value: newid},
		Tag:                    *newtag,
	})
	rg := tagToResourceGroup(*newtag)
	return &rg.Descripter, nil
}

func (c *TagManager) DeleteResourceGroup(ctx context.Context, name string) error {
	tag, err := c.GetTagByName(ctx, name)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return WrapError(err, "resource group", name)
	}
	if err := c.tagmanager.DeleteTag(ctx, tag); err != nil {
		return WrapError(err, "resource group", name)
	}
	// remove from cache
	c.cache.Delete(types.ManagedObjectReference{Type: vmware.TypeTag, Value: tag.ID})
	return nil
}

func (c *TagManager) ListResourceGroup(ctx context.Context) ([]common.ResourceGroup, error) {
	tags, err := vmware.CacheToList(c.cache, vmware.TypeTag, func(tag vmware.Tag) bool {
		return tag.Tag.CategoryID == c.defaultCategoryID && tag.Tag.Name != ISMCManaged
	})
	if err != nil {
		return nil, err
	}
	resourceGroups := make([]common.ResourceGroup, 0, len(tags))
	for _, tag := range tags {
		resourceGroups = append(resourceGroups, tagToResourceGroup(tag.Tag))
	}
	return resourceGroups, nil
}

func tagToResourceGroup(tag tags.Tag) common.ResourceGroup {
	return common.ResourceGroup{
		Descripter: common.Descripter{
			ID:          tag.Name,
			Name:        tag.Name,
			Description: tag.Description,
		},
	}
}

// GetResourceGroup implements common.ResourceGroupOperation.
// GetResourceGroup may fallback to tag manager if the group is not found in cache.
// Incase of get immediatly after create, the cache may not be updated yet.
func (c *TagManager) GetResourceGroup(ctx context.Context, groupName string) (*common.ResourceGroup, error) {
	tag, err := c.GetTagByName(ctx, groupName)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil, err
		}
		return nil, WrapError(err, "resource group", groupName)
	}
	rg := tagToResourceGroup(*tag)
	return &rg, nil
}

func (c *TagManager) listResourceWithTags(ctx context.Context, tagids []string) ([]mo.Reference, error) {
	attachedObjects, err := c.tagmanager.ListAttachedObjectsOnTags(ctx, tagids)
	if err != nil {
		return nil, err
	}
	lists := make([][]mo.Reference, 0, len(attachedObjects))
	for _, item := range attachedObjects {
		lists = append(lists, item.ObjectIDs)
	}
	return intersection(lists), nil
}

func intersection[T comparable](lists [][]T) []T {
	for _, list := range lists {
		if len(list) == 0 {
			return nil // if any list is empty, return empty
		}
	}
	intersectionMap := make(map[T]int)
	for _, list := range lists {
		for _, item := range list {
			intersectionMap[item] += 1
		}
	}
	var intersection []T
	for item, count := range intersectionMap {
		// only include items that appear in all lists
		if count == len(lists) {
			intersection = append(intersection, item)
		}
	}
	return intersection
}
