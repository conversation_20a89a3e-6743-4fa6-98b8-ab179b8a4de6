package bob

import (
	"context"
	"testing"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
	"xiaoshiai.cn/core/ismc/vmoci"
)

// 设置自定义的属性
func TestSetCustomValue(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()
	vm, err := vmware.FromResourceID[*object.VirtualMachine](ctx, provider.Client.Finder, "vm-8048", TypeVirtualMachine)
	if err != nil {
		t.Fatal(err)
	}
	if err := vm.SetCustomValue(ctx, vmware.ImageCustomValueKey, "registry.develop.xiaoshiai.cn/alpine/alpine:latest"); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestGetCustomValue(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()
	vm, err := vmware.FromResourceToMo[mo.VirtualMachine](ctx, provider.Client.VimClient, "vm-8048", TypeVirtualMachine, []string{"summary"})
	if err != nil {
		t.Fatal(err)
	}
	summary := vm.Summary
	for _, vc := range summary.CustomValue {
		cfs, ok := vc.(*types.CustomFieldStringValue)
		if !ok {
			continue
		}
		key := cfs.Key
		value := cfs.Value
		t.Log("key:", key, "value:", value)
	}
	t.Log("success")
}

func TestListHosts(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()
	hosts, err := provider.ListHosts(ctx, common.ListHostOptions{
		ListOptions: common.ListOptions{
			Page: 1,
			Size: 15,
			// Search: "abc",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	for _, host := range hosts.Items {
		t.Logf("host: %v", host)
	}
}

func TestListImages(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()
	images, err := provider.ListImages(ctx, common.ListImageOptions{})
	if err != nil {
		t.Fatal(err)
	}
	for _, image := range images.Items {
		t.Logf("image: %v", image)
	}
	t.Log("finished")
}

var testimage = "registry.develop.xiaoshiai.cn/alpine/alpine:latest"

func TestProvider_ListImages(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()
	var imageID string
	images, err := provider.ListImages(ctx, common.ListImageOptions{})
	if err != nil {
		t.Fatal(err)
	}
	for _, image := range images.Items {
		if image.Name == testimage {
			imageID = image.ID
			break
		}
	}
	if imageID == "" {
		t.Fatal("not found image:", testimage)
	}

	vmociopt := vmoci.Options{}
	vmoclcli, err := vmoci.NewClient(vmociopt)
	if err != nil {
		t.Fatal(err)
		return
	}

	img, err := provider.GetImage(ctx, imageID)
	if err != nil {
		if !errors.IsNotFound(err) {
			t.Fatal(err)
		}
		// 此处使用镜像名称
		file, err := vmoclcli.PullImageFile(ctx, testimage, vmoci.PullImageFileOptions{
			Formats: []vmoci.ImageFormat{vmoci.ImageFormatOVA},
		})
		if err != nil {
			t.Fatal(err)
		}

		newimg := &common.Image{
			Descripter: common.Descripter{
				Name: testimage,
			},
		}
		content, err := file.File.GetContent(ctx)
		if err != nil {
			t.Fatal(err)
		}
		defer content.Close()

		newfile := common.FileContent{
			FileName:      file.File.Filename,
			Content:       content,
			ContentType:   file.File.ContentType,
			ContentLength: file.File.ContentLength,
		}
		if _, err := provider.ImportImage(ctx, newimg, newfile); err != nil {
			t.Fatal(err)
		}
	}
	_ = img
}
