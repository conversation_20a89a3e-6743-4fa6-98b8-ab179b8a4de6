package bob

import (
	"context"

	"xiaoshiai.cn/core/ismc/common"
)

var _ common.DiskSnapshotOperation = &Provider{}

// CreateDiskSnapshot implements ismc.DiskSnapshotOperation.
func (p *Provider) CreateDiskSnapshot(ctx context.Context, snapshot *common.DiskSnapshot) error {
	return nil
}

// ListDiskSnapshots implements ismc.DiskSnapshotOperation.
func (p *Provider) ListDiskSnapshots(ctx context.Context, options common.ListDiskSnapshotOption) (common.List[common.DiskSnapshot], error) {
	return common.List[common.DiskSnapshot]{}, nil
}

// RemoveDiskSnapshot implements ismc.DiskSnapshotOperation.
func (p *Provider) RemoveDiskSnapshot(ctx context.Context, name string) error {
	return nil
}
