package bob

import (
	"context"

	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.DiscoveryOperation = &Provider{}

// Version implements common.DiscoveryOperation.
func (p *Provider) GetVersion(ctx context.Context) (*common.Version, error) {
	about := p.Client.VimClient.ServiceContent.About
	vendorversion := common.VersionInfo{
		Name:    about.FullName,
		Build:   about.Build,
		Vendor:  about.Vendor,
		Version: about.FullName,
	}
	agentversion := version.Get()
	agentv := common.VersionInfo{
		Name:      "agent",
		Version:   agentversion.GitVersion,
		Build:     agentversion.GitCommit,
		BuildTime: agentversion.BuildDate,
	}
	v := &common.Version{Vendor: vendorversion, Agent: agentv}
	return v, nil
}
