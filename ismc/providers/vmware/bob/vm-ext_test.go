package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestProvider_ListVirtualMachineNetworkInterfaces(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)

	vmid := "vm-5079"
	vm, err := p.getvm(ctx, vmid)
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log(vm)
	list, err := p.ListVirtualMachineNetworkInterfaces(ctx, vmid, common.ListVirtualMachineNetworkOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(list)

	disklist, err := p.ListVirtualMachineDisks(ctx, vmid, common.ListVirtualMachineDiskOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(disklist)
}

func TestListVirtualMachineDisks(t *testing.T) {
	ctx := context.Background()
	p := SetupTestProvider(t)
	disks, err := p.ListVirtualMachineDisks(ctx, "vm-8801", common.ListVirtualMachineDiskOptions{})
	if err != nil {
		t.Fatal(err)
	}
	for _, disk := range disks.Items {
		t.Log(disk)
	}
	t.Log("success")
}
