package bob

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"path"
	"slices"
	"strconv"
	"strings"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/vapi/vcenter"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"golang.org/x/exp/rand"
	"golang.org/x/sync/errgroup"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/proxy"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	libproxy "xiaoshiai.cn/common/rest/proxy"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

const (
	TypeVirtualMachine = "VirtualMachine"
	TypeNetwork        = "Network"
)

const KeepVMDataDisksOnRemove = false

/*
虚拟机使用tag来作为资源组，资源组下创建虚拟机时，虚拟机会自动添加资源组标签
在datastore中也会根据资源组进行区分，每个资源组下面有一个文件夹
虚拟机创建时若选择了资源组，则创建在对应资源组的文件夹下。
资源组文件夹下的磁盘能够在查询该资源组的磁盘是被过滤。
*/
var _ common.VirtualMachineOperation = &Provider{}

// ListVirtualMachines implements ismc.VirtualMachineOperation.
func (p *Provider) ListVirtualMachines(ctx context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	list := common.List[common.VirtualMachine]{Items: []common.VirtualMachine{}}
	vmfields := []string{"name", "tag", "disabledMethod", "recentTask", "config", "runtime", "guest", "network", "layout", "summary"}
	var objectTags []string
	if options.ResourceGroup != "" {
		objectTags = append(objectTags, options.ResourceGroup)
	}
	for _, tag := range options.Tags {
		if tag == "" {
			continue
		}
		objectTags = append(objectTags, tag)
	}

	var vms []mo.VirtualMachine
	if len(objectTags) > 0 {
		objects, err := p.Groups.ListResourceWithTags(ctx, objectTags)
		if err != nil {
			return list, err
		}
		vmrefs := []types.ManagedObjectReference{}
		for _, ref := range objects {
			thisref := ref.Reference()
			if thisref.Type != TypeVirtualMachine {
				continue
			}
			vmrefs = append(vmrefs, thisref)
		}
		groupvms, err := p.listvmsByRefs(ctx, vmrefs, vmfields...)
		if err != nil {
			return list, err
		}
		vms = groupvms
	} else {
		allvms, err := p.listvms(ctx, vmfields...)
		if err != nil {
			return list, err
		}
		vms = allvms
	}
	items, err := p.coverWithGorutine(ctx, vms)
	if err != nil {
		return list, err
	}
	return common.PageList(items, options.ListOptions), nil
}

func (p *Provider) coverWithGorutine(ctx context.Context, vms []mo.VirtualMachine) ([]common.VirtualMachine, error) {
	result := base.SafeSlice[common.VirtualMachine]{}
	eg := errgroup.Group{}
	for _, vm := range vms {
		vm := vm
		eg.Go(func() error {
			item := p.ConvertVMToISMCVM(ctx, vm)
			result.Append(item)
			return nil
		})
	}
	_ = eg.Wait()
	return result.Get(), nil
}

func virtualMachineIsReady(disables []string) bool {
	if len(disables) == 0 {
		return false
	}
	return !slices.Contains(disables, "ReconfigVM_Task")
}

func ResourceGroupFromTags(tags []tags.Tag) string {
	for _, tag := range tags {
		if tag.Name == ISMCManaged {
			continue
		}
		return tag.Name
	}
	return ""
}

func (p *Provider) ConvertVMToISMCVM(ctx context.Context, vm mo.VirtualMachine) common.VirtualMachine {
	tags, _ := p.listObjectTags(ctx, vm.Self)
	item := common.VirtualMachine{
		Descripter: common.Descripter{
			ID:            vm.Self.Value,
			Name:          vm.Name,
			UID:           vm.Self.ServerGUID,
			Annotations:   map[string]string{},
			Labels:        p.Fields.CustomValuesToLabels(vm.Summary.CustomValue),
			ResourceGroup: ResourceGroupFromTags(tags),
		},
		Features: map[string]string{},
		Status: common.VirtualMachineStatus{
			Addtional:  map[string]string{},
			PowerState: common.PowerStateUnknown,
			Phase:      "CHANGING",
		},
	}
	if item.Labels != nil {
		// image from labels
		item.Image.Name = item.Labels[vmware.ImageCustomValueKey]
	}
	for _, tag := range tags {
		item.Tags = append(item.Tags, tag.Name)
	}
	if virtualMachineIsReady(vm.DisabledMethod) {
		item.Status.Phase = "ACTIVE"
	}
	runtime := vm.Runtime
	switch runtime.PowerState {
	case types.VirtualMachinePowerStatePoweredOn:
		item.Status.PowerState = common.PowerStateOn
		item.Power = common.PowerStateOn
	case types.VirtualMachinePowerStatePoweredOff:
		item.Status.PowerState = common.PowerStateOff
		item.Power = common.PowerStateOff
	case types.VirtualMachinePowerStateSuspended:
		item.Status.PowerState = common.PowerStateSuspended
		item.Power = common.PowerStateSuspended
	}
	if boottime := runtime.BootTime; boottime != nil {
		item.Status.BootTime = *boottime
	}
	if host := runtime.Host; host != nil {
		item.Zone = host.Value
	}
	// config
	if config := vm.Config; config != nil {
		if creationtime := config.CreateDate; creationtime != nil {
			item.CreationTimestamp = *creationtime
		}
		item.UID = config.InstanceUuid
		if pathname := config.Files.VmPathName; pathname != "" {
			item.Status.Addtional["vmPathName"] = pathname
		}
		item.Status.Addtional["guestID"] = config.GuestId
		item.Status.Addtional["guestFullName"] = config.GuestFullName
		item.Status.Addtional["vmxVersion"] = config.Version
		item.Status.Guest = common.VirtualMachineGuest{
			OSName: config.GuestId,
		}
		// instance type
		instanceType := p.findInstanceType(config.Hardware)
		item.InstanceType = common.VirtualMachineInstanceTypeRef{
			ID:   instanceType,
			Name: instanceType,
		}
		if bootoptions := config.BootOptions; bootoptions != nil {
			_ = bootoptions
		}
		switch config.Firmware {
		case "bios":
			item.Status.BootMode = common.BootModeBIOS
		case "uefi":
			item.Status.BootMode = common.BootModeUEFI
		default:
			item.Status.BootMode = common.BootMode(config.Firmware)
		}
		hardware := config.Hardware
		resources := map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:    *resource.NewQuantity(int64(hardware.NumCPU), resource.DecimalSI),
			common.ResourceMemory: *resource.NewQuantity(int64(hardware.MemoryMB)*1024*1024, resource.BinarySI),
		}
		item.Status.Resources = resources
		// disks
		disks, nets := parseVmDisksAndNetworks(hardware.Device)
		item.Interfaces = nets
		diskrefs := make([]common.VirtualMachineDiskRef, 0, len(disks))
		for _, disk := range disks {
			diskrefs = append(diskrefs, common.VirtualMachineDiskRef{Disk: disk, UseExisting: false})
		}
		item.Disks = diskrefs
		// features
		if isTrue(config.CpuHotAddEnabled) {
			item.Features["cpuHotAddEnabled"] = "true"
		}
		if isTrue(config.MemoryHotAddEnabled) {
			item.Features["memoryHotAddEnabled"] = "true"
		}
	}
	// guest
	if guest := vm.Guest; guest != nil {
		item.Status.Guest.HostName = guest.HostName
		item.Status.Guest.OSType = converOSType(guest.GuestFamily)
		item.Status.Guest.OSName = guest.GuestFullName
		item.Status.Guest.OSStatus = common.OSStatus(guest.GuestState)
		item.Status.Guest.Interfaces = getGuestsInterfaces(vm)
	}
	return item
}

func isTrue(b *bool) bool {
	return b != nil && *b
}

func getGuestsInterfaces(vm mo.VirtualMachine) []common.VirtualMachineNetworkInterface {
	if vm.Config == nil {
		return nil
	}
	var ins []common.VirtualMachineNetworkInterface
	for _, dev := range vm.Config.Hardware.Device {
		switch netdev := dev.(type) {
		case types.BaseVirtualEthernetCard:
			ethcard := netdev.GetVirtualEthernetCard()
			iface := common.VirtualMachineNetworkInterface{
				MAC:       ethcard.MacAddress,
				Connected: true,
			}
			ipv4, ipv6 := getIPsFromMacAddress(vm.Guest, ethcard.MacAddress)
			iface.IPs = append(iface.IPs, ipv4...)
			iface.IPs = append(iface.IPs, ipv6...)
			ins = append(ins, iface)
		}
	}
	return ins
}

func parseVmDisksAndNetworks(devices []types.BaseVirtualDevice) ([]common.Disk, []common.VirtualMachineInterfaceRef) {
	var (
		disks    []common.Disk
		networks []common.VirtualMachineInterfaceRef
	)
	for _, dev := range devices {
		switch typedev := dev.(type) {
		case *types.VirtualDisk:
			disk := common.Disk{
				Descripter: common.Descripter{
					UID:         typedev.DiskObjectId,
					Annotations: map[string]string{},
				},
				Size: *resource.NewQuantity(int64(typedev.CapacityInKB)*1024, resource.BinarySI),
			}
			if backing := typedev.Backing; backing != nil {
				switch typedbacking := backing.(type) {
				case *types.VirtualDiskFlatVer2BackingInfo:
					disk.ID = vmware.EncodeID(typedbacking.FileName)
					disk.Name = findDiskName(typedbacking.FileName)
					dspath := new(object.DatastorePath)
					if dspath.FromString(typedbacking.FileName) {
						disk.DiskCluster = dspath.Datastore
					}
				}
			}
			disks = append(disks, disk)
		case types.BaseVirtualEthernetCard:
			ethcard := typedev.GetVirtualEthernetCard()
			nt := common.VirtualMachineInterfaceRef{
				ID:         ethcard.ExternalId,
				SubNetwork: DefaultSubnetworkID,
			}
			switch backing := ethcard.Backing.(type) {
			case *types.VirtualEthernetCardDistributedVirtualPortBackingInfo:
				nt.Network = backing.Port.SwitchUuid
			case *types.VirtualEthernetCardNetworkBackingInfo:
				nt.Network = backing.Network.Value
			}
			networks = append(networks, nt)
		}
	}
	return disks, networks
}

func getIPsFromMacAddress(guest *types.GuestInfo, mac string) ([]string, []string) {
	if guest == nil {
		return nil, nil
	}
	var ipv4, ipv6 []string
	for _, nic := range guest.Net {
		if nic.MacAddress == mac {
			for _, ip := range nic.IpAddress {
				parse := net.ParseIP(ip)
				if parse == nil {
					continue
				}
				if parse.To4() != nil {
					ipv4 = append(ipv4, ip)
				} else {
					ipv6 = append(ipv6, ip)
				}
			}
		}
	}
	return ipv4, ipv6
}

func parseVmNetworkInterfaces(mo mo.VirtualMachine) []common.NetworkInterface {
	if mo.Config == nil {
		return nil
	}
	interfaces := []common.NetworkInterface{}

	devices := object.VirtualDeviceList(mo.Config.Hardware.Device)
	for _, dev := range devices {
		switch netdev := dev.(type) {
		case types.BaseVirtualEthernetCard:
			ethcard := netdev.GetVirtualEthernetCard()
			d := dev.GetVirtualDevice()
			info := d.DeviceInfo.GetDescription()
			iface := common.NetworkInterface{
				Descripter: common.Descripter{
					ID:   devices.Name(dev),
					Name: info.Label,
				},
				MAC: ethcard.MacAddress,
			}
			iface.IPv4s, iface.IPv6s = getIPsFromMacAddress(mo.Guest, ethcard.MacAddress)
			switch backing := ethcard.Backing.(type) {
			case *types.VirtualEthernetCardDistributedVirtualPortBackingInfo:
				iface.VirtualNetwork.ID = backing.Port.SwitchUuid
				iface.VirtualNetwork.Name = backing.Port.PortKey
				iface.VirtualSubnetwork.ID = DefaultSubnetworkID
				iface.VirtualSubnetwork.Name = DefaultSubnetworkID
				if desc := ethcard.DeviceInfo.GetDescription(); desc != nil {
					iface.Name = desc.Label
				}
			case *types.VirtualEthernetCardNetworkBackingInfo:
				// iface.Name = backing.DeviceName
				iface.VirtualNetwork.ID = backing.Network.Value
				iface.VirtualNetwork.Name = backing.DeviceName
				iface.VirtualSubnetwork.ID = DefaultSubnetworkID
				iface.VirtualSubnetwork.Name = DefaultSubnetworkID
			}
			interfaces = append(interfaces, iface)
		}
	}
	return interfaces
}

func converOSType(family string) common.OSType {
	switch family {
	case "windowsGuest":
		return common.OSWindows
	case "linuxGuest":
		return common.OSLinux
	case "otherGuest":
		return common.OSUnknown
	default:
		return common.OSType(family)
	}
}

func (p *Provider) configureDisks(ctx context.Context, ds *mo.Datastore, vm *mo.VirtualMachine, disks []common.VirtualMachineDiskRef, resourceGroup string, into *types.VirtualMachineConfigSpec) error {
	devices := object.VirtualDeviceList(vm.Config.Hardware.Device)
	for idx, disk := range disks {
		// first disk as the system disk and resize it to [common.VirtualMachineDiskRef.Size]
		if idx == 0 {
			// find first disk
			var systemDisk *types.VirtualDisk
			for _, device := range devices {
				if disk, ok := device.(*types.VirtualDisk); ok {
					systemDisk = disk
					break
				}
			}
			if systemDisk != nil {
				if newCapacityInBytes := disk.Size.Value(); newCapacityInBytes != systemDisk.CapacityInBytes {
					systemDisk.CapacityInBytes = newCapacityInBytes
					deviceConfigSpec := types.VirtualDeviceConfigSpec{
						Operation: types.VirtualDeviceConfigSpecOperationEdit,
						Device:    systemDisk,
					}
					into.DeviceChange = append(into.DeviceChange, &deviceConfigSpec)
				}
				continue
			}
			// if vm not disk, treat it as new disk
		}
		controller := findAvailableDiskController(devices)
		if controller == nil {
			return fmt.Errorf("failed to find available disk controller for vm %s", vm.Name)
		}
		backing := &types.VirtualDiskFlatVer2BackingInfo{
			DiskMode:        string(types.VirtualDiskModePersistent),
			ThinProvisioned: types.NewBool(true),
		}
		diskdev := &types.VirtualDisk{
			VirtualDevice: types.VirtualDevice{Backing: backing},
		}
		changeSpec := &types.VirtualDeviceConfigSpec{
			Operation: types.VirtualDeviceConfigSpecOperationAdd,
			Device:    diskdev,
		}
		if disk.UseExisting {
			// attach existing disk
			backing.FileName = vmware.DecodeID(disk.Disk.ID)
			dsref := ds.Reference()
			backing.Datastore = &dsref
			diskdev.CapacityInKB = 0      // must set to zero
			changeSpec.FileOperation = "" // set to empty when "attach" exists file
		} else {
			// new disk
			diskname := disk.Disk.Name
			if diskname == "" {
				diskname = strconv.Itoa(idx)
			}
			diskDSPath, _ := newDiskDSPath(*ds, vm.Name+"-"+diskname, resourceGroup)
			parentdir := path.Dir(diskDSPath.Path)
			if parentdir != "" {
				// ensure parent directory exists
				if err := p.ensureDatastoreDirectory(ctx, ds.Name, parentdir); err != nil {
					return fmt.Errorf("ensure parent directory %s exists: %w", parentdir, err)
				}
			}
			diskdev.CapacityInKB = int64(disk.Size.Value() / 1024)
			backing.FileName = diskDSPath.String()
			changeSpec.FileOperation = types.VirtualDeviceConfigSpecFileOperationCreate
		}
		devices.AssignController(diskdev, controller)
		devices = append(devices, diskdev)
		into.DeviceChange = append(into.DeviceChange, changeSpec)
	}
	return nil
}

func (p *Provider) ensureDatastoreDirectory(ctx context.Context, dsname, dirname string) error {
	if dsname == "" || dirname == "" {
		return nil
	}
	fulldspath := (&object.DatastorePath{Datastore: dsname, Path: dirname}).String()
	cachepath, err := vmware.CacheToGet[*vmware.DatastoreFile](p.Cache, types.ManagedObjectReference{
		Type:  vmware.TypeDatastoreFile,
		Value: fulldspath,
	})
	if err != nil {
		if !errors.IsNotFound(err) {
			return fmt.Errorf("get datastore file %s: %w", fulldspath, err)
		}
		fm := object.NewFileManager(p.Client.VimClient.Client)
		if err := fm.MakeDirectory(ctx, fulldspath, p.DC, true); err != nil {
			if vmware.IsAlreadyExists(err) {
				return nil
			}
			return fmt.Errorf("make directory %s: %w", fulldspath, err)
		}
	}
	_ = cachepath
	return nil
}

func (p *Provider) configureNetworkInterfaces(ctx context.Context, vm *mo.VirtualMachine, interfaces []common.VirtualMachineInterfaceRef, reConfigSpec *types.VirtualMachineConfigSpec) error {
	devices := object.VirtualDeviceList(vm.Config.Hardware.Device)
	// remove all network interfaces event if ovf deploy auto add an interface
	for _, device := range devices {
		switch typeddev := device.(type) {
		case types.BaseVirtualEthernetCard:
			vnetdev := typeddev.GetVirtualEthernetCard()
			if vnetdev.Backing != nil {
				reConfigSpec.DeviceChange = append(reConfigSpec.DeviceChange, &types.VirtualDeviceConfigSpec{
					Operation: types.VirtualDeviceConfigSpecOperationRemove,
					Device:    vnetdev,
				})
			}
		}
	}
	// add new network interfaces according to interfaces
	for _, network := range interfaces {
		backing, err := p.networkref(network.Network).EthernetCardBackingInfo(ctx)
		if err != nil {
			return err
		}
		netdev, err := object.EthernetCardTypes().CreateEthernetCard("", backing)
		if err != nil {
			return err
		}
		reConfigSpec.DeviceChange = append(reConfigSpec.DeviceChange, &types.VirtualDeviceConfigSpec{
			Operation: types.VirtualDeviceConfigSpecOperationAdd,
			Device:    netdev,
		})
	}
	return nil
}

// CreateVirtualMachine implements ismc.VirtualMachineOperation.
func (p *Provider) CreateVirtualMachine(ctx context.Context, vm *common.VirtualMachine, options common.CreateVirtualMachineOptions) (*common.Descripter, error) {
	log := log.FromContext(ctx).WithValues("vm-name", vm.Name)
	if vm.Zone == "" {
		return nil, errors.NewBadRequest("zone is required")
	}
	// check image
	if vm.Image.ID == "" {
		if vm.Image.Name == "" {
			return nil, errors.NewBadRequest("image is required")
		}
		// from image name
		if imgname := vm.Image.Name; imgname != "" {
			imagelist, err := p.ListImages(ctx, common.ListImageOptions{Name: imgname})
			if err != nil {
				return nil, fmt.Errorf("list images: %w", err)
			}
			if len(imagelist.Items) == 0 {
				return nil, errors.NewBadRequest(fmt.Sprintf("image %s not found", imgname))
			}
			vm.Image.ID = imagelist.Items[0].ID
		}
	}
	if vm.Image.ID == "" {
		return nil, errors.NewBadRequest("image is required")
	}
	image, err := p.GetImage(ctx, vm.Image.ID) // validate image
	if err != nil {
		return nil, errors.NewBadRequest(fmt.Sprintf("image %s: %v", vm.Image.ID, err))
	}
	// check system disk size
	if len(vm.Disks) != 0 {
		sysdisk := vm.Disks[0]
		if !sysdisk.UseExisting && sysdisk.Size.Value() < image.MinDisk.Value() {
			return nil, errors.NewBadRequest(fmt.Sprintf("system disk size %s is less than image min disk size %s", sysdisk.Size.String(), image.MinDisk.String()))
		}
	}
	resourceGroup := vm.ResourceGroup
	log.Info("start create virtual machine", "resourceGroup", resourceGroup)
	//  group
	if err := p.Groups.CheckGroupExists(ctx, resourceGroup); err != nil {
		return nil, err
	}
	// vmfolder
	vmfolder, err := p.ensureVmFolder(ctx, resourceGroup, true)
	if err != nil {
		log.Error(err, "get vm folder", "resourceGroup", resourceGroup)
		return nil, err
	}
	// host
	hostsystemRef := object.NewHostSystem(p.Client.VimClient.Client,
		types.ManagedObjectReference{Type: TypeHostSystem, Value: vm.Zone})
	resourcePool, err := hostsystemRef.ResourcePool(ctx)
	if err != nil {
		log.Error(err, "get resource pool", "hostsystem", vm.Zone)
		return nil, err
	}
	// resource
	if vm.InstanceType.ID == "" {
		return nil, errors.NewBadRequest("instance type is required")
	}
	// instance type
	instanceType, err := p.GetInstanceType(ctx, vm.InstanceType.ID)
	if err != nil {
		return nil, err
	}
	cpu := instanceType.Resources[common.ResourceCPU]
	memory := instanceType.Resources[common.ResourceMemory]
	if cpu.IsZero() || memory.IsZero() {
		return nil, errors.NewInternalError(fmt.Errorf("instance type %s has no cpu or memory", vm.InstanceType.ID))
	}
	datastore, err := p.selectDatastoreToCreateVM(ctx, hostsystemRef.Reference().Value, vm.Disks)
	if err != nil {
		return nil, err
	}
	properties := []vcenter.Property{}
	if vm.Password != "" {
		properties = append(properties, vcenter.Property{
			ID:          "password",
			Label:       "Default User's password",
			Description: "If set, the default user's password will be set to this value to allow password based login.  The password will be good for only a single login.  If set to the string 'RANDOM' then a random password will be generated, and written to the console.",
			Type:        "string",
			Value:       vm.Password,
		})
	}
	if vm.CloudInit.UserData != "" {
		properties = append(properties, vcenter.Property{
			ID:          "user-data",
			Label:       "Encoded user-data",
			Description: "In order to fit into a xml attribute, this value is base64 encoded . It will be decoded, and then processed normally as user-data.",
			Type:        "string",
			Value:       vm.CloudInit.UserData,
		})
	}
	addtional := []vcenter.AdditionalParams{}
	if len(properties) != 0 {
		addtional = append(addtional, vcenter.AdditionalParams{
			Class:      vcenter.ClassPropertyParams,
			Type:       vcenter.TypePropertyParams,
			Properties: properties,
		})
	}
	deploy := VMDeploy{
		ResourceGroup: resourceGroup,
		LibraryItemID: vm.Image.ID,
		Zone:          vm.Zone,
		CPU:           cpu,
		Labels:        vm.Labels,
		Memory:        memory,
		Disks:         vm.Disks,
		Networks:      vm.Interfaces,
		Deploy: vcenter.Deploy{
			DeploymentSpec: vcenter.DeploymentSpec{
				Name:                vm.Name,
				DefaultDatastoreID:  datastore.Reference().Value,
				AcceptAllEULA:       true,
				Annotation:          vm.Description,
				StorageProvisioning: "thin",
				AdditionalParams:    addtional,
			},
			Target: vcenter.Target{
				ResourcePoolID: resourcePool.Reference().Value,
				HostID:         hostsystemRef.Reference().Value,
				FolderID:       vmfolder.Reference().Value,
			},
		},
	}
	if options.Wait {
		return p.libraryItemDeploy(ctx, deploy)
	} else {
		// nolint errcheck
		go p.libraryItemDeploy(context.Background(), deploy)
		return &common.Descripter{}, nil
	}
}

type VMDeploy struct {
	ResourceGroup string
	LibraryItemID string
	Zone          string
	CPU           resource.Quantity
	Memory        resource.Quantity
	Labels        map[string]string
	Disks         []common.VirtualMachineDiskRef
	Networks      []common.VirtualMachineInterfaceRef
	vcenter.Deploy
}

func (p *Provider) libraryItemDeploy(ctx context.Context, vm VMDeploy) (*common.Descripter, error) {
	log := log.FromContext(ctx).WithValues("vm-name", vm.Name, "resourceGroup", vm.ResourceGroup)
	log.Info("start deploy vm")
	libitem, err := p.Client.LibraryManager.GetLibraryItem(ctx, vm.LibraryItemID)
	if err != nil {
		return nil, fmt.Errorf("get library item %s: %w", vm.LibraryItemID, err)
	}
	labels := vm.Labels
	if labels == nil {
		labels = map[string]string{}
	}
	// image is store as a label
	labels[vmware.ImageCustomValueKey] = libitem.Name

	dsref := &types.ManagedObjectReference{
		Type:  TypeDatastore,
		Value: vm.Deploy.DefaultDatastoreID,
	}
	// get direct from vmware,do not from cache
	dsmo, err := vmware.FindReferenceInto[*types.ManagedObjectReference, mo.Datastore](ctx, p.Client.VimClient, dsref, nil)
	if err != nil {
		return nil, err
	}
	// deploy the library item
	vmref, err := vcenter.NewManager(p.Client.RestClient).DeployLibraryItem(ctx, vm.LibraryItemID, vm.Deploy)
	if err != nil {
		return nil, err
	}
	vmEntity := object.NewVirtualMachine(p.Client.VimClient.Client, *vmref)
	// get direct from vmware,do not from cache
	vmmo, err := vmware.FindReferenceInto[*object.VirtualMachine, mo.VirtualMachine](ctx, p.Client.VimClient, vmEntity, nil)
	if err != nil {
		return nil, err
	}
	resourceGroup := vm.ResourceGroup
	if err := p.Groups.TagIt(ctx, vmEntity.Reference(), ISMCManaged, resourceGroup); err != nil {
		log.Error(err, "tag vm")
	}
	if err := p.SetLabels(ctx, vmEntity.Reference(), labels); err != nil {
		log.Error(err, "set vm labels")
	}
	// reconfigure the VM
	reConfigSpec := &types.VirtualMachineConfigSpec{
		NumCPUs:  int32(vm.CPU.Value()),
		MemoryMB: int64(vm.Memory.Value() / 1024 / 1024),
		// CPU HotPlug not commonly supported by guest os.
		// keep it unset to use default.
		CpuHotAddEnabled:    nil,
		CpuHotRemoveEnabled: nil,
		MemoryHotAddEnabled: nil,
	}
	log.Info("configure vm disks")
	if err := p.configureDisks(ctx, dsmo, vmmo, vm.Disks, resourceGroup, reConfigSpec); err != nil {
		log.Error(err, "set disks")
		return nil, err
	}
	log.Info("configure vm network")
	if err := p.configureNetworkInterfaces(ctx, vmmo, vm.Networks, reConfigSpec); err != nil {
		log.Error(err, "set network")
		return nil, err
	}
	log.Info("reconfiguring vm")
	reconfigurationTask, err := vmEntity.Reconfigure(ctx, *reConfigSpec)
	if err != nil {
		log.Error(err, "reconfigure vm")
		return nil, err
	}
	if err := reconfigurationTask.Wait(ctx); err != nil {
		log.Error(err, "wait for reconfiguration task")
		return nil, err
	}
	log.Info("power on vm")
	task, err := vmEntity.PowerOn(ctx)
	if err != nil {
		return nil, err
	}
	if err := task.Wait(ctx); err != nil {
		return nil, err
	}
	return &common.Descripter{
		ID:            vmEntity.Reference().Value,
		Name:          vmEntity.Name(),
		ResourceGroup: resourceGroup,
	}, nil
}

func (p *Provider) SetLabels(ctx context.Context, ref types.ManagedObjectReference, labels map[string]string) error {
	var errs []error
	for k, v := range labels {
		if k == "" || v == "" {
			continue
		}
		if err := p.Fields.SetKeyValue(ctx, ref, k, v); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

func (p *Provider) selectDatastoreToCreateVM(ctx context.Context, host string, disks []common.VirtualMachineDiskRef) (*mo.Datastore, error) {
	var datastores []mo.Datastore
	if host == "" {
		zones, err := p.ListZones(ctx, common.ListZoneOptions{})
		if err != nil {
			return nil, err
		}
		availablezones := []string{}
		for _, zone := range zones.Items {
			if !zone.Status.Ready {
				continue
			}
			availablezones = append(availablezones, zone.ID)
		}
		// rand select a zone
		if len(availablezones) == 0 {
			return nil, errors.NewInternalError(fmt.Errorf("no available zone"))
		}
		host = availablezones[rand.Intn(len(availablezones))]
	}
	datastores, err := p.listDatastores(ctx, host)
	if err != nil {
		return nil, err
	}
	limitedDiskClass := map[string]struct{}{}
	limitedDiskCluster := map[string]struct{}{}
	for _, disk := range disks {
		if !disk.UseExisting && disk.DiskClass != "" {
			limitedDiskClass[disk.DiskClass] = struct{}{}
		}
		if !disk.UseExisting && disk.Disk.DiskCluster != "" {
			limitedDiskCluster[disk.Disk.DiskCluster] = struct{}{}
		}
		// TODO: check exists disk's cluster and class
		if disk.UseExisting && disk.Disk.ID == "" {
			return nil, errors.NewBadRequest("disk ID is required when UseExisting disk")
		}
	}
	// select a datastores from host(zone)
	var available []mo.Datastore
	for _, ds := range datastores {
		if !ds.Summary.Accessible {
			continue
		}
		freepercent := float64(ds.Summary.FreeSpace) / float64(ds.Summary.Capacity)
		if freepercent < 0.1 {
			continue
		}
		if len(limitedDiskClass) > 0 {
			if _, ok := limitedDiskClass[getDsClass(ds)]; !ok {
				continue
			}
		}
		if len(limitedDiskCluster) > 0 {
			if _, ok := limitedDiskCluster[ds.Self.Value]; !ok {
				continue
			}
		}
		available = append(available, ds)
	}
	if len(available) == 0 {
		return nil, errors.NewInternalError(fmt.Errorf("no suitable datastore found"))
	}
	// sort by free space
	slices.SortFunc(available, func(a, b mo.Datastore) int {
		if a.Summary.FreeSpace < b.Summary.FreeSpace {
			return -1
		} else if a.Summary.FreeSpace > b.Summary.FreeSpace {
			return 1
		}
		return 0
	})
	selected := &available[0]
	return selected, nil
}

func (p *Provider) detachVirtualMachineDisks(ctx context.Context, vmObj *object.VirtualMachine) error {
	devicelist, err := vmObj.Device(ctx)
	if err != nil {
		return err
	}
	// reconfigure the VM
	var deviceChanges []types.BaseVirtualDeviceConfigSpec
	for _, device := range devicelist {
		var diskPath string
		if disk, ok := device.(*types.VirtualDisk); ok {
			switch val := disk.Backing.(type) {
			case *types.VirtualDiskFlatVer1BackingInfo:
				diskPath = val.FileName
			case *types.VirtualDiskFlatVer2BackingInfo:
				diskPath = val.FileName
			case *types.VirtualDiskSeSparseBackingInfo:
				diskPath = val.FileName
			case *types.VirtualDiskRawDiskMappingVer1BackingInfo:
				diskPath = val.FileName
			case *types.VirtualCdromIsoBackingInfo:
				diskPath = val.FileName
			}
			// 属于资源池的磁盘，不跟随虚拟机一起释放
			dspath := DSPathFromString(diskPath)
			if KeepVMDataDisksOnRemove &&strings.HasPrefix(dspath.Path, DefautlDisksPath) {
				deviceChanges = append(deviceChanges, &types.VirtualDeviceConfigSpec{
					Operation:     types.VirtualDeviceConfigSpecOperationRemove,
					Device:        device,
					FileOperation: "",
				})
			}
		}
	}
	if len(deviceChanges) == 0 {
		return nil // no disks to detach
	}
	task, err := vmObj.Reconfigure(ctx, types.VirtualMachineConfigSpec{
		DeviceChange: deviceChanges,
	})
	if err != nil {
		return fmt.Errorf("reconfigure vm %s to detach disks: %w", vmObj.Name(), err)
	}
	if err := task.Wait(ctx); err != nil {
		return fmt.Errorf("wait for detach disks task: %w", err)
	}
	return nil
}

// DeleteVirtualMachine implements ismc.VirtualMachineOperation.
func (p *Provider) DeleteVirtualMachine(ctx context.Context, id string, options common.DeleteVirtualMachineOptions) error {
	vm := p.vmref(id)
	var (
		task  *object.Task
		state types.VirtualMachinePowerState
	)
	state, err := vm.PowerState(ctx)
	if err != nil {
		// return not found if the vm is not found
		return WrapError(err, "virtualmachine", id)
	}
	if state == types.VirtualMachinePowerStatePoweredOn {
		task, err = vm.PowerOff(ctx)
		if err != nil {
			return err
		}
		// Ignore error since the VM may already been in powered off state.
		// vm.Destroy will fail if the VM is still powered on.
		_ = task.Wait(ctx)
	}
	if err := p.detachVirtualMachineDisks(ctx, vm); err != nil {
		return err
	}
	task, err = vm.Destroy(ctx)
	if err != nil {
		return err
	}
	return task.Wait(ctx)
}

// GetVirtualMachine implements ismc.VirtualMachineOperation.
func (p *Provider) GetVirtualMachine(ctx context.Context, id string) (*common.VirtualMachine, error) {
	vm, err := p.getvm(ctx, id)
	if err != nil {
		return nil, err
	}
	virt := p.ConvertVMToISMCVM(ctx, *vm)
	return &virt, nil
}

// UpdateVirtualMachine implements ismc.VirtualMachineOperation.
func (p *Provider) UpdateVirtualMachine(ctx context.Context, vm *common.VirtualMachine) error {
	objVM := p.vmref(vm.ID)
	task, err := objVM.Rename(ctx, vm.Name)
	if err != nil {
		return err
	}
	return task.Wait(ctx)
}

// GetVirtualMachineRemoteConnectionInfo implements ismc.VirtualMachineOperation.
func (p *Provider) GetVirtualMachineRemoteConnectionInfo(ctx context.Context, name string) (*common.RemoteConnectionInfo, error) {
	info, err := p.getVmVNCInfo(ctx, name)
	if err != nil {
		return nil, err
	}
	return &common.RemoteConnectionInfo{URL: info.URL.String()}, nil
}

type VNCInfo struct {
	URL    url.URL
	Ticket types.VirtualMachineTicket
}

func (p *Provider) getVmVNCInfo(ctx context.Context, id string) (*VNCInfo, error) {
	moVM, err := p.getvm(ctx, id)
	if err != nil {
		return nil, err
	}
	switch moVM.Summary.Runtime.PowerState {
	case types.VirtualMachinePowerStatePoweredOff, types.VirtualMachinePowerStateSuspended:
		return nil, fmt.Errorf("VM %s is not powered on", id)
	}
	vmref := object.NewVirtualMachine(p.Client.VimClient.Client, moVM.Self)
	ticket, err := vmref.AcquireTicket(ctx, string(types.VirtualMachineTicketTypeWebmks))
	if err != nil {
		return nil, err
	}
	url := url.URL{
		Scheme: "wss",
		Host:   fmt.Sprintf("%s:%d", ticket.Host, ticket.Port),
		Path:   fmt.Sprintf("/ticket/%s", ticket.Ticket),
	}
	return &VNCInfo{URL: url, Ticket: *ticket}, nil
}

func (p *Provider) ensureVmFolder(ctx context.Context, resourcegroup string, create bool) (*object.Folder, error) {
	folders, err := p.DC.Folders(ctx)
	if err != nil {
		return nil, err
	}
	rootfolder := folders.VmFolder

	if resourcegroup == "" {
		return rootfolder, nil
	}

	children, err := rootfolder.Children(ctx)
	if err != nil {
		return nil, err
	}
	var childfolders []object.Reference
	for _, child := range children {
		if child.Reference().Type == "Folder" {
			childfolders = append(childfolders, child)
		}
	}
	childrenmo, err := vmware.FindReferenceSliceInto[object.Reference, mo.Folder](ctx, p.Client.VimClient, childfolders, []string{"name"})
	if err != nil {
		return nil, err
	}
	for _, child := range childrenmo {
		if child.Name == resourcegroup {
			childfolder := object.NewFolder(p.Client.VimClient.Client, child.Reference())
			return childfolder, nil
		}
	}
	if !create {
		return nil, vmware.NewNotFoundError("folder", resourcegroup)
	}
	return rootfolder.CreateFolder(ctx, resourcegroup)
}

const (
	WMKSPath = "/wmks"
)

// VNCVirtualMachine implements common.VirtualMachineOperation.
func (p *Provider) VNCVirtualMachine(w http.ResponseWriter, r *http.Request, id string, path string) {
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}
	// proxy to vnc
	if path != WMKSPath {
		http.ServeFileFS(w, r, p.ConsoleStatisFS, path)
		return
	}
	info, err := p.getVmVNCInfo(r.Context(), id)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	switch strings.ToLower(info.URL.Scheme) {
	case "ws":
		info.URL.Scheme = "http"
	case "wss":
		info.URL.Scheme = "https"
	}
	handler := proxy.NewUpgradeAwareHandler(&info.URL, p.Client.RestClient.Transport, false, false, libproxy.ErrorResponser{})
	handler.ServeHTTP(w, r)
}

func (p *Provider) SetVirtualMachinePower(ctx context.Context, id string, power common.PowerAction, options common.VirtualMachinePowerOptions) error {
	vmInfo, err := vmware.FromResourceToMo[mo.VirtualMachine](ctx, p.Client.VimClient, id, "VirtualMachine", []string{"guest"})
	if err != nil {
		return err
	}
	// vmInfo.Config.BootOptions.BootOrder
	toolNotInstalled := vmInfo.Guest.ToolsRunningStatus == "guestToolsNotRunning"
	vmref := p.vmref(id)
	switch power {
	case common.PowerActionOn:
		task, err := vmref.PowerOn(ctx)
		if err != nil {
			return WrapError(err, "virtualmachine", id)
		}
		return task.Wait(ctx)
	case common.PowerActionOff:
		if options.Hard || toolNotInstalled {
			task, err := vmref.PowerOff(ctx)
			if err != nil {
				return WrapError(err, "virtualmachine", id)
			}
			return task.Wait(ctx)
		}
		return vmref.ShutdownGuest(ctx)
	case common.PowerActionSuspend:
		if options.Hard || toolNotInstalled {
			task, err := vmref.Suspend(ctx)
			if err != nil {
				return WrapError(err, "virtualmachine", id)
			}
			return task.Wait(ctx)
		}
		return vmref.StandbyGuest(ctx)
	case common.PowerActionReboot:
		if options.Hard || toolNotInstalled {
			task, err := vmref.Reset(ctx)
			if err != nil {
				return WrapError(err, "virtualmachine", id)
			}
			return task.Wait(ctx)
		}
		return vmref.RebootGuest(ctx)
	}
	return nil
}
