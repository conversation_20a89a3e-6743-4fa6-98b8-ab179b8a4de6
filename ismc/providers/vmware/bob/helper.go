package bob

import (
	"context"
	"fmt"
	"slices"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/view"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

func (p *Provider) getdsFromCache(dsid string) *mo.Datastore {
	ds, _ := vmware.CacheToGet[*mo.Datastore](p.Cache, types.ManagedObjectReference{
		Type: TypeDatastore, Value: dsid,
	})
	return ds
}

func (p *Provider) getds(ctx context.Context, dsid string, fields ...string) (*mo.Datastore, error) {
	ref := types.ManagedObjectReference{Type: TypeDatastore, Value: dsid}
	ds, err := vmware.CacheToGet[*mo.Datastore](p.Cache, ref)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, WrapError(err, "datastore", dsid)
		}
		// if not found in cache, try to find it directly
		ds, err = vmware.FindReferenceInto[mo.Reference, mo.Datastore](ctx,
			p.Client.VimClient, ref, fields)
		if err != nil {
			return nil, WrapError(err, "datastore", dsid)
		}
		// update cache
		p.Cache.Set(ds)
		return ds, nil
	}
	return ds, nil
}

func (p *Provider) listDatastoresByRefs(ctx context.Context, refs []types.ManagedObjectReference, fields []string) ([]mo.Datastore, error) {
	if p.Cache != nil {
		return vmware.CacheToList(p.Cache, TypeDatastore, func(ds mo.Datastore) bool {
			return vmware.ContainsReference(refs, ds)
		})
	}
	return vmware.FindReferenceSliceInto[types.ManagedObjectReference, mo.Datastore](
		ctx, p.Client.VimClient, refs, fields)
}

func (p *Provider) listDatastores(ctx context.Context, optionalzone string) ([]mo.Datastore, error) {
	fields := []string{"name", "summary", "host"}
	if optionalzone != "" {
		host, err := p.getHost(ctx, optionalzone, []string{"name", "datastore"})
		if err != nil {
			return nil, err
		}
		return p.listDatastoresByRefs(ctx, host.Datastore, fields)
	}
	return p.listAllDatastores(ctx, fields)
}

func (p *Provider) listAllDatastores(ctx context.Context, fields []string) ([]mo.Datastore, error) {
	if p.Cache != nil {
		return vmware.CacheToList[*mo.Datastore](p.Cache, TypeDatastore, nil)
	}
	refs, err := p.Client.Finder.DatastoreList(ctx, fmt.Sprintf("/%s/datastore/*", p.DC.Name()))
	if err != nil {
		return nil, err
	}
	return vmware.FindReferenceSliceInto[*object.Datastore, mo.Datastore](ctx, p.Client.VimClient, refs, fields)
}

func (p *Provider) getHostFromCache(hostid string) *mo.HostSystem {
	hostsys, _ := vmware.CacheToGet[*mo.HostSystem](p.Cache, types.ManagedObjectReference{
		Type: TypeHostSystem, Value: hostid,
	})
	return hostsys
}

func (p *Provider) getHost(ctx context.Context, hostid string, attr []string) (*mo.HostSystem, error) {
	hostref := types.ManagedObjectReference{Type: TypeHostSystem, Value: hostid}
	hostsys, err := vmware.CacheToGet[*mo.HostSystem](p.Cache, hostref)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
		// if not found in cache, try to find it directly
		hostsys, err := vmware.FindReferenceInto[mo.Reference, mo.HostSystem](ctx, p.Client.VimClient, hostref, attr)
		if err != nil {
			return nil, WrapError(err, "host", hostid)
		}
		return hostsys, nil
	}
	return hostsys, nil
}

func (p *Provider) listHostsRefs(ctx context.Context) ([]types.ManagedObjectReference, error) {
	if p.Cache != nil {
		list, err := vmware.CacheToList[*mo.HostSystem](p.Cache, TypeHostSystem, nil)
		if err != nil {
			return nil, err
		}
		refs := make([]types.ManagedObjectReference, 0, len(list))
		for _, host := range list {
			refs = append(refs, host.Reference())
		}
		return refs, nil
	}
	hostsrefs, err := p.Client.Finder.HostSystemList(ctx, fmt.Sprintf("/%s/host/*", p.DC.Name()))
	if err != nil {
		return nil, err
	}
	refs := make([]types.ManagedObjectReference, 0, len(hostsrefs))
	for _, ref := range hostsrefs {
		refs = append(refs, ref.Reference())
	}
	return refs, nil
}

func (p *Provider) listHosts(ctx context.Context, attr ...string) ([]mo.HostSystem, error) {
	if p.Cache != nil {
		return vmware.CacheToList[*mo.HostSystem](p.Cache, TypeHostSystem, nil)
	}
	refs, err := p.Client.Finder.HostSystemList(ctx, fmt.Sprintf("/%s/host/*", p.DC.Name()))
	if err != nil {
		return nil, err
	}
	return vmware.FindReferenceSliceInto[*object.HostSystem, mo.HostSystem](ctx, p.Client.VimClient, refs, attr)
}

func (p *Provider) vmref(id string) *object.VirtualMachine {
	vmref := types.ManagedObjectReference{Type: TypeVirtualMachine, Value: id}
	return object.NewVirtualMachine(p.Client.VimClient.Client, vmref)
}

func (p *Provider) networkref(id string) *object.Network {
	netref := types.ManagedObjectReference{Type: TypeNetwork, Value: id}
	return object.NewNetwork(p.Client.VimClient.Client, netref)
}

// The [object.VirtualMachine.Device] do a property query to VirtualMachine
// get the devices of a virtual machine,we can use getvm to get the vm object
// and then get the devices from vm.Config.Hardware.Device
func (p *Provider) getvmDevice(ctx context.Context, id string) (*object.VirtualMachine, *mo.VirtualMachine, object.VirtualDeviceList, error) {
	vm, err := p.getvm(ctx, id, "config.hardware.device")
	if err != nil {
		return nil, nil, nil, err
	}
	// may not occur, just for safety
	if vm.Config == nil {
		return nil, nil, nil, fmt.Errorf("virtual machine %s has no hardware configuration", id)
	}
	devices := object.VirtualDeviceList(vm.Config.Hardware.Device)
	return p.vmref(id), vm, devices, nil
}

func (p *Provider) getvm(ctx context.Context, id string, vmfields ...string) (*mo.VirtualMachine, error) {
	ref := types.ManagedObjectReference{Type: TypeVirtualMachine, Value: id}
	vm, err := vmware.CacheToGet[*mo.VirtualMachine](p.Cache, ref)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, WrapError(err, "virtualmachine", id)
		}
		// if not found in cache, try to find it directly
		vm, err = vmware.FindReferenceInto[mo.Reference, mo.VirtualMachine](ctx, p.Client.VimClient, p.vmref(id), vmfields)
		if err != nil {
			return nil, WrapError(err, "virtualmachine", id)
		}
		return vm, nil
	}
	return vm, nil
}

func (p *Provider) listvmsByRefs(ctx context.Context, refs []types.ManagedObjectReference, fields ...string) ([]mo.VirtualMachine, error) {
	if len(refs) == 0 {
		return nil, nil
	}
	if p.Cache != nil {
		return vmware.CacheToList(p.Cache, TypeVirtualMachine, func(vm mo.VirtualMachine) bool {
			return !vmIsTemplate(vm) && vmware.ContainsReference(refs, vm)
		})
	}
	return vmware.FindReferenceSliceInto[types.ManagedObjectReference, mo.VirtualMachine](ctx, p.Client.VimClient, refs, fields)
}

func (p *Provider) listvms(ctx context.Context, fields ...string) ([]mo.VirtualMachine, error) {
	if p.Cache != nil {
		return vmware.CacheToList(p.Cache, TypeVirtualMachine, func(vm mo.VirtualMachine) bool {
			return !vmIsTemplate(vm)
		})
	}
	if !slices.Contains(fields, "config") {
		fields = append(fields, "config")
	}
	v, err := view.NewManager(p.Client.VimClient.Client).CreateContainerView(ctx, p.DC.Reference(), []string{"VirtualMachine"}, true)
	if err != nil {
		return nil, err
	}
	// nolint errcheck
	defer v.Destroy(context.Background())

	var vms []mo.VirtualMachine
	if err := v.Retrieve(ctx, []string{"VirtualMachine"}, fields, &vms); err != nil {
		return nil, err
	}
	vms = slices.DeleteFunc(vms, vmIsTemplate) // remove templates
	return vms, nil
}

func vmIsTemplate(vm mo.VirtualMachine) bool {
	if vm.Config == nil {
		return false
	}
	return vm.Config.Template
}

// statDatastoreFiles list files in datastore
// it optimizes for list all files under same datastore dir
// especially for vm disks list
func (p *Provider) statDatastoreFiles(ctx context.Context, dsid string, filenames []string) (map[string]types.FileInfo, error) {
	if len(filenames) == 0 {
		return nil, nil
	}
	dsfiles, err := vmware.ListDataStoreDiskFiles(ctx, p.Client.VimClient.Client, dsid, filenames)
	if err != nil {
		return nil, fmt.Errorf("list datastore files %s failed: %w", dsid, err)
	}
	ret := map[string]types.FileInfo{}
	for _, file := range dsfiles {
		ret[file.Value] = file.FileInfo
	}
	return ret, nil
}

func FindMaxLengthCommonPrefix(strs []string) string {
	if len(strs) == 0 {
		return ""
	}
	if len(strs) == 1 {
		return strs[0]
	}
	prefix := strs[0]
	for _, str := range strs[1:] {
		prefix = findPrefix(prefix, str)
		if prefix == "" {
			break
		}
	}
	return prefix
}

func findPrefix(a, b string) string {
	if a == "" || b == "" {
		return ""
	}
	minLen := min(len(a), len(b))
	for i := range minLen {
		if a[i] != b[i] {
			return a[:i]
		}
	}
	return a[:minLen]
}

func (p *Provider) listObjectTags(ctx context.Context, ref types.ManagedObjectReference) ([]tags.Tag, error) {
	atatchedObjectRef := types.ManagedObjectReference{
		Type: vmware.TypeAttachedObject, Value: ref.String(),
	}

	var tagids []string
	attachedObject, err := vmware.CacheToGet[*vmware.AttachedObject](p.Cache, atatchedObjectRef)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
		// find from vapi tags
		tagman := tags.NewManager(p.Client.RestClient)
		vapitagids, err := tagman.ListAttachedTags(ctx, ref)
		if err != nil {
			return nil, fmt.Errorf("get attached tags for %s failed: %w", ref.Value, err)
		}
		// update cache
		p.Cache.Set(&vmware.AttachedObject{
			ManagedObjectReference: atatchedObjectRef,
			TagIDs:                 vapitagids,
		})
		tagids = vapitagids
	} else {
		tagids = attachedObject.TagIDs
	}
	return p.listTagsByIDs(ctx, tagids)
}

func (p *Provider) listTagsByIDs(ctx context.Context, ids []string) ([]tags.Tag, error) {
	vmtaglist, err := vmware.CacheToList(p.Cache, vmware.TypeTag, func(vmtag vmware.Tag) bool {
		return slices.Contains(ids, vmtag.Tag.ID)
	})
	if err != nil {
		return nil, err
	}
	result := make([]tags.Tag, 0, len(ids))
	for _, id := range ids {
		idx := slices.IndexFunc(vmtaglist, func(vmtag vmware.Tag) bool {
			return vmtag.Tag.ID == id
		})
		if idx != -1 {
			result = append(result, vmtaglist[idx].Tag)
			continue
		}
		// not in cache
		tag, err := tags.NewManager(p.Client.RestClient).GetTag(ctx, id)
		if err != nil {
			// ignore error
			log.FromContext(ctx).Error(err, "get tag", "tagid", id)
			continue
		}
		// add to cache
		p.Cache.Set(&vmware.Tag{
			ManagedObjectReference: types.ManagedObjectReference{
				Type: vmware.TypeTag, Value: tag.ID,
			},
			Tag: *tag,
		})
		result = append(result, *tag)
	}
	return result, nil
}
