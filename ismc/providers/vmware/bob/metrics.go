package bob

import (
	"context"
	"fmt"
	"maps"
	"path/filepath"
	"slices"
	"strings"

	"github.com/vmware/govmomi/performance"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

var _ common.MetricsOperations = &Provider{}

type supportMetrics struct {
	counters map[int32]*types.PerfCounterInfo
	performance.MetricList
}

// cpu.idle.summation: Idle
func (s *supportMetrics) toMap() map[string]string {
	var (
		seen   = make(map[int32]struct{})
		res    []types.PerfMetricId
		result = make(map[string]string)
	)
	for _, id := range s.MetricList {
		if _, exist := seen[id.CounterId]; !exist {
			seen[id.CounterId] = struct{}{}
			res = append(res, id)
		}
	}
	for _, id := range res {
		info, ok := s.counters[id.CounterId]
		if !ok {
			continue
		}
		result[info.Name()] = info.NameInfo.GetElementDescription().Label
	}
	return result
}

// 获取资源支持的指标
func (p *Provider) getResourceSupportMetrics(ctx context.Context, resource types.ManagedObjectReference) (*supportMetrics, error) {
	s, err := p.PerfManager.ProviderSummary(ctx, resource)
	if err != nil {
		return nil, err
	}
	interval := s.RefreshRate
	if s.RefreshRate == -1 {
		// realtime not supported
		interval = 300
	}
	mids, err := p.PerfManager.AvailableMetric(ctx, resource, interval)
	if err != nil {
		return nil, err
	}
	counters, err := p.PerfManager.CounterInfoByKey(ctx)
	if err != nil {
		return nil, err
	}
	return &supportMetrics{
		MetricList: mids,
		counters:   counters,
	}, nil
}

func (p *Provider) ListResourcesMetrics(ctx context.Context, metrics []string, entities []types.ManagedObjectReference) ([]ResourceMetrics, error) {
	spec := types.PerfQuerySpec{
		Format:     string(types.PerfFormatNormal),
		IntervalId: 20,
	}
	sample, err := p.PerfManager.SampleByName(ctx, spec, metrics, entities)
	if err != nil {
		return nil, err
	}
	return ToResourceMetrics(ctx, p.PerfManager, sample)
}

type ResourceMetrics struct {
	ResourceID string
	Metrics    []MetricsValues
}

type MetricsValues struct {
	Name  string
	Value []int64

	Unit     string
	Instance string
}

func ToResourceMetrics(ctx context.Context, m *performance.Manager, series []types.BasePerfEntityMetricBase) ([]ResourceMetrics, error) {
	counters, err := m.CounterInfoByKey(ctx)
	if err != nil {
		return nil, err
	}
	result := make([]ResourceMetrics, 0, len(series))
	for i := range series {
		s, ok := series[i].(*types.PerfEntityMetric)
		if !ok {
			continue
		}
		item := ResourceMetrics{
			ResourceID: s.Entity.Value,
			Metrics:    make([]MetricsValues, 0, len(s.Value)),
		}
		for j := range s.Value {
			value, ok := s.Value[j].(*types.PerfMetricIntSeries)
			if !ok {
				continue
			}
			info, ok := counters[value.Id.CounterId]
			if !ok {
				continue
			}
			item.Metrics = append(item.Metrics, MetricsValues{
				Name:     info.Name(),
				Value:    value.Value,
				Unit:     info.UnitInfo.GetElementDescription().Label,
				Instance: value.Id.Instance,
			})
		}
		result = append(result, item)
	}
	return result, nil
}

// instance=*，表示所有的设备,metrics为空表示所有指标
func (p *Provider) GetResourcesMetrics(ctx context.Context, resource types.ManagedObjectReference, metrics []string, instance string) (*ResourceMetrics, error) {
	providerSummary, err := p.PerfManager.ProviderSummary(ctx, resource)
	if err != nil {
		return nil, err
	}
	interval := providerSummary.RefreshRate
	if providerSummary.RefreshRate == -1 {
		// realtime not supported
		interval = 300
	}
	spec := types.PerfQuerySpec{
		Format:     string(types.PerfFormatNormal),
		MetricId:   []types.PerfMetricId{{Instance: instance}},
		IntervalId: interval,
	}
	sample, err := p.PerfManager.SampleByName(ctx, spec, metrics, []types.ManagedObjectReference{resource})
	if err != nil {
		return nil, err
	}
	list, err := ToResourceMetrics(ctx, p.PerfManager, sample)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return &ResourceMetrics{ResourceID: resource.Value, Metrics: []MetricsValues{}}, nil
	}
	return &list[0], nil
}

func (p *Provider) ListDiskMetrics(ctx context.Context) ([]*common.DiskMetrics, error) {
	dmsmapping := make(map[string]*common.DiskMetrics, 0)
	vms, err := p.listvms(ctx, "name", "tag", "config", "runtime", "summary")
	if err != nil {
		return nil, err
	}
	vmentities := make([]types.ManagedObjectReference, 0, len(vms))
	vmmomap := make(map[string]mo.VirtualMachine, len(vms))
	for _, vm := range vms {
		vmentities = append(vmentities, vm.Reference())
		vmmomap[vm.Reference().Value] = vm
	}
	metricsids := []string{
		"virtualDisk.read.average",
		"virtualDisk.write.average",
		"virtualDisk.readOIO.latest",
		"virtualDisk.writeOIO.latest",
	}
	metrics, err := p.ListResourcesMetrics(ctx, metricsids, vmentities)
	if err != nil {
		log.FromContext(ctx).Error(err, "query vm metrics failed")
		return nil, err
	}
	for _, vm := range metrics {
		vmmo, ok := vmmomap[vm.ResourceID]
		if !ok {
			continue
		}
		maps.Copy(dmsmapping, p.ToDiskMetrics(vmmo, vm))
	}
	disks, err := p.listDiskFiles(ctx, "")
	if err != nil {
		log.Info("list origin disks failed", "error", err)
		return slices.Collect(maps.Values(dmsmapping)), nil
	}
	for _, diskfile := range disks {
		if diskfile.VmDiskFileInfo == nil {
			continue // skip non-disk files
		}
		diskid := vmware.EncodeID(diskfile.Value)
		diskValue, ok := dmsmapping[diskid]
		if !ok {
			// if no metrics but disk exists, create a new empty disk metrics
			diskValue = &common.DiskMetrics{ID: vmware.EncodeID(diskid)}
			dmsmapping[diskid] = diskValue
		}
		total := float64(diskfile.VmDiskFileInfo.CapacityKb * KB)
		used := float64(diskfile.VmDiskFileInfo.FileSize)
		dspath := DSPathFromString(diskfile.Value)
		//diskValue.Name = dspath.Path
		diskValue.Name = filepath.Base(dspath.Path)
		diskValue.ResourceGroup = GroupFromDsPath(dspath.Path)
		diskValue.DiskTotalBytes = total
		diskValue.DiskUsedBytes = used
		diskValue.DiskUsedPercent = used / total * 100
	}
	list := slices.Collect(maps.Values(dmsmapping))
	slices.SortFunc(list, func(a, b *common.DiskMetrics) int {
		return strings.Compare(a.Name, b.Name)
	})
	return list, nil
}

func (p *Provider) ToDiskMetrics(vm mo.VirtualMachine, ms ResourceMetrics) map[string]*common.DiskMetrics {
	dmsmapping := make(map[string]*common.DiskMetrics, 0)
	if vm.Config == nil {
		return dmsmapping
	}
	hardware := vm.Config.Hardware
	for _, dev := range hardware.Device {
		switch typedev := dev.(type) {
		case *types.VirtualDisk:
			var diskID string
			scsi := findScsiController(hardware.Device, typedev.ControllerKey)
			scsiNumber := fmt.Sprintf("%s:%d", scsi, *typedev.UnitNumber)
			if backing := typedev.Backing; backing != nil {
				switch typedbacking := backing.(type) {
				case *types.VirtualDiskFlatVer2BackingInfo:
					diskID = typedbacking.FileName
				default:
					continue
				}
			}
			dm := &common.DiskMetrics{
				ID:   vmware.EncodeID(diskID),
				Name: findDiskName(diskID),
				Labels: map[string]string{
					"vm":     vm.Self.Value,
					"vmname": vm.Name,
				},
			}
			for _, m := range ms.Metrics {
				if m.Instance != scsiNumber {
					continue
				}
				if len(m.Value) == 0 {
					continue
				}
				value := max(m.Value[0], 0.0)
				switch m.Name {
				case "virtualDisk.read.average":
					dm.DiskReadBytesPerSecond = float64(value * KB)
				case "virtualDisk.write.average":
					dm.DiskWriteBytesPerSecond = float64(value * KB)
				case "virtualDisk.readOIO.latest":
					dm.DiskReadOperationsPerSecond = float64(value)
				case "virtualDisk.writeOIO.latest":
					dm.DiskWriteOperationsPerSecond = float64(value)
				}
			}
			//dmsmapping[diskID] = dm
			dmsmapping[dm.ID] = dm
		}
	}
	return dmsmapping
}

func (p *Provider) ToVirtualMachineMetrics(ctx context.Context, vm mo.VirtualMachine, ms ResourceMetrics) *common.VirtualMachineMetrics {
	tags, _ := p.listObjectTags(ctx, vm.Self)
	metrics := &common.VirtualMachineMetrics{
		ID:            vm.Reference().Value,
		Name:          vm.Name,
		ResourceGroup: ResourceGroupFromTags(tags),
		Labels:        p.Fields.CustomValuesToLabels(vm.Summary.CustomValue),
	}
	if config := vm.Config; config != nil {
		hardware := config.Hardware
		metrics.CPUTotalCores = float64(hardware.NumCPU)
		metrics.MemoryTotalBytes = int64(hardware.MemoryMB) * MB
	}
	for _, m := range ms.Metrics {
		if len(m.Value) == 0 {
			continue
		}
		value := max(m.Value[0], 0.0)
		switch m.Name {
		case "cpu.usage.average":
			// The CPU utilization.  This value is reported with 100% representing all  processor cores on the system.
			// As an example, a 2-way VM using 50% of a four-core system is completely using two cores.
			metrics.CPUUsedPercent = float64(value) / 100
			metrics.CPUUsedCores = metrics.CPUUsedPercent * metrics.CPUTotalCores / 100
		case "mem.usage.average":
			// The percentage of memory used as a percent of all available machine memory.  Available for host and VM.
			metrics.MemoryUsedPercent = float64(value) / 100
			metrics.MemoryUsedBytes = int64(metrics.MemoryUsedPercent * float64(metrics.MemoryTotalBytes) / 100)
		case "net.bytesTx.average":
			if strings.TrimSpace(m.Instance) != "" {
				metrics.NetworkTXBytesPerSecond += float64(value * KB)
			}
		case "net.bytesRx.average":
			if strings.TrimSpace(m.Instance) != "" {
				metrics.NetworkRXBytesPerSecond += float64(value * KB)
			}
		}
	}
	switch vm.Runtime.PowerState {
	case types.VirtualMachinePowerStatePoweredOn:
		metrics.Up = true
		metrics.Health = true
	default:
		metrics.Up = false
		metrics.Health = false
	}
	return metrics
}

// GetVirtualMachineMetrics implements common.Provider.
func (p *Provider) GetVirtualMachineMetrics(ctx context.Context, id string) (*common.VirtualMachineMetrics, error) {
	vmmo, err := p.getvm(ctx, id, "name", "tag", "runtime", "summary", "config")
	if err != nil {
		if errors.IsNotFound(err) {
			return &common.VirtualMachineMetrics{Name: id, ID: id}, nil
		}
		return nil, err
	}
	vmMetricsIDs := []string{
		"cpu.usage.average",
		"mem.usage.average",
		"net.bytesTx.average",
		"net.bytesRx.average",
	}
	metrics, err := p.GetResourcesMetrics(ctx, vmmo.Reference(), vmMetricsIDs, "*")
	if err != nil {
		if errors.IsNotFound(err) {
			return &common.VirtualMachineMetrics{}, nil
		}
		return nil, err
	}
	return p.ToVirtualMachineMetrics(ctx, *vmmo, *metrics), nil
}

// GetZoneMetrics implements common.Provider.
func (p *Provider) GetZoneMetrics(ctx context.Context, id string) (*common.ZoneMetrics, error) {
	return &common.ZoneMetrics{}, nil
}

// ListVirtualMachineMetrics implements common.Provider.
func (p *Provider) ListVirtualMachineMetrics(ctx context.Context, options common.ListVirtualMachineMetricsOptions) ([]*common.VirtualMachineMetrics, error) {
	vms, err := p.listvms(ctx, "name", "tag", "runtime", "summary", "config")
	if err != nil {
		return nil, err
	}
	entities := make([]types.ManagedObjectReference, 0, len(vms))
	vmmap := make(map[string]mo.VirtualMachine, len(vms))
	for _, vm := range vms {
		entities = append(entities, vm.Reference())
		vmmap[vm.Reference().Value] = vm
	}
	vmMetricsIDs := []string{
		"cpu.usage.average",
		"mem.usage.average",
		"net.bytesTx.average",
		"net.bytesRx.average",
	}
	resourcemetrics, err := p.ListResourcesMetrics(ctx, vmMetricsIDs, entities)
	if err != nil {
		return nil, err
	}
	metricslist := make([]*common.VirtualMachineMetrics, 0, len(resourcemetrics))
	for _, vm := range resourcemetrics {
		vmmo, ok := vmmap[vm.ResourceID]
		if !ok {
			continue
		}
		metricslist = append(metricslist, p.ToVirtualMachineMetrics(ctx, vmmo, vm))
	}
	return metricslist, nil
}

// ListZoneMetrics implements common.Provider.
func (p *Provider) ListZoneMetrics(ctx context.Context) ([]*common.ZoneMetrics, error) {
	return nil, common.ErrUnsupported
}

func findScsiController(devices []types.BaseVirtualDevice, key int32) string {
	for _, device := range devices {
		virtualDevice := device.GetVirtualDevice()
		deviceKey := virtualDevice.Key
		if key == deviceKey {
			scsiLabel := virtualDevice.DeviceInfo.GetDescription().Label
			return strings.ReplaceAll(scsiLabel, "SCSI controller ", "scsi")
		}
	}
	return ""
}

// GetDiskMetrics implements common.MetricsOperations.
func (p *Provider) GetDiskMetrics(ctx context.Context, id string) (*common.DiskMetrics, error) {
	return &common.DiskMetrics{ID: id, Name: id}, nil
}

// GetHostMetrics implements common.MetricsOperations.
func (p *Provider) GetHostMetrics(ctx context.Context, id string) (*common.HostMetrics, error) {
	host, err := p.getHost(ctx, id, []string{"name", "summary", "config", "datastore"})
	if err != nil {
		if errors.IsNotFound(err) {
			return &common.HostMetrics{Name: id, ID: id}, nil
		}
		return nil, err
	}
	datastores, _ := p.listDatastoresByRefs(ctx, host.Datastore, []string{"name", "summary"})

	metricnames := []string{
		"cpu.usage.average",
		"mem.usage.average",
		"net.bytesRx.average",
		"net.bytesTx.average",
		"disk.read.average",
		"disk.write.average",
	}
	mrs, err := p.GetResourcesMetrics(ctx, host.Reference(), metricnames, "")
	if err != nil {
		log.FromContext(ctx).Error(err, "query host metrics failed", "id", id)
	}
	return p.ToHostMetrics(*host, datastores, mrs), nil
}

func (p *Provider) ToHostMetrics(host mo.HostSystem, datastores []mo.Datastore, ms *ResourceMetrics) *common.HostMetrics {
	metrics := &common.HostMetrics{
		ID:     host.Reference().Value,
		Labels: map[string]string{"host": host.Name},
		Name:   host.Name,
	}

	if hardware := host.Summary.Hardware; hardware != nil {
		metrics.MemoryTotalBytes = hardware.MemorySize
		metrics.CPUTotalCores = float64(hardware.NumCpuThreads)
	}

	for _, ds := range datastores {
		metrics.DiskTotalBytes += ds.Summary.Capacity
		metrics.DiskUsedBytes += ds.Summary.Capacity - ds.Summary.FreeSpace
	}
	metrics.DiskUsedPercent = float64(metrics.DiskUsedBytes) / float64(metrics.DiskTotalBytes) * 100

	if ms != nil {
		for _, m := range ms.Metrics {
			if len(m.Value) == 0 {
				continue
			}
			value := max(m.Value[0], 0.0)
			switch m.Name {
			case "cpu.usage.average":
				metrics.CPUUsedPercent = float64(value) / 100
				metrics.CPUUsedCores = metrics.CPUTotalCores * metrics.CPUUsedPercent / 100
			case "mem.usage.average":
				metrics.MemoryUsedPercent = float64(value) / 100
				metrics.MemoryUsedBytes = int64(float64(metrics.MemoryTotalBytes) * metrics.MemoryUsedPercent / 100)
			case "disk.read.average":
				metrics.DiskReadBytesPerSecond = float64(value * KB)
			case "disk.write.average":
				metrics.DiskWriteBytesPerSecond = float64(value * KB)
			case "net.bytesRx.average":
				metrics.NetworkRXBytesPerSecond = float64(value * KB)
			case "net.bytesTx.average":
				metrics.NetworkTXBytesPerSecond = float64(value * KB)
			}
		}
	}
	return metrics
}

// ListHostMetrics implements common.MetricsOperations.
func (p *Provider) ListHostMetrics(ctx context.Context) ([]*common.HostMetrics, error) {
	refs, err := p.listHostsRefs(ctx)
	if err != nil {
		return nil, err
	}
	var hostMetrics []*common.HostMetrics
	for _, ref := range refs {
		metric, err := p.GetHostMetrics(ctx, ref.Value)
		if err != nil {
			log.FromContext(ctx).Error(err, "query host metrics failed", "id", ref.Reference().Value)
			continue
		}
		hostMetrics = append(hostMetrics, metric)
	}
	return hostMetrics, nil
}
