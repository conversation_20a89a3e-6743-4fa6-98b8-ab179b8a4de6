package bob

import (
	"context"

	"github.com/vmware/govmomi/view"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/vmware"
)

var _ common.VirtualNetworkOperation = &Provider{}

// ListVirtualNetworks implements ismc.VirtualNetworkOperation.
func (p *Provider) ListVirtualNetworks(ctx context.Context, options common.ListVirtualNetworkOptions) (common.List[common.VirtualNetwork], error) {
	list := common.List[common.VirtualNetwork]{Items: []common.VirtualNetwork{}}
	networks, err := p.listNetworks(ctx)
	if err != nil {
		return list, nil
	}
	items := make([]common.VirtualNetwork, 0, len(networks))
	for _, network := range networks {
		items = append(items, ConvertNetwork(network))
	}
	return common.PageList(items, options.ListOptions), nil
}

func ConvertNetwork(network mo.Network) common.VirtualNetwork {
	ret := common.VirtualNetwork{
		Descripter: common.Descripter{
			ID:   network.Self.Value,
			Name: network.Name,
		},
		Status: common.VirtualNetworkStatus{
			Phase: common.VirtualNetworkPhaseUnknown,
		},
	}
	if summary := network.Summary; summary != nil {
		if netsummary := summary.GetNetworkSummary(); netsummary != nil {
			if netsummary.Accessible {
				ret.Status.Phase = common.VirtualNetworkPhaseReady
			}
		}
	}
	return ret
}

// GetVirtualNetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) GetVirtualNetwork(ctx context.Context, id string) (*common.VirtualNetwork, error) {
	network, err := p.getNetwork(ctx, id, []string{"name", "summary"})
	if err != nil {
		return nil, err
	}
	ret := ConvertNetwork(*network)
	return &ret, nil
}

// UpdateVirtualNetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) UpdateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) error {
	return common.ErrUnsupported
}

// CreateVirtualNetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) CreateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteVirtualNetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) DeleteVirtualNetwork(ctx context.Context, name string) error {
	return common.ErrUnsupported
}

// DeleteVirtualSubnetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) DeleteVirtualSubnetwork(ctx context.Context, network string, name string) error {
	return common.ErrUnsupported
}

// ListVirtualSubnetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) ListVirtualSubnetwork(ctx context.Context, network string, options common.ListVirtualSubnetworkOptions) (common.List[common.VirtualSubnetwork], error) {
	items := []common.VirtualSubnetwork{
		p.getDefaultSubnetwork(ctx, network, ""),
	}
	return common.PageList(items, options.ListOptions), nil
}

const DefaultSubnetworkID = "default"

func (p *Provider) getDefaultSubnetwork(_ context.Context, network string, zone string) common.VirtualSubnetwork {
	return common.VirtualSubnetwork{
		Descripter: common.Descripter{
			ID:   DefaultSubnetworkID,
			Name: DefaultSubnetworkID,
		},
		IsDefault:      true,
		VirtualNetwork: network,
		DHCP:           common.VirtualSubnetworkDHCP{Enabled: true},
		Zone:           zone,
		Status: common.VirtualSwitchStatus{
			Phase: common.VirtualSwitchPhaseReady,
		},
	}
}

// CreateVirtualSubnetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) CreateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// GetVirtualSubnetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) GetVirtualSubnetwork(ctx context.Context, network string, name string) (*common.VirtualSubnetwork, error) {
	networks, err := p.ListVirtualSubnetwork(ctx, network, common.ListVirtualSubnetworkOptions{})
	if err != nil {
		return nil, err
	}
	for _, n := range networks.Items {
		if n.ID == network {
			return &n, nil
		}
	}
	return nil, errors.NewNotFound("virtual subnetwork", name)
}

// ListVirtualSubnetworkAllocations implements ismc.VirtualNetworkOperation.
func (p *Provider) ListVirtualSubnetworkAllocations(ctx context.Context, network string, name string, options common.ListVirtualSwitchAllocationOptions) (common.List[common.VirtualSubnetworkAllocation], error) {
	return common.List[common.VirtualSubnetworkAllocation]{}, nil
}

// UpdateVirtualSubnetwork implements ismc.VirtualNetworkOperation.
func (p *Provider) UpdateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) error {
	return common.ErrUnsupported
}

func (p *Provider) getNetwork(ctx context.Context, networkid string, attr []string) (*mo.Network, error) {
	networkref := types.ManagedObjectReference{Type: TypeNetwork, Value: networkid}
	cachednetwork ,err:= vmware.CacheToGet[*mo.Network](p.Cache, networkref)
	if err != nil {
		if !errors.IsNotFound(err) {
			return nil,err
		}
		// fallback to backend
		network, err := vmware.FindReferenceInto[types.ManagedObjectReference, mo.Network](ctx, p.Client.VimClient, networkref, attr)
	if err != nil {
		return nil, WrapError(err, "network", networkid)
	}
	return network, nil
	}
	return cachednetwork, nil
}

func (p *Provider) listNetworks(ctx context.Context, fields ...string) ([]mo.Network, error) {
	if p.Cache != nil {
		return vmware.CacheToList[*mo.Network](p.Cache, TypeNetwork, nil)
	}
	v, err := view.NewManager(p.Client.VimClient.Client).CreateContainerView(ctx, p.DC.Reference(), []string{"Network"}, true)
	if err != nil {
		return nil, err
	}
	// nolint errcheck
	defer v.Destroy(ctx)

	var nets []mo.Network
	if err := v.Retrieve(ctx, []string{TypeNetwork}, fields, &nets); err != nil {
		return nil, err
	}
	return nets, nil
}
