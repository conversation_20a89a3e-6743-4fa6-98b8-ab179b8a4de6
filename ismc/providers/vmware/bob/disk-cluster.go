package bob

import (
	"context"
	"fmt"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.DiskClusterOperation = &Provider{}

// CreateDiskCluster 创建datastore
func (p *Provider) CreateDiskCluster(ctx context.Context, cluster *common.DiskCluster) (*common.Descripter, error) {
	hostDiskName := cluster.Labels["host-disk"]
	if hostDiskName == "" {
		return nil, errors.NewBadRequest("host-disk is required")
	}
	hostref := object.NewHostSystem(p.Client.VimClient.Client,
		types.ManagedObjectReference{Type: TypeHostSystem, Value: cluster.Zone})

	ds, err := hostref.ConfigManager().DatastoreSystem(ctx)
	if err != nil {
		return nil, err
	}
	// Find the specified disk
	disks, err := ds.QueryAvailableDisksForVmfs(ctx)
	if err != nil {
		return nil, err
	}

	var disk *types.HostScsiDisk
	for _, e := range disks {
		if e.CanonicalName == hostDiskName {
			disk = &e
			break
		}
	}
	if disk == nil {
		return nil, fmt.Errorf("no eligible disk found for name %#v", hostDiskName)
	}
	// Query for creation options and pick the right one
	options, err := ds.QueryVmfsDatastoreCreateOptions(ctx, disk.DevicePath)
	if err != nil {
		return nil, err
	}
	var option *types.VmfsDatastoreOption
	for _, e := range options {
		if _, ok := e.Info.(*types.VmfsDatastoreAllExtentOption); ok {
			option = &e
			break
		}
	}
	if option == nil {
		return nil, fmt.Errorf("cannot use entire disk for datastore for name %#v", hostDiskName)
	}
	spec := *option.Spec.(*types.VmfsDatastoreCreateSpec)
	spec.Vmfs.VolumeName = cluster.ID
	_, err = ds.CreateVmfsDatastore(ctx, spec)
	return nil, err
}

// DeleteDiskCluster 删除datastore
func (p *Provider) DeleteDiskCluster(ctx context.Context, id string) error {
	ds, err := p.getds(ctx, id)
	if err != nil {
		return err
	}
	for _, hostmount := range ds.Host {
		hostref := object.NewHostSystem(p.Client.VimClient.Client, hostmount.Key)
		hds, err := hostref.ConfigManager().DatastoreSystem(ctx)
		if err != nil {
			return err
		}
		dsref := object.NewDatastore(p.Client.VimClient.Client, hds.Reference())
		if err := hds.Remove(ctx, dsref); err != nil {
			return err
		}
	}
	return nil
}

// GetDiskCluster 获取datastore基本信息
func (p *Provider) GetDiskCluster(ctx context.Context, id string) (*common.DiskCluster, error) {
	ds, err := p.getds(ctx, id, "name", "host")
	if err != nil {
		return nil, WrapError(err, "diskcluster", id)
	}
	diskcluster := p.ConvertDatastoreToDiskCluster(*ds)
	return &diskcluster, nil
}

// ListDiskClusters 列举datastore
func (p *Provider) ListDiskClusters(ctx context.Context, options common.ListDiskClusterOptions) (common.List[common.DiskCluster], error) {
	items, err := p.listDiskClusters(ctx, options.Zone, options.StorageClass)
	if err != nil {
		return common.List[common.DiskCluster]{}, err
	}
	return common.PageList(items, options.ListOptions), nil
}

func (p *Provider) listDiskClusters(ctx context.Context, optionalZone string, optionalClass string) ([]common.DiskCluster, error) {
	datastores, err := p.listDatastores(ctx, optionalZone)
	if err != nil {
		return nil, err
	}
	items := make([]common.DiskCluster, 0, len(datastores))
	for _, ds := range datastores {
		if optionalClass != "" && getDsClass(ds) != optionalClass {
			continue
		}
		items = append(items, p.ConvertDatastoreToDiskCluster(ds))
	}
	return items, nil
}

func (p *Provider) ConvertDatastoreToDiskCluster(ds mo.Datastore) common.DiskCluster {
	cluster := common.DiskCluster{
		Descripter: common.Descripter{
			ID:   ds.Reference().Value,
			Name: ds.Name,
		},
	}
	summary := ds.Summary
	if summary.Accessible {
		cluster.Status.Phase = common.DiskClusterPhaseReady
	} else {
		cluster.Status.Phase = common.DiskClusterPhaseUnknown
	}
	cluster.Type = string(summary.Type)
	cluster.Status.Total = *resource.NewQuantity(int64(summary.Capacity), resource.BinarySI)
	cluster.Status.Used = *resource.NewQuantity(int64(summary.Capacity-summary.FreeSpace), resource.BinarySI)
	for _, host := range ds.Host {
		if cluster.Zone == "" {
			cluster.Zone = host.Key.Value
		}
		zoneref := common.ObjectRef{
			ID: host.Key.Value,
		}
		if host := p.getHostFromCache(host.Key.Value); host != nil {
			zoneref.Name = host.Name
		}
		cluster.Status.AvailableZones = append(cluster.Status.AvailableZones, zoneref)
	}
	return cluster
}

// ListSupportedDiskClass implements ismc.DiskClusterOperation.
func (p *Provider) ListDiskClass(ctx context.Context, opt common.ListDiskClassOptions) (common.List[common.DiskClass], error) {
	datastores, err := p.listDatastores(ctx, opt.Zone)
	if err != nil {
		return common.List[common.DiskClass]{}, err
	}
	dsclass := make(map[string]struct{})
	for _, ds := range datastores {
		dsclass[getDsClass(ds)] = struct{}{}
	}
	var items []common.DiskClass
	for k := range dsclass {
		items = append(items, common.DiskClass{
			Descripter: common.Descripter{ID: k, Name: k},
		})
	}
	return common.PageList(items, opt.ListOptions), nil
}

// GetDiskClass implements common.DiskClusterOperation.
func (p *Provider) GetDiskClass(ctx context.Context, id string) (*common.DiskClass, error) {
	return &common.DiskClass{Descripter: common.Descripter{ID: id, Name: id}}, nil
}
