package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestProvider_ListTasks(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()

	tasklist, err := provider.ListTasks(ctx, common.ListTaskOptions{
		ListOptions: common.ListOptions{Size: 2},
	})
	if err != nil {
		t.Fatal(err)
	}
	for _, tk := range tasklist.Items {
		t.Log("TaskInfo:", tk)
	}
}

func TestProvider_WatchTasks(t *testing.T) {
	provider := SetupTestProvider(t)
	ctx := context.Background()
	onEvent := func(ctx context.Context, event common.WatchEvent[common.Task]) error {
		t.Logf("event: %v", event)
		return nil
	}
	if err := provider.WactchTasks(ctx, onEvent, common.WactchTaskOptions{}); err != nil {
		t.<PERSON><PERSON>(err)
	}
}
