package vmware

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"github.com/vmware/govmomi/event"
	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/property"
	"github.com/vmware/govmomi/vapi/library"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/log"
)

func (c *ObjectCache) runEvents(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.V(2).Info("starting cache event listener")
	vmcli := c.cli.VimClient.Client
	eventManager := event.NewManager(vmcli)
	eventFilter := types.EventFilterSpec{
		Entity: &types.EventFilterSpecByEntity{
			Entity:    vmcli.ServiceContent.RootFolder,
			Recursion: types.EventFilterSpecRecursionOptionAll,
		},
		MaxCount: 1,
		EventTypeId: []string{
			// for virtual disks
			"VmBeingCreatedEvent",
			"VmReconfiguredEvent",
			"VmRemovedEvent",
			// for datastore files
			"DatastoreFileDeletedEvent",
			"DatastoreFileMovedEvent",
			"DatastoreFileCopiedEvent",
			// custom fields
			"CustomFieldValueChangedEvent",
			"CustomFieldDefAddedEvent",
			"CustomFieldDefRemovedEvent",
			"CustomFieldDefRenamedEvent",
			// library events
			"com.vmware.cl.CreateLibraryEvent",
			"com.vmware.cl.DeleteLibraryEvent",
			"com.vmware.cl.UpdateLibraryEvent",
			// library item events
			"com.vmware.cl.CreateLibraryItemEvent",
			"com.vmware.cl.UpdateLibraryItemEvent",
			"com.vmware.cl.DeleteLibraryItemEvent",
			// tags
			"com.vmware.cis.tagging.attach",
			"com.vmware.cis.tagging.detach",
			// categories
		},
	}
	collector, err := eventManager.CreateCollectorForEvents(ctx, eventFilter)
	if err != nil {
		return fmt.Errorf("failed to create event collector: %w", err)
	}
	// nolint errcheck
	defer collector.Destroy(ctx)

	req := &property.WaitFilter{
		CreateFilter: types.CreateFilter{
			Spec: types.PropertyFilterSpec{
				ObjectSet: []types.ObjectSpec{{Obj: collector.Reference()}},
				PropSet: []types.PropertySpec{
					{Type: collector.Reference().Type, PathSet: []string{"latestPage"}},
				},
			},
		},
	}
	newpc, err := property.DefaultCollector(vmcli).Create(ctx)
	if err != nil {
		return fmt.Errorf("create property collector: %w", err)
	}
	return property.WaitForUpdatesEx(ctx, newpc, req, func(updates []types.ObjectUpdate) bool {
		for _, update := range updates {
			for _, change := range update.ChangeSet {
				if change.Name != "latestPage" {
					continue
				}
				arrayOfEvent, ok := change.Val.(types.ArrayOfEvent)
				if !ok {
					log.Error(fmt.Errorf("invalid event type: %T", change.Val), "process event updates")
					continue
				}
				for _, event := range arrayOfEvent.Event {
					if err := c.onEvent(ctx, event); err != nil {
						log.Error(err, "process event", "event", event)
					}
				}
			}
		}
		return false
	})
}

func (c *ObjectCache) onEvent(ctx context.Context, event types.BaseEvent) error {
	switch typedevent := event.(type) {
	case *types.VmBeingCreatedEvent:
		changes := c.findDeviceChangeDisks(typedevent.ConfigSpec.DeviceChange)
		log.V(5).Info("event vm being created", "vm", typedevent.Vm, "changes", changes)
		c.updateFilesCacheFromChanges(ctx, changes)
	case *types.VmReconfiguredEvent:
		changes := c.findDeviceChangeDisks(typedevent.ConfigSpec.DeviceChange)
		log.V(5).Info("event vm reconfigured", "vm", typedevent.Vm, "changes", changes)
		c.updateFilesCacheFromChanges(ctx, changes)
	case *types.VmRemovedEvent:
		log.V(5).Info("event vm removed", "vm", typedevent.Vm)
		// remove all virtual disks from the cache
		// currently we do not have a way to know which files are removed
		// so we just notify the cache to resync all files
		ref := types.ManagedObjectReference{Type: TypeDatastoreFile, Value: ""}
		c.Notify(ref)
	case *types.VmPoweredOnEvent:
		log.V(5).Info("event vm powered on", "vm", typedevent.Vm)
		// some times tag attached events may not receive,
		// when every time a vm powered on, we notify the cache to update tags
		// this is a workaround to ensure the tags are up-to-date
		if vm := typedevent.Vm; vm != nil {
			tagref := types.ManagedObjectReference{
				Type:  TypeAttachedObject,
				Value: vm.Vm.String(),
			}
			c.Notify(tagref) // notify the cache to update the tag
		}
	case *types.DatastoreFileDeletedEvent:
		changes := DiskChanges{
			Removed: []DatastoreIDPath{
				{
					DatastoreID: typedevent.Datastore.Datastore.Value,
					DatastorePath: object.DatastorePath{
						Datastore: typedevent.Datastore.Name,
						Path:      dsURIToPath(typedevent.TargetFile),
					},
				},
			},
		}
		log.V(5).Info("event datastore file deleted", "changes", changes, "file", typedevent.TargetFile)
		c.updateFilesCacheFromChanges(ctx, changes)
	case *types.DatastoreFileMovedEvent:
		changes := DiskChanges{
			Updated: []DatastoreIDPath{
				{
					DatastoreID: typedevent.Datastore.Name,
					DatastorePath: object.DatastorePath{
						Datastore: typedevent.Datastore.Name,
						Path:      dsURIToPath(typedevent.TargetFile),
					},
				},
			},
			Removed: []DatastoreIDPath{
				{
					DatastoreID: typedevent.Datastore.Datastore.Value,
					DatastorePath: object.DatastorePath{
						Datastore: typedevent.Datastore.Name,
						Path:      dsURIToPath(typedevent.SourceFile),
					},
				},
			},
		}
		log.V(5).Info("event datastore file moved", "changes", changes, "sourceFile", typedevent.SourceFile, "targetFile", typedevent.TargetFile)
		c.updateFilesCacheFromChanges(ctx, changes)
	case *types.DatastoreFileCopiedEvent:
		changes := DiskChanges{
			Updated: []DatastoreIDPath{
				{
					DatastoreID: typedevent.Datastore.Name,
					DatastorePath: object.DatastorePath{
						Datastore: typedevent.Datastore.Name,
						Path:      dsURIToPath(typedevent.TargetFile),
					},
				},
			},
		}
		log.V(5).Info("datastore file copied event", "changes", changes, "sourceFile", typedevent.SourceFile, "targetFile", typedevent.TargetFile)
		c.updateFilesCacheFromChanges(ctx, changes)
	case *types.CustomFieldValueChangedEvent:
		log.V(5).Info("event custom field value changed", "name", typedevent.Name, "key", typedevent.Value)
		ref := types.ManagedObjectReference{
			Type:  TypeCustomField,
			Value: strconv.Itoa(int(typedevent.Key)),
		}
		c.Notify(ref) // notify the cache to update the custom field value
	case *types.CustomFieldDefAddedEvent:
		log.V(5).Info("event custom field definition added", "name", typedevent.Name, "key", typedevent.Key)
		ref := types.ManagedObjectReference{
			Type:  TypeCustomField,
			Value: strconv.Itoa(int(typedevent.Key)),
		}
		c.Update(ref) // add the new custom field definition to the cache
	case *types.CustomFieldDefRemovedEvent:
		log.V(5).Info("event custom field definition removed", "name", typedevent.Name, "key", typedevent.Key)
		ref := types.ManagedObjectReference{
			Type:  TypeCustomField,
			Value: strconv.Itoa(int(typedevent.Key)),
		}
		c.Delete(ref) // remove the custom field definition from the cache
	case *types.CustomFieldDefRenamedEvent:
		log.V(5).Info("event custom field definition renamed", "oldName", typedevent.Name, "newName", typedevent.NewName, "key", typedevent.Key)
		// update the custom field definition name in the cache
		ref := types.ManagedObjectReference{
			Type:  TypeCustomField,
			Value: strconv.Itoa(int(typedevent.Key)),
		}
		c.Notify(ref) // notify the cache to update the custom field definition name
	case *types.EventEx:
		switch typedevent.EventTypeId {
		case "com.vmware.cl.CreateLibraryEvent", "com.vmware.cl.UpdateLibraryEvent":
			libid := getclibid(typedevent.ObjectId)
			log.V(2).Info("event adding/updating library", "library", libid, "name", typedevent.ObjectName)
			// refresh the library
			lib, err := library.NewManager(c.cli.RestClient).GetLibraryByID(ctx, libid)
			if err != nil {
				return fmt.Errorf("refresh library %s: %w", libid, err)
			}
			log.V(5).Info("set library to cache", "library", libid, "name", typedevent.ObjectName)
			libmo := &ContentLibrary{
				ManagedObjectReference: types.ManagedObjectReference{Type: TypeContentLibrary, Value: libid},
				Library:                *lib,
			}
			c.Update(libmo)
		case "com.vmware.cl.DeleteLibraryEvent":
			libid := getclibid(typedevent.ObjectId)
			log.V(2).Info("event remove library", "library", libid)
			// remove the library from the cache
			c.Delete(types.ManagedObjectReference{Type: TypeContentLibrary, Value: libid})
			// remove all items in the library
			list, _ := c.ListByType(TypeContentLibraryItem)
			for _, item := range list {
				if contentlibitem, ok := item.(*ContentLibraryItem); ok && contentlibitem.Item.LibraryID == libid {
					log.V(5).Info("removing library item from cache", "item", contentlibitem.Value, "name", contentlibitem.Item.Name)
					c.Delete(contentlibitem.Reference())
				}
			}
		case "com.vmware.cl.CreateLibraryItemEvent":
			log.V(2).Info("event create library item", "item", typedevent.ObjectId, "name", typedevent.ObjectName)
			libitemref := types.ManagedObjectReference{
				Type: TypeContentLibraryItem, Value: getclibitemid(typedevent.ObjectId),
			}
			c.Notify(libitemref)
		case "com.vmware.cl.UpdateLibraryItemEvent":
			log.V(2).Info("event update library item", "item", typedevent.ObjectId, "name", typedevent.ObjectName)
			libitemref := types.ManagedObjectReference{
				Type: TypeContentLibraryItem, Value: getclibitemid(typedevent.ObjectId),
			}
			c.Notify(libitemref)
		case "com.vmware.cl.DeleteLibraryItemEvent":
			log.V(2).Info("event delete library item", "item", typedevent.ObjectId, "name", typedevent.ObjectName)
			libitemref := types.ManagedObjectReference{
				Type: TypeContentLibraryItem, Value: getclibitemid(typedevent.ObjectId),
			}
			log.V(2).Info("remove library item from cache", "item", libitemref.Value, "name", typedevent.ObjectName)
			c.Delete(libitemref)
		case "com.vmware.cis.tagging.attach":
			log.V(2).Info("event tagging attach", "objectId", typedevent.ObjectId,
				"objectName", typedevent.ObjectName, "objectType", typedevent.ObjectType,
			)
			tagref := types.ManagedObjectReference{Type: TypeAttachedObject}
			c.Notify(tagref) // notify the cache to update the tag
		case "com.vmware.cis.tagging.detach":
			log.V(2).Info("event tagging detach", "objectId", typedevent.ObjectId,
				"objectName", typedevent.ObjectName, "objectType", typedevent.ObjectType,
			)
			tagref := types.ManagedObjectReference{Type: TypeAttachedObject}
			c.Notify(tagref) // notify the cache to update the tag
		default:
			log.V(2).Info("unknown eventEx",
				"event", typedevent.EventTypeId,
				"objectId", typedevent.ObjectId,
				"objectName", typedevent.ObjectName,
				"objectType", typedevent.ObjectType)
		}
	}
	return nil
}

func (c *ObjectCache) updateFilesCacheFromChanges(ctx context.Context, change DiskChanges) {
	log := log.FromContext(ctx)
	log.V(2).Info("updating cache from disk changes", "changed", len(change.Updated), "removed", len(change.Removed))
	// query the updated disks details and update the cache
	bydatastore := make(map[string][]string)
	for _, dspath := range change.Updated {
		if dspath.DatastoreID == "" || dspath.Path == "" {
			continue
		}
		bydatastore[dspath.DatastoreID] = append(bydatastore[dspath.DatastoreID], dspath.Path)
	}
	for datastore, pathes := range bydatastore {
		if len(pathes) == 0 {
			continue // skip empty pathes
		}
		if err := c.refreshDatastoreFiles(ctx, datastore, pathes); err != nil {
			log.Error(err, "refresh datastore files", "datastore", datastore, "files", pathes)
		}
	}
	for _, dspath := range change.Removed {
		log.Info("removing disk from cache", "datastore", dspath.Datastore, "path", dspath.Path)
		c.Delete(types.ManagedObjectReference{Type: TypeDatastoreFile, Value: dspath.String()})
	}
}

func (c *ObjectCache) refreshDatastoreFiles(ctx context.Context, datastore string, files []string) error {
	log := log.FromContext(ctx)
	log.V(2).Info("refreshing datastore files", "datastore", datastore, "files", files)
	if len(files) == 0 {
		return nil // nothing to refresh
	}
	infos, err := ListDataStoreDiskFiles(ctx, c.cli.VimClient.Client, datastore, files)
	if err != nil {
		return fmt.Errorf("list virtual disk files for datastore %s: %w", datastore, err)
	}
	for _, info := range infos {
		log.Info("updating disk file in cache", "path", info.Value)
		c.Update(&info)
	}
	return nil
}

type DatastoreIDPath struct {
	DatastoreID string
	object.DatastorePath
}

type DiskChanges struct {
	Updated []DatastoreIDPath
	Removed []DatastoreIDPath
}

// findDeviceChangeDisks finds all disks added or removed in the given device changes.
func (c *ObjectCache) findDeviceChangeDisks(deviceChanges []types.BaseVirtualDeviceConfigSpec) DiskChanges {
	var addeddisks, removedDisks []DatastoreIDPath
	for _, change := range deviceChanges {
		spec := change.GetVirtualDeviceConfigSpec()
		diskdev, ok := spec.Device.(*types.VirtualDisk)
		if !ok {
			continue
		}
		if diskdev.Backing == nil {
			continue
		}
		var filebacking types.VirtualDeviceFileBackingInfo
		switch backing := diskdev.Backing.(type) {
		case *types.VirtualDiskFlatVer2BackingInfo:
			filebacking = backing.VirtualDeviceFileBackingInfo
		case *types.VirtualDiskFlatVer1BackingInfo:
			filebacking = backing.VirtualDeviceFileBackingInfo
		default:
			continue // skip unsupported disk backing types
		}
		dspath := DatastoreIDPath{
			DatastorePath: object.DatastorePath{
				Path: dsURIToPath(filebacking.FileName),
			},
		}
		if filebacking.Datastore != nil {
			dspath.DatastoreID = filebacking.Datastore.Value
		} else {
			// if no datastore is specified, we find out from cache by VMFS path
			if ds := c.findDatastoreFromVMFSPath(filebacking.FileName); ds != nil {
				dspath.DatastoreID = ds.Reference().Value
			}
		}
		switch spec.Operation {
		case types.VirtualDeviceConfigSpecOperationAdd:
			if spec.FileOperation == types.VirtualDeviceConfigSpecFileOperationCreate {
				addeddisks = append(addeddisks, dspath)
			}
		case types.VirtualDeviceConfigSpecOperationEdit:
			// when editing a disk, we consider it as an update
			addeddisks = append(addeddisks, dspath)
		case types.VirtualDeviceConfigSpecOperationRemove:
			if spec.FileOperation == types.VirtualDeviceConfigSpecFileOperationDestroy {
				removedDisks = append(removedDisks, dspath)
			}
		}
	}
	return DiskChanges{Updated: addeddisks, Removed: removedDisks}
}

func (c *ObjectCache) findDatastoreFromVMFSPath(vmfs string) *mo.Datastore {
	items, _ := CacheToList(c, TypeDatastore, func(ds mo.Datastore) bool {
		for _, host := range ds.Host {
			// host.MountInfo.Path is look like:
			// path = "/vmfs/volumes/66f7daf5-79c91ca2-cedf-049226bdf82d"
			// vmfs = "ds:///vmfs/volumes/66f7daf5-79c91ca2-cedf-049226bdf82d/alpine1/alpine1.vmdk"
			if strings.HasPrefix(strings.TrimPrefix(vmfs, "ds://"), host.MountInfo.Path) {
				return true
			}
		}
		return false
	})
	if len(items) == 0 {
		return nil // no datastore found
	}
	shallow := items[0]
	return &shallow
}

func dsURIToPath(full string) string {
	_, path := parseDatastoreURI(full)
	return path
}

var datastoreURIRegexp = regexp.MustCompile(`^(?:ds://)?/vmfs/volumes/([^/]+)/(.*)`)

func parseDatastoreURI(full string) (string, string) {
	matches := datastoreURIRegexp.FindStringSubmatch(full)
	if len(matches) < 3 {
		return "", full // no match, return as is
	}
	return matches[1], matches[2] // return the datastore ID and path
}

func getclibid(id string) string {
	return strings.TrimPrefix(id, "clib-")
}

func getclibitemid(id string) string {
	return strings.TrimPrefix(id, "clibitem-")
}
