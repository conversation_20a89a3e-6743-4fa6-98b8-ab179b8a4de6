package vmware

import (
	"context"
	"encoding/json"
	"os"
	"testing"
)

func SetupTestClient(t *testing.T) *Client {
	options := Options{
		Address:    "*************",
		Username:   "<EMAIL>",
		Datacenter: "Datacenter",
		Password:   "",
	}
	client, err := NewClient(context.Background(), options)
	if err != nil {
		t.Fatalf("failed to create client: %v", err)
	}
	return client
}

func TestClient_GetProprties(t *testing.T) {
	c := SetupTestClient(t)
	ctx := context.Background()
	vm, err := c.GetVirtualMachineWithParents(ctx, "vm-7002")
	if err != nil {
		t.Fatal(err)
	}
	datas, err := json.MarshalIndent(vm, "", "  ")
	if err != nil {
		t.Fatal(err)
	}
	_ = os.WriteFile("vm.json", datas, 0o644)
	t.Logf("vm: %v", vm)
}
