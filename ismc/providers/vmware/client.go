package vmware

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"path"
	"strings"
	"time"

	"github.com/vmware/govmomi"
	"github.com/vmware/govmomi/find"
	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/session"
	"github.com/vmware/govmomi/task"
	"github.com/vmware/govmomi/vapi/library"
	"github.com/vmware/govmomi/vapi/rest"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/vim25"
	"github.com/vmware/govmomi/vim25/debug"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/soap"
	"github.com/vmware/govmomi/vim25/types"
	"xiaoshiai.cn/common/log"
)

// DefaultIdentity is the global identity used mark a resource
const (
	DefaultIdentityCategory = "bob"
	ImageCustomValueKey     = "image"
)

type Client struct {
	VimClient      *govmomi.Client
	RestClient     *rest.Client
	LibraryManager *library.Manager
	Finder         *find.Finder
	TagManager     *tags.Manager
	Datacenter     *object.Datacenter
}

func NewClient(ctx context.Context, opts Options) (*Client, error) {
	log := log.FromContext(ctx)
	if !strings.HasPrefix(opts.Address, "https://") && !strings.HasPrefix(opts.Address, "http://") {
		opts.Address = "https://" + opts.Address
	}
	u, err := url.Parse(opts.Address)
	if err != nil {
		return nil, fmt.Errorf("error parse url: %s", err)
	}
	if u.Scheme == "" {
		u.Scheme = "https"
	}
	u.Path = path.Join(u.Path, vim25.Path)
	var userinfo *url.Userinfo
	if opts.Username != "" && opts.Password != "" {
		userinfo = url.UserPassword(opts.Username, opts.Password)
	}
	if opts.Debug {
		debug.SetProvider(&LogProvider{})
	}
	keepAliveIdle := time.Minute * 5
	// vmomi client
	soapClient := soap.NewClient(u, true)
	if proxy := opts.Proxy; proxy != "" {
		proxyu, err := url.Parse(proxy)
		if err != nil {
			return nil, fmt.Errorf("invalid proxy url: %s", err)
		}
		httptp, ok := soapClient.Transport.(*http.Transport)
		if ok {
			log.Info("set proxy for vmomi client", "proxy", proxyu.String())
			httptp.Proxy = http.ProxyURL(proxyu)
		} else {
			log.Info("vmomi client transport is not http.Transport, cannot set proxy")
		}
	}
	vimClient, err := vim25.NewClient(ctx, soapClient)
	if err != nil {
		return nil, fmt.Errorf("error create client: %s", err)
	}
	cli := &govmomi.Client{
		Client:         vimClient,
		SessionManager: session.NewManager(vimClient),
	}
	if err := cli.Login(ctx, userinfo); err != nil {
		return nil, fmt.Errorf("error login: %s", err)
	}
	// nolint errcheck
	go RunAutoLoginVMClient(ctx, cli, keepAliveIdle, userinfo)
	// rest client
	restcli := rest.NewClient(cli.Client)
	if err := restcli.Login(ctx, userinfo); err != nil {
		return nil, fmt.Errorf("error rest login: %s", err)
	}
	// nolint errcheck
	go RunAutoLoginRESTClient(ctx, restcli, keepAliveIdle, userinfo)

	finder := find.NewFinder(cli.Client, false)
	var dc *object.Datacenter
	if opts.Datacenter != "" {
		dc, err = finder.Datacenter(ctx, "/"+opts.Datacenter)
		if err != nil {
			return nil, fmt.Errorf("error find datacenter %s: %s", opts.Datacenter, err)
		}
		finder.SetDatacenter(dc)
	} else {
		dc, _ = finder.DefaultDatacenter(ctx)
		finder.SetDatacenter(dc)
	}
	return &Client{
		VimClient:      cli,
		RestClient:     restcli,
		LibraryManager: library.NewManager(restcli),
		Finder:         finder,
		TagManager:     tags.NewManager(restcli),
		Datacenter:     dc,
	}, nil
}

func RunAutoLoginVMClient(ctx context.Context, cli *govmomi.Client, idle time.Duration, info *url.Userinfo) error {
	checkOnce := func(ctx context.Context) error {
		seseion, err := cli.SessionManager.UserSession(ctx)
		if err != nil {
			return err
		}
		if seseion != nil {
			return nil
		}
		return fmt.Errorf("session expired")
	}
	login := func(ctx context.Context) error {
		return cli.Login(ctx, info)
	}
	return runAutoLogin(ctx, idle, checkOnce, login)
}

func RunAutoLoginRESTClient(ctx context.Context, cli *rest.Client, idle time.Duration, info *url.Userinfo) error {
	checkOnce := func(ctx context.Context) error {
		s, err := cli.Session(ctx)
		if err != nil {
			return err
		}
		if s != nil {
			return nil
		}
		return fmt.Errorf("session expired")
	}
	login := func(ctx context.Context) error {
		return cli.Login(ctx, info)
	}
	return runAutoLogin(ctx, idle, checkOnce, login)
}

func runAutoLogin(ctx context.Context, interval time.Duration, check func(context.Context) error, login func(context.Context) error) error {
	log := log.FromContext(ctx)
	log.Info("start auto login session checker", "interval", interval)
	timer := time.NewTimer(interval)
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-timer.C:
			log.V(1).Info("check session")
			if err := check(ctx); err != nil {
				log.Error(err, "error check session, try login")
				if err := login(ctx); err != nil {
					log.Error(err, "error login")
				} else {
					log.Info("login success")
				}
			}
			timer.Reset(interval)
		}
	}
}

func (vc *Client) GetOrCreateDefaultTagCategory(ctx context.Context) (string, error) {
	tagManager := tags.NewManager(vc.RestClient)
	tagList, err := tagManager.GetCategories(ctx)
	if err != nil {
		return "", err
	}
	for i := range tagList {
		if tagList[i].Name == DefaultIdentityCategory {
			return tagList[i].ID, nil
		}
	}
	tagsCategory := &tags.Category{
		Name:        DefaultIdentityCategory,
		Description: "automatically created by " + DefaultIdentityCategory,
		Cardinality: "MULTIPLE", // MULTIPLE对应多个SINGLE单个
		AssociableTypes: []string{
			"Folder", "ClusterComputeResource", "Datacenter", "Datastore", "StoragePod", "DistributedVirtualPortgroup",
			"DistributedVirtualSwitch", "VmwareDistributedVirtualSwitch", "HostSystem", "com.vmware.content.Library",
			"com.vmware.content.library.Item", "HostNetwork", "Network", "OpaqueNetwork", "ResourcePool",
			"VirtualApp", "VirtualMachine",
		},
	}
	return tagManager.CreateCategory(ctx, tagsCategory)
}

// vm-111 -> mo.VirtualMachine
func FromResourceToMo[T any](ctx context.Context, cli *govmomi.Client, id, resourceType string, fields []string) (T, error) {
	ref := types.ManagedObjectReference{
		Type:  resourceType,
		Value: id,
	}
	var into T
	if err := cli.RetrieveOne(ctx, ref.Reference(), fields, &into); err != nil {
		return into, err
	}
	return into, nil
}

func FromResourceID[T object.Reference](ctx context.Context, cli *find.Finder, id, resourceType string) (T, error) {
	ref := types.ManagedObjectReference{
		Type:  resourceType,
		Value: id,
	}
	result, err := cli.ObjectReference(ctx, ref)
	if err != nil {
		return *new(T), err
	}
	if typedResult, ok := result.(T); ok {
		return typedResult, nil
	}
	return *new(T), fmt.Errorf("not a %T type", *new(T))
}

func FindReferenceInto[T mo.Reference, F mo.Reference](ctx context.Context, cli *govmomi.Client, ref T, attr []string) (*F, error) {
	var into F
	if err := cli.RetrieveOne(ctx, ref.Reference(), attr, &into); err != nil {
		return nil, err
	}
	return &into, nil
}

func FindReferenceSliceInto[T mo.Reference, F mo.Reference](ctx context.Context, cli *govmomi.Client, refs []T, attr []string) ([]F, error) {
	if len(refs) == 0 {
		return nil, nil
	}
	into := make([]F, 0, len(refs))
	references := make([]types.ManagedObjectReference, 0, len(refs))
	for _, item := range refs {
		references = append(references, item.Reference())
	}
	if err := cli.Retrieve(ctx, references, attr, &into); err != nil {
		return nil, IgnoreNotFound(err)
	}
	return into, nil
}

func (vc *Client) FindMaxFreeDatastoreReference(ctx context.Context) (*types.ManagedObjectReference, error) {
	refs, err := vc.Finder.DatastoreList(ctx, path.Join(vc.Datacenter.InventoryPath, "datastore/*"))
	if err != nil {
		return nil, err
	}
	dses, err := FindReferenceSliceInto[*object.Datastore, mo.Datastore](ctx, vc.VimClient, refs, []string{"summary"})
	if err != nil {
		return nil, err
	}
	var selectedDS mo.Datastore
	for i := range refs {
		ds, err := FindReferenceInto[*object.Datastore, mo.Datastore](ctx, vc.VimClient, refs[i], []string{"summary"})
		if err != nil {
			return nil, err
		}
		if ds.Summary.FreeSpace > selectedDS.Summary.FreeSpace {
			selectedDS = dses[i]
		}
	}
	dsref := selectedDS.Reference()
	if dsref.Value == "" {
		return nil, fmt.Errorf("no available datastore found")
	}
	return &dsref, nil
}

func (c *Client) GetNetwork(ctx context.Context, dc string, name string) (*mo.Network, error) {
	ref, err := c.Finder.Network(ctx, "/"+dc+"/network/"+name)
	if err != nil {
		return nil, err
	}
	var networks *object.Network
	if ref.Reference().Type != "Network" {
		return nil, fmt.Errorf("not a Network type")
	}
	return FindReferenceInto[*object.Network, mo.Network](ctx, c.VimClient, networks, nil)
}

func (c *Client) NetworkList(ctx context.Context, dc string) ([]mo.Network, error) {
	refs, err := c.Finder.NetworkList(ctx, "/"+dc+"/network/*")
	if err != nil {
		return nil, err
	}
	var networks []*object.Network
	for _, ele := range refs {
		// 这里需要区分DistributedVirtualPortgroup，DistributedVirtualSwitch，Network等类型
		if ele.Reference().Type != "Network" {
			continue
		}
		networks = append(networks, object.NewNetwork(c.VimClient.Client, ele.Reference()))
	}

	return FindReferenceSliceInto[*object.Network, mo.Network](ctx, c.VimClient, networks, nil)
}

func (c *Client) GetVirtualMachineWithParents(ctx context.Context, vmid string, fields ...string) (any, error) {
	specs := []types.PropertyFilterSpec{
		{
			PropSet: []types.PropertySpec{
				{
					Type:    "VirtualMachine",
					PathSet: fields,
					All:     types.NewBool(true),
				},
				{
					Type:    "ManagedEntity",
					PathSet: []string{"name", "parent"},
				},
			},
			ObjectSet: []types.ObjectSpec{
				{
					Obj: types.ManagedObjectReference{
						Type:  "VirtualMachine",
						Value: vmid,
					},
					SelectSet: []types.BaseSelectionSpec{
						&types.TraversalSpec{
							Type: "ManagedEntity",
							Path: "parent",
							Skip: types.NewBool(false),
							SelectSet: []types.BaseSelectionSpec{
								&types.SelectionSpec{Name: "traverseParent"},
							},
							SelectionSpec: types.SelectionSpec{Name: "traverseParent"},
						},
						&types.TraversalSpec{
							SelectionSpec: types.SelectionSpec{},
							Type:          "VirtualMachine",
							Path:          "parentVApp",
							Skip:          types.NewBool(false),
							SelectSet: []types.BaseSelectionSpec{
								&types.SelectionSpec{Name: "traverseParent"},
							},
						},
					},
					Skip: types.NewBool(false),
				},
			},
		},
	}
	objectcontents, err := mo.RetrievePropertiesEx(ctx, c.VimClient.Client, types.RetrievePropertiesEx{
		This:    c.VimClient.ServiceContent.PropertyCollector,
		SpecSet: specs,
	})
	if err != nil {
		return nil, err
	}
	list := []any{}
	if err := mo.LoadObjectContent(objectcontents, &list); err != nil {
		return nil, err
	}
	return list, nil
}

// 用于编码复杂的资源ID,比如磁盘ID
func EncodeID(id string) string {
	return base64.StdEncoding.EncodeToString([]byte(id))
}

func DecodeID(id string) string {
	data, err := base64.StdEncoding.DecodeString(id)
	if err != nil {
		return id
	}
	return string(data)
}

func IgnoreNotFound(err error) error {
	if IsNotFound(err) {
		return nil
	}
	return err
}

func IsNotFound(err error) bool {
	switch val := err.(type) {
	case *find.NotFoundError,
		*find.MultipleFoundError,
		*find.DefaultNotFoundError,
		*find.DefaultMultipleFoundError,
		object.DatastoreNoSuchFileError,
		object.DatastoreNoSuchDirectoryError,
		*NotFoundError: // custom
		return true
	case task.Error:
		fault := val.Fault()
		switch fault.(type) {
		case *types.FileNotFound:
			return true
		}
	}
	if rest.IsStatusError(err, http.StatusNotFound) {
		return true
	}
	errmsg := err.Error()
	// "The object 'vim.VirtualMachine:vm-6031' has already been deleted or has not been completely created"
	if strings.Contains(errmsg, "has already been deleted") || strings.Contains(errmsg, "has not been completely created") {
		return true
	}
	return false
}

func IsAlreadyExists(err error) bool {
	switch val := err.(type) {
	case task.Error:
		fault := val.Fault()
		switch fault.(type) {
		case *types.FileAlreadyExists:
			return true
		}
	}
	return false
}

type NotFoundError struct {
	kind, message string
}

func NewNotFoundError(kind, message string) error {
	return &NotFoundError{kind: kind, message: message}
}

func (e *NotFoundError) Error() string {
	return fmt.Sprintf("%s %s not found", e.kind, e.message)
}

func IsTaskFileNotFoundError(err error) bool {
	return IsTaskFaultType[*types.FileNotFound](err)
}

func IsTaskFileAlreadyExistsError(err error) bool {
	return IsTaskFaultType[*types.FileAlreadyExists](err)
}

func IsTaskFaultType[T types.BaseMethodFault](err error) bool {
	if taskerr, ok := err.(task.Error); ok {
		_, ok := taskerr.Fault().(T)
		return ok
	}
	return false
}
