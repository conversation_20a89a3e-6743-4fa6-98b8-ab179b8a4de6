package vmware

import (
	"context"
	"fmt"
	"io"
	"maps"
	"net/url"
	"path"
	"slices"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/vmware/govmomi/object"
	"github.com/vmware/govmomi/ovf"
	"github.com/vmware/govmomi/property"
	"github.com/vmware/govmomi/vapi/library"
	"github.com/vmware/govmomi/vapi/rest"
	"github.com/vmware/govmomi/vapi/tags"
	"github.com/vmware/govmomi/view"
	"github.com/vmware/govmomi/vim25"
	"github.com/vmware/govmomi/vim25/mo"
	"github.com/vmware/govmomi/vim25/soap"
	"github.com/vmware/govmomi/vim25/types"
	"golang.org/x/sync/errgroup"
	"k8s.io/client-go/util/workqueue"
	"xiaoshiai.cn/common/collections"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/retry"
)

const (
	TypeContentLibrary     = "ContentLibrary"
	TypeContentLibraryItem = "ContentLibraryItem"
	TypeDatacenter         = "Datacenter"
	TypeDatastore          = "Datastore"
	TypeDatastoreFile      = "DatastoreFile"
	TypeHostSystem         = "HostSystem"
	TypeVirtualMachine     = "VirtualMachine"
	TypeNetwork            = "Network"
	TypeTag                = "Tag"
	TypeAttachedObject     = "AttachedObject"
	TypeCategory           = "Category"
	TypeCustomField        = "CustomField"
)

type ObjectCacheConfig struct {
	TypedIndexers  map[string]Indexers[mo.Reference]
	PropertySet    []types.PropertySpec
	ResyncInterval time.Duration // ResyncInterval is the interval to resync the cache
}

func NewDefaultObjectCache(cli *Client) *ObjectCache {
	return NewObjectCache(cli, ObjectCacheConfig{
		TypedIndexers: map[string]Indexers[mo.Reference]{},
		PropertySet: []types.PropertySpec{
			{Type: "Datacenter", All: types.NewBool(true)},
			{Type: "Datastore", All: types.NewBool(true)},
			{Type: "HostSystem", All: types.NewBool(true)},
			{Type: "VirtualMachine", All: types.NewBool(true)},
			{Type: "Network", All: types.NewBool(true)},
		},
		ResyncInterval: 12 * time.Hour, // default resync interval
	})
}

func NewObjectCache(cli *Client, config ObjectCacheConfig) *ObjectCache {
	if config.TypedIndexers == nil {
		config.TypedIndexers = make(map[string]Indexers[mo.Reference])
	}
	if len(config.PropertySet) == 0 {
		config.PropertySet = []types.PropertySpec{
			{Type: "VirtualMachine", All: types.NewBool(true)},
		}
	}
	queue := workqueue.NewTypedWithConfig(workqueue.TypedQueueConfig[types.ManagedObjectReference]{
		Name: "ObjectCache Notifications",
	})
	return &ObjectCache{
		config: config,
		bytype: make(map[string]*IndexThreadSafeMap[mo.Reference]),
		cli:    cli,
		queue:  queue,
	}
}

type ObjectCache struct {
	bytype map[string]*IndexThreadSafeMap[mo.Reference]
	lock   sync.RWMutex
	cli    *Client
	config ObjectCacheConfig
	queue  workqueue.TypedInterface[types.ManagedObjectReference]
	synced bool // synced indicates whether the cache is synced
}

// ListByType returns a list of objects of the given type.
// Type is the type of the object
// e.g. "VirtualMachine", "Datacenter", "HostSystem", "Datastore", etc.
func (c *ObjectCache) ListByType(typ string) ([]mo.Reference, bool) {
	c.lock.RLock()
	typecache, ok := c.bytype[typ]
	c.lock.RUnlock()
	if !ok {
		return nil, false
	}
	return typecache.List(), true
}

func (c *ObjectCache) ListByTypeIndex(typ string, indexName, indexedValue string) []mo.Reference {
	c.lock.RLock()
	typecache, ok := c.bytype[typ]
	c.lock.RUnlock()
	if !ok {
		return nil
	}
	list, _ := typecache.ListByIndex(indexName, indexedValue)
	return list
}

// Delete implements CacheStore.
func (c *ObjectCache) Delete(key types.ManagedObjectReference) bool {
	c.lock.Lock()
	defer c.lock.Unlock()
	typecache, ok := c.bytype[key.Type]
	if !ok {
		return false
	}
	return typecache.Delete(key.Value)
}

// Get implements CacheStore.
func (c *ObjectCache) Get(key types.ManagedObjectReference) (mo.Reference, bool) {
	c.lock.RLock()
	typecache, ok := c.bytype[key.Type]
	c.lock.RUnlock()
	if !ok {
		return nil, false
	}
	return typecache.Get(key.Value)
}

func (c *ObjectCache) Set(value mo.Reference) {
	c.lock.Lock()
	defer c.lock.Unlock()
	key := value.Reference()
	typecache, ok := c.bytype[key.Type]
	if !ok {
		typecache = NewIndexThreadSafeMap(c.config.TypedIndexers[key.Type])
		c.bytype[key.Type] = typecache
	}
	typecache.Update(key.Value, value)
}

func (c *ObjectCache) Remove(key types.ManagedObjectReference) {
	c.lock.Lock()
	defer c.lock.Unlock()
	typecache, ok := c.bytype[key.Type]
	if !ok {
		return
	}
	typecache.Delete(key.Value)
}

// Notify to notify the cache to refresh the object.
func (c *ObjectCache) Notify(key types.ManagedObjectReference) {
	c.queue.Add(key)
}

func (c *ObjectCache) consumeNotifications(ctx context.Context) error {
	for {
		select {
		case <-ctx.Done():
			return nil
		default:
			item, shutdown := c.queue.Get()
			if shutdown {
				return nil // queue is shutdown, exit
			}
			if err := c.consumeNotification(ctx, item); err != nil {
				log.FromContext(ctx).Error(err, "consume notification from queue", "key", item)
			}
			c.queue.Done(item)
		}
	}
}

func (c *ObjectCache) consumeNotification(ctx context.Context, key types.ManagedObjectReference) error {
	log := log.FromContext(ctx)
	log.V(5).Info("consuming notification", "key", key)
	switch key.Type {
	case TypeContentLibraryItem:
		libitemid := getclibitemid(key.Value)
		// add the item to the cache
		log.V(2).Info("adding library item to cache", "item", libitemid)
		// refresh the item
		libitem, err := GetContentLibraryItem(ctx, c.cli, libitemid, "")
		if err != nil {
			return fmt.Errorf("refresh library item %s: %w", libitemid, err)
		}
		log.V(5).Info("add library item to cache", "item", libitemid, "name", libitem.Item.Description)
		c.Update(libitem)
	case TypeCustomField:
		// refresh the custom field definition
		// custom fields are only allow all refresh
		log.V(2).Info("refreshing custom field definition", "key", key.Value)
		return c.initCustomFields(ctx)
	case TypeDatastoreFile:
		if key.Value == "" {
			// refresh all datastore file
			log.V(2).Info("refreshing datastore file", "key", key.Value)
			return c.initDatastoreFiles(ctx)
		}
	case TypeAttachedObject:
		if key.Value == "" {
			log.V(2).Info("refreshing attached objects")
			if err := c.initTags(ctx); err != nil {
				return err
			}
			if err := c.initAttachedObjects(ctx); err != nil {
				return err
			}
		} else {
			objref := types.ManagedObjectReference{}
			objref.FromString(key.Value)
			log.V(2).Info("refreshing attached object", "ref", objref)
			if err := c.refreshAttachedObjects(ctx, objref); err != nil {
				return fmt.Errorf("refresh attached object %s: %w", key.Value, err)
			}
		}
	}
	return nil
}

// Replace implements CacheStore.
func (c *ObjectCache) Replace(items map[types.ManagedObjectReference]mo.Reference) {
	newbytype := make(map[string]*IndexThreadSafeMap[mo.Reference])
	for key, item := range items {
		if item == nil {
			continue
		}
		thistype := item.Reference().Type
		typecache, ok := newbytype[thistype]
		if !ok {
			typecache = NewIndexThreadSafeMap(c.config.TypedIndexers[thistype])
			newbytype[thistype] = typecache
		}
		typecache.Update(key.Value, item)
	}
	c.lock.Lock()
	defer c.lock.Unlock()
	// replace the the existing types
	for typ, newtypecache := range newbytype {
		c.bytype[typ] = newtypecache
	}
}

func (c *ObjectCache) ReplaceType(typ string, items map[string]mo.Reference) {
	c.lock.Lock()
	defer c.lock.Unlock()
	typecache, ok := c.bytype[typ]
	if !ok {
		typecache = NewIndexThreadSafeMap(c.config.TypedIndexers[typ])
		c.bytype[typ] = typecache
	}
	typecache.Replace(items)
}

// Update implements CacheStore.
func (c *ObjectCache) Update(value mo.Reference) {
	c.lock.Lock()
	key := value.Reference()
	typecache, ok := c.bytype[key.Type]
	if !ok {
		typecache = NewIndexThreadSafeMap(c.config.TypedIndexers[key.Type])
		c.bytype[key.Type] = typecache
	}
	c.lock.Unlock()
	typecache.Update(key.Value, value)
}

func (c *ObjectCache) WaitSync(ctx context.Context) error {
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			if c.synced {
				return nil // cache is synced
			}
			log.FromContext(ctx).V(2).Info("waiting for cache to sync")
			time.Sleep(time.Second) // wait for a while before checking again
		}
	}
}

func (c *ObjectCache) Run(ctx context.Context) error {
	// initialize cache
	log := log.FromContext(ctx).WithName("ObjectCache")
	if err := c.resync(ctx); err != nil {
		log.Error(err, "initialize libraries")
		return err
	}
	// watch changes
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return retry.OnError(ctx, func(ctx context.Context) error {
			if err := c.runEvents(ctx); err != nil {
				return fmt.Errorf("run events: %w", err)
			}
			return nil
		})
	})
	eg.Go(func() error {
		return retry.OnError(ctx, func(ctx context.Context) error {
			if err := c.runTasks(ctx); err != nil {
				return fmt.Errorf("run tasks: %w", err)
			}
			return nil
		})
	})
	eg.Go(func() error {
		return retry.OnError(ctx, func(ctx context.Context) error {
			if err := runContainerViewCache(ctx, c.cli.VimClient.Client, c.config.PropertySet, c, &c.synced); err != nil {
				return fmt.Errorf("run container view cache: %w", err)
			}
			return nil
		})
	})
	eg.Go(func() error {
		if c.config.ResyncInterval > 0 {
			log.V(2).Info("resyncing cache periodically", "interval", c.config.ResyncInterval)
			ticker := time.NewTimer(c.config.ResyncInterval)
			defer ticker.Stop()
			for {
				select {
				case <-ticker.C:
					log.Info("resyncing cache")
					if err := c.resync(ctx); err != nil {
						log.Error(err, "resync cache")
					}
				case <-ctx.Done():
					return ctx.Err()
				}
			}
		}
		return nil
	})
	eg.Go(func() error {
		log.V(2).Info("consuming notifications")
		go func() {
			<-ctx.Done()
			c.queue.ShutDown() // ensure the queue is shutdown when done
		}()
		return c.consumeNotifications(ctx)
	})
	return eg.Wait()
}

func (c *ObjectCache) resync(ctx context.Context) error {
	if err := c.initCategories(ctx); err != nil {
		log.Error(err, "initialize categories")
		return err
	}
	if err := c.initCustomFields(ctx); err != nil {
		log.Error(err, "initialize custom fields")
		return err
	}
	if err := c.initTags(ctx); err != nil {
		log.Error(err, "initialize tags")
		return err
	}
	if err := c.initAttachedObjects(ctx); err != nil {
		log.Error(err, "initialize attached objects")
		return err
	}
	if err := c.initLibraries(ctx); err != nil {
		log.Error(err, "initialize libraries")
		return err
	}
	if err := c.initLibraryItems(ctx); err != nil {
		log.Error(err, "initialize library items")
		return err
	}
	if err := c.initDatastoreFiles(ctx); err != nil {
		log.Error(err, "initialize datastore files")
		return err
	}
	return nil
}

func (c *ObjectCache) initLibraries(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.V(2).Info("initializing libraries cache")
	libraries, err := library.NewManager(c.cli.RestClient).GetLibraries(ctx)
	if err != nil {
		return fmt.Errorf("list libraries: %w", err)
	}
	cache := NewSimpleThreadSafeMap[mo.Reference]()
	for _, lib := range libraries {
		libmo := &ContentLibrary{
			ManagedObjectReference: types.ManagedObjectReference{
				Type: TypeContentLibrary, Value: lib.ID,
			},
			Library: lib,
		}
		log.V(2).Info("adding library to cache", "name", lib.Name)
		cache.Set(libmo.Reference().Value, libmo)
	}
	log.Info("initialized libraries cache", "count", len(cache.Map()))
	c.ReplaceType(TypeContentLibrary, cache.Map())
	return nil
}

func (c *ObjectCache) initLibraryItems(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.V(2).Info("initializing library items cache")
	contentLibraries, err := CacheToList[*ContentLibrary](c, TypeContentLibrary, nil)
	if err != nil {
		return fmt.Errorf("list content libraries: %w", err)
	}
	libman := library.NewManager(c.cli.RestClient)

	cache := NewSimpleThreadSafeMap[mo.Reference]()
	eg, ctx := errgroup.WithContext(ctx)
	eg.SetLimit(10) // limit concurrent requests to avoid overwhelming the vCenter
	for _, item := range contentLibraries {
		eg.Go(func() error {
			libitemids, err := libman.ListLibraryItems(ctx, item.Value)
			if err != nil {
				return fmt.Errorf("get library items for %s: %w", item.Value, err)
			}
			optionalDatastoreID := findContentLibraryDataStore(item.Library)
			for _, libitemid := range libitemids {
				eg.Go(func() error {
					// TODO: merge multi content library search into one search for performance
					libitem, err := GetContentLibraryItem(ctx, c.cli, libitemid, optionalDatastoreID)
					if err != nil {
						return fmt.Errorf("refresh library item %s: %w", libitemid, err)
					}
					log.V(2).Info("adding library item to cache", "item", libitemid, "name", libitem.Item.Name)
					cache.Set(libitemid, libitem)
					return nil
				})
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return fmt.Errorf("initialize library items: %w", err)
	}
	// replace the old cache with the new one
	c.ReplaceType(TypeContentLibraryItem, cache.Map())
	return nil
}

func (c *ObjectCache) initCategories(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.V(2).Info("initializing categories cache")
	categories, err := tags.NewManager(c.cli.RestClient).GetCategories(ctx)
	if err != nil {
		return fmt.Errorf("list categories: %w", err)
	}
	cache := make(map[string]mo.Reference, len(categories))
	for _, cat := range categories {
		catmo := &Category{
			ManagedObjectReference: types.ManagedObjectReference{Type: TypeCategory, Value: cat.ID},
			Category:               cat,
		}
		log.V(2).Info("adding category to cache", "name", cat.Name)
		cache[cat.ID] = catmo
	}
	log.Info("initialized categories cache", "count", len(cache))
	c.ReplaceType(TypeCategory, cache)
	return nil
}

type Category struct {
	types.ManagedObjectReference
	Category tags.Category
}

func (c *ObjectCache) initTags(ctx context.Context) error {
	tagman := tags.NewManager(c.cli.RestClient)
	tags, err := tagman.GetTags(ctx)
	if err != nil {
		return fmt.Errorf("list tags: %w", err)
	}
	cache := map[string]mo.Reference{}
	for _, tag := range tags {
		tagmo := &Tag{
			ManagedObjectReference: types.ManagedObjectReference{Type: TypeTag, Value: tag.ID},
			Tag:                    tag,
		}
		log.V(2).Info("adding tag to cache", "name", tag.Name)
		cache[tag.ID] = tagmo
	}
	log.Info("initialized tags cache", "count", len(cache))
	c.ReplaceType(TypeTag, cache)
	return nil
}

type Tag struct {
	types.ManagedObjectReference
	Tag tags.Tag
}

func (c *ObjectCache) initAttachedObjects(ctx context.Context) error {
	taglist, err := CacheToList[*Tag](c, TypeTag, nil)
	if err != nil {
		return fmt.Errorf("list tags: %w", err)
	}
	tagman := tags.NewManager(c.cli.RestClient)
	tagids := make([]string, len(taglist))
	for i, tag := range taglist {
		tagids[i] = tag.Value
	}
	attachedObjects, err := tagman.ListAttachedObjectsOnTags(ctx, tagids)
	if err != nil {
		return fmt.Errorf("list attached objects for tags: %w", err)
	}
	objecttags := map[string]mo.Reference{}
	for _, item := range attachedObjects {
		for _, objid := range item.ObjectIDs {
			key := objid.Reference().String()
			obj, ok := objecttags[key]
			if !ok {
				obj = &AttachedObject{ManagedObjectReference: objid.Reference()}
			}
			attachedObject, ok := obj.(*AttachedObject)
			if !ok {
				continue // skip if not an AttachedObject
			}
			attachedObject.TagIDs = append(attachedObject.TagIDs, item.TagID)
			objecttags[key] = attachedObject
		}
	}
	log.Info("initialized attached objects cache", "count", len(objecttags))
	c.ReplaceType(TypeAttachedObject, objecttags)
	return nil
}

func (c *ObjectCache) refreshAttachedObjects(ctx context.Context, ref types.ManagedObjectReference) error {
	log := log.FromContext(ctx)
	log.V(2).Info("refreshing attached objects", "ref", ref)
	tagman := tags.NewManager(c.cli.RestClient)

	vapitagids, err := tagman.ListAttachedTags(ctx, ref)
	if err != nil {
		return fmt.Errorf("get attached tags for %s: %w", ref.Value, err)
	}
	// update the cache
	c.Set(&AttachedObject{
		ManagedObjectReference: types.ManagedObjectReference{
			Type:  TypeAttachedObject,
			Value: ref.String(),
		},
		TagIDs: vapitagids,
	})
	return nil
}

type AttachedObject struct {
	types.ManagedObjectReference
	TagIDs []string
}

func findContentLibraryDataStore(lib library.Library) string {
	for _, backend := range lib.Storage {
		if backend.Type == "DATASTORE" {
			return backend.DatastoreID
		}
	}
	return ""
}

type ContentLibrary struct {
	types.ManagedObjectReference
	Library library.Library
}

func GetContentLibraryItem(ctx context.Context, cli *Client, libraryItemID, optionalDatastoreID string) (*ContentLibraryItem, error) {
	libman := library.NewManager(cli.RestClient)
	item, err := libman.GetLibraryItem(ctx, libraryItemID)
	if err != nil {
		return nil, fmt.Errorf("get library items for %s: %w", libraryItemID, err)
	}
	// try from content library item
	if optionalDatastoreID == "" {
		library, err := libman.GetLibraryByID(ctx, item.LibraryID)
		if err != nil {
			return nil, fmt.Errorf("get library for %s: %w", item.LibraryID, err)
		}
		optionalDatastoreID = findContentLibraryDataStore(*library)
	}
	libitemmo := &ContentLibraryItem{
		ManagedObjectReference: types.ManagedObjectReference{
			Type: TypeContentLibraryItem, Value: item.ID,
		},
		DataStore: optionalDatastoreID,
		Item:      *item,
	}
	if optionalDatastoreID != "" {
		// find OVF file in the library item files
		if item.Type == library.ItemTypeOVF {
			ovfEnvelope, err := findContentLibraryItemOVFFromDatastore(ctx,
				cli.VimClient.Client, cli.Datacenter.InventoryPath,
				item.LibraryID, libraryItemID, optionalDatastoreID)
			if err != nil {
				log.V(2).Info("download OVF", "desc", item.Description, "error", err)
			} else {
				libitemmo.OVF = ovfEnvelope
			}
		}
	}
	return libitemmo, nil
}

func FindContentLibraryItemOVF(ctx context.Context, cli *rest.Client, libraryItemID string) (*ovf.Envelope, error) {
	libman := library.NewManager(cli)
	libitemfiles, err := libman.ListLibraryItemFiles(ctx, libraryItemID)
	if err != nil {
		return nil, fmt.Errorf("list library item files for %s: %w", libraryItemID, err)
	}
	for _, file := range libitemfiles {
		if !strings.HasSuffix(file.Name, ".ovf") {
			continue // only process OVF files
		}
		// Fixme: the DownloadEndpoint is always be empty
		// even use [library.Manager.GetLibraryItemFile] to download the file
		// Another way is use [library.Manager.GetLibraryItemDownloadSessionFile] to download the file
		// but is to complex and import the download session management.
		//
		// Another way is to use [library.Manager.CreateLibraryItemDownloadSession]
		// and then [library.Manager.PrepareLibraryItemDownloadSessionFile] the file
		// poll until the file is ready and then download it.
		// It too complex for now.
		downloadurl, err := url.Parse(file.DownloadEndpoint)
		if err != nil {
			return nil, fmt.Errorf("parse download endpoint %s: %w", file.DownloadEndpoint, err)
		}
		rc, _, err := library.NewManager(cli).Download(ctx, downloadurl, &soap.DefaultDownload)
		if err != nil {
			return nil, fmt.Errorf("download library item %s: %w", file.Name, err)
		}
		defer rc.Close()
		return ovf.Unmarshal(rc)
	}
	return nil, errors.NewNotFound("library item ovf", "")
}

func findContentLibraryItemOVFFromDatastore(ctx context.Context, cli *vim25.Client, datacenterPath string, libraryID, libraryItemID, dataStoreid string) (*ovf.Envelope, error) {
	contentlibitempath := fmt.Sprintf("contentlib-%s/%s", libraryID, libraryItemID)
	results, err := SearchDataStore(ctx, cli, dataStoreid, contentlibitempath, nil, []string{})
	if err != nil {
		return nil, fmt.Errorf("list ovf files for library item %s: %w", libraryItemID, err)
	}
	for _, result := range results {
		for _, file := range result.File {
			fi := file.GetFileInfo()
			if !strings.HasSuffix(fi.Path, ".ovf") {
				continue
			}
			dspath := object.DatastorePath{}
			dspath.FromString(JoinDatastorePath(result.FolderPath, fi.Path))
			rc, err := downloadDatastoreFile(ctx, cli, datacenterPath, dspath.Datastore, dspath.Path)
			if err != nil {
				return nil, fmt.Errorf("download ovf file for library item %s: %w", libraryItemID, err)
			}
			defer rc.Close()
			return ovf.Unmarshal(rc)
		}
	}
	return nil, errors.NewNotFound("ovf file", fmt.Sprintf("library item %s", libraryItemID))
}

var _ mo.Reference = &ContentLibraryItem{}

type ContentLibraryItem struct {
	types.ManagedObjectReference
	Item      library.Item
	DataStore string // DataStore is the datastore ID where the library item is stored
	Files     []library.File
	OVF       *ovf.Envelope
}

func (c *ObjectCache) initDatastoreFiles(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.V(2).Info("initializing datastore files cache")
	datastores, err := c.cli.RestListDatastore(ctx, c.cli.Datacenter.Reference().Value)
	if err != nil {
		return fmt.Errorf("list datastores: %w", err)
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.SetLimit(5) // limit concurrent requests to avoid overwhelming the vCenter

	cache := NewSimpleThreadSafeMap[mo.Reference]()
	for _, ds := range datastores {
		eg.Go(func() error {
			log.V(2).Info("scanning datastore for files", "datastore", ds.Name)
			files, err := ListDataStoreDiskFiles(ctx, c.cli.VimClient.Client, ds.Datastore, []string{"*.vmdk"})
			if err != nil {
				return fmt.Errorf("list files for datastore %s: %w", ds.Name, err)
			}
			for _, file := range files {
				shallow := file
				cache.Set(file.Reference().Value, &shallow)
			}
			log.V(2).Info("finished scanning datastore files", "datastore", ds.Name)
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}
	kvs := cache.Map()
	log.Info("initialized datastore files cache", "count", len(kvs))
	c.ReplaceType(TypeDatastoreFile, kvs)
	return nil
}

func ListDataStoreDiskFiles(ctx context.Context, cli *vim25.Client, dsid string, files []string) ([]DatastoreFile, error) {
	query := []types.BaseFileQuery{
		&types.VmDiskFileQuery{
			Details: &types.VmDiskFileQueryFlags{
				CapacityKb:      true,
				DiskType:        true,
				HardwareVersion: true,
				ControllerType:  types.NewBool(true),
				DiskExtents:     types.NewBool(true),
				Thin:            types.NewBool(true),
			},
		},
	}
	// convert to search pattern
	for i, file := range files {
		files[i] = FilenameToSearchPattern(file)
	}
	return ListDataStoreFiles(ctx, cli, dsid, query, "", files)
}

func FindMaxLengthCommonPrefix(strs []string) string {
	if len(strs) == 0 {
		return ""
	}
	if len(strs) == 1 {
		return strs[0]
	}
	prefix := strs[0]
	for _, str := range strs[1:] {
		prefix = findPrefix(prefix, str)
		if prefix == "" {
			break
		}
	}
	return prefix
}

func findPrefix(a, b string) string {
	if a == "" || b == "" {
		return ""
	}
	minLen := min(len(a), len(b))
	for i := range minLen {
		if a[i] != b[i] {
			return a[:i]
		}
	}
	return a[:minLen]
}

func ListDataStoreFiles(ctx context.Context, cli *vim25.Client, dsid string, query []types.BaseFileQuery, dir string, files []string) ([]DatastoreFile, error) {
	log := log.FromContext(ctx)
	log.V(2).Info("scanning datastore", "datastore", dsid, "dir", dir, "files", files)
	results, err := SearchDataStore(ctx, cli, dsid, dir, query, files)
	if err != nil {
		return nil, err
	}
	vmdkfiles := hostDatastoreBrowserSearchResultsToDatastoreFile(results)
	log.V(5).Info("found datastore files", "count", len(vmdkfiles), "datastore", dsid)
	return vmdkfiles, nil
}

// FilenameToSearchPattern converts a filename to a search pattern.
// exact match is not supported on seatch, must convet to a pattern
// e.g. "foo.vmdk" -> "*.vmdk"
// e.g. "alpine/alpine-3.12.0-x86_64.iso" -> "alpine*.iso"
func FilenameToSearchPattern(filename string) string {
	if !strings.Contains(filename, "/") {
		// no path, return as is
		// filename pattern is already a pattern
		return filename
	}
	if strings.ContainsAny(filename, "*?") {
		return filename // already a pattern, return as is
	}
	dir, base := path.Split(filename)
	base = "*" + path.Ext(base)
	return path.Join(dir, base)
}

func hostDatastoreBrowserSearchResultsToDatastoreFile(list []types.HostDatastoreBrowserSearchResults) []DatastoreFile {
	var datastorefiles []DatastoreFile
	for _, r := range list {
		for _, f := range r.File {
			fi := f.GetFileInfo()
			firef := types.ManagedObjectReference{
				Type:  TypeDatastoreFile,
				Value: JoinDatastorePath(r.FolderPath, fi.Path),
			}
			fimo := &DatastoreFile{
				ManagedObjectReference: firef,
				DataStore:              r.Datastore.Value,
				FileInfo:               *fi,
			}
			switch file := f.(type) {
			case *types.VmDiskFileInfo:
				fimo.VmDiskFileInfo = file
			case *types.FolderFileInfo:
				continue // skip folder files
			default:
				// we do not concern about other file types
			}
			datastorefiles = append(datastorefiles, *fimo)
		}
	}
	return datastorefiles
}

// JoinDatastorePath joins a folder and file path into a full datastore path.
// incase of "[data-ssd]" "/alpine.vmdk" it will return "[data-ssd] /alpine.vmdk"
// instead of "[data-ssd] alpine.vmdk"
func JoinDatastorePath(folder, file string) string {
	file = strings.TrimPrefix(file, "/") // remove leading slash
	if folder != "" && folder[len(folder)-1] == ']' {
		return folder + " " + file // if folder ends with ']', do not add a slash
	}
	if !strings.HasSuffix(folder, "/") {
		folder += "/" // ensure folder ends with a slash
	}
	return folder + file
}

func SearchDataStore(ctx context.Context, cli *vim25.Client, dsid string, dir string, query []types.BaseFileQuery, files []string) ([]types.HostDatastoreBrowserSearchResults, error) {
	dsmo := mo.Datastore{}
	// [object.Datastore.Browser] is get "browser" property from datastore
	// we get the browser and name property
	dsobj := types.ManagedObjectReference{Type: TypeDatastore, Value: dsid}
	if err := property.DefaultCollector(cli).RetrieveOne(ctx, dsobj, []string{"name", "summary", "browser"}, &dsmo); err != nil {
		return nil, fmt.Errorf("get datastore browser for %s: %w", dsmo.Name, err)
	}
	if !dsmo.Summary.Accessible {
		log.FromContext(ctx).V(2).Info("skip search datastore is not accessible", "datastore", dsmo.Name, "dir", dir, "files", files)
		return nil, nil
	}
	browser := object.NewHostDatastoreBrowser(cli, dsmo.Browser)
	searchSpec := &types.HostDatastoreBrowserSearchSpec{
		Query: query,
		Details: &types.FileQueryFlags{
			FileType:     true,
			FileSize:     true,
			Modification: true,
			FileOwner:    types.NewBool(false),
		},
		MatchPattern: files,
	}
	dspath := object.DatastorePath{
		Datastore: dsmo.Name,
		Path:      dir,
	}
	task, err := browser.SearchDatastoreSubFolders(ctx, dspath.String(), searchSpec)
	if err != nil {
		if IsTaskFileNotFoundError(err) {
			log.V(2).Info("no files found error", "datastore", dsmo.Name, "dir", dir, "files", files)
			return nil, nil // no files found, return empty result
		}
		return nil, fmt.Errorf("search datastore %s for virtual disks: %w", dsmo.Name, err)
	}
	// WaitForResult is expensive, it
	// 1. DefaultCollector.Create
	// 2. collector.CreateFilter
	// 3. collector.WaitForUpdatesEx
	// 4. collector.Destroy
	result, err := task.WaitForResult(ctx)
	if err != nil {
		return nil, fmt.Errorf("wait for datastore search task for %s: %w", dsmo.Name, err)
	}
	if result.Error != nil {
		return nil, fmt.Errorf("datastore search task for %s failed: %s", dsmo.Name, result.Error.LocalizedMessage)
	}
	var resultlist []types.HostDatastoreBrowserSearchResults
	switch typedresult := result.Result.(type) {
	case types.HostDatastoreBrowserSearchResults:
		resultlist = append(resultlist, typedresult)
	case types.ArrayOfHostDatastoreBrowserSearchResults:
		resultlist = typedresult.HostDatastoreBrowserSearchResults
	}
	return resultlist, nil
}

func downloadDatastoreFile(ctx context.Context, cli *vim25.Client, dcpath, dsname, file string) (io.ReadCloser, error) {
	// more information see [object.Datastore.NewURL]
	cliurl := cli.URL()
	downloadurl := &url.URL{
		Scheme:   cliurl.Scheme,
		Host:     cliurl.Host,
		Path:     fmt.Sprintf("/folder/%s", file),
		RawQuery: url.Values{"dcPath": []string{dcpath}, "dsName": []string{dsname}}.Encode(),
	}
	rc, len, err := cli.Download(ctx, downloadurl, &soap.DefaultDownload)
	if err != nil {
		return nil, fmt.Errorf("download file %s from datastore %s: %w", file, dsname, err)
	}
	log.V(5).Info("downloading file from datastore", "datastore", dsname, "file", file, "size", len)
	return rc, nil
}

var _ mo.Reference = &DatastoreFile{}

// DatastoreFile wraps a VmDiskFileInfo and adds the folder path
// The Type is "VmDiskFileInfo" to distinguish it from the original VmDiskFileInfo
type DatastoreFile struct {
	types.ManagedObjectReference
	FileInfo types.FileInfo
	// VmDiskFileInfo is the original VmDiskFileInfo if the file is a virtual disk file
	VmDiskFileInfo *types.VmDiskFileInfo
	DataStore      string // DataStore is the datastore ID where the file is stored
}

func (c *ObjectCache) initCustomFields(ctx context.Context) error {
	log := log.FromContext(ctx)
	log.V(2).Info("initializing custom fields cache")

	fm, err := object.GetCustomFieldsManager(c.cli.VimClient.Client)
	if err != nil {
		if err == object.ErrNotSupported {
			return nil // custom fields are not supported, skip initialization
		}
		return err
	}
	customFields, err := fm.Field(ctx)
	if err != nil {
		return fmt.Errorf("list custom fields: %w", err)
	}
	cache := make(map[string]mo.Reference, len(customFields))
	for _, field := range customFields {
		fieldmo := &CustomField{
			ManagedObjectReference: types.ManagedObjectReference{
				Type:  TypeCustomField,
				Value: strconv.Itoa(int(field.Key)),
			},
			CustomFieldDef: field,
		}
		log.V(2).Info("adding custom field to cache", "name", field.Name, "key", field.Key)
		cache[fieldmo.ManagedObjectReference.Value] = fieldmo
	}
	log.Info("initialized custom fields cache", "count", len(cache))
	c.ReplaceType(TypeCustomField, cache)
	return nil
}

type CustomField struct {
	types.ManagedObjectReference
	CustomFieldDef types.CustomFieldDef
}

var _ CacheStore = &ObjectCache{}

type CacheStore interface {
	Replace(items map[types.ManagedObjectReference]mo.Reference)
	Update(value mo.Reference)
	Delete(key types.ManagedObjectReference) bool
	Get(key types.ManagedObjectReference) (mo.Reference, bool)
}

func runContainerViewCache(ctx context.Context, cli *vim25.Client, propertySet []types.PropertySpec, cache CacheStore, synced *bool) error {
	log := log.FromContext(ctx)
	managedObjectTypes := make([]string, 0, len(propertySet))
	for _, ps := range propertySet {
		managedObjectTypes = append(managedObjectTypes, ps.Type)
	}
	view, err := view.NewManager(cli).CreateContainerView(ctx, cli.ServiceContent.RootFolder, managedObjectTypes, true)
	if err != nil {
		return err
	}
	// nolint errcheck
	defer view.Destroy(ctx)

	spec := types.PropertyFilterSpec{
		ObjectSet: []types.ObjectSpec{
			{
				Obj:  view.Reference(),
				Skip: types.NewBool(true),
				SelectSet: []types.BaseSelectionSpec{
					&types.TraversalSpec{Type: view.Reference().Type, Path: "view"},
				},
			},
		},
		PropSet: propertySet,
	}
	pc := property.DefaultCollector(cli)

	isFirstEvent := true
	updatefunc := func(ou []types.ObjectUpdate) bool {
		if isFirstEvent {
			// The first event is always a full sync
			// We can clear the cache and replace it with the new items
			isFirstEvent = false
			items := make(map[types.ManagedObjectReference]mo.Reference, len(ou))
			for _, update := range ou {
				if update.Kind != types.ObjectUpdateKindEnter {
					continue
				}
				newobj, err := newObjectFromEnterEvent(update)
				if err != nil {
					continue
				}
				log.V(3).Info("initializing cache item", "key", update.Obj)
				items[update.Obj] = newobj
			}
			log.V(2).Info("init cache with new items", "count", len(items))
			cache.Replace(items)
			if synced != nil {
				*synced = true // mark the cache as synced
			}
			log.V(2).Info("cache initialized", "synced", *synced)
			return false
		}
		for _, update := range ou {
			key := update.Obj
			switch update.Kind {
			case types.ObjectUpdateKindEnter:
				newobj, err := newObjectFromEnterEvent(update)
				if err != nil {
					continue
				}
				log.V(3).Info("adding new item to cache", "key", update.Obj)
				cache.Update(newobj)
			case types.ObjectUpdateKindLeave:
				log.V(3).Info("removing item from cache", "key", update.Obj)
				cache.Delete(key)
			case types.ObjectUpdateKindModify:
				val, ok := cache.Get(key)
				if !ok {
					log.Error(fmt.Errorf("object %s not found in cache", key), "update item in cache")
					continue
				}
				log.V(3).Info("updating item in cache", "key", update.Obj)
				mo.ApplyPropertyChange(val, update.ChangeSet)
				cache.Update(val)
			}
		}
		return false
	}
	return property.WaitForUpdatesEx(ctx, pc, &property.WaitFilter{CreateFilter: types.CreateFilter{Spec: spec}}, updatefunc)
}

func newObjectFromEnterEvent(update types.ObjectUpdate) (mo.Reference, error) {
	// Convert the ChangeSet to a slice of DynamicProperty
	// and then convert the ObjectContent to the type T
	// Do not use [mo.ApplyPropertyChange] to an new empty object, it panics if the field is not found
	propset := make([]types.DynamicProperty, 0, len(update.ChangeSet))
	for _, prop := range update.ChangeSet {
		propset = append(propset, types.DynamicProperty{Name: prop.Name, Val: prop.Val})
	}
	obj, err := mo.ObjectContentToType(types.ObjectContent{Obj: update.Obj, PropSet: propset, MissingSet: update.MissingSet}, true)
	if err != nil {
		return nil, fmt.Errorf("convert object content to type: %w", err)
	}
	moobj, ok := obj.(mo.Reference)
	if !ok {
		return nil, fmt.Errorf("object %s is not a reference type", update.Obj)
	}
	return moobj, nil
}

type IndexThreadSafeMap[T any] struct {
	lock  sync.RWMutex
	items map[string]T
	index *storeIndex[T]
}

// NewIndexThreadSafeMap creates a new cache for objects
func NewIndexThreadSafeMap[T any](indexers Indexers[T]) *IndexThreadSafeMap[T] {
	return &IndexThreadSafeMap[T]{
		items: map[string]T{},
		index: &storeIndex[T]{indexers: indexers, indices: Indices{}},
	}
}

func (c *IndexThreadSafeMap[T]) Update(key string, value T) {
	c.lock.Lock()
	defer c.lock.Unlock()
	oldObject := c.items[key]
	c.items[key] = value
	c.index.updateIndices(oldObject, value, key)
}

func (c *IndexThreadSafeMap[T]) Delete(key string) bool {
	c.lock.Lock()
	defer c.lock.Unlock()
	if obj, exists := c.items[key]; exists {
		c.index.updateIndices(obj, *new(T), key)
		delete(c.items, key)
		return true
	}
	return false
}

func (c *IndexThreadSafeMap[T]) Get(key string) (item T, exists bool) {
	c.lock.RLock()
	defer c.lock.RUnlock()
	item, exists = c.items[key]
	return item, exists
}

func (c *IndexThreadSafeMap[T]) List() []T {
	c.lock.RLock()
	defer c.lock.RUnlock()
	list := make([]T, 0, len(c.items))
	for _, item := range c.items {
		list = append(list, item)
	}
	return list
}

func (c *IndexThreadSafeMap[T]) ListPrefixed(prefix string) []T {
	c.lock.RLock()
	defer c.lock.RUnlock()
	list := make([]T, 0, len(c.items))
	for key, item := range c.items {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			list = append(list, item)
		}
	}
	return list
}

func (c *IndexThreadSafeMap[T]) ListByIndex(indexName, indexedValue string) ([]T, error) {
	c.lock.RLock()
	defer c.lock.RUnlock()

	set, err := c.index.getKeysByIndex(indexName, indexedValue)
	if err != nil {
		return nil, err
	}
	list := make([]T, 0, set.Len())
	for key := range set {
		list = append(list, c.items[key])
	}
	return list, nil
}

func (c *IndexThreadSafeMap[T]) AddIndexers(newIndexers Indexers[T]) error {
	c.lock.Lock()
	defer c.lock.Unlock()

	if err := c.index.addIndexers(newIndexers); err != nil {
		return err
	}
	// If there are already items, index them
	for key, item := range c.items {
		for name := range newIndexers {
			_ = c.index.updateSingleIndex(name, *new(T), item, key)
		}
	}
	return nil
}

func (c *IndexThreadSafeMap[T]) Replace(items map[string]T) {
	c.lock.Lock()
	defer c.lock.Unlock()
	c.items = items

	// rebuild any index
	c.index.reset()
	for key, item := range c.items {
		c.index.updateIndices(*new(T), item, key)
	}
}

type (
	IndexFunc[T any] func(obj T) ([]string, error)
	Indexers[T any]  map[string]IndexFunc[T]
	Index            map[string]collections.Set[string]
	Indices          map[string]Index
)

type storeIndex[T any] struct {
	indexers Indexers[T]
	indices  Indices
}

func (i *storeIndex[T]) updateIndices(oldObj, newObj T, key string) {
	for name := range i.indexers {
		_ = i.updateSingleIndex(name, oldObj, newObj, key)
	}
}

func (i *storeIndex[T]) updateSingleIndex(name string, oldObj, newObj T, key string) error {
	indexFunc, ok := i.indexers[name]
	if !ok {
		return fmt.Errorf("indexer %q does not exist", name)
	}
	oldIndexValues, err := indexFunc(oldObj)
	if err != nil {
		return fmt.Errorf("unable to calculate an index entry for key %q on index %q: %v", key, name, err)
	}
	indexValues, err := indexFunc(newObj)
	if err != nil {
		return fmt.Errorf("unable to calculate an index entry for key %q on index %q: %v", key, name, err)
	}
	index := i.indices[name]
	if index == nil {
		index = Index{}
		i.indices[name] = index
	}
	if len(indexValues) == 1 && len(oldIndexValues) == 1 && indexValues[0] == oldIndexValues[0] {
		return nil
	}
	for _, value := range oldIndexValues {
		i.deleteKeyFromIndex(key, value, index)
	}
	for _, value := range indexValues {
		i.addKeyToIndex(key, value, index)
	}
	return nil
}

func (i *storeIndex[T]) deleteKeyFromIndex(key, indexValue string, index Index) {
	set := index[indexValue]
	if set == nil {
		return
	}
	set.Delete(key)
	if len(set) == 0 {
		delete(index, indexValue)
	}
}

func (i *storeIndex[T]) addKeyToIndex(key, indexValue string, index Index) {
	set := index[indexValue]
	if set == nil {
		set = collections.Set[string]{}
		index[indexValue] = set
	}
	set.Insert(key)
}

func (i *storeIndex[T]) getKeysByIndex(indexName, indexedValue string) (collections.Set[string], error) {
	indexFunc := i.indexers[indexName]
	if indexFunc == nil {
		return nil, fmt.Errorf("Index with name %s does not exist", indexName)
	}

	index := i.indices[indexName]
	return index[indexedValue], nil
}

func (i *storeIndex[T]) addIndexers(newIndexers Indexers[T]) error {
	maps.Copy(i.indexers, newIndexers)
	return nil
}

func (i *storeIndex[T]) reset() {
	i.indices = Indices{}
}

func CacheToListRefs[T *F, F mo.Reference](cache *ObjectCache, typ string, refs []types.ManagedObjectReference) ([]F, error) {
	return CacheToList(cache, typ, func(f F) bool {
		return ContainsReference(refs, f.Reference())
	})
}

func CacheToList[T *F, F mo.Reference](cache *ObjectCache, typ string, filterFunc func(F) bool) ([]F, error) {
	list, ok := cache.ListByType(typ)
	if !ok {
		return nil, nil
	}
	ret := make([]F, 0, len(list))
	for _, item := range list {
		typedp, ok := item.(T)
		if !ok {
			continue
		}
		typed := *typedp
		if filterFunc == nil || filterFunc(typed) {
			ret = append(ret, typed)
		}
	}
	// sort once
	slices.SortFunc(ret, func(a, b F) int {
		return strings.Compare(a.Reference().Value, b.Reference().Value)
	})
	return ret, nil
}

// CacheToGet return the cached object for the given reference.
// it return pointer to the object if T is a pointer type.
// Use the reutrn value carefuly, it may update the cache
func CacheToGet[T *F, F mo.Reference](cache *ObjectCache, ref types.ManagedObjectReference) (T, error) {
	val, ok := cache.Get(ref)
	if !ok {
		return nil, errors.NewNotFound(ref.Type, ref.Value)
	}
	typedp, ok := val.(T)
	if !ok {
		return nil, errors.NewNotFound(ref.Type, ref.Value)
	}
	return typedp, nil
}

func ContainsReference(refs []types.ManagedObjectReference, mo mo.Reference) bool {
	return slices.ContainsFunc(refs, func(item types.ManagedObjectReference) bool {
		return item.Type == mo.Reference().Type && item.Value == mo.Reference().Value
	})
}

type SimpleThreadSafeMap[T any] struct {
	lock  sync.RWMutex
	items map[string]T
}

func NewSimpleThreadSafeMap[T any]() *SimpleThreadSafeMap[T] {
	return &SimpleThreadSafeMap[T]{
		items: make(map[string]T),
	}
}

func (c *SimpleThreadSafeMap[T]) Set(key string, value T) {
	c.lock.Lock()
	defer c.lock.Unlock()
	c.items[key] = value
}

func (c *SimpleThreadSafeMap[T]) Map() map[string]T {
	c.lock.RLock()
	defer c.lock.RUnlock()
	return c.items
}
