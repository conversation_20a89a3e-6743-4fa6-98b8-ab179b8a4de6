package remote

import (
	"context"

	"xiaoshiai.cn/core/ismc/common"
)

// GetHost implements common.Provider.
func (r *RemoteProvider) GetHost(ctx context.Context, id string) (*common.Host, error) {
	ret := &common.Host{}
	if err := r.client.Get("/hosts/" + id).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// ListHosts implements common.Provider.
func (r *RemoteProvider) ListHosts(ctx context.Context, option common.ListHostOptions) (common.List[common.Host], error) {
	ret := common.List[common.Host]{}
	if err := r.client.Get("/hosts").
		Queries(option.Values()).
		Return(&ret).Send(ctx); err != nil {
		return ret, err
	}
	return ret, nil
}

// UpdateHost implements common.Provider.
func (r *RemoteProvider) UpdateHost(ctx context.Context, id string, host *common.Host) error {
	return r.client.Put("/hosts/" + id).JSON(host).Send(ctx)
}
