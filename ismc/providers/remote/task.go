package remote

import (
	"context"
	"strconv"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/ismc/common"
)

// ListTasks implements common.Provider.
func (r *RemoteProvider) ListTasks(ctx context.Context, opts common.ListTaskOptions) (common.List[common.Task], error) {
	ret := common.List[common.Task]{}
	if err := r.client.Get("/tasks").
		Queries(opts.Values()).
		Query("resourceType", opts.ResourceType).
		Return(&ret).
		Send(ctx); err != nil {
		return ret, err
	}
	return ret, nil
}

// GetTask implements common.Provider.
func (p *RemoteProvider) GetTask(ctx context.Context, id string) (*common.Task, error) {
	task := &common.Task{}
	if err := p.client.Get("/tasks/" + id).Return(task).Send(ctx); err != nil {
		return nil, err
	}
	return task, nil
}

// RemoveTask implements common.Provider.
func (r *RemoteProvider) RemoveTask(ctx context.Context, id string) error {
	return r.client.Delete("/tasks/" + id).Send(ctx)
}

// RestartTask implements common.Provider.
func (r *RemoteProvider) RestartTask(ctx context.Context, id string) error {
	return r.client.Post("/tasks/" + id + ":restart").Send(ctx)
}

// StopTask implements common.Provider.
func (r *RemoteProvider) StopTask(ctx context.Context, id string) error {
	return r.client.Post("/tasks/" + id + ":stop").Send(ctx)
}

// WactchTasks implements common.Provider.
func (r *RemoteProvider) WactchTasks(ctx context.Context, on common.OnTaskEvent, opts common.WactchTaskOptions) error {
	resp, err := r.client.Get("/tasks").
		Query("resourceType", opts.ResourceType).
		Query("size", strconv.Itoa(opts.Size)).
		Query("phase", string(opts.Phase)).
		Query("sort", opts.Sort).
		Query("watch", "true").
		Do(ctx)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	decoder, err := api.NewStreamDecoderFromResponse[common.WatchEvent[common.Task]](resp)
	if err != nil {
		return err
	}
	return decoder.Decode(ctx, func(ctx context.Context, kind string, data common.WatchEvent[common.Task]) error {
		return on(ctx, data)
	})
}
