package remote

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/core/ismc/common"
)

const ResourceGroup = "resourceGroup"

type Options struct {
	Server    string
	Transport http.RoundTripper
}

var _ common.Provider = (*RemoteProvider)(nil)

func NewRemote(options *Options) (*RemoteProvider, error) {
	cli := &httpclient.Client{
		RoundTripper: options.Transport,
		Server:       options.Server + "/v1/cloud",
		OnResponse: func(req *http.Request, resp *http.Response) error {
			if resp.StatusCode >= 400 {
				statuserr := &errors.Status{}
				//  limited to 1KB
				bodydata, _ := io.ReadAll(io.LimitReader(resp.Body, 1024))
				if err := json.Unmarshal(bodydata, statuserr); err != nil {
					return fmt.Errorf("invalid response data: %s", bodydata)
				}
				return statuserr
			}
			return nil
		},
	}
	return &RemoteProvider{client: cli}, nil
}

type RemoteProvider struct {
	client *httpclient.Client
}

// Version implements common.Provider.
func (r *RemoteProvider) GetVersion(ctx context.Context) (*common.Version, error) {
	version := &common.Version{}
	if err := r.client.Get("/discovery/version").Return(version).Send(ctx); err != nil {
		return nil, err
	}
	return version, nil
}

var _ common.Provider = (*RemoteProvider)(nil)
