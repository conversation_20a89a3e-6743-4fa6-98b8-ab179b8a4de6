package remote

import (
	"context"

	"xiaoshiai.cn/core/ismc/common"
)

var _ common.ResourceGroupOperation = (*RemoteProvider)(nil)

// CreateResourceGroup implements common.ResourceGroupOperation.
func (r *RemoteProvider) CreateResourceGroup(ctx context.Context, resourceGroup *common.ResourceGroup) (*common.Descripter, error) {
	desc := &common.Descripter{}
	if err := r.client.Post("/resourcegroups").JSON(resourceGroup).Return(desc).Send(ctx); err != nil {
		return nil, err
	}
	return desc, nil
}

// UpdateResourceGroup implements common.ResourceGroupOperation.
func (r *RemoteProvider) UpdateResourceGroup(ctx context.Context, id string, resourceGroup *common.ResourceGroup) error {
	panic("unimplemented")
}

// DeleteResourceGroup implements common.ResourceGroupOperation.
func (r *RemoteProvider) DeleteResourceGroup(ctx context.Context, id string) error {
	return r.client.Delete("/resourcegroups/" + id).Send(ctx)
}

// GetResourceGroup implements common.ResourceGroupOperation.
func (r *RemoteProvider) GetResourceGroup(ctx context.Context, id string) (*common.ResourceGroup, error) {
	ret := &common.ResourceGroup{}
	if err := r.client.Get("/resourcegroups/" + id).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// ListResourceGroups implements common.ResourceGroupOperation.
func (r *RemoteProvider) ListResourceGroups(ctx context.Context, options common.ListResourceGroupOptions) (common.List[common.ResourceGroup], error) {
	ret := common.List[common.ResourceGroup]{}
	req := r.client.
		Get("/resourcegroups").
		Queries(options.Values()).
		Query("name", options.Name)
	if err := req.Return(&ret).Send(ctx); err != nil {
		return ret, err
	}
	return ret, nil
}
