package remote

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.VirtualMachineOperation = (*RemoteProvider)(nil)

// GetVirtualMachine implements common.Provider.
func (r *RemoteProvider) GetVirtualMachine(ctx context.Context, id string) (*common.VirtualMachine, error) {
	vm := &common.VirtualMachine{}
	if err := r.client.Get(fmt.Sprintf("/virtualmachines/%s", id)).Return(vm).Send(ctx); err != nil {
		return nil, err
	}
	return vm, nil
}

// CreateVirtualMachine implements common.Provider.
func (r *RemoteProvider) CreateVirtualMachine(ctx context.Context, vm *common.VirtualMachine, options common.CreateVirtualMachineOptions) (*common.Descripter, error) {
	descripter := &common.Descripter{}
	if err := r.client.Post("/virtualmachines").
		JSON(vm).
		Query("wait", strconv.FormatBool(options.Wait)).
		Return(descripter).
		Send(ctx); err != nil {
		return nil, err
	}
	return descripter, nil
}

// ListVirtualMachines implements common.Provider.
func (r *RemoteProvider) ListVirtualMachines(ctx context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	vmlist := common.List[common.VirtualMachine]{}
	req := r.client.Get("/virtualmachines").
		Query(ResourceGroup, options.ResourceGroup).
		Query("zone", options.Zone).
		Query("name", options.Name).
		Queries(options.Values()).
		Return(&vmlist)

	if len(options.Tags) > 0 {
		req.Query("tags", strings.Join(options.Tags, ","))
	}
	if err := req.Send(ctx); err != nil {
		return vmlist, err
	}
	return vmlist, nil
}

// UpdateVirtualMachine implements common.Provider.
func (r *RemoteProvider) UpdateVirtualMachine(ctx context.Context, vm *common.VirtualMachine) error {
	return r.client.Put(fmt.Sprintf("/virtualmachines/%s", vm.ID)).JSON(vm).Return(vm).Send(ctx)
}

// DeleteVirtualMachine implements common.Provider.
func (r *RemoteProvider) DeleteVirtualMachine(ctx context.Context, id string, options common.DeleteVirtualMachineOptions) error {
	return r.client.Delete(fmt.Sprintf("/virtualmachines/%s", id)).
		Query("wait", strconv.FormatBool(options.Wait)).
		Query("force", strconv.FormatBool(options.Force)).
		Query("withDisks", strconv.FormatBool(options.WithDisks)).
		Send(ctx)
}

// GetVirtualMachineRemoteConnectionInfo implements common.Provider.
func (r *RemoteProvider) GetVirtualMachineRemoteConnectionInfo(ctx context.Context, id string) (*common.RemoteConnectionInfo, error) {
	info := &common.RemoteConnectionInfo{}
	if err := r.client.Get(fmt.Sprintf("/virtualmachines/%s/remoteconnectioninfo", id)).
		Return(info).
		Send(ctx); err != nil {
		return nil, err
	}
	return info, nil
}

// VNCVirtualMachine implements common.VirtualMachineOperation.
func (r *RemoteProvider) VNCVirtualMachine(resp http.ResponseWriter, req *http.Request, id string, path string) {
	target, err := url.Parse(r.client.Server)
	if err != nil {
		http.Error(resp, err.Error(), http.StatusInternalServerError)
		return
	}
	target.Path += fmt.Sprintf("/virtualmachines/%s/vnc/", id)
	p := httpclient.Proxy{
		ClientConfig: &httpclient.ClientConfig{Server: *target, RoundTripper: r.client.RoundTripper},
		RequestPath:  path,
	}
	p.ServeHTTP(resp, req)
}

// SetVirtualMachinePower implements common.Provider.
func (r *RemoteProvider) SetVirtualMachinePower(ctx context.Context, id string, power common.PowerAction, options common.VirtualMachinePowerOptions) error {
	body := &map[string]any{
		"power": power,
		"hard":  options.Hard,
	}
	return r.client.Post(fmt.Sprintf("/virtualmachines/%s/power", id)).
		JSON(body).
		Send(ctx)
}
