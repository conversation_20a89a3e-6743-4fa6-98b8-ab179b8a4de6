package remote

import (
	"context"
	"strconv"

	"xiaoshiai.cn/core/ismc/common"
)

// ListVirtualMachineDisks implements common.Provider.
func (r *RemoteProvider) ListVirtualMachineDisks(ctx context.Context, id string, options common.ListVirtualMachineDiskOptions) (common.List[common.Disk], error) {
	ret := common.List[common.Disk]{}
	if err := r.client.Get("/virtualmachines/" + id + "/disks").
		Queries(options.Values()).
		Return(&ret).
		Send(ctx); err != nil {
		return ret, err
	}
	return ret, nil
}

// AttachVirtualMachineDisk implements common.Provider.
func (r *RemoteProvider) AttachVirtualMachineDisk(ctx context.Context, id string, options common.AttachVirtualMachineDiskOptions) error {
	return r.client.Post("/virtualmachines/"+id+"/disks").
		JSON(options.Disk).
		Query("wait", strconv.FormatBool(options.Wait)).
		Send(ctx)
}

// DetachVirtualMachineDisk implements common.Provider.
func (r *RemoteProvider) DetachVirtualMachineDisk(ctx context.Context, id string, diskid string, options common.DetachVirtualMachineDiskOptions) error {
	return r.client.Delete("/virtualmachines/" + id + "/disks/" + diskid).Send(ctx)
}

// ListVirtualMachineNetworkInterfaces implements common.Provider.
func (r *RemoteProvider) ListVirtualMachineNetworkInterfaces(ctx context.Context, id string, options common.ListVirtualMachineNetworkOptions) (common.List[common.NetworkInterface], error) {
	ret := common.List[common.NetworkInterface]{}
	if err := r.client.Get("/virtualmachines/" + id + "/interfaces").
		Queries(options.Values()).
		Return(&ret).
		Send(ctx); err != nil {
		return ret, err
	}
	return ret, nil
}

// AttachVirtualMachineNetworkInterface implements common.Provider.
func (r *RemoteProvider) AttachVirtualMachineNetworkInterface(ctx context.Context, id string, options common.AttachVirtualMachineNetworkInterfaceOptions) error {
	return r.client.Post("/virtualmachines/"+id+"/interfaces").
		JSON(options.Interface).
		Query("wait", strconv.FormatBool(options.Wait)).
		Send(ctx)
}

// DetachVirtualMachineNetworkInterface implements common.Provider.
func (r *RemoteProvider) DetachVirtualMachineNetworkInterface(ctx context.Context, id string, nicid string, options common.DetachVirtualMachineNetworkInterfaceOptions) error {
	return r.client.Delete("/virtualmachines/" + id + "/interfaces/" + nicid).Send(ctx)
}

// ChangeVirtualMachineInstanceType implements common.Provider.
func (r *RemoteProvider) ChangeVirtualMachineInstanceType(ctx context.Context, id string, options common.ChangeVirtualMachineInstanceTypeOptions) error {
	return r.client.Put("/virtualmachines/" + id + "/instancetype").
		JSON(options.InstanceType).
		Send(ctx)
}

// ChangeVirtualMachineCloudInit implements common.Provider.
func (r *RemoteProvider) ChangeVirtualMachineCloudInit(ctx context.Context, id string, options common.ResetVirtualMachineCloudInitOptions) error {
	return r.client.Put("/virtualmachines/"+id+"/cloudinit").
		JSON(options.CloudInit).
		Query("wait", strconv.FormatBool(options.Wait)).
		Send(ctx)
}

// ReInstallVirtualMachine implements common.Provider.
func (r *RemoteProvider) ReInstallVirtualMachine(ctx context.Context, id string, options common.ReInstallVirtualMachineOptions) error {
	return r.client.Post("/virtualmachines/" + id + ":reinstall").
		JSON(options).
		Send(ctx)
}

// ResetVirtualMachinePassword implements common.Provider.
func (r *RemoteProvider) ResetVirtualMachinePassword(ctx context.Context, id string, options common.ResetVirtualMachinePasswordOptions) error {
	return r.client.Post("/virtualmachines/" + id + ":reset-password").
		JSON(options).
		Send(ctx)
}

// ChangeVirtualMachineImage implements common.Provider.
func (r *RemoteProvider) ChangeVirtualMachineImage(ctx context.Context, id string, options common.ChangeVirtualMachineImageOptions) error {
	return r.client.Post("/virtualmachines/" + id + ":change-image").
		JSON(options).
		Send(ctx)
}
