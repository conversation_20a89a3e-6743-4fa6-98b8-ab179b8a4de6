package remote

import (
	"context"

	"xiaoshiai.cn/core/ismc/common"
)

var _ common.MetricsOperations = &RemoteProvider{}

// GetVirtualMachineMetrics implements common.Provider.
func (r *RemoteProvider) GetVirtualMachineMetrics(ctx context.Context, id string) (*common.VirtualMachineMetrics, error) {
	ret := &common.VirtualMachineMetrics{}
	if err := r.client.Get("/virtualmachine-metrics/" + id).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// GetZoneMetrics implements common.Provider.
func (r *RemoteProvider) GetZoneMetrics(ctx context.Context, id string) (*common.ZoneMetrics, error) {
	ret := &common.ZoneMetrics{}
	if err := r.client.Get("/zone-metrics/" + id).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// ListVirtualMachineMetrics implements common.Provider.
func (r *RemoteProvider) ListVirtualMachineMetrics(ctx context.Context, options common.ListVirtualMachineMetricsOptions) ([]*common.VirtualMachineMetrics, error) {
	list := []*common.VirtualMachineMetrics{}
	if err := r.client.Get("/virtualmachine-metrics").
		Queries(options.Values()).
		Query("resourceGroup", options.ResourceGroup).
		Query("zone", options.Zone).
		Return(&list).Send(ctx); err != nil {
		return nil, err
	}
	return list, nil
}

// ListZoneMetrics implements common.Provider.
func (r *RemoteProvider) ListZoneMetrics(ctx context.Context) ([]*common.ZoneMetrics, error) {
	list := []*common.ZoneMetrics{}
	if err := r.client.Get("/zone-metrics").Return(&list).Send(ctx); err != nil {
		return nil, err
	}
	return list, nil
}

// ListDiskMetrics implements common.Provider.
func (r *RemoteProvider) ListDiskMetrics(ctx context.Context) ([]*common.DiskMetrics, error) {
	list := []*common.DiskMetrics{}
	if err := r.client.Get("/disk-metrics").Return(&list).Send(ctx); err != nil {
		return nil, err
	}
	return list, nil
}

// GetDiskMetrics implements common.Provider.
func (r *RemoteProvider) GetDiskMetrics(ctx context.Context, id string) (*common.DiskMetrics, error) {
	ret := &common.DiskMetrics{}
	if err := r.client.Get("/disk-metrics/" + id).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// GetHostMetrics implements common.Provider.
func (r *RemoteProvider) GetHostMetrics(ctx context.Context, id string) (*common.HostMetrics, error) {
	ret := &common.HostMetrics{}
	if err := r.client.Get("/host-metrics/" + id).Return(ret).
		Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// ListHostMetrics implements common.Provider.
func (r *RemoteProvider) ListHostMetrics(ctx context.Context) ([]*common.HostMetrics, error) {
	list := []*common.HostMetrics{}
	if err := r.client.Get("/host-metrics").Return(&list).Send(ctx); err != nil {
		return nil, err
	}
	return list, nil
}
