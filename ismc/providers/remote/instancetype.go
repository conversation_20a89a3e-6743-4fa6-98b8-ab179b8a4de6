package remote

import (
	"context"
	"fmt"

	"xiaoshiai.cn/core/ismc/common"
)

// UpdateInstanceType implements common.Provider.
func (r *RemoteProvider) UpdateInstanceType(ctx context.Context, instanceType *common.InstanceType) error {
	return r.client.Put(fmt.Sprintf("/instancetypes/%s", instanceType.ID)).JSON(instanceType).Send(ctx)
}

// ListInstanceTypes implements common.Provider.
func (r *RemoteProvider) ListInstanceTypes(ctx context.Context, options common.ListInstanceTypeOptions) (common.List[common.InstanceType], error) {
	list := common.List[common.InstanceType]{}
	if err := r.client.Get("/instancetypes").
		Query("zone", options.Zone).
		Query("vm", options.Vm).
		Queries(options.Values()).
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// GetInstanceType implements common.Provider.
func (r *RemoteProvider) GetInstanceType(ctx context.Context, id string) (*common.InstanceType, error) {
	ret := &common.InstanceType{}
	if err := r.client.Get(fmt.Sprintf("/instancetypes/%s", id)).
		Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// DeleteInstanceType implements common.Provider.
func (r *RemoteProvider) DeleteInstanceType(ctx context.Context, id string) error {
	return r.client.Delete(fmt.Sprintf("/instancetypes/%s", id)).Send(ctx)
}

// CreateInstanceType implements common.Provider.
func (r *RemoteProvider) CreateInstanceType(ctx context.Context, instanceType *common.InstanceType) (*common.Descripter, error) {
	p := "/instancetypes"
	desc := &common.Descripter{}
	if err := r.client.Post(p).JSON(instanceType).Return(desc).Send(ctx); err != nil {
		return nil, err
	}
	return desc, nil
}
