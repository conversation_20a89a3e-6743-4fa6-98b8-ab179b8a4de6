package remote

import (
	"context"
	"fmt"

	"xiaoshiai.cn/core/ismc/common"
)

// ListZones implements common.Provider.
func (r *RemoteProvider) ListZones(ctx context.Context, option common.ListZoneOptions) (common.List[common.Zone], error) {
	zonelist := common.List[common.Zone]{}
	if err := r.client.Get("/zones").
		Return(&zonelist).
		Queries(option.Values()).Send(ctx); err != nil {
		return zonelist, err
	}
	return zonelist, nil
}

// GetZone implements common.Provider.
func (r *RemoteProvider) GetZone(ctx context.Context, id string) (*common.Zone, error) {
	zone := &common.Zone{}
	if err := r.client.Get(fmt.Sprintf("/zones/%s", id)).Return(zone).Send(ctx); err != nil {
		return nil, err
	}
	return zone, nil
}
