package remote

import (
	"context"

	"xiaoshiai.cn/core/ismc/common"
)

var _ common.VirtualNetworkOperation = (*RemoteProvider)(nil)

// ListVirtualNetworks implements common.Provider.
func (r *RemoteProvider) ListVirtualNetworks(ctx context.Context, options common.ListVirtualNetworkOptions) (common.List[common.VirtualNetwork], error) {
	list := common.List[common.VirtualNetwork]{}
	if err := r.client.Get("/networks").
		Queries(options.Values()).
		Query("resourceGroup", options.ResourceGroup).
		Return(&list).
		Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// CreateVirtualNetwork implements common.Provider.
func (r *RemoteProvider) CreateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) (*common.Descripter, error) {
	descripter := &common.Descripter{}
	if err := r.client.Post("/networks").JSON(network).Return(descripter).Send(ctx); err != nil {
		return nil, err
	}
	return descripter, nil
}

// GetVirtualNetwork implements common.Provider.
func (r *RemoteProvider) GetVirtualNetwork(ctx context.Context, id string) (*common.VirtualNetwork, error) {
	network := &common.VirtualNetwork{}
	if err := r.client.Get("/networks/" + id).Return(network).Send(ctx); err != nil {
		return nil, err
	}
	return network, nil
}

// DeleteVirtualNetwork implements common.Provider.
func (r *RemoteProvider) DeleteVirtualNetwork(ctx context.Context, id string) error {
	return r.client.Delete("/networks/" + id).Send(ctx)
}

// UpdateVirtualNetwork implements common.Provider.
func (r *RemoteProvider) UpdateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) error {
	return r.client.Put("/networks/" + network.ID).JSON(network).Send(ctx)
}

// ListVirtualSubnetwork implements common.Provider.
func (r *RemoteProvider) ListVirtualSubnetwork(ctx context.Context, network string, options common.ListVirtualSubnetworkOptions) (common.List[common.VirtualSubnetwork], error) {
	list := common.List[common.VirtualSubnetwork]{}
	if err := r.client.Get("/networks/"+network+"/subnetworks").
		Queries(options.Values()).
		Query("resourceGroup", options.ResourceGroup).
		Return(&list).
		Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// CreateVirtualSubnetwork implements common.Provider.
func (r *RemoteProvider) CreateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) (*common.Descripter, error) {
	descripter := &common.Descripter{}
	if err := r.client.Post("/networks/" + network + "/subnetworks").JSON(vswitch).Return(descripter).Send(ctx); err != nil {
		return nil, err
	}
	return descripter, nil
}

// UpdateVirtualSubnetwork implements common.Provider.
func (r *RemoteProvider) UpdateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) error {
	return r.client.Put("/networks/" + network + "/subnetworks/" + vswitch.ID).JSON(vswitch).Return(vswitch).Send(ctx)
}

// GetVirtualSubnetwork implements common.Provider.
func (r *RemoteProvider) GetVirtualSubnetwork(ctx context.Context, network string, id string) (*common.VirtualSubnetwork, error) {
	vswitch := &common.VirtualSubnetwork{}
	if err := r.client.Get("/networks/" + network + "/subnetworks/" + id).Return(vswitch).Send(ctx); err != nil {
		return nil, err
	}
	return vswitch, nil
}

// DeleteVirtualSubnetwork implements common.Provider.
func (r *RemoteProvider) DeleteVirtualSubnetwork(ctx context.Context, network string, id string) error {
	return r.client.Delete("/networks/" + network + "/subnetworks/" + id).Send(ctx)
}

// ListVirtualSubnetworkAllocations implements common.Provider.
func (r *RemoteProvider) ListVirtualSubnetworkAllocations(ctx context.Context, network string, id string, options common.ListVirtualSwitchAllocationOptions) (common.List[common.VirtualSubnetworkAllocation], error) {
	list := common.List[common.VirtualSubnetworkAllocation]{}
	if err := r.client.Get("/networks/" + network + "/subnetworks/" + id + "/allocations").
		Queries(options.Values()).
		Return(&list).
		Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}
