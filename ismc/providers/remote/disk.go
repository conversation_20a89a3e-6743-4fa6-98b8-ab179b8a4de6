package remote

import (
	"context"
	"fmt"
	"strconv"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.DiskOperation = (*RemoteProvider)(nil)

// ListDisks implements common.Provider.
func (r *RemoteProvider) ListDisks(ctx context.Context, options common.ListDiskOptions) (common.List[common.Disk], error) {
	list := common.List[common.Disk]{}
	if err := r.client.
		Get("/disks").
		Queries(options.Values()).
		Query(ResourceGroup, options.ResourceGroup).
		Query("name", options.Name).
		Query("virtualMachine", options.VirtualMachine).
		Query("zone", options.Zone).
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// CreateDisk implements common.Provider.
func (r *RemoteProvider) CreateDisk(ctx context.Context, disk *common.Disk, options common.CreateDiskOptions) (*common.Descripter, error) {
	descripter := &common.Descripter{}
	if err := r.client.Post("/disks").JSON(disk).
		Query("wait", strconv.FormatBool(options.Wait)).
		Return(descripter).Send(ctx); err != nil {
		return nil, err
	}
	return descripter, nil
}

// UpdateDisk implements common.Provider.
func (r *RemoteProvider) UpdateDisk(ctx context.Context, disk *common.Disk) error {
	return r.client.Put(fmt.Sprintf("/disks/%s", disk.ID)).JSON(disk).Send(ctx)
}

// GetDisk implements common.Provider.
func (r *RemoteProvider) GetDisk(ctx context.Context, id string) (*common.Disk, error) {
	disk := &common.Disk{}
	if err := r.client.Get(fmt.Sprintf("/disks/%s", id)).
		Return(disk).Send(ctx); err != nil {
		return nil, err
	}
	return disk, nil
}

// DeleteDisk implements common.Provider.
func (r *RemoteProvider) DeleteDisk(ctx context.Context, id string, options common.DeleteDiskOptions) error {
	return r.client.Delete(fmt.Sprintf("/disks/%s", id)).
		Query("wait", strconv.FormatBool(options.Wait)).
		Send(ctx)
}

var _ common.DiskClusterOperation = (*RemoteProvider)(nil)

// ListDiskClusters implements common.Provider.
func (r *RemoteProvider) ListDiskClusters(ctx context.Context, options common.ListDiskClusterOptions) (common.List[common.DiskCluster], error) {
	list := common.List[common.DiskCluster]{}
	if err := r.client.Get("/diskclusters").
		Queries(options.Values()).
		Query("zone", options.Zone).
		Query("storageClass", options.StorageClass).
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// GetDiskCluster implements common.Provider.
func (r *RemoteProvider) GetDiskCluster(ctx context.Context, id string) (*common.DiskCluster, error) {
	cluster := &common.DiskCluster{}
	if err := r.client.Get(fmt.Sprintf("/diskclusters/%s", id)).
		Return(cluster).
		Send(ctx); err != nil {
		return nil, err
	}
	return cluster, nil
}

// CreateDiskCluster implements common.Provider.
func (r *RemoteProvider) CreateDiskCluster(ctx context.Context, cluster *common.DiskCluster) (*common.Descripter, error) {
	desc := &common.Descripter{}
	if err := r.client.
		Post("/diskclusters").
		JSON(cluster).
		Return(desc).
		Send(ctx); err != nil {
		return nil, err
	}
	return desc, nil
}

// DeleteDiskCluster implements common.Provider.
func (r *RemoteProvider) DeleteDiskCluster(ctx context.Context, id string) error {
	return r.client.Delete(fmt.Sprintf("/diskclusters/%s", id)).Send(ctx)
}

// ListDiskClass implements common.Provider.
func (r *RemoteProvider) ListDiskClass(ctx context.Context, options common.ListDiskClassOptions) (common.List[common.DiskClass], error) {
	diskclasses := common.List[common.DiskClass]{}
	if err := r.client.Get("/diskclasses").
		Queries(options.Values()).
		Query("zone", options.Zone).
		Return(&diskclasses).Send(ctx); err != nil {
		return diskclasses, err
	}
	return diskclasses, nil
}

// GetDiskClass implements common.Provider.
func (r *RemoteProvider) GetDiskClass(ctx context.Context, id string) (*common.DiskClass, error) {
	diskclass := &common.DiskClass{}
	if err := r.client.Get(fmt.Sprintf("/diskclasses/%s", id)).
		Return(diskclass).
		Send(ctx); err != nil {
		return nil, err
	}
	return diskclass, nil
}

var _ common.DiskSnapshotOperation = (*RemoteProvider)(nil)

// ListDiskSnapshots implements common.Provider.
func (r *RemoteProvider) ListDiskSnapshots(ctx context.Context, options common.ListDiskSnapshotOption) (common.List[common.DiskSnapshot], error) {
	list := common.List[common.DiskSnapshot]{}
	if err := r.client.Get("/disksnapshots").
		Queries(options.Values()).
		Query("zone", options.Zone).
		Query(ResourceGroup, options.ResourceGroup).
		Query("virtualMachine", options.VirtualMachine).
		Query("disk", options.Disk).
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// CreateDiskSnapshot implements common.Provider.
func (r *RemoteProvider) CreateDiskSnapshot(ctx context.Context, snapshot *common.DiskSnapshot) error {
	return r.client.Post("/disksnapshots").
		JSON(snapshot).Send(ctx)
}

// RemoveDiskSnapshot implements common.Provider.
func (r *RemoteProvider) RemoveDiskSnapshot(ctx context.Context, id string) error {
	return r.client.Delete(fmt.Sprintf("/disksnapshots/%s", id)).Send(ctx)
}

// ResizeDisk implements common.Provider.
func (r *RemoteProvider) ResizeDisk(ctx context.Context, id string, size resource.Quantity, options common.ResizeDiskOptions) error {
	body := map[string]interface{}{
		"size": size.String(),
	}
	return r.client.Post(fmt.Sprintf("/disks/%s:resize", id)).
		JSON(body).
		Query("wait", strconv.FormatBool(options.Wait)).
		Send(ctx)
}

// ReInitalizeDisk implements common.Provider.
func (r *RemoteProvider) ReInitalizeDisk(ctx context.Context, id string) error {
	return r.client.Post(fmt.Sprintf("/disks/%s:reinitialize", id)).Send(ctx)
}
