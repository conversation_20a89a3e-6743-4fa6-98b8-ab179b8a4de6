package remote

import (
	"context"
	"fmt"

	"xiaoshiai.cn/core/ismc/common"
)

// ListSecurityGroups implements common.Provider.
func (r *RemoteProvider) ListSecurityGroups(ctx context.Context, options common.ListSecurityGroupOptions) (common.List[common.SecurityGroup], error) {
	list := common.List[common.SecurityGroup]{}
	if err := r.client.Get("/securitygroups").
		Queries(options.Values()).
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// CreateSecurityGroup implements common.Provider.
func (r *RemoteProvider) CreateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) (*common.Descripter, error) {
	desc := &common.Descripter{}
	if err := r.client.Post("/securitygroups").JSON(sg).Return(desc).Send(ctx); err != nil {
		return nil, err
	}
	return desc, nil
}

// GetSecurityGroup implements common.Provider.
func (r *RemoteProvider) GetSecurityGroup(ctx context.Context, id string) (*common.SecurityGroup, error) {
	sg := &common.SecurityGroup{}
	if err := r.client.Get(fmt.Sprintf("/securitygroups/%s", id)).
		Return(sg).Send(ctx); err != nil {
		return nil, err
	}
	return sg, nil
}

// UpdateSecurityGroup implements common.Provider.
func (r *RemoteProvider) UpdateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) error {
	return r.client.Put(fmt.Sprintf("/securitygroups/%s", sg.ID)).JSON(sg).Send(ctx)
}

// DeleteSecurityGroup implements common.Provider.
func (r *RemoteProvider) DeleteSecurityGroup(ctx context.Context, id string) error {
	return r.client.Delete(fmt.Sprintf("/securitygroups/%s", id)).Send(ctx)
}

// ListSecurityGroupRules implements common.Provider.
func (r *RemoteProvider) ListSecurityGroupRules(ctx context.Context, securityGroup string, options common.ListSecurityGroupRuleOptions) (common.List[common.SecurityGroupRule], error) {
	rules := common.List[common.SecurityGroupRule]{}
	if err := r.client.Get(fmt.Sprintf("/securitygroups/%s/rules", securityGroup)).
		Queries(options.Values()).
		Return(&rules).Send(ctx); err != nil {
		return rules, err
	}
	return rules, nil
}

// AddSecurityGroupRule implements common.Provider.
func (r *RemoteProvider) AddSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return r.client.Post(fmt.Sprintf("/securitygroups/%s/rules", securityGroup)).JSON(rule).Send(ctx)
}

// UpdateSecurityGroupRule implements common.Provider.
func (r *RemoteProvider) UpdateSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return r.client.Put(fmt.Sprintf("/securitygroups/%s/rules/%s", securityGroup, rule.ID)).JSON(rule).Send(ctx)
}

// DeleteSecurityGroupRule implements common.Provider.
func (r *RemoteProvider) DeleteSecurityGroupRule(ctx context.Context, securityGroup string, id string) error {
	return r.client.Delete(fmt.Sprintf("/securitygroups/%s/rules/%s", securityGroup, id)).Send(ctx)
}
