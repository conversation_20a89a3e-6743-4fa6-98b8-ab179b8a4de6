package remote

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/textproto"
	"strconv"

	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/core/ismc/common"
)

// ListImages implements common.Provider.
func (r *RemoteProvider) ListImages(ctx context.Context, options common.ListImageOptions) (common.List[common.Image], error) {
	list := common.List[common.Image]{}
	if err := r.client.Get("/images").
		Queries(options.Values()).
		Query("name", options.Name).
		Query(ResourceGroup, options.ResourceGroup).
		Query("source", string(options.Source)).
		Query("phase", string(options.Phase)).
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

// ImportImage implements common.Provider.
func (r *RemoteProvider) ImportImage(ctx context.Context, image *common.Image, file common.FileContent) (*common.Descripter, error) {
	imagedata, err := json.Marshal(image)
	if err != nil {
		return nil, err
	}
	fileheader := textproto.MIMEHeader{}
	fileheader.Set("Content-Type", file.ContentType)
	if file.ContentLength > 0 {
		fileheader.Set("Content-Length", strconv.FormatInt(file.ContentLength, 10))
	}
	ret := &common.Descripter{}
	if err := r.client.Post("/images").
		MultiFormDataStream(
			[]httpclient.MultiFormPart{
				{
					FieldName: "image",
					Reader:    bytes.NewReader(imagedata),
				},
				{
					FieldName: "file",
					FileName:  file.FileName,
					Reader:    file.Content,
					Header:    fileheader,
				},
			},
		).
		Return(ret).
		Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// UpdateImage implements common.Provider.
func (r *RemoteProvider) UpdateImage(ctx context.Context, image *common.Image) error {
	return r.client.Put(fmt.Sprintf("/images/%s", image.ID)).JSON(image).Send(ctx)
}

// DeleteImage implements common.Provider.
func (r *RemoteProvider) DeleteImage(ctx context.Context, id string) error {
	return r.client.Delete(fmt.Sprintf("/images/%s", id)).Send(ctx)
}

// GetImage implements common.Provider.
func (r *RemoteProvider) GetImage(ctx context.Context, id string) (*common.Image, error) {
	image := &common.Image{}
	if err := r.client.Get(fmt.Sprintf("/images/%s", id)).Return(image).Send(ctx); err != nil {
		return nil, err
	}
	return image, nil
}
