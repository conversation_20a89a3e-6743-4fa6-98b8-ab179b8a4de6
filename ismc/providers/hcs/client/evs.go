package client

import (
	"context"
	"fmt"
	"reflect"
	"unsafe"

	evs "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/evs/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/evs/v2/model"
)

func NewCreateVolumeOptionVolumeType(typ string) model.CreateVolumeOptionVolumeType {
	ret := model.CreateVolumeOptionVolumeType{}
	field := reflect.ValueOf(&ret).Elem().Field(0)
	reflect.NewAt(field.Type(), unsafe.Pointer(field.UnsafeAddr())).Elem().SetString(typ)
	return ret
}

type ListVolumesResponse struct {
	// 查询到的云硬盘总数量，不受分页影响。
	Count *int32 `json:"count,omitempty"`

	// 云硬盘列表查询位置标记。如果本次查询只返回部分列表信息时，会返回查询到的当前磁盘mark标记的url，可以继续使用这个url查询剩余列表信息。
	VolumesLinks *[]model.Link `json:"volumes_links,omitempty"`

	// 查询请求返回的云硬盘列表。
	Volumes        *[]VolumeDetail `json:"volumes,omitempty"`
	HttpStatusCode int             `json:"-"`
}

type VolumeDetail struct {
	model.VolumeDetail `json:",inline"`

	// Shareable 是bool类型，当前sdk使用的是 string 类型，无法反序列化
	Shareable *bool `json:"shareable"`
}

func (c *Client) ListVolumes(request *model.ListVolumesRequest) (*ListVolumesResponse, error) {
	requestDef := evs.GenReqDefForListVolumes()
	requestDef.Response = new(ListVolumesResponse)
	if resp, err := c.EVS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*ListVolumesResponse), nil
	}
}

// ShowVolumeResponse Response Object
type ShowVolumeResponse struct {
	Volume         *VolumeDetail `json:"volume,omitempty"`
	HttpStatusCode int           `json:"-"`
}

// ShowVolume 查询单个云硬盘详情
//
// 查询单个云硬盘的详细信息。支持企业项目授权功能。
//
// Please refer to HUAWEI cloud API Explorer for details.
func (c *Client) ShowVolume(request *model.ShowVolumeRequest) (*ShowVolumeResponse, error) {
	requestDef := evs.GenReqDefForShowVolume()
	requestDef.Response = new(ShowVolumeResponse)

	if resp, err := c.EVS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*ShowVolumeResponse), nil
	}
}

type UpdateVolumeResponse struct {
	Volume VolumeDetail `json:"volume,omitempty"`
}

type UpdateVolumeRequest struct {
	// description 否 String 云硬盘描述。最大支持255个字
	Description string `json:"description,omitempty"`
	// display_description 否 String 同description，description和display_description任意指定一个即可（若两个都指定，则以description为主）。
	// 最大支持255个字节
	DisplayDescription string `json:"display_description,omitempty"`
	// display_name 否 String 同name，name和display_name任意指定一个即可（若两个都指定，则以name为主）最大支持255个字节
	DisplayName string `json:"display_name,omitempty"`
	// Metadata 否 Map<String, String> 云硬盘元数据。
	Metadata map[string]string `json:"metadata,omitempty"`
	// Name 否 String 云硬盘名称。最大支持255个字节
	Name string `json:"name,omitempty"`
}

func (c *Client) UpdateVolume(ctx context.Context, id string, request UpdateVolumeRequest) (*UpdateVolumeResponse, error) {
	ret := &UpdateVolumeResponse{}
	err := c.EVS_NEXT.
		Put(fmt.Sprintf("/v2/{project_id}/volumes/%s", id)).
		JSON(map[string]any{"volume": request}).
		Return(ret).Send(ctx)
	if err != nil {
		return nil, err
	}
	return ret, nil
}
