package client

import (
	"fmt"
	"testing"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ims/v2/model"
	"k8s.io/utils/ptr"
)

func TestClient_ListImages(t *testing.T) {
	cli := setupTestClient(t)
	resp, err := cli.ListImages(&model.ListImagesRequest{
		Imagetype: ptr.To(model.GetListImagesRequestImagetypeEnum().GOLD),
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}
