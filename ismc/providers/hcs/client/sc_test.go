package client

import (
	"context"
	"encoding/json"
	"testing"
)

func TestClient_ListOrders(t *testing.T) {
	cli := setupTestClient(t)

	ctx := context.Background()

	list, err := cli.ListOrders(ctx, ListOrderRequest{})
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log(list)
}

func TestGetHostsystem(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()
	hostsystems, err := cli.GetHostsystemMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}
	for _, host := range hostsystems.Metrics {
		t.Log(host)
	}
}

func TestClient_CreateSubscription(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	params := map[string]any{
		"tenantId":          "7e149859f56047f69ff5663f89b3c09d",
		"availability_zone": "az2.dc2",
		"name":              "sub-vm-9",
		"description":       "",
		"imageRef":          "2aeb2050-e138-4e0b-876d-fb66577a2363",
		"flavorRef":         "19ebd6f0-b310-492c-9865-7d0f3b66cf66",
		"data_volumes":      []any{},
		"vpcid":             "4ca4f1e0-39a7-4729-b041-578e77209fc1",
		"nics": []any{
			map[string]any{
				"subnet_id":        "0afd9ab8-fbf5-4dcf-bbb8-5df9e1023ac6",
				"ip_address":       "",
				"ip_address_v6":    "",
				"nictype":          "",
				"physical_network": "",
				"extra_dhcp_opts":  []any{},
				"binding:profile": map[string]any{
					"disable_security_groups": false,
					"availability_zone":       "",
				},
			},
		},
		"security_groups": []map[string]any{
			{"id": "1b019942-7219-4bcc-ac33-2755aede0ffa"},
		},
		"personality": []any{},
		"count":       1,
		"extendparam": map[string]any{
			"chargingMode":          0,
			"regionID":              "majnoon-dccloud-1",
			"enterprise_project_id": "0",
		},
		"metadata": map[string]any{
			"op_svc_userid":        "ff1ea3a9585446de836f0654e5afe352",
			"__instance_vwatchdog": "false",
			"_ha_policy_type":      "remote_rebuild",
			"productId":            "acd6d1c04a9545409f94075d3437708a",
		},
		"tags":               []any{},
		"resource_subset_id": "0",
		"root_volume": map[string]any{
			"volumetype": "O3B_SAS_Volume",
			"size":       40,
			"extendparam": map[string]any{
				"resourceSpecCode": "",
				"resourceType":     "",
			},
			"encryption_info": map[string]any{},
		},
		"config_drive": false,
		"power_on":     true,
		"extra": map[string]any{
			"devices": []map[string]any{
				{"device_type": "cdrom"},
			},
		},
	}
	secretParams := map[string]any{
		"metadata": map[string]any{
			"admin_pass": "",
		},
		"admin_password": "P@ssw0rd",
	}
	paramsjson, _ := json.Marshal(params)
	secretParamsjson, _ := json.Marshal(secretParams)
	req := SubscriptionRequest{
		OperateType:     "apply",
		ProjectID:       "7e149859f56047f69ff5663f89b3c09d",
		ProductID:       "acd6d1c04a9545409f94075d3437708a",
		RegionID:        "majnoon-dccloud-1",
		TimeZone:        "GMT+08:00",
		Tenancy:         "0",
		ServiceType:     "ecs",
		Params:          string(paramsjson),
		SecretParams:    string(secretParamsjson),
		TimeoutInterval: 6,
	}
	resp, err := cli.CreateSubscription(ctx, SubscriptionsRequest{Subscriptions: []SubscriptionRequest{req}})
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Log(resp)
}
