package client

import (
	"context"
	"strconv"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/def"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/sdktime"
	modelv2 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v2/model"
	vpcv3 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v3"
	modelv3 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v3/model"
)

type ListVpcsRequest struct {
	// EnterpriseProjectID 增加企业项目ID过滤，显示指定企业项目下的资源
	EnterpriseProjectID *string `json:"enterprise_project_id"`
	// 功能说明：每页返回的个数 取值范围：0-2000
	Limit *int32 `json:"limit,omitempty"`

	Offset *int32 `json:"offset,omitempty"`

	// 分页查询起始的资源ID，为空时查询第一页
	Marker *string `json:"marker,omitempty"`

	// VPC资源ID。可以使用该字段过滤VPC
	Id *[]string `json:"id,omitempty"`

	// VPC的name信息，可以使用该字段过滤VPC
	Name *[]string `json:"name,omitempty"`

	// VPC的描述信息。可以使用该字段过滤VPC
	Description *[]string `json:"description,omitempty"`

	// VPC的CIDR。可以使用该字段过滤VPC
	Cidr *[]string `json:"cidr,omitempty"`
}

// ListVpcs 查询虚拟私有云列表
// 相比于原生SDK，增加了企业项目ID过滤
func (c *Client) ListVpcs(request *ListVpcsRequest) (*modelv3.ListVpcsResponse, error) {
	requestDef := vpcv3.GenReqDefForListVpcs()
	requestDef.RequestFields = append(requestDef.RequestFields, &def.FieldDef{
		LocationType: def.Query,
		Name:         "EnterpriseProjectID",
		JsonTag:      "enterprise_project_id",
	})
	requestDef.RequestFields = append(requestDef.RequestFields, &def.FieldDef{
		LocationType: def.Query,
		Name:         "Offset",
		JsonTag:      "offset",
	})
	if resp, err := c.VPCV3.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*modelv3.ListVpcsResponse), nil
	}
}

type ListSecurityGroupsResponse struct {
	SecurityGroups []SecurityGroup `json:"security_groups,omitempty"`
	HttpStatusCode int             `json:"-"`
}

type SecurityGroup struct {
	modelv2.SecurityGroup `json:",inline"`
	// 功能说明：安全组创建时间 取值范围：UTC时间格式：yyyy-MM-ddTHH:mm:ss
	CreatedAt *sdktime.SdkTime `json:"created_at"`
	SysTags   []string         `json:"sys_tags"`
}

type ListSecurityGroupsOptions struct {
	SortKey             *string // sort_key: created_at
	SortDir             *string // sort_dir: desc
	Offset              int     // offset: 0
	Limit               *int32  // limit: 10
	Marker              *string
	Name                string  // 当 regexpress 为 true 时，该字段为正则表达式搜索，否则为精确匹配
	Regexpress          bool    // regexpress: true ,当name为正则表达式时，该字段为true
	EnterpriseProjectId *string // all_granted_eps 表示查询当前用户所有企业项目绑定的安全组
	VpcId               *string
}

// ListSecurityGroups 查询安全组列表
//
//	api 有 v1 v2 v3 版本，v1 版本没有返回创建时间，v3版本没有返回 enterprise_project_id
//	v2 的 list 查询中，不返回 enterprise_project_id 字段, 但是返回 sys_tags 字段, 里面包含了企业项目ID
//	v2 版本尚可以用，但是SDK没有 v2 版本
func (c *Client) ListSecurityGroups(ctx context.Context, request ListSecurityGroupsOptions) (*ListSecurityGroupsResponse, error) {
	ret := &ListSecurityGroupsResponse{}
	req := c.VPC_NEXT.Get("/v2.0/security-groups").Return(ret)
	if request.EnterpriseProjectId != nil {
		req.Query("enterprise_project_id", *request.EnterpriseProjectId)
	}
	if request.Limit != nil {
		req.Query("limit", strconv.Itoa(int(*request.Limit)))
	}
	if request.SortKey != nil {
		req.Query("sort_key", *request.SortKey)
	}
	if request.SortDir != nil {
		req.Query("sort_dir", *request.SortDir)
	}
	if request.Marker != nil {
		req.Query("marker", *request.Marker)
	}
	if request.VpcId != nil {
		req.Query("vpc_id", *request.VpcId)
	}
	if request.Name != "" {
		req.Query("name", request.Name)
	}
	if request.Regexpress {
		req.Query("regexpress", "true")
	}
	if err := req.Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

type CreateSecurityGroupRequest struct {
	Name                string `json:"name"`
	Description         string `json:"description"`
	EnterpriseProjectId string `json:"enterprise_project_id"`
}

type CreateSecurityGroupResponse struct {
	SecurityGroup SecurityGroupNext `json:"security_group"`
}

type SecurityGroupNext struct {
	ID                 string                      `json:"id"`
	Name               string                      `json:"name"`
	ProjectID          string                      `json:"project_id"`
	Description        string                      `json:"description"`
	SecurityGroupRules []modelv2.SecurityGroupRule `json:"security_group_rules"`
}

// SDK没有 v2 版本
// CreateSecurityGroup 创建安全组
func (c *Client) CreateSecurityGroup(ctx context.Context, request CreateSecurityGroupRequest) (*CreateSecurityGroupResponse, error) {
	ret := &CreateSecurityGroupResponse{}
	// POST /vpc/rest/v2/7e149859f56047f69ff5663f89b3c09d/extension/security-groups
	if err := c.VPC_NEXT.Post("/v2.0/security-groups").JSON(request).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

const ISO8601Layout = "2006-01-02T15:04:05"

type ISO8601Time struct {
	time.Time
}

func (t *ISO8601Time) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		t.Time = time.Time{}
		return nil
	}
	tt, err := time.Parse(`"`+ISO8601Layout+`"`, string(data))
	if err != nil {
		return err
	}
	t.Time = tt
	return nil
}

func (t ISO8601Time) MarshalJSON() ([]byte, error) {
	if t.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte(`"` + t.Time.Format(ISO8601Layout) + `"`), nil
}

type VPCPort struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	NetworkID     string                 `json:"network_id"`
	TenantID      string                 `json:"tenant_id"`
	Mac           string                 `json:"mac_address"`
	AdminStateUp  bool                   `json:"admin_state_up"`
	Status        string                 `json:"status"`
	FixedIPs      []modelv2.FixedIp      `json:"fixed_ips"`
	DeviceID      string                 `json:"device_id"`
	DeviceOwner   string                 `json:"device_owner"`
	ExtraDhcpOpts []modelv2.ExtraDhcpOpt `json:"extra_dhcp_opts"`
	Description   string                 `json:"description"`
	InstanceID    string                 `json:"instance_id"`
	InstanceType  string                 `json:"instance_type"`
	ProjectID     string                 `json:"project_id"`
	CreatedAt     ISO8601Time            `json:"created_at"`
	UpdatedAt     ISO8601Time            `json:"updated_at"`
	Tags          []any                  `json:"tags"`

	BindingHostID        string         `json:"binding:host_id"`
	BindingMigrationInfo map[string]any `json:"binding:migration_info"`
	BindingProfile       map[string]any `json:"binding:profile"`
	BindingVifDetails    map[string]any `json:"binding:vif_details"`
	BindingVifType       string         `json:"binding:vif_type"`
	BindingVnicType      string         `json:"binding:vnic_type"`
}

type ListVPCPortsOptions struct {
	// device_owner: network:router_interface
	// device_owner: network:router_interface_distributed
	// device_owner: neutron:SC_INTERNAL_NETWORK
	DeviceOwner []string
	DeviceID    []string
	ID          []string
	TenantID    string
	SortKey     string // sort_key: created_at
	SortDir     string // sort_dir: desc
}

// ListPorts 查询端口列表
// 与原生SDK [NeutronListPorts] 的区别是增加了支持多个 id 和 device_id 的查询
func (c *Client) ListPorts(ctx context.Context, options ListVPCPortsOptions) ([]VPCPort, error) {
	type PortsReponse struct {
		Ports []VPCPort
	}
	resp := PortsReponse{}
	req := c.VPC_NEXT.Get("/v2.0/ports").Return(&resp)
	if options.TenantID != "" {
		req.Query("tenant_id", options.TenantID)
	} else {
		req.Query("tenant_id", c.DefaultProjectID)
	}
	for _, id := range options.ID {
		req.Query("id", id)
	}
	for _, owner := range options.DeviceOwner {
		req.Query("device_owner", owner)
	}
	for _, id := range options.DeviceID {
		req.Query("device_id", id)
	}
	if err := req.Send(ctx); err != nil {
		return nil, err
	}
	return resp.Ports, nil
}
