package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
)

type SysPhysicalHost struct {
	AllocatedCpu                 string `json:"allocatedCpu"`
	AllocatedDisk                string `json:"allocatedDisk"`
	AllocatedDiskSizeMB          int    `json:"allocatedDiskSizeMB"`
	AllocatedMemory              string `json:"allocatedMemory"`
	AllocatedRamSizeMB           int    `json:"allocatedRamSizeMB"`
	AllocatedVcpuCores           int    `json:"allocatedVcpuCores"`
	AllocatedVmemoryMB           int    `json:"allocatedVmemoryMB"`
	AssociateType                string `json:"associateType"`
	AzoneId                      string `json:"azoneId"`
	AzoneName                    string `json:"azoneName"`
	BizRegionId                  string `json:"bizRegionId"`
	BizRegionName                string `json:"bizRegionName"`
	BmcIp                        string `json:"bmcIp"`
	ClassId                      int    `json:"class_Id"`
	ClassName                    string `json:"class_Name"`
	ClusterId                    string `json:"clusterId"`
	ClusterName                  string `json:"clusterName"`
	ConfirmStatus                string `json:"confirmStatus"`
	CpuQuantityForVirtualization int    `json:"cpuQuantityForVirtualization"`
	CpuRatio                     string `json:"cpuRatio"`
	DeviceName                   string `json:"deviceName"`
	ExtraSpecs                   string `json:"extraSpecs"`
	FreeDiskSizeMB               int    `json:"freeDiskSizeMB"`
	FreeVcpuCores                int    `json:"freeVcpuCores"`
	FreeVmemoryMB                int    `json:"freeVmemoryMB"`
	GpuTotal                     int    `json:"gpuTotal"`
	GpuUsed                      int    `json:"gpuUsed"`
	HypervisorEnable             bool   `json:"hypervisorEnable"`
	HypervisorType               string `json:"hypervisorType"`
	Id                           string `json:"id"`
	IpAddress                    string `json:"ipAddress"`
	IsVirtual                    bool   `json:"isVirtual"`
	IsLocal                      bool   `json:"is_Local"`
	KeystoneId                   string `json:"keystoneId"`
	LastModified                 int    `json:"last_Modified"`
	LogicalRegionId              string `json:"logicalRegionId"`
	LogicalRegionName            string `json:"logicalRegionName"`
	Manager                      bool   `json:"manager"`
	Name                         string `json:"name"`
	NativeId                     string `json:"nativeId"`
	NumaTopology                 string `json:"numaTopology"`
	OwnerId                      string `json:"ownerId"`
	OwnerName                    string `json:"ownerName"`
	OwnerType                    string `json:"ownerType"`
	PodId                        string `json:"podId"`
	RamAllocationRatio           string `json:"ramAllocationRatio"`
	RegionId                     string `json:"regionId"`
	RegionName                   string `json:"regionName"`
	RegionType                   string `json:"regionType"`
	ResId                        string `json:"resId"`
	ResourcePoolId               string `json:"resourcePoolId"`
	ResourcePoolName             string `json:"resourcePoolName"`
	ResourcePoolType             string `json:"resourcePoolType"`
	SerialNumber                 string `json:"serialNumber"`
	ServerId                     string `json:"serverId"`
	Service                      string `json:"service"`
	Status                       string `json:"status"`
	TotalCpu                     string `json:"totalCpu"`
	TotalDisk                    string `json:"totalDisk"`
	TotalDiskSizeMB              int    `json:"totalDiskSizeMB"`
	TotalMemory                  string `json:"totalMemory"`
	TotalRamSizeMB               int    `json:"totalRamSizeMB"`
	TotalVcpuCores               int    `json:"totalVcpuCores"`
	TotalVmemoryMB               int    `json:"totalVmemoryMB"`
	TrustLvl                     string `json:"trustLvl"`
	UsedCpu                      string `json:"usedCpu"`
	UsedDisk                     string `json:"usedDisk"`
	UsedMemory                   string `json:"usedMemory"`
}

func (c *Client) ListSystemPhysicalHost(ctx context.Context, options ListResourceModelRequest) (ListResourceModelResponse[SysPhysicalHost], error) {
	list := ListResourceModelResponse[SysPhysicalHost]{}
	err := c.ListSystemResourceModel(ctx, SYS_PhysicalHost, options, &list)
	return list, err
}

type CloudVM struct {
	// CI名称
	Class_Name string `json:"class_Name,omitempty"`
	// ID
	Id string `json:"id,omitempty"`
	// 最后修改时间
	Last_Modified int `json:"last_Modified,omitempty"`
	// 可用分区
	AzoneName string `json:"azoneName,omitempty"`
	// 私有IP
	PrivateIps string `json:"privateIps,omitempty"`
	// 是否为BMS
	IronicFlag string `json:"ironicFlag,omitempty"`
	// 所属区域
	RegionName string `json:"regionName,omitempty"`
	// 资源内部唯一标识
	ResId string `json:"resId,omitempty"`
	// UUID
	Uuid string `json:"uuid,omitempty"`
	// 是否可展示
	VmVisibleId string `json:"vmVisibleId,omitempty"`
	// 规格ID
	FlavorId string `json:"flavorId,omitempty"`
	// vCPU核数
	FlavorVcpu string `json:"flavorVcpu,omitempty"`
	// 内存容量
	FlavorRamSize string `json:"flavorRamSize,omitempty"`
	// 磁盘容量
	FlavorDiskSize string `json:"flavorDiskSize,omitempty"`
	// 来源系统ID
	OwnerId string `json:"ownerId,omitempty"`
	// 部署Region
	RegionId string `json:"regionId,omitempty"`
	// 创建时间
	CreatedAt string `json:"createdAt,omitempty"`
	// 电源状态
	PowerState string `json:"powerState,omitempty"`
	// 任务状态，VM中是否有任务
	TaskState string `json:"taskState,omitempty"`
	// 租户名称
	TenantName string `json:"tenantName,omitempty"`
	// PODID
	PodId string `json:"podId,omitempty"`
	// 区域ID
	BizRegionId string `json:"bizRegionId,omitempty"`
	// 原ID
	BizRegionNativeId string `json:"bizRegionNativeId,omitempty"`
	// 区域ID
	LogicalRegionId string `json:"logicalRegionId,omitempty"`
	// 所属区域
	LogicalRegionName string `json:"logicalRegionName,omitempty"`
	// 镜像ID
	ImageId string `json:"imageId,omitempty"`
	// 操作系统
	OsType string `json:"osType,omitempty"`
	// 操作系统类型
	OsVersion string `json:"osVersion,omitempty"`
	// 浮动IP
	FloatingIp string `json:"floatingIp,omitempty"`
	// 宿主机ID
	HostId string `json:"hostId,omitempty"`
	// 虚拟化类型
	HypervisorType string `json:"hypervisorType,omitempty"`
	// 用户ID
	UserId string `json:"userId,omitempty"`
	// 标签
	Tags string `json:"tags,omitempty"`
	// 可用分区ID
	AzoneId string `json:"azoneId,omitempty"`
	// 宿主机ID
	PhysicalHostId string `json:"physicalHostId,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 租户ID
	TenantId string `json:"tenantId,omitempty"`
	// 原ID
	NativeId string `json:"nativeId,omitempty"`
	// 启动时间
	LaunchedAt string `json:"launchedAt,omitempty"`
	// 项目ID
	ProjectId string `json:"projectId,omitempty"`
	// 项目名称
	ProjectName string `json:"projectName,omitempty"`
	// 虚拟机状态
	VmState string `json:"vmState,omitempty"`
	// 区域名称
	BizRegionName string `json:"bizRegionName,omitempty"`
	// 状态
	// DEACTIVATED=未激活, RECYCLING=循环中, ACTIVE=正常, SAVING=存储中, DELETED=已删除, OTHER=其它, UPLOADING=正在上传, PENDING_DELETE=等待删除, QUEUED=队列中, STOPPED=已停止, IMPORTING=正在导入, KILLED=已结束, CREATING=创建中
	Status string `json:"status,omitempty"`
	// VDC ID
	VdcId string `json:"vdcId,omitempty"`
	// VDC名称
	VdcName string `json:"vdcName,omitempty"`
	// CNAID
	CnaId string `json:"cnaId,omitempty"`
	// 集群ID
	ClusterId string `json:"clusterId,omitempty"`
	// 集群名称
	ClusterName string `json:"clusterName,omitempty"`
	// 所属级联层
	CascadeType int `json:"cascadeType,omitempty"`
	// 云区域名称
	CloudLocationName string `json:"cloudLocationName,omitempty"`
	// 云区域类型
	CloudType string `json:"cloudType,omitempty"`
	// 云区域ID
	CloudLocationId string `json:"cloudLocationId,omitempty"`
	// 系统类型
	OwnerType string `json:"ownerType,omitempty"`
	// 资源池Id
	ResourcePoolId string `json:"resourcePoolId,omitempty"`
	// 资源池名称
	ResourcePoolName string `json:"resourcePoolName,omitempty"`
	// 资源池类型
	ResourcePoolType string `json:"resourcePoolType,omitempty"`
	// 原ID
	ResourcePoolNativeId string `json:"resourcePoolNativeId,omitempty"`
	// 额外规格
	ExtraSpecs string `json:"extraSpecs,omitempty"`
	// CPU核数
	CpuCoreNum string `json:"cpuCoreNum,omitempty"`
	// 是否为管理磁盘
	Management bool `json:"management,omitempty"`
	// IP地址
	IpAddress string `json:"ipAddress,omitempty"`
	// 是否本地
	Is_Local bool `json:"is_Local,omitempty"`
	// 规格类型
	FlavorPerformanceType string `json:"flavorPerformanceType,omitempty"`
	// ID
	UnHexId string `json:"unHexId,omitempty"`
	// 租户类型
	// other=其他, manager=管理租户, smallBearer=承载租户, business=业务租户, resource=资源租户, largeBearer=承载租户
	TenantType string `json:"tenantType,omitempty"`

	// EnterpriseProjectId not implemented
	EnterpriseProjectId string `json:"enterpriseProjectId,omitempty"`
}

func (c *Client) ListCloudVM(ctx context.Context, options ListResourceModelRequest) (ListResourceModelResponse[CloudVM], error) {
	list := ListResourceModelResponse[CloudVM]{}
	if options.PageSize == 0 {
		allvmlist, err := ListTenantResourceModelAll[CloudVM](ctx, c, CLOUD_VM, options)
		if err != nil {
			return list, err
		}
		list.ObjList = allvmlist
		list.TotalNum = len(allvmlist)
		return list, nil
	} else {
		err := c.ListTenantResourceModel(ctx, CLOUD_VM, options, &list)
		return list, err
	}
}

type CloudVolume struct {
	// id
	ID string `json:"id,omitempty"`
	// 最后修改时间
	LastModified int `json:"last_Modified,omitempty"`
	// CI名称
	Class_Name string `json:"class_Name,omitempty"`
	// 是否本地
	Is_Local bool `json:"is_Local,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 原ID
	NativeId string `json:"nativeId,omitempty"`
	// 资源内部唯一标识
	ResId string `json:"resId,omitempty"`
	// 来源系统ID
	OwnerId string `json:"ownerId,omitempty"`
	// 系统类型
	OwnerType string `json:"ownerType,omitempty"`
	// 子系统类型
	SubOwnerType string `json:"subOwnerType,omitempty"`
	// 来源系统
	OwnerName string `json:"ownerName,omitempty"`
	// 部署Region
	RegionId string `json:"regionId,omitempty"`
	// 所属区域
	RegionName string `json:"regionName,omitempty"`
	// 资源池Id
	ResourcePoolId string `json:"resourcePoolId,omitempty"`
	// 资源池名称
	ResourcePoolName string `json:"resourcePoolName,omitempty"`
	// 资源池类型
	ResourcePoolType string `json:"resourcePoolType,omitempty"`
	// 区域ID
	LogicalRegionId string `json:"logicalRegionId,omitempty"`
	// 所属区域
	LogicalRegionName string `json:"logicalRegionName,omitempty"`
	// 区域ID
	BizRegionId string `json:"bizRegionId,omitempty"`
	// 区域名称
	BizRegionName string `json:"bizRegionName,omitempty"`
	// 项目ID
	ProjectId string `json:"projectId,omitempty"`
	// 租户ID
	TenantId string `json:"tenantId,omitempty"`
	// 租户名称
	TenantName string `json:"tenantName,omitempty"`
	// VDC ID
	VdcId string `json:"vdcId,omitempty"`
	// VDC名称
	VdcName string `json:"vdcName,omitempty"`
	// 租户类型 other=其他, manager=管理租户, smallBearer=承载租户, business=业务租户, resource=资源租户, largeBearer=承载租户
	TenantType string `json:"tenantType,omitempty"`
	// 业务类型
	BusinessType string `json:"businessType,omitempty"`
	// 数据平面
	DataPlane string `json:"dataPlane,omitempty"`
	// UUID
	Uuid string `json:"uuid,omitempty"`
	// 状态 DEACTIVATED=未激活, RECYCLING=循环中, ACTIVE=正常, SAVING=存储中, DELETED=已删除, OTHER=其它, UPLOADING=正在上传, PENDING_DELETE=等待删除, QUEUED=队列中, STOPPED=已停止, IMPORTING=正在导入, KILLED=已结束, CREATING=创建中
	Status string `json:"status,omitempty"`
	// 源盘ID
	SourceVolId string `json:"sourceVolId,omitempty"`
	// 快照ID
	SnapshotId string `json:"snapshotId,omitempty"`
	// 描述
	Description string `json:"description,omitempty"`
	// 创建时间
	CreatedAt string `json:"createdAt,omitempty"`
	// 容量
	Size string `json:"size,omitempty"`
	// 是否加密
	Encrypted string `json:"encrypted,omitempty"`
	// 用户ID
	UserId string `json:"userId,omitempty"`
	// 是否启动盘
	Bootable string `json:"bootable,omitempty"`
	// 磁盘类型ID
	VolumeTypeId string `json:"volumeTypeId,omitempty"`
	// 镜像ID
	ImageId string `json:"imageId,omitempty"`
	// 可用分区
	AzoneName string `json:"azoneName,omitempty"`
	// 可用分区ID
	AzoneId string `json:"azoneId,omitempty"`
	// 云区域名称
	CloudLocationName string `json:"cloudLocationName,omitempty"`
	// 云区域类型
	CloudType string `json:"cloudType,omitempty"`
	// 云区域ID
	CloudLocationId string `json:"cloudLocationId,omitempty"`
	// 额外规格
	ExtraSpecs string `json:"extraSpecs,omitempty"`
	// 企业项目ID
	EnterpriseProjectId string `json:"enterpriseProjectId,omitempty"`
	// volProvisionSize
	VolProvisionSize string `json:"volProvisionSize,omitempty"`
	// 已使用空间
	UserUsedSize string `json:"userUsedSize,omitempty"`
	// 是否共享
	ShareType string `json:"shareType,omitempty"`
	// URN
	Urn string `json:"urn,omitempty"`
	// 付费模式
	PayModel string `json:"payModel,omitempty"`
	// 描述
	Remark string `json:"remark,omitempty"`
	// 合规状态 unconfirmed=不合规, confirmed=合规
	ConfirmStatus string `json:"confirmStatus,omitempty"`
	// 原始状态
	OriginalState string `json:"originalState,omitempty"`
	// keystoneId
	KeystoneId string `json:"keystoneId,omitempty"`
	// 后端LUN WWN
	LunWwn string `json:"lunWwn,omitempty"`
	// 是否为管理磁盘
	Management bool `json:"management,omitempty"`
	// 数据存储ID
	DataStoreId string `json:"dataStoreId,omitempty"`
	// 标签
	Tags string `json:"tags,omitempty"`
	// 存储池Id
	AttachBackendId string `json:"attachBackendId,omitempty"`
	// 存储池名称
	AttachBackendName string `json:"attachBackendName,omitempty"`
	// 项目名称
	ProjectName string `json:"projectName,omitempty"`
}

func (c *Client) ListCloudVolume(ctx context.Context, options ListResourceModelRequest) (ListResourceModelResponse[CloudVolume], error) {
	list := ListResourceModelResponse[CloudVolume]{}
	if options.PageSize == 0 {
		allvmlist, err := ListTenantResourceModelAll[CloudVolume](ctx, c, CLOUD_VOLUME, options)
		if err != nil {
			return list, err
		}
		list.ObjList = allvmlist
		list.TotalNum = len(allvmlist)
		return list, nil
	} else {
		err := c.ListTenantResourceModel(ctx, CLOUD_VOLUME, options, &list)
		return list, err
	}
}

type ListResourceModelRequest struct {
	// pageNo ⻚码，默认值：1，取值范围：1~65535。⾮必选。
	PageNo int `json:"pageNo"`
	// pageSize ⻚⼤⼩，默认值20。取值范围：1~1000。⾮必选。
	PageSize int `json:"pageSize"`
	// contentSelector 要返回的字段，默认返回所有字段，格式：["attr1","attr2"]。⾮必选。
	ContentSelector []string `json:"contentSelector"`
	// orderBy 排序⽅式，格式：[{"field":"attr1","asc":true}]。⾮必选。
	OrderBy []ListResourceModelRequestOrderByItem `json:"orderBy"`
	// condition
	Condition []ListResourceModelRequestConstraint `json:"condition"`
}

func (l ListResourceModelRequest) ToValues() (url.Values, error) {
	queries := url.Values{}

	if l.PageNo != 0 {
		queries.Add("pageNo", fmt.Sprintf("%d", l.PageNo))
	}
	if l.PageSize != 0 {
		queries.Add("pageSize", fmt.Sprintf("%d", l.PageSize))
	}
	if len(l.ContentSelector) != 0 {
		contentjson, err := json.Marshal(l.ContentSelector)
		if err != nil {
			return nil, err
		}
		queries.Add("contentSelector", string(contentjson))
	}
	if l.OrderBy != nil {
		orderjson, err := json.Marshal(l.OrderBy)
		if err != nil {
			return nil, err
		}
		queries.Add("orderBy", string(orderjson))
	}
	if len(l.Condition) != 0 {
		conditionjson, err := json.Marshal(ListResourceModelRequestCondition{Constraint: l.Condition})
		if err != nil {
			return nil, err
		}
		queries.Add("condition", string(conditionjson))
	}
	return queries, nil
}

type ListResourceModelRequestOrderByItem struct {
	Field string `json:"field"`
	Asc   bool   `json:"asc"`
}

// 查询租户资源关系模型 和 查询系统资源关系模型	不需要加最外层的{"constraint":xxxx}。
// 如：[{"simple":{"name":"source_Instance_Id","value":"C726DF8E2E8446FDA17D6D3B7C495DFF","operator":"equal"}}]
type ListResourceModelRequestCondition struct {
	Constraint []ListResourceModelRequestConstraint `json:"constraint"`
}

type ListResourceModelRequestConstraint struct {
	// 条件说明：{"simple": {"name":"incharger","value":"ManageOne","operator":"equal"}}表示incharger="ManageOne"
	// {"logOp":"and","simple":{"caseSensitive":false,"name":"name","value":"B080-Cabinet01","operator":"equal"}}
	// 表示name="B080-Cabinet01",加上"caseSensitive":false则表示属性值"B080-Cabinet01"不区分⼤⼩写，
	// "logOp":"and"表示这个查询条件与上⼀个条件是and关系。
	Simple ListResourceModelRequestSimple `json:"simple"`
	// logOp(条件连接符,可取的值有"and","or")
	LogOp string `json:"logOp"`
}

type ListResourceModelRequestSimple struct {
	// name(资源的属性名称)，
	Name string `json:"name"`
	// value(属性值)
	Value string `json:"value"`
	// operator(运算符，可取的值有"equal","not equal","contain","not contain","in","not in","is null","not null"等)
	Operator      string `json:"operator"`
	CaseSensitive bool   `json:"caseSensitive"`
}

func ListResourceModelRequestConstraintEqual(name, value string) ListResourceModelRequestConstraint {
	return ListResourceModelRequestConstraint{
		Simple: ListResourceModelRequestSimple{Name: name, Value: value, Operator: "equal"},
	}
}

// ListResourceModelRequestConstraintsIn
// 另一种 in 查询，直接使用 in 提示不支持
func ListResourceModelRequestConstraintsIn(name string, vals []string) []ListResourceModelRequestConstraint {
	var constraints []ListResourceModelRequestConstraint
	for _, val := range vals {
		constraints = append(constraints, ListResourceModelRequestConstraint{
			Simple: ListResourceModelRequestSimple{Name: name, Operator: "equal", Value: val},
			LogOp:  "or",
		})
	}
	return constraints
}

type ResourceModelName string

const (
	// 弹性云服务器(ECS)
	CLOUD_VM ResourceModelName = "CLOUD_VM"

	// 裸金属服务器(BMS)
	CLOUD_BMS ResourceModelName = "CLOUD_BMS"
	// 裸金属服务器节点
	CLOUD_BMS_NODE ResourceModelName = "CLOUD_BMS_NODE"

	// 弹性IP(EIP)
	CLOUD_EIP ResourceModelName = "CLOUD_EIP"
	// 带宽
	CLOUD_BANDWIDTHS ResourceModelName = "CLOUD_BANDWIDTHS"

	// 弹性云硬盘(EVS)
	CLOUD_VOLUME ResourceModelName = "CLOUD_VOLUME"
	// 快照
	CLOUD_SNAPSHOT ResourceModelName = "CLOUD_SNAPSHOT"
	// 备份
	CLOUD_BACKUP ResourceModelName = "CLOUD_BACKUP"

	// 镜像管理服务(IMS)
	CLOUD_IMAGE ResourceModelName = "CLOUD_IMAGE"

	// 虚拟私有云(VPC)
	// 路由/VPC
	CLOUD_ROUTER ResourceModelName = "CLOUD_ROUTER"
	// 网络
	CLOUD_NETWORK ResourceModelName = "CLOUD_NETWORK"
	// 子网
	CLOUD_SUBNET ResourceModelName = "CLOUD_SUBNET"
	// 端口/网卡
	CLOUD_PORT ResourceModelName = "CLOUD_PORT"

	// 安全组(SG)
	// 安全组
	CLOUD_SECURITY_GROUP ResourceModelName = "CLOUD_SECURITY_GROUP"
	// 安全组规则
	CLOUD_SECURITY_GROUP_RULE ResourceModelName = "CLOUD_SECURITY_GROUP_RULE"

	// 弹性负载均衡(ELB)
	// 弹性负载均衡
	CLOUD_ELB ResourceModelName = "CLOUD_ELB"
	// 监听器
	CLOUD_LISTENER ResourceModelName = "CLOUD_LISTENER"
	// 虚拟防火墙

	// (网络ACL、vFW)
	// 虚拟防火墙
	CLOUD_VFW ResourceModelName = "CLOUD_VFW"
	// 虚拟防火墙策略
	CLOUD_VFW_POLICY ResourceModelName = "CLOUD_VFW_POLICY"
	// 虚拟防火墙规则
	CLOUD_VFW_RULE ResourceModelName = "CLOUD_VFW_RULE"

	// 虚拟专有网络（VPN）
	// 虚拟专有网络
	CLOUD_IPSEC_CONNECTION ResourceModelName = "CLOUD_IPSEC_CONNECTION"

	// NAT网关(NAT)
	// NAT网关
	CLOUD_NATGATEWAY ResourceModelName = "CLOUD_NATGATEWAY"

	// 弹性文件服务(SFS)
	// 弹性文件服务
	CLOUD_SFS_SHARE ResourceModelName = "CLOUD_SFS_SHARE"

	// 租户信息
	// 项目/资源集	GET https://{OC北向接口域名}/rest/tenant-resource/v1/instances/TENANT_PROJECT_VDC
	// 租户/VDC	GET https://{OC北向接口域名}/rest/tenant-resource/v1/instances/TENANT_VDC
	// 租户用户	GET https://{OC北向接口域名}/rest/tenant-resource/v1/instances/TENANT_USER
	TENANT_PROJECT_VDC ResourceModelName = "TENANT_PROJECT_VDC" // 项目/资源集
	TENANT_VDC         ResourceModelName = "TENANT_VDC"         // 租户/VDC
	TENANT_USER        ResourceModelName = "TENANT_USER"        // 租户用户

	// 系统资源
	// 服务器	GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_X86Server
	// 主机组	GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_Cluster
	// 宿主机	GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_PhysicalHost
	// 存储设备	GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_StorDevice
	// 网络设备	GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_NetworkDevice（根据class_Name区分：SYS_Switch-交换机、SYS_Router-路由器、SYS_Firewall-防火墙、SYS_LoadBalancer-负载均衡、SYS_FCSwitch-FC交换机、SYS_ThirdpartyNetWorkDevice-其他网络设备）
	// 管理面虚拟机	GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_VM
	// 区域 GET https://{OC北向接口域名}/rest/cmdb/v1/instances/SYS_BusinessRegion
	SYS_X86Server      ResourceModelName = "SYS_X86Server"      // 服务器
	SYS_Cluster        ResourceModelName = "SYS_Cluster"        // 主机组
	SYS_PhysicalHost   ResourceModelName = "SYS_PhysicalHost"   // 宿主机
	SYS_StorDevice     ResourceModelName = "SYS_StorDevice"     // 存储设备
	SYS_NetworkDevice  ResourceModelName = "SYS_NetworkDevice"  // 网络设备
	SYS_VM             ResourceModelName = "SYS_VM"             // 管理面虚拟机
	SYS_BusinessRegion ResourceModelName = "SYS_BusinessRegion" // 区域
	SYS_StoragePool    ResourceModelName = "SYS_StoragePool"    // 存储池
)

type ListResourceModelResponse[T any] struct {
	TotalNum      int `json:"totalNum"`
	PageSize      int `json:"pageSize"`
	TotalPageNo   int `json:"totalPageNo"`
	CurrentPageNo int `json:"currentPageNo"`
	ObjList       []T `json:"objList"`
}

func ListTenantResourceModelAll[T any](ctx context.Context, cli *Client, model ResourceModelName, options ListResourceModelRequest) ([]T, error) {
	var ret []T
	page := 1
	options.PageSize = 1000
	for {
		options.PageNo = page
		thislist := ListResourceModelResponse[T]{}
		if err := cli.ListTenantResourceModel(ctx, model, options, &thislist); err != nil {
			return nil, err
		}
		ret = append(ret, thislist.ObjList...)
		if thislist.CurrentPageNo >= thislist.TotalPageNo ||
			len(thislist.ObjList) == 0 ||
			len(thislist.ObjList) < thislist.PageSize {
			break
		}
	}
	return ret, nil
}

// 查询租户资源模型
func (c *Client) ListTenantResourceModel(ctx context.Context, model ResourceModelName, options ListResourceModelRequest, into any) error {
	queries, err := options.ToValues()
	if err != nil {
		return err
	}
	return c.OC_NEXT.Get(fmt.Sprintf("/rest/tenant-resource/v1/instances/%s", model)).Queries(queries).Return(into).Send(ctx)
}

func (c *Client) GetTenantResourceModel(ctx context.Context, model ResourceModelName, id string, into any) error {
	return c.OC_NEXT.Get(fmt.Sprintf("/rest/tenant-resource/v1/instances/%s/%s", model, id)).Return(into).Send(ctx)
}

// 17.74 查询资源实例数量信息
func (c *Client) CountTenantResource(ctx context.Context, dimensionName, dimensionId string) (int, error) {
	ret := struct {
		TotalNum int `json:"totalNum"`
	}{}
	err := c.OC_NEXT.Get(fmt.Sprintf("/rest/tenant-resource/v1/instances/count/%s/%s", dimensionName, dimensionId)).
		Return(&ret).Send(ctx)
	return ret.TotalNum, err
}

// 查询系统资源模型
// 17.71 查询指定CI实例数据
func (c *Client) ListSystemResourceModel(ctx context.Context, model ResourceModelName, options ListResourceModelRequest, into any) error {
	queries, err := options.ToValues()
	if err != nil {
		return err
	}
	return c.OC_NEXT.Get(fmt.Sprintf("/rest/cmdb/v1/instances/%s", model)).Queries(queries).Return(into).Send(ctx)
}

// 查询系统资源关系模型
// 17.75 查询指定CI关系的实例数据
func (c *Client) ListInstanceRelations(ctx context.Context, relationName string, options ListResourceModelRequest, into any) error {
	queries, err := options.ToValues()
	if err != nil {
		return err
	}
	return c.OC_NEXT.Get(fmt.Sprintf("/rest/cmdb/v1/relations/%s", relationName)).Queries(queries).Return(into).Send(ctx)
}

// 租户资源关系模型
func (c *Client) ListTenantInstanceRelations(ctx context.Context, relationName string, options ListResourceModelRequest, into any) error {
	queries, err := options.ToValues()
	if err != nil {
		return err
	}
	return c.OC_NEXT.Get(fmt.Sprintf("/rest/tenant-resource/v1/instances/relations/%s", relationName)).Queries(queries).Return(into).Send(ctx)
}

type StatisticsTenantResourceRequest struct {
	Condition []ListResourceModelRequestConstraint `json:"condition"`
}

func (s StatisticsTenantResourceRequest) ToValues() (url.Values, error) {
	queries := url.Values{}
	if len(s.Condition) != 0 {
		conditionjson, err := json.Marshal(s.Condition)
		if err != nil {
			return nil, err
		}
		queries.Add("condition", string(conditionjson))
	}
	return queries, nil
}

type StatisticsTenantResource struct {
	Count int `json:"count"`
}

// 17.72 查询指定CI类型的实例数量
func (c *Client) StatisticsTenantResource(ctx context.Context, model ResourceModelName, options StatisticsTenantResourceRequest) (*StatisticsTenantResource, error) {
	queries, err := options.ToValues()
	if err != nil {
		return nil, err
	}
	ret := &StatisticsTenantResource{}
	err = c.OC_NEXT.Get(fmt.Sprintf("/rest/tenant-resource/v1/instances/%s/statistics", model)).
		Queries(queries).
		Return(ret).Send(ctx)
	return ret, err
}

type SysStoragePoolResponse[T any] struct {
	PageNo    int `json:"pageNo"`
	PageSize  int `json:"pageSize"`
	TotalSize int `json:"totalSize"`
	Datas     []T `json:"datas"`
}
type SysStoragePool struct {
	FreeCapacity       float64 `json:"freeCapacity"`
	Name               string  `json:"name"`
	RegionId           string  `json:"regionId"`
	RegionName         string  `json:"regionName"`
	TotalCapacity      float64 `json:"totalCapacity"`
	UsedCapacity       float64 `json:"usedCapacity"`
	VAllocatedCapacity float64 `json:"vAllocatedCapacity"`
	VTotalCapacity     float64 `json:"vTotalCapacity"`
	VfreeCapacity      float64 `json:"vfreeCapacity"`
}

type StoragePoolBody struct {
	Filters    Filters      `json:"filters"`
	Dimensions []Dimensions `json:"dimensions"`
	Metrics    []Metrics    `json:"metrics"`
}
type (
	Filters    struct{}
	Dimensions struct {
		Field string `json:"field"`
		Index int    `json:"index"`
	}
)
type Metrics struct {
	AggType string `json:"aggType"`
	Field   string `json:"field"`
}

// POST https://{OC北向接口域名}/rest/analysis/v1/datasets/stat-cloud-storage-pool?pageNo=1&pageSize=1000
func (c *Client) SysStoragePool(ctx context.Context, options ListResourceModelRequest) (SysStoragePoolResponse[SysStoragePool], error) {
	list := SysStoragePoolResponse[SysStoragePool]{}
	queries, err := options.ToValues()
	if err != nil {
		return list, err
	}
	body := StoragePoolBody{
		Filters: Filters{},
		Dimensions: []Dimensions{
			{
				Field: "dimensions.logicLoc.regionId",
				Index: 1,
			},
			{
				Field: "dimensions.logicLoc.regionName",
				Index: 2,
			},
			{
				Field: "dimensions.object.name",
				Index: 3,
			},
		},
		Metrics: []Metrics{
			{
				AggType: "sum",
				Field:   "metrics.totalCapacity",
			},
			{
				AggType: "sum",
				Field:   "metrics.usedCapacity",
			},
			{
				AggType: "sum",
				Field:   "metrics.freeCapacity",
			},
			{
				AggType: "sum",
				Field:   "metrics.vTotalCapacity",
			},
			{
				AggType: "sum",
				Field:   "metrics.vAllocatedCapacity",
			},
			{
				AggType: "sum",
				Field:   "metrics.vfreeCapacity",
			},
		},
	}
	reqBody, err := json.Marshal(body)
	if err != nil {
		return list, err
	}
	if err := c.OC_NEXT.Post("/rest/analysis/v1/datasets/stat-cloud-storage-pool").
		Queries(queries).
		Body(bytes.NewReader(reqBody), "application/json").
		Return(&list).Send(ctx); err != nil {
		return list, err
	}
	return list, nil
}

type ObjectTypeId string

const (
	// 数据来自 《ManageOne 8.3.0 性能指标统一模型 01.xlsx》
	ObjectTypeId_CLOUD_VM           ObjectTypeId = "562958543355904"
	ObjectTypeId_CLOUD_VOLUME       ObjectTypeId = "562967133290496"
	ObjectTypeId_SYS_PhysicalServer ObjectTypeId = "844429225099264"
)

type IndicatorId string

type QueryHistoryDataRange string

const (
	QueryHistoryDataRange_BEGIN_END_TIME QueryHistoryDataRange = "BEGIN_END_TIME"
	// LAST_5_MINUTE (最近5分钟，返回数据粒度为分钟）
	QueryHistoryDataRange_LAST_5_MINUTE QueryHistoryDataRange = "LAST_5_MINUTE"
	QueryHistoryDataRange_LAST_1_HOUR   QueryHistoryDataRange = "LAST_1_HOUR"
)

type QueryHistoryDataInterval string

const (
	QueryHistoryDataInterval_MINUTE QueryHistoryDataInterval = "MINUTE"
	QueryHistoryDataInterval_HOUR   QueryHistoryDataInterval = "HOUR"
	QueryHistoryDataInterval_DAY    QueryHistoryDataInterval = "DAY"
	QueryHistoryDataInterval_WEEK   QueryHistoryDataInterval = "WEEK"
	QueryHistoryDataInterval_MONTH  QueryHistoryDataInterval = "MONTH"
)

type QueryHistoryDataRequest struct {
	// 其中，obj_type_id：562958543355904表示弹性云服务器(CLOUD_VM)；
	ObjTypeId ObjectTypeId `json:"obj_type_id"`
	// IndicatorIds 指标id，可传多个值，562958543421441表示CPU使⽤率，562958543486979表示内存使⽤率；
	IndicatorIds []IndicatorId `json:"indicator_ids"`
	// ObjIds 监控对象标识列表，对应资源实例ID。
	// 取接口“/rest/tenant-resource/v1/instances/{class-name}”或“/rest/cmdb/v1/instances/{class-name}”响应体的resId。
	ObjIds []string `json:"obj_ids"`
	//  interval：MINUTE 表示返回数据的时间粒度为“分钟”；
	Interval QueryHistoryDataInterval `json:"interval"`
	// range：BEGIN_END_TIME 表示⾃定义性能数据的时间范围；
	Range QueryHistoryDataRange `json:"range"`
	// begin_time和end_time表示查询的起⽌时间。详细说明请参考“接⼝介绍”⼩节。
	// unix millisecond timestamp
	// eg. 1649397636000
	BeginTime int64 `json:"begin_time"`
	// end_time表示查询的结束时间。
	EndTime int64 `json:"end_time"`
}

// 资源的resId -> 指标的indicatorId -> 指标数据
type HistoryData map[string]map[IndicatorId]HistoryDataItem

type HistoryDataItem struct {
	// 性能指标键值对序列<时间戳,指标值>，按时间升序排列。
	// [{"1649397600000": "3.5819677352905273"},{"1649397900000": "3.5417004823684692"}]
	Series []map[string]string `json:"series"`
	// {"1649397834000": "3.5011096000671387"}
	Min map[string]string `json:"min"`
	// {"1649397894000": "3.756303548812866"}
	Max map[string]string `json:"max"`
	// {"0": "3.5640711784362793"}
	Avg map[string]string `json:"avg"`
}

type HistoryDataResponse[T any] struct {
	StatusCode IntegerMayString `json:"status_code"`
	ErrorCode  IntegerMayString `json:"error_code"`
	ErrorMsg   string           `json:"error_msg"`
	Data       T                `json:"data"`
}

// QueryHistoryDataAll 查询历史性能数据但是会自动分批查询
func (c *Client) QueryHistoryDataAll(ctx context.Context, options QueryHistoryDataRequest) (HistoryData, error) {
	// "Too many tasks for query , objIds x indicators should be no more than 100."
	ret := HistoryData{}
	indicatorscnt := len(options.IndicatorIds)
	if indicatorscnt == 0 {
		return nil, fmt.Errorf("no indicators to query")
	}
	if indicatorscnt > 100 {
		return nil, fmt.Errorf("indicators should be no more than 100")
	}
	perqueryidcnt := 100 / indicatorscnt
	ids := options.ObjIds
	for i := 0; i < len(ids); i += perqueryidcnt {
		end := i + perqueryidcnt
		if end > len(ids) {
			end = len(ids)
		}
		options.ObjIds = ids[i:end]
		data, err := c.QueryHistoryData(ctx, options)
		if err != nil {
			return nil, err
		}
		for k, v := range data {
			ret[k] = v
		}
	}
	return ret, nil
}

// QueryHistoryData 查询历史性能数据
// 历史性能数据上报频率：5 min。(以OC资源性能界面显示为准)
// 限制：每次查询最多支持 objIds x indicators <= 100
func (c *Client) QueryHistoryData(ctx context.Context, options QueryHistoryDataRequest) (HistoryData, error) {
	var resp HistoryDataResponse[HistoryData]
	err := c.OC_NEXT.Post("/rest/performance/v1/data-svc/history-data/action/query").JSON(options).Return(&resp).Send(ctx)
	if err != nil {
		return nil, err
	}
	switch resp.ErrorCode {
	// -60 "ES: Query DB return empty"
	case -60:
		return resp.Data, nil
	case 0:
	default:
		return nil, fmt.Errorf("erro_code: %d, message: %s", resp.ErrorCode, resp.ErrorMsg)
	}
	return resp.Data, err
}

type StatCloudResourceRequest struct {
	// 如果需要查询指定VDC的配额，可增加如下过滤条件。（注：接⼝吞吐量限制30次/min，请尽可能减少查询次数）
	// "filters": { "dimensions": [ { "field": "dimensions.vdc.name", "values": [ "hzp_vdc" ] } ] },
	Filters StatCloudResourceFilter `json:"filters"`
	// Dimensions 维度
	// 	维度可按需添加“dimensions.logicLoc.regionName”（区域）、”dimensions.logicLoc.resourcePoolName（“ 资源池）、”
	// dimensions.logicLoc.azoneName（“ 可⽤分区），上述维度有下钻关系，添加维度时需要同时添加上级维度，例如：若维度中包含"可⽤分
	// 区"，"区域"、"资源池"也需要同添加到到维度中。
	Dimensions []StatCloudResourceDimension `json:"dimensions"`
	Metrics    []StatCloudResourceMetric    `json:"metrics"`
}

type StatCloudResourceFilter struct {
	Dimensions []StatCloudResourceDimension `json:"dimensions"`
}

type StatCloudResourceDimensionField string

const (
	// "dimensions.vdc.vdcLevel1"（VDC）
	StatCloudResourceDimensionType_VDC StatCloudResourceDimensionField = "dimensions.vdc.vdcLevel1"
	// 如需列出全部⼦级VDC，可将请求体中的”vdc.vdcLevel1“替换为”vdc.name“。
	// "dimensions.vdc.name"（VDC）
	StatCloudResourceDimensionType_VDC_NAME StatCloudResourceDimensionField = "dimensions.vdc.name"
	// dimensions.object.typeName（对象类型）
	StatCloudResourceDimensionType_OBJECT_TYPE_NAME StatCloudResourceDimensionField = "dimensions.object.typeName"
	// dimensions.logicLoc.regionName（区域）
	StatCloudResourceDimensionType_LOGIC_LOC_REGION StatCloudResourceDimensionField = "dimensions.logicLoc.regionName"
	// dimensions.logicLoc.resourcePoolName（资源池）
	StatCloudResourceDimensionType_LOGIC_LOC_POOL StatCloudResourceDimensionField = "dimensions.logicLoc.resourcePoolName"
	// dimensions.logicLoc.azoneName（可⽤分区）
	StatCloudResourceDimensionType_LOGIC_LOC_AZONE StatCloudResourceDimensionField = "dimensions.logicLoc.azoneName"
	// dimensions.logicLoc.clusterName （集群）
	StatCloudResourceDimensionType_LOGIC_LOC_CLUSTER StatCloudResourceDimensionField = "dimensions.logicLoc.clusterName"
	// dimensions.otherInfo.status（状态）
	StatCloudResourceDimensionType_OTHER_INFO_STATUS StatCloudResourceDimensionField = "dimensions.otherInfo.status"

	// "dimensions.time.year"
	StatCloudResourceDimensionType_TIME_YEAR StatCloudResourceDimensionField = "dimensions.time.year"
	// "dimensions.time.month"
	StatCloudResourceDimensionType_TIME_MONTH StatCloudResourceDimensionField = "dimensions.time.month"
	// "dimensions.time.day"
	StatCloudResourceDimensionType_TIME_DAY StatCloudResourceDimensionField = "dimensions.time.day"
)

type StatCloudResourceDimension struct {
	Field StatCloudResourceDimensionField `json:"field"`
	Index int                             `json:"index"`
	// Values 仅在filter中使用
	Values []string `json:"values"`
}

type StatCloudResourceMetricAggType string

const (
	StatCloudResourceMetricAggType_SUM StatCloudResourceMetricAggType = "sum"
	StatCloudResourceMetricAggType_AVG StatCloudResourceMetricAggType = "avg"
	StatCloudResourceMetricAggType_MAX StatCloudResourceMetricAggType = "max"
	StatCloudResourceMetricAggType_MIN StatCloudResourceMetricAggType = "min"
)

type StatCloudResourceMetricField string

const (
	// metrics.count1
	StatCloudResourceMetricType_COUNT1 StatCloudResourceMetricField = "metrics.count1"
	// metrics.cpuUsage
	StatCloudResourceMetricType_CPU_USAGE StatCloudResourceMetricField = "metrics.cpuUsage"
	// metrics.memoryUsage
	StatCloudResourceMetricType_MEMORY_USAGE StatCloudResourceMetricField = "metrics.memoryUsage"
	// metrics.nicByteIn
	StatCloudResourceMetricType_NIC_BYTE_IN StatCloudResourceMetricField = "metrics.nicByteIn"
	// metrics.nicByteOut
	StatCloudResourceMetricType_NIC_BYTE_OUT StatCloudResourceMetricField = "metrics.nicByteOut"
	// metrics.diskIoIn
	StatCloudResourceMetricType_DISK_IO_IN StatCloudResourceMetricField = "metrics.diskIoIn"
	// metrics.diskIoOut
	StatCloudResourceMetricType_DISK_IO_OUT StatCloudResourceMetricField = "metrics.diskIoOut"
	// metrics.diskUsage
	StatCloudResourceMetricType_DISK_USAGE StatCloudResourceMetricField = "metrics.diskUsage"
)

type StatCloudResourceMetric struct {
	AggType StatCloudResourceMetricAggType `json:"aggType"`
	Field   StatCloudResourceMetricField   `json:"field"`
}

type StatCloudResourceData struct {
	Count1   float64 `json:"count1"`
	TypeName string  `json:"typeName"`
}

func (c *Client) StatCloudResource(ctx context.Context, options AnalysisDatasetsOptions) (*AnalysisDatasetsResponse[StatCloudResourceData], error) {
	ret := &AnalysisDatasetsResponse[StatCloudResourceData]{}
	req := StatCloudResourceRequest{
		Dimensions: []StatCloudResourceDimension{
			{
				Field: StatCloudResourceDimensionType_OBJECT_TYPE_NAME,
				Index: 1,
			},
		},
		Metrics: []StatCloudResourceMetric{
			{
				AggType: StatCloudResourceMetricAggType_SUM,
				Field:   StatCloudResourceMetricType_COUNT1,
			},
		},
	}
	if err := c.AnalysisDatasets(ctx, AnalysisDatasetsKind_STAT_CLOUD_RESOURCE, req, options, ret); err != nil {
		return nil, err
	}
	return ret, nil
}

type AnalysisDatasetsKind string

const (
	// 统计各类型云资源数量
	AnalysisDatasetsKind_STAT_CLOUD_RESOURCE AnalysisDatasetsKind = "stat-cloud-resource"
	// 统计各类型物理设备数量
	AnalysisDatasetsKind_STAT_PHYSICAL_DEVICE AnalysisDatasetsKind = "stat-device"
	// 统计管理⾯虚拟机数量
	// ”dimensions.otherInfo.status”（管理⾯虚拟机状态）
	AnalysisDatasetsKind_STAT_VM AnalysisDatasetsKind = "stat-vm"
	// VDC数量统计
	AnalysisDatasetsKind_STAT_VDC AnalysisDatasetsKind = "stat-vdc"
	// 统计弹性云服务器(ECS)状态
	AnalysisDatasetsKind_STAT_ECS AnalysisDatasetsKind = "stat-ecs"
)

type AnalysisDatasetsResponse[T any] struct {
	TotalSize int `json:"totalSize"`
	PageSize  int `json:"pageSize"`
	PageNo    int `json:"pageNo"`
	Datas     []T `json:"datas"`
}

type AnalysisDatasetsOptions struct {
	PageNo   int `json:"pageNo"`
	PageSize int `json:"pageSize"`
}

// POST https://{OC北向接⼝域名}/rest/analysis/v1/datasets/stat-cloud-resource
func (c *Client) AnalysisDatasets(ctx context.Context, kind AnalysisDatasetsKind, req StatCloudResourceRequest, options AnalysisDatasetsOptions, into any) error {
	request := c.OC_NEXT.Post("/rest/analysis/v1/datasets/stat-cloud-resource").
		JSON(req).
		Return(into)
	if options.PageNo != 0 {
		request.Query("pageNo", fmt.Sprintf("%d", options.PageNo))
	}
	if options.PageSize != 0 {
		request.Query("pageSize", fmt.Sprintf("%d", options.PageSize))
	}
	return request.Send(ctx)
}
