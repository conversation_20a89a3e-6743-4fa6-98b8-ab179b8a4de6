package client

import (
	"context"
	"net/url"
	"strconv"
)

type ListProjectResponse struct {
	Projects []ProjectResponse `json:"projects"`
	Links    map[string]string `json:"links"`
}

type ProjectResponse struct {
	ID          string            `json:"id"`
	Description string            `json:"description"`
	Enabled     bool              `json:"enabled"`
	DomainID    string            `json:"domain_id"`
	Name        string            `json:"name"`
	IsDomain    bool              `json:"is_domain"`
	ParentID    string            `json:"parent_id"`
	Links       map[string]string `json:"links"`
}

type ListProjectRequest struct {
	DomainID string `json:"domain_id,omitempty"`
	Name     string `json:"name,omitempty"`
	ParentID string `json:"parent_id,omitempty"`
	Enabled  bool   `json:"enabled,omitempty"`
	IsDomain bool   `json:"is_domain,omitempty"`
	Page     int    `json:"page,omitempty"`
	PerPage  int    `json:"per_page,omitempty"`
}

func (g ListProjectRequest) ToValues() url.Values {
	values := make(url.Values)
	if g.DomainID != "" {
		values.Set("domain_id", g.DomainID)
	}
	if g.Name != "" {
		values.Set("name", g.Name)
	}
	if g.ParentID != "" {
		values.Set("parent_id", g.ParentID)
	}
	if g.Enabled {
		values.Set("enabled", "true")
	}
	if g.IsDomain {
		values.Set("is_domain", "true")
	}
	if g.Page > 0 {
		values.Set("page", strconv.Itoa(g.Page))
	}
	if g.PerPage > 0 {
		values.Set("per_page", strconv.Itoa(g.PerPage))
	}
	return values
}

func (p *Client) ListProjects(ctx context.Context, options ListProjectRequest) (*ListProjectResponse, error) {
	ret := &ListProjectResponse{}
	if err := p.IAM_NEXT.Get("/v3/projects").
		Queries(options.ToValues()).
		Return(ret).
		Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}
