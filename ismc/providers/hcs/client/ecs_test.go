package client

import (
	"context"
	"fmt"
	"testing"

	ecsModel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	"k8s.io/utils/ptr"
)

func TestNewPrePaidServerRootVolumeVolumetype(t *testing.T) {
	typ := "LDC_SAS_Volume"
	ret := NewPrePaidServerRootVolumeVolumetype(typ)
	fmt.Printf("%+v\n", ret)
}

func TestListVirtualMachines(t *testing.T) {
	cli := setupTestClient(t)

	response, err := cli.ECS.ListServersDetails(&ecsModel.ListServersDetailsRequest{
		// EnterpriseProjectId: stringPtr("all_granted_eps"),
		Limit:  ptr.To(int32(10)),
		Offset: ptr.To(int32(0)),
	})
	// Handle error and print response
	if err == nil {
		fmt.Printf("%+v\n", response)
	} else {
		fmt.Println(err)
	}
}

func TestListServersDetails(t *testing.T) {
	cli := setupTestClient(t)

	response, err := cli.ListServersDetails(&ListServersDetailsRequest{
		Limit:  ptr.To(int32(10)),
		Offset: ptr.To(int32(0)),
		// ExpectFields: ptr.To("id,name,flavor,vpcid,availability_zone,root_volume,nics"),
		ExpectFields: ptr.To(string(AllServerExpectFields)),
	})
	// Handle error and print response
	if err == nil {
		fmt.Printf("%+v\n", response)
	} else {
		fmt.Println(err)
	}
}

func TestCreateVirtualMachine(t *testing.T) {
	cli := setupTestClient(t)
	req := &ecsModel.CreateServersRequest{
		Body: &ecsModel.CreateServersRequestBody{
			Server: &ecsModel.PrePaidServer{
				Name:             "direct-creation",
				ImageRef:         "8bb364f4-71b8-4f40-9d06-0681158f9b21",
				FlavorRef:        "f6fb37f1-4b29-41bd-9ed3-46d68274b646",
				Vpcid:            "4ca4f1e0-39a7-4729-b041-578e77209fc1",
				AvailabilityZone: ptr.To("az1.dc1"),
				RootVolume: &ecsModel.PrePaidServerRootVolume{
					Volumetype: NewPrePaidServerRootVolumeVolumetype("LDC_SAS_Volume"),
					Size:       ptr.To(int32(40)),
				},
				Nics: []ecsModel.PrePaidServerNic{
					{
						SubnetId: "0afd9ab8-fbf5-4dcf-bbb8-5df9e1023ac6",
					},
				},
			},
		},
	}
	resp, err := cli.ECS.CreateServers(req)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}

func TestShowFlovar(t *testing.T) {
	cli := setupTestClient(t)
	resp, err := cli.GetFlavor(context.Background(), "992ddafa-d7ac-45fd-a9f7-3c8f391d20fe")
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}

func TestListFlovars(t *testing.T) {
	cli := setupTestClient(t)
	resp, err := cli.ListFlavors(context.Background(), ListFlavorsOptions{})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}

func TestBatchAttachVolume(t *testing.T) {
	cli := setupTestClient(t)
	serverid := ""
	resp, err := cli.BatchAttachVolume(context.Background(), serverid, BatchAttachVolumeRequest{})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}

func TestListNovaServers(t *testing.T) {
	cli := setupTestClient(t)
	resp, err := cli.ECS.ListCloudServers(&ecsModel.ListCloudServersRequest{})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}
