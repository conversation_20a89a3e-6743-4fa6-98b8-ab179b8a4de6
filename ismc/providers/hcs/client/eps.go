package client

import (
	"context"
	"strconv"
	"time"

	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1/model"
)

// CreateEnterpriseProjectRequest Request Object
type CreateEnterpriseProjectRequest struct {
	Body *EnterpriseProject `json:"body,omitempty"`
}

// EnterpriseProject 企业项目
type EnterpriseProject struct {
	// 项目ID，最大长度36个字符。
	ProjectID string `json:"project_id,omitempty"`

	// 只能由中文字符、英文字母（a~zA~Z）、数字（0~9）、下划线（_）、中划线（-）组成，且长度为[1-64]个字符。名称不能为大小写混合的default，且在租户账号内唯一。
	Name string `json:"name"`

	// 最大长度512个字符。
	Description *string `json:"description,omitempty"`

	// 企业项目类型
	Type *string `json:"type,omitempty"`
}

func (c *Client) CreateEnterpriseProject(request *CreateEnterpriseProjectRequest) (*model.CreateEnterpriseProjectResponse, error) {
	if request.Body.ProjectID == "" {
		request.Body.ProjectID = c.DefaultProjectID
	}
	requestDef := eps.GenReqDefForCreateEnterpriseProject()
	if resp, err := c.EPS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*model.CreateEnterpriseProjectResponse), nil
	}
}

// offset=0&limit=10&sort_key=created_at&sort_dir=asc&query_type=list&domain_id=a39bb0e8994c4d5ba829c33b665b1ea1&contain_default=false&name=ismc&vdc_id=59d974a6-af83-4d26-9270-f01fc0c561f6&inherit=true&project_id=7e149859f56047f69ff5663f89b3c09d
type ListEnterpriseProjectOptions struct {
	ID        string  // 企业项目ID，"0"表示默认企业项目
	Limit     *int32  // 查询记录数默认为1000，limit最多为1000, 最小值为1
	Offset    *int32  // 索引位置，从offset指定的下一条数据开始查询，必须为数字，不能为负数，默认为0
	Name      string  // 企业项目名称，支持模糊搜索
	SortDir   *string // 降序或升序,默认为“desc” 。desc表示降序 。asc 表示升序
	SortKey   *string // 返回结果按该关键字排序（支持updated_at等关键字，默认为“created_at”）
	Status    int     // 企业项目状态。 1--启用，2--停用
	ProjectID string  // 项目ID, 用于查询指定项目下的企业项目
	DomainID  string  // 企业项目所属账号ID
}

type ListEnterpriseProjectResponse struct {
	EnterpriseProjects *[]EpDetail `json:"enterprise_projects,omitempty"` // 企业项目列表
	TotalCount         *int32      `json:"total_count,omitempty"`         // 企业项目总数
}

type EpDetail struct {
	Id          string    `json:"id"`          // 企业项目ID
	Name        string    `json:"name"`        // 企业项目名称
	Description string    `json:"description"` // 企业项目描述
	Status      int32     `json:"status"`      // 企业项目状态。1启用，2停用
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Type        string    `json:"type"`        // 项目类型： - prod：商用项目 - poc：测试项目
	DomainID    string    `json:"domain_id"`   // 企业项目所属账号ID
	DomainName  string    `json:"domain_name"` // 企业项目所属账号名称
	VDCName     string    `json:"vdc_name"`    // 企业项目所属VDC名称
	VDCID       string    `json:"vdc_id"`      // 企业项目所属VDC ID
	ProjectName string    `json:"project_name"`
	ProjectID   string    `json:"project_id"`
	DeleteFlag  bool      `json:"delete_flag"` // 是否删除
}

func (c *Client) ListEnterpriseProject(ctx context.Context, options ListEnterpriseProjectOptions) (*ListEnterpriseProjectResponse, error) {
	req := c.SC_NEXT_PROJECT.Get("/v1.0/enterprise-projects")
	if options.ID != "" {
		req.Query("id", options.ID)
	}
	if options.ProjectID != "" {
		req.Query("project_id", options.ProjectID)
	}
	if options.Limit != nil {
		req.Query("limit", strconv.Itoa(int(*options.Limit)))
	}
	if options.Offset != nil {
		req.Query("offset", strconv.Itoa(int(*options.Offset)))
	}
	if options.Name != "" {
		req.Query("name", options.Name)
	}
	if options.SortDir != nil {
		req.Query("sort_dir", *options.SortDir)
	}
	if options.SortKey != nil {
		req.Query("sort_key", *options.SortKey)
	}
	if options.Status > 0 {
		req.Query("status", strconv.Itoa(options.Status))
	}
	ret := &ListEnterpriseProjectResponse{}
	if err := req.Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

func (c *Client) GetEnterpriseProject(ctx context.Context, id string) (*EpDetail, error) {
	req := c.SC_NEXT_PROJECT.Get("/v1.0/enterprise-projects/" + id)
	data := struct {
		EnterpriseProject *EpDetail `json:"enterprise_project"`
	}{}
	if err := req.Return(&data).Send(ctx); err != nil {
		return nil, err
	}
	return data.EnterpriseProject, nil
}

// DeleteEnterpriseProject
// "The API does not exist: method DELETE not found"
func (c *Client) DeleteEnterpriseProject(ctx context.Context, id string) error {
	req := c.SC_NEXT_PROJECT.Delete("/v1.0/enterprise-projects/" + id)
	if err := req.Send(ctx); err != nil {
		return err
	}
	return nil
}
