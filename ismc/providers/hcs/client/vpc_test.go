package client

import (
	"context"
	"fmt"
	"testing"
)

func TestClient_ListSecurityGroups(t *testing.T) {
	cli := setupTestClient(t)
	resp, err := cli.ListSecurityGroups(context.Background(), ListSecurityGroupsOptions{})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}

func TestClient_ListPorts(t *testing.T) {
	cli := setupTestClient(t)
	resp, err := cli.ListPorts(context.Background(), ListVPCPortsOptions{})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Printf("%+v\n", resp)
}
