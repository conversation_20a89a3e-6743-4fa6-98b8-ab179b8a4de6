package client

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/config"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/impl"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/request"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/response"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/sdkerr"
	cesv1 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ces/v1"
	ecs "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2"
	eps "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/eps/v1"
	evs "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/evs/v2"
	iam "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3"
	iammodel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	ims "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ims/v2"
	vpcv2 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v2"
	vpcv3 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v3"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
)

type Options struct {
	Proxy        string `json:"proxy,omitempty" description:"proxy url, use http proxy for hcs client"`
	GlobalDomain string `json:"globalDomain,omitempty" description:"global api domain suffix,eg: exmaple.com"`
	Region       string `json:"region,omitempty" description:"region to manage"`

	Domain   string `json:"domain,omitempty" description:"domain name for IAM"`
	UserName string `json:"userName,omitempty" description:"username for IAM"`
	Password string `json:"password,omitempty" description:"password for IAM"`

	DefaultProject string `json:"defaultProject,omitempty" description:"default project name or id"`
	Debug          bool   `json:"debug,omitempty" description:"debug mode"`
}

func NewDefaultOptions() *Options {
	return &Options{
		Domain:       "xiaoshiai",
		GlobalDomain: "majnoon-dccloud.com",
		Region:       "majnoon-dccloud-1",
	}
}

/*
*
服务端调用结构，所有流量都指向 APIgateway，API Gateway 有高可用浮动IP

3rd APP
───────────────────────────────────────

	|
	| https
	v

APIGateway
───────────────────────────────────────

	|
	| https
	v

ManageOne
───────────────────────────────────────

	|        |        |        |
	|        |        |        |
	|        |        |        |

运维侧   运营侧     IAM     运营指挥中心
*/
type Client struct {
	Transport   http.RoundTripper
	projectAuth *UsernamePasswordCredentials
	globalAuth  *UsernamePasswordCredentials

	DefaultProjectID string
	IAM              *iam.IamClient
	IAM_NEXT         *httpclient.Client
	IMS              *ims.ImsClient
	IMS_NEXT         *httpclient.Client
	ECS              *ecs.EcsClient
	ECS_NEXT         *httpclient.Client
	VPCV3            *vpcv3.VpcClient
	VPCV2            *vpcv2.VpcClient
	VPC_NEXT         *httpclient.Client
	EVS              *evs.EvsClient
	EVS_NEXT         *httpclient.Client
	// CESv1 云监控服务 CES
	CESv1 *cesv1.CesClient
	// alias SC
	EPS *eps.EpsClient
	// System Center
	SC_NEXT         *httpclient.Client
	SC_NEXT_PROJECT *httpclient.Client
	// Operation Center
	OC_NEXT *httpclient.Client

	productsCache []ProductDTO
}

func NewClient(ctx context.Context, options *Options) (*Client, error) {
	log := log.FromContext(ctx)
	// OC 的认证从 oc.{region_id}.{external_global_domain_name}/rest/plat/smapp/v1/oauth/token 获取
	// 其他认证从 iam-apigateway-proxy.{region_id}.{external_global_domain_name}/v3/auth/tokens 获取

	// OC获取对接帐号
	// 创建用户，并将角色和用户绑定。
	// 操作步骤
	// 步骤 1使用浏览器，输入地址“https://ManageOne主门户的访问地址”，例如https://console.demo.com/moserviceaccesswebsite/unifyportal#/home，登录ManageOne主门户，然后选择“运维中心（OC）”，进入ManageOne运维面。
	// 步骤 2创建北向用户。具体操作请参见《ManageOne 8.3.0 运维指南》中的“系统管理  >  系统集成 >  管理北向用户”章节。
	roundTripper := http.DefaultTransport.(*http.Transport).Clone()
	if options.Proxy != "" {
		proxyu, err := url.Parse(options.Proxy)
		if err != nil {
			return nil, fmt.Errorf("failed to parse proxy url: %v", err)
		}
		roundTripper.Proxy = http.ProxyURL(proxyu)
	}
	roundTripper.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}

	httpcli := impl.NewDefaultHttpClient(
		config.DefaultHttpConfig().WithHttpRoundTripper(roundTripper))

	authEndpoint := GetServiceEndpoint("iam-apigateway-proxy", options.Region, options.GlobalDomain)

	glbalCridentialOptions := CridentialOptions{
		Endpoint: authEndpoint,
		Username: options.UserName,
		Password: options.Password,
		Domain:   options.Domain,
	}
	globalauth, err := NewUsernamePasswordCredentials(ctx, httpcli, glbalCridentialOptions)
	if err != nil {
		return nil, err
	}
	if globalauth.TokenCache == nil {
		return nil, errors.NewBadRequest("failed to get token")
	}
	currentuser := globalauth.TokenCache.Token.User
	if currentuser == nil {
		return nil, errors.NewBadRequest("failed to get current user")
	}
	log.Info("current user", "name", currentuser.Name, "id", currentuser.Id)

	iamcli := iam.NewIamClient(core.NewHcHttpClient(httpcli).
		WithEndpoints([]string{authEndpoint}).
		WithErrorHandler(&ConvertErrorErrorHandler{}).
		WithCredential(globalauth))

	resp, err := iamcli.KeystoneListProjects(&iammodel.KeystoneListProjectsRequest{})
	if err != nil {
		return nil, fmt.Errorf("failed to list projects: %v", err)
	}
	if resp.HttpStatusCode != 200 {
		return nil, fmt.Errorf("failed to list projects: %v", resp)
	}
	if resp.Projects == nil || len(*resp.Projects) == 0 {
		return nil, fmt.Errorf("no project found")
	}
	var defaultProjectID string
	for _, project := range *resp.Projects {
		if project.Name == options.DefaultProject || project.Id == options.DefaultProject {
			defaultProjectID = project.Id
			break
		}
	}
	if defaultProjectID == "" {
		return nil, fmt.Errorf("failed to find project %s", options.DefaultProject)
	}

	projectCridentialOptions := glbalCridentialOptions
	projectCridentialOptions.ProjectID = defaultProjectID
	projectauth, err := NewUsernamePasswordCredentials(ctx, httpcli, projectCridentialOptions)
	if err != nil {
		return nil, err
	}

	newcli := func(svc string, auth *UsernamePasswordCredentials) *httpclient.Client {
		return &httpclient.Client{
			Server:       GetServiceEndpoint(svc, options.Region, options.GlobalDomain),
			RoundTripper: roundTripper,
			OnResponse:   ConvertErrorErrorHandler{}.HandleRawError,
			OnRequest:    auth.PreRequest,
			Debug:        options.Debug,
		}
	}

	return &Client{
		projectAuth: projectauth,
		globalAuth:  globalauth,

		DefaultProjectID: defaultProjectID,
		Transport:        roundTripper,

		IAM:      iamcli,
		IAM_NEXT: newcli("iam-apigateway-proxy", globalauth),

		ECS: ecs.NewEcsClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("ecs", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),

		ECS_NEXT: newcli("ecs", projectauth),

		VPCV3: vpcv3.NewVpcClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("vpc", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),

		VPCV2: vpcv2.NewVpcClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("vpc", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),

		VPC_NEXT: newcli("vpc", projectauth),

		EVS: evs.NewEvsClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("evs", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),
		EVS_NEXT: newcli("evs", projectauth),

		IMS: ims.NewImsClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("ims", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),

		IMS_NEXT: newcli("ims", projectauth),

		// 运营侧 API
		SC_NEXT:         newcli("sc", globalauth),
		SC_NEXT_PROJECT: newcli("sc", projectauth),

		EPS: eps.NewEpsClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("sc", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),

		// 运维侧 API, 就是超级管理员的系统管理
		OC_NEXT: newcli("oc", globalauth),

		CESv1: cesv1.NewCesClient(core.NewHcHttpClient(httpcli).
			WithEndpoints([]string{
				GetServiceEndpoint("ces", options.Region, options.GlobalDomain),
			}).
			WithErrorHandler(&ConvertErrorErrorHandler{}).
			WithCredential(projectauth)),
	}, nil
}

// GetServiceEndpoint
// 运维侧域名，oc.{region_id}.{external_global_domain_name}
// 运营侧域名，sc.{external_global_domain_name}
// IAM域名，iam-apigateway-proxy.{region_id}.{external_global_domain_name}
//
// external_global_domain_name，APIGateway浮动IP可联系环境管理员获取。
// 终端节点（Endpoint）信息由服务名、Region ID、外部域名三部分组成，格式为：service_name.region0_id.external_global_domain_name。
func GetServiceEndpoint(svc, region, domain string) string {
	if region == "" {
		return fmt.Sprintf("https://%s.%s", svc, domain)
	}
	return fmt.Sprintf("https://%s.%s.%s", svc, region, domain)
}

type UserInfo struct {
	ID     string `json:"id,omitempty"`
	Name   string `json:"name,omitempty"`
	Domain string `json:"domain,omitempty"`
}

func (c *Client) CurrentUserInfo(ctx context.Context) (*UserInfo, error) {
	if c.globalAuth.TokenCache == nil || c.globalAuth.TokenCache.Token == nil {
		return nil, errors.NewBadRequest("no token")
	}
	user := c.globalAuth.TokenCache.Token.User
	if user == nil {
		return nil, errors.NewBadRequest("no user")
	}
	return &UserInfo{ID: user.Id, Name: user.Name, Domain: user.Domain.Name}, nil
}

type ConvertErrorErrorHandler struct{}

func (h ConvertErrorErrorHandler) HandleRawError(req *http.Request, resp *http.Response) error {
	return h.handleResponse(reqmeta{Endpoint: req.URL.Host, Method: req.Method, Path: req.URL.Path}, resp)
}

var _ sdkerr.ErrorHandler = ConvertErrorErrorHandler{}

func (h ConvertErrorErrorHandler) HandleError(req *request.DefaultHttpRequest, resp *response.DefaultHttpResponse) error {
	return h.handleResponse(reqmeta{Endpoint: req.GetEndpoint(), Method: req.GetMethod(), Path: req.GetPath()}, resp.Response)
}

type reqmeta struct {
	Endpoint string
	Method   string
	Path     string
}

func (h ConvertErrorErrorHandler) handleResponse(req reqmeta, resp *http.Response) error {
	if resp.StatusCode < 400 {
		return nil
	}
	formatederr := sdkerr.NewServiceResponseError(resp)
	log.Error(formatederr, "sdk error", "endpoint", req.Endpoint, "method", req.Method, "path", req.Path, "status", resp.StatusCode)

	// "{\"itemNotFound\": {\"code\": 404, \"message\": \"Volume 036f4488-8106-488e-a4d8-c54654508c217 could not be found.\"}}"
	switch formatederr.ErrorCode {
	case "Ecs.1000":
		// "Failed to query server[79820403-a2f0-4a4f-a270-e2422bcc3c2a4] detail: Instance 79820403-a2f0-4a4f-a270-e2422bcc3c2a4 could not be found."
		return &errors.Status{
			Status:  errors.StatusFailure,
			Code:    http.StatusNotFound,
			Message: formatederr.ErrorMessage,
			Reason:  errors.StatusReasonNotFound,
		}
	case "VPC.0104":
		// "Router contains subnets, please delete subnet first."
		return &errors.Status{
			Status:  errors.StatusFailure,
			Code:    http.StatusBadRequest,
			Message: "can't delete network which contains subnetworks",
		}
	case "VPC.0209":
		// "subnet is still used ,such as computer,LB."
		return &errors.Status{
			Status:  errors.StatusFailure,
			Code:    http.StatusBadRequest,
			Message: "subnetwork is still used by virtual machines or load balancers",
		}
	}
	errmsg := formatederr.ErrorMessage
	if maybejson(errmsg) {
		m := ErrorMessageFormated{}
		if err := json.Unmarshal([]byte(errmsg), &m); err != nil {
			return errors.NewBadRequest(errmsg)
		}
		if m.ItemNotFound != nil {
			return &errors.Status{
				Status:  errors.StatusFailure,
				Code:    http.StatusNotFound,
				Message: m.ItemNotFound.Message,
				Reason:  errors.StatusReasonNotFound,
			}
		} else if m.NeutronError != nil {
			errmsg = m.NeutronError.Message
		}
		// [
		// "Failed to calculate the cloud service quota because the cloud service does not return correct information or is abnormal.",
		// "{\"moorder3rdMsg\":\"invalid volume size!\",\"error\":{\"code\":\"EVS.1010\",\"message\":\"invalid volume size!\"}}"
		// ]
		if len(m.DetailArgs) > 0 {
			for i := len(m.DetailArgs) - 1; i >= 0; i-- {
				detail := m.DetailArgs[i]
				if maybejson(detail) {
					detailm := ErrorDetailArg{}
					if err := json.Unmarshal([]byte(detail), &detailm); err != nil {
						continue
					}
					errmsg = detailm.Moorder3rdMsg
					break
				}
				errmsg = detail
			}
		} else {
			for _, arg := range m.ReasonArgs {
				if arg != "" {
					errmsg = arg
					break
				}
			}
		}
	}
	if resp.StatusCode >= 500 {
		return errors.NewInternalError(fmt.Errorf("http error: %d %s", resp.StatusCode, errmsg))
	}
	return errors.NewBadRequest(errmsg)
}

func maybejson(errmsg string) bool {
	return len(errmsg) > 0 && errmsg[0] == '{'
}

type ErrorDetailArg struct {
	Moorder3rdMsg string           `json:"moorder3rdMsg"`
	Error         ErrorCodeMessage `json:"error"`
}

type ErrorCodeMessage struct {
	Code    StringOrInt `json:"code"`
	Message string      `json:"message"`
}

type StringOrInt struct {
	Str string
	Int int
}

func (s *StringOrInt) UnmarshalJSON(data []byte) error {
	if data[0] == '"' {
		return json.Unmarshal(data, &s.Str)
	}
	return json.Unmarshal(data, &s.Int)
}

type ErrorMessageFormated struct {
	// "{\"itemNotFound\": {\"code\": 404, \"message\": \"Volume 036f4488-8106-488e-a4d8-c54654508c217 could not be found.\"}}"
	ItemNotFound *ErrorCodeMessage `json:"itemNotFound"`
	NeutronError *NeutronError     `json:"NeutronError"`
	// "{\"exceptionId\":\"mosub-00008\",\"exceptionType\":\"ROA_EXFRAME_EXCEPTION\",\"descArgs\":[\"Failed to calculate the cloud service quota.\"],\"reasonArgs\":[\"Failed to call the interface for calculating the cloud service quota.\"],\"detailArgs\":[\"Failed to calculate the cloud service quota because the cloud service does not return correct information or is abnormal.\",\"{\\\"moorder3rdMsg\\\":\\\"invalid volume size!\\\",\\\"error\\\":{\\\"code\\\":\\\"EVS.1010\\\",\\\"message\\\":\\\"invalid volume size!\\\"}}\"],\"adviceArgs\":[\"Check the cloud service log or check whether the cloud service is normal.\"]}"
	// "{\"exceptionId\":\"movdcmt-00013\",\"exceptionType\":\"ROA_EXFRAME_EXCEPTION\",\"descArgs\":[\"no permission.\"],\"reasonArgs\":[\"role list is empty.\"],\"detailArgs\":null,\"adviceArgs\":[\"\"]}"
	// "{\"exceptionId\":\"moord-00001\",\"exceptionType\":\"ROA_EXFRAME_EXCEPTION\",\"descArgs\":[\"Parameter error.\"],\"reasonArgs\":[\"The type and specifications of the entered parameter value do not meet the requirements.\"],\"detailArgs\":[\"The type and specifications of the entered value, such as the character type and length, do not meet the requirements.\"],\"adviceArgs\":[\"Enter another value whose type and specifications meet the requirements.\"]}"
	ExceptionId   string   `json:"exceptionId"`
	ExceptionType string   `json:"exceptionType"`
	DescArgs      []string `json:"descArgs"`
	ReasonArgs    []string `json:"reasonArgs"`
	DetailArgs    []string `json:"detailArgs"`
	AdviceArgs    []string `json:"adviceArgs"`
}

type NeutronError struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Detail  string `json:"detail"`
}

type IntegerMayString int64

func (i *IntegerMayString) UnmarshalJSON(data []byte) error {
	if data[0] == '"' {
		// unmarshal as string
		var s string
		if err := json.Unmarshal(data, &s); err != nil {
			return err
		}
		if s == "" {
			*i = 0
			return nil
		}
		n, err := strconv.Atoi(s)
		if err != nil {
			return err
		}
		*i = IntegerMayString(n)
	} else {
		// unmarshal as int
		var n int64
		if err := json.Unmarshal(data, &n); err != nil {
			return err
		}
		*i = IntegerMayString(n)
	}
	return nil
}
