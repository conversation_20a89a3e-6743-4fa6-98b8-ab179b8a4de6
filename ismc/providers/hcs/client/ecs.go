package client

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"unsafe"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/def"
	ecs "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
)

func NewPrePaidServerRootVolumeVolumetype(typ string) model.PrePaidServerRootVolumeVolumetype {
	ret := model.PrePaidServerRootVolumeVolumetype{}
	field := reflect.ValueOf(&ret).Elem().Field(0)
	reflect.NewAt(field.Type(), unsafe.Pointer(field.UnsafeAddr())).Elem().SetString(typ)
	return ret
}

func NewPrePaidServerDataVolumeVolumetype(typ string) model.PrePaidServerDataVolumeVolumetype {
	ret := model.PrePaidServerDataVolumeVolumetype{}
	field := reflect.ValueOf(&ret).Elem().Field(0)
	reflect.NewAt(field.Type(), unsafe.Pointer(field.UnsafeAddr())).Elem().SetString(typ)
	return ret
}

type CreateServersRequest struct {
	// 保证客户端请求幂等性的标识
	XClientToken *string                   `json:"X-Client-Token,omitempty"`
	Body         *CreateServersRequestBody `json:"body,omitempty"`
}

type CreateServersRequestBody struct {
	// 是否只预检此次请求。  true：发送检查请求，不会创建实例。检查项包括是否填写了必需参数、请求格式等。 如果检查不通过，则返回对应错误。 如果检查通过，则返回202状态码。 false：发送正常请求，通过检查后并且执行创建云服务器请求。
	DryRun *bool          `json:"dry_run,omitempty"`
	Server *PrePaidServer `json:"server"`
}

type PrePaidServer struct {
	model.PrePaidServer `json:",inline"`
	// 覆盖默认的PrePaidServerDataVolume
	// 云服务器对应数据盘相关配置。每一个数据结构代表一块待创建的数据盘。   约束：目前新创建的弹性云服务器最多可挂载23块数据盘。
	DataVolumes *[]PrePaidServerDataVolume `json:"data_volumes,omitempty"`
	PowerOn     *bool                      `json:"power_on,omitempty"`
	Extra       PrePaidServerExtra         `json:"extra,omitempty"`
}

func (p PrePaidServer) String() string {
	data, err := json.Marshal(p)
	if err != nil {
		return "PrePaidServer struct{}"
	}
	return strings.Join([]string{"PrePaidServer", string(data)}, " ")
}

type PrePaidServerDataVolume struct {
	model.PrePaidServerDataVolume `json:",inline"`
	// 云硬盘标签。 也就是名称，使用已有硬盘时指定 .name
	Label string `json:"label,omitempty"`
	// 使用已有的硬盘时指定
	VolumeId string `json:"volume_id,omitempty"`
}

type PrePaidServerExtra struct {
	Devices []PrePaidServerExtraDevice `json:"devices"`
}

type PrePaidServerExtraDevice struct {
	// "cdrom"
	DeviceType string `json:"device_type"`
}

func (c *Client) CreateServers(request *CreateServersRequest) (*model.CreateServersResponse, error) {
	requestDef := ecs.GenReqDefForCreateServers()
	if resp, err := c.ECS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*model.CreateServersResponse), nil
	}
}

// ShowServerRemoteConsoleRequest Request Object
type ShowServerRemoteConsoleRequest struct {
	// 云服务器ID。
	ServerId string `json:"server_id"`

	Body *ShowServerRemoteConsoleRequestBody `json:"body,omitempty"`
}

// ShowServerRemoteConsoleRequestBody This is a auto create Body Object
type ShowServerRemoteConsoleRequestBody struct {
	RemoteConsole *GetServerRemoteConsoleOption `json:"remote_console"`
}

type GetServerRemoteConsoleOption struct {
	// 远程登录协议，请将protocol配置为“vnc”。
	Protocol model.GetServerRemoteConsoleOptionProtocol `json:"protocol"`

	// 远程登录的类型，请将type配置为“novnc”。
	Type model.GetServerRemoteConsoleOptionType `json:"type"`

	// 期望获取的控制台URL。
	ExpectConsoleUrl *string `json:"expect_console_url,omitempty"`
}

// ShowServerRemoteConsoleResponse Response Object
type ShowServerRemoteConsoleResponse struct {
	RemoteConsole  ServerRemoteConsole `json:"remote_console,omitempty"`
	HttpStatusCode int                 `json:"-"`
}

// ServerRemoteConsole
type ServerRemoteConsole struct {
	// 远程登录的协议。
	Protocol string `json:"protocol"`

	// 远程登录的类型。
	Type string `json:"type"`

	// 远程登录的url。
	Url string `json:"url"`

	// 控制台URL。
	ConsoleUrl string `json:"consoleUrl"`
}

// ShowServerRemoteConsole 获取VNC远程登录地址
//
// 获取弹性云服务器VNC远程登录地址。
//
// Please refer to HUAWEI cloud API Explorer for details.
func (c *Client) ShowServerRemoteConsole(request *ShowServerRemoteConsoleRequest) (*ShowServerRemoteConsoleResponse, error) {
	requestDef := ecs.GenReqDefForShowServerRemoteConsole()
	requestDef.Response = new(ShowServerRemoteConsoleResponse)

	if resp, err := c.ECS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*ShowServerRemoteConsoleResponse), nil
	}
}

type (
	ServerActionRequestBody struct {
		OsStart *ServerActionStartOption  `json:"os-start,omitempty"`
		OsStop  *ServerActionStopOption   `json:"os-stop,omitempty"`
		Reboot  *ServerActionRebootOption `json:"reboot,omitempty"`
	}

	ServerActionStartOption struct{}

	ServerActionStopOption struct {
		// 重启类型：  - SOFT：普通重启。 - HARD：强制重启。
		Type *string `json:"type,omitempty"`
	}
	ServerActionRebootOption struct {
		// 重启类型：  - SOFT：普通重启。 - HARD：强制重启。
		Type *string `json:"type,omitempty"`
	}

	BatchServersResponse struct {
		// 提交任务成功后返回的任务ID，用户可以使用该ID对任务执行情况进行查询。
		JobId          *string `json:"job_id,omitempty"`
		HttpStatusCode int     `json:"-"`
	}
)

const (
	ActionTypeHARD = "HARD"
	ActionTypeSOFT = "SOFT"
)

// BatchRebootServersRequest Request Object
type ServerActionRequest struct {
	ServerID string                   `json:"server_id"`
	Body     *ServerActionRequestBody `json:"body,omitempty"`
}

func (c *Client) ServerAction(request *ServerActionRequest) (*BatchServersResponse, error) {
	reqDefBuilder := def.NewHttpRequestDefBuilder().
		WithMethod(http.MethodPost).
		WithPath("/v2.1/{project_id}/servers/{server_id}/action").
		WithResponse(new(BatchServersResponse)).
		WithContentType("application/json;charset=UTF-8")

	reqDefBuilder.WithRequestField(def.NewFieldDef().
		WithName("ServerID").
		WithJsonTag("server_id").
		WithLocationType(def.Path))

	reqDefBuilder.WithRequestField(def.NewFieldDef().
		WithName("Body").
		WithLocationType(def.Body))

	requestDef := reqDefBuilder.Build()
	if resp, err := c.ECS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*BatchServersResponse), nil
	}
}

type CreateFlavorRequestBody struct {
	Flavor FlavorReq `json:"flavor"`
}
type FlavorReq struct {
	Vcpus                  int    `json:"vcpus"`
	Disk                   int    `json:"disk"`
	Name                   string `json:"name"`
	Ram                    int    `json:"ram"`
	OsFlavoraccessIsPublic bool   `json:"os-flavor-access:is_public,omitempty"`
	OSFLVEXTDATAEphemeral  int    `json:"OS-FLV-EXT-DATA:ephemeral,omitempty"`
	ID                     string `json:"id,omitempty"`
	Swap                   int    `json:"swap,omitempty"`
}

type CreateFlavorRequest struct {
	Body *CreateFlavorRequestBody `json:"body,omitempty"`
}

// ListFlavorsResponse Response Object
type CreateFlavorResponse struct {
	HttpStatusCode int `json:"-"`
}

func (c *Client) CreateFlavor(request *CreateFlavorRequest) (*CreateFlavorResponse, error) {
	reqDefBuilder := def.NewHttpRequestDefBuilder().
		WithMethod(http.MethodPost).
		WithPath("/v2/{project_id}/flavors").
		WithResponse(new(CreateFlavorResponse)).
		WithContentType("application/json")

	requestDef := reqDefBuilder.Build()
	if resp, err := c.ECS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*CreateFlavorResponse), nil
	}
}

type ShowFlavorRequest struct {
	FlavorId string `json:"flavor_id"`
}

type ShowFlavorResponse struct {
	Flavor         Flavor `json:"flavor"`
	HttpStatusCode int    `json:"-"`
}

type Flavor struct {
	// 云服务器规格的ID。
	Id string `json:"id"`
	// 云服务器规格的名称。
	Name string `json:"name"`
	// 云服务器规格对应的内存大小，单位为MB。
	Ram IntegerMayString `json:"ram"`
	// 云服务器规格对应的CPU核数。
	Vcpus IntegerMayString `json:"vcpus"`
	// 云服务器规格对应要求的系统盘大小。  当前未使用该参数，缺省值为0。
	Disk IntegerMayString `json:"disk"`
	// 云服务器规格对应要求的交换分区大小。  当前未使用该参数，缺省值为\"\"。
	Swap IntegerMayString `json:"swap"`
	// 扩展属性，临时盘大小。  当前未使用该参数，缺省值为0
	OSFLVEXTDATAephemeral int32 `json:"OS-FLV-EXT-DATA:ephemeral"`
	// 扩展属性，该云服务器规格是否禁用。  当前未使用该参数，缺省值为false。
	OSFLVDISABLEDdisabled bool `json:"OS-FLV-DISABLED:disabled"`
	// 云服务器可使用网络带宽与网络硬件带宽的比例。  当前未使用该参数，缺省值为1.0。
	RxtxFactor float32 `json:"rxtx_factor"`
	// 云服务器可使用网络带宽的软限制。  当前未使用该参数，缺省值为null。
	RxtxQuota string `json:"rxtx_quota"`
	// 云服务器可使用网络带宽的硬限制。  当前未使用该参数，缺省值为null。
	RxtxCap string `json:"rxtx_cap"`
	// 扩展属性，flavor是否给所有租户使用。  - true：表示给所有租户使用。 - false：表示给指定租户使用。  缺省值为true。
	OsFlavorAccessisPublic bool `json:"os-flavor-access:is_public"`
	// 规格相关快捷链接地址。
	Links              []model.FlavorLink              `json:"links"`
	OsExtraSpecs       FlavorExtraSpec                 `json:"os_extra_specs"`
	AttachableQuantity *model.ServerAttachableQuantity `json:"attachableQuantity,omitempty"`
}

type FlavorExtraSpec struct {
	model.FlavorExtraSpec `json:",inline"`
	// capabilities:cpu_info:arch: "x86_64"
	CapabilitiesCpuInfoArch string `json:"capabilities:cpu_info:arch,omitempty"`

	// capabilities:cpu_info:vendor: "Intel"
	CapabilitiesCpuInfoVendor string `json:"capabilities:cpu_info:vendor,omitempty"`
}

func (c *Client) ShowFlavor(request *ShowFlavorRequest) (*ShowFlavorResponse, error) {
	reqDefBuilder := def.NewHttpRequestDefBuilder().
		WithMethod(http.MethodGet).
		WithPath("/v2.1/{project_id}/flavors/{flavor_id}").
		WithResponse(new(ShowFlavorResponse)).
		WithContentType("application/json")

	reqDefBuilder.WithRequestField(def.NewFieldDef().
		WithName("FlavorId").
		WithJsonTag("flavor_id").
		WithLocationType(def.Path))

	requestDef := reqDefBuilder.Build()

	if resp, err := c.ECS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*ShowFlavorResponse), nil
	}
}

type ListFlavorsOptions struct {
	// AvailabilityZone
	// eg. az1.dc1
	AvailabilityZone string `json:"availability_zone,omitempty"`

	// NotKey
	// example: __advance_flavor:service
	NotKey string `json:"not_key,omitempty"`
}

func (c *Client) ListFlavors(ctx context.Context, opts ListFlavorsOptions) ([]Flavor, error) {
	req := c.ECS_NEXT.Get("/v1/{project_id}/cloudservers/flavors")
	if opts.AvailabilityZone != "" {
		req.Query("availability_zone", opts.AvailabilityZone)
	}
	if opts.NotKey != "" {
		req.Query("not_key", opts.NotKey)
	}
	data := struct {
		Flavors []Flavor `json:"flavors"`
	}{}
	if err := req.Return(&data).Send(ctx); err != nil {
		return nil, err
	}
	return data.Flavors, nil
}

type ListFlavorsDetailsOptins struct {
	// 	是否显示public的flavor，只有admin可以查询private的 flavor。可选值为true/false/none，非admin用户指定
	// is_public过滤无效，返回所有public的规格。
	// 枚举值：
	// ● true
	// ● false
	// ● none
	IsPublic string `json:"is_public,omitempty"`
	// 最小的硬盘规格，单位GB，大于等于此规格的都可以查询到。
	MinDisk int `json:"minDisk,omitempty"`
	// 最小的内存规格，单位MB，大于等于此规格的都可以查询到。
	MinRam int `json:"minRam,omitempty"`

	// 	排序字段，默认值为：flavorid。可以指定的key为 name、memory_mb、vcpus、root_gb、flavorid。
	// 缺省值：flavorid
	// 枚举值：
	// ● name
	// ● memory_mb
	// ● vcpus
	// ● root_gb
	// ● flavorid
	SortKey string `json:"sort_key,omitempty"`
	// 	升序/降序排序，默认值为：asc。可以指定的参数为asc/desc。
	// 缺省值：asc
	// 枚举值：
	// ● asc
	// ● desc
	SortDir string `json:"sort_dir,omitempty"`
}

func (c *Client) ListFlavorsDetails(ctx context.Context, opts ListFlavorsDetailsOptins) ([]Flavor, error) {
	req := c.ECS_NEXT.Get("/v2.1/{project_id}/flavors/detail")
	if opts.IsPublic != "" {
		req.Query("is_public", opts.IsPublic)
	}
	if opts.MinDisk > 0 {
		req.Query("minDisk", strconv.Itoa(opts.MinDisk))
	}
	if opts.MinRam > 0 {
		req.Query("minRam", strconv.Itoa(opts.MinRam))
	}
	if opts.SortKey != "" {
		req.Query("sort_key", opts.SortKey)
	}
	if opts.SortDir != "" {
		req.Query("sort_dir", opts.SortDir)
	}
	data := struct {
		Flavors []Flavor `json:"flavors"`
	}{}
	if err := req.Return(&data).Send(ctx); err != nil {
		return nil, err
	}
	return data.Flavors, nil
}

func (c *Client) GetFlavor(ctx context.Context, id string) (*Flavor, error) {
	data := struct {
		Flavor Flavor `json:"flavor"`
	}{}
	err := c.ECS_NEXT.
		Get(fmt.Sprintf("/v2.1/{project_id}/flavors/%s", id)).
		Return(&data).
		Send(ctx)
	if err != nil {
		return nil, err
	}
	return &data.Flavor, nil
}

// ListServersDetailsRequest Request Object
type ListServersDetailsRequest struct {
	// 查询绑定某个企业项目的弹性云服务器。  若需要查询当前用户所有企业项目绑定的弹性云服务，请传参all_granted_eps。
	EnterpriseProjectId *string `json:"enterprise_project_id,omitempty"`

	// 云服务器规格ID,已上线的规格请参见《弹性云服务器用户指南》的“实例和应用场景”章节。
	Flavor *string `json:"flavor,omitempty"`

	// IPv4地址过滤结果，匹配规则为模糊匹配。
	Ip *string `json:"ip,omitempty"`

	// 查询返回云服务器当前页面的大小。每页默认值是25，最多返回1000台云服务器的信息。
	Limit *int32 `json:"limit,omitempty"`

	// 云服务器名称，匹配规则为模糊匹配。
	Name *string `json:"name,omitempty"`

	// 查询tag字段中不包含该值的云服务器。
	NotTags *string `json:"not-tags,omitempty"`

	// 页码。 当前页面数，默认为1，取值范围大于等于0。 当取值为0时，系统默认返回第1页，与取值为1时相同。 建议设置该参数大于等于1。
	Offset *int32 `json:"offset,omitempty"`

	// 批量创建弹性云服务器时，指定返回的ID，用于查询本次批量创建的弹性云服务器。
	ReservationId *string `json:"reservation_id,omitempty"`

	// 云服务器状态。  取值范围：  ACTIVE， BUILD，DELETED，ERROR，HARD_REBOOT，MIGRATING，REBOOT，RESIZE，REVERT_RESIZE，SHELVED，SHELVED_OFFLOADED，SHUTOFF，UNKNOWN，VERIFY_RESIZE  只有管理员可以使用“deleted”状态过滤查询已经删除的弹性云服务器。  弹性云服务器状态说明请参考[云服务器状态](https://support.huaweicloud.com/api-ecs/ecs_08_0002.html)
	Status *string `json:"status,omitempty"`

	// 查询tag字段中包含该值的云服务器。
	Tags *string `json:"tags,omitempty"`

	// IPv4地址过滤结果，匹配规则为精确匹配。
	IpEq *string `json:"ip_eq,omitempty"`

	// 云服务器ID，格式为UUID，匹配规则为精确匹配  示例: server_id={id1},{id2}  说明： 在使用server_id作为过滤条件时，不能同时使用其他过滤条件。如果同时指定server_id及其他过滤条件，则以server_id条件为准，其他过滤条件会被忽略 当server_id中含有不存在的云服务器ID时，返回的响应参数中该云服务器ID对应的servers结构体中除了id和fault其它字段均为null 为了避免API的URI过长，建议一次查询的server_id个数不超过100个
	ServerId *string `json:"server_id,omitempty"`

	// 以单页最后一条server的id作为分页标记。
	Marker *string `json:"marker,omitempty"`

	// 期望返回的云服务器信息字段。
	ExpectFields *string `json:"expect_fields,omitempty"`
}

const (
	AllServerExpectFields = "basic,flavor,image_meta,flavor_detail,metadata,addresses,tags,cdrom,vmtools_detail,block_device,vcpu_model,advanced_properties,os_hostname,enterprise_project_id"
)

// ListServersDetailsResponse Response Object
type ListServersDetailsResponse struct {
	// 弹性云服务器的列表总数。
	Count *int32 `json:"count,omitempty"`

	// 弹性云服务器详情列表，具体参照-查询云服务器详情接口。查询级别不同，返回的详情不同。
	Servers        *[]ServerDetail `json:"servers,omitempty"`
	HttpStatusCode int             `json:"-"`
}

type ServerDetail struct {
	model.ServerDetail `json:",inline"`
	ImageMeta          *ServerImageMetadata `json:"image_meta,omitempty"`
	BlockDevice        []ServerBlockDevice  `json:"block_device,omitempty"`
	VMTools            *ServerVMTool        `json:"vmtools,omitempty"`
	Architecture       string               `json:"architecture"`
	CpuVendor          string               `json:"cpu_vendor"`
}

type ServerBlockDevice struct {
	// 磁盘挂载点，即磁盘挂载到弹性云服务器上的路径。
	Device string `json:"device,omitempty"`
	// 磁盘挂载到的弹性云服务器ID。
	ServerId string `json:"serverId,omitempty"`
	// 磁盘大小，单位为GB。
	Size IntegerMayString `json:"size,omitempty"`
	// 磁盘ID。
	Id string `json:"id,omitempty"`
	// 磁盘类型。 volume：云硬盘。
	Type string `json:"type,omitempty"`
	// 磁盘启动标识。
	BootIndex IntegerMayString `json:"bootIndex,omitempty"`
	// 磁盘总线类型。 virtio：VirtIO
	DiskBus string `json:"diskBus,omitempty"`
	// 磁盘驱动类型。 iscsi：iSCSI协议。
	DriverVolumeType string `json:"driver_volume_type,omitempty"`
	// 磁盘的目标Iqn。
	TargetIqn []string `json:"target_iqn,omitempty"`
	// 磁盘的PCI地址。
	PciAddress string `json:"pciAddress,omitempty"`
	// 磁盘名称。
	DeviceName string `json:"device_name,omitempty"`
}

type ServerImageMetadata struct {
	MinRam          string                        `json:"min_ram"`
	MinDisk         string                        `json:"min_disk"`
	ContainerFormat string                        `json:"container_format"`
	Properties      ServerImageMetadataProperties `json:"properties"`
}

type ServerImageMetadataProperties struct {
	OsType                 string `json:"__os_type"`
	RootOrigin             string `json:"__root_origin"`
	ImageType              string `json:"__imagetype"`
	VirtualEnvType         string `json:"virtual_env_type"`
	ImageSourceType        string `json:"__image_source_type"`
	Platform               string `json:"__platform"`
	OsVersion              string `json:"__os_version"`
	OsBit                  string `json:"__os_bit"`
	TempOsVersion          string `json:"__temp_os_version"`
	IsRegistered           string `json:"__isregistered"`
	Architecture           string `json:"architecture"`
	ImgSignature           string `json:"img_signature"`
	ImgDigestValue         string `json:"img_digest_value"`
	ImgSignatureHashMethod string `json:"img_signature_hash_method"`
	ImgSignatureKeyType    string `json:"img_signature_key_type"`
	Id                     string `json:"id"`
	BaseImageRef           string `json:"base_image_ref"`
}

// VMTools状态。
type ServerVMTool struct {
	// not_attached：未挂载。
	IsoAttachmentStatus string `json:"iso_attachment_status"`

	// 弹性云服务器上VMTools的安装版本。
	// err_unknown：未知错误。
	ServerInstalledVersion string `json:"server_installed_version"`

	// 弹性云服务器所在主机上VMTools的安装版本。
	// err_unknown：未知错误。
	HostInstalledVersion string `json:"host_installed_version"`
}

// ListServersDetails 查询云服务器详情列表
//
// 根据用户请求条件从数据库筛选、查询所有的弹性云服务器，并关联相关表获取到弹性云服务器的详细信息。
//
// 该接口支持查询弹性云服务器计费方式，以及是否被冻结。
//
// Please refer to HUAWEI cloud API Explorer for details.
func (c *Client) ListServersDetails(request *ListServersDetailsRequest) (*ListServersDetailsResponse, error) {
	requestDef := ecs.GenReqDefForListServersDetails()
	requestDef.Response = new(ListServersDetailsResponse)
	requestDef.RequestFields = append(requestDef.RequestFields, &def.FieldDef{
		LocationType: def.Query,
		Name:         "ExpectFields",
		JsonTag:      "expect_fields",
	})
	if request.ExpectFields == nil {
		// request.ExpectFields = ptr.To(AllServerExpectFields)
	}
	if resp, err := c.ECS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*ListServersDetailsResponse), nil
	}
}

type ListNovaServersOptions struct{}

type NoveServer map[string]any

func (c *Client) ListNovaServers(ctx context.Context, opts ListNovaServersOptions) ([]NoveServer, error) {
	req := c.ECS_NEXT.Get("/v2.1/{project_id}/servers/detail")
	data := struct {
		Servers []NoveServer `json:"servers"`
	}{}
	if err := req.Return(&data).Send(ctx); err != nil {
		return nil, err
	}
	return data.Servers, nil
}

type JobResponse struct {
	JobID   string `json:"job_id"`
	OrderID string `json:"order_id"`
}

type BatchAttachVolumeRequest struct {
	Count            int                                        `json:"count,omitempty"`
	AttachVolumeList []BatchAttachVolumeRequestAttachVolumeList `json:"attachVolumeList,omitempty"`
}

type BatchAttachVolumeRequestAttachVolumeList struct {
	VolumeID   string `json:"volumeId,omitempty"`
	Address    string `json:"address,omitempty"`
	SystemDisk bool   `json:"system_disk,omitempty"`
}

func (c *Client) BatchAttachVolume(ctx context.Context, serverid string, request BatchAttachVolumeRequest) (*JobResponse, error) {
	ret := &JobResponse{}
	req := c.ECS_NEXT.
		Post(fmt.Sprintf("/v1/{project_id}/cloudservers/%s/batchattachvolume", serverid)).
		JSON(map[string]any{"volumeAttachment": request}).
		Return(ret)
	if err := req.Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}
