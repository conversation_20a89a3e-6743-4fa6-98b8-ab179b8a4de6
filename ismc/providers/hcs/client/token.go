package client

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/impl"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/request"
	iam "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3"
	iammodel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/iam/v3/model"
	"xiaoshiai.cn/common/log"
)

type UsernamePasswordCredentials struct {
	Options    CridentialOptions
	IAM        *iam.IamClient
	TokenCache *iammodel.KeystoneCreateUserTokenByPasswordResponse
}

type CridentialOptions struct {
	Endpoint  string
	Username  string
	Password  string
	ProjectID string
	Domain    string
}

func NewUsernamePasswordCredentials(ctx context.Context, httpclient *impl.DefaultHttpClient, options CridentialOptions) (*UsernamePasswordCredentials, error) {
	c := &UsernamePasswordCredentials{
		Options: options,
		IAM: iam.NewIamClient(
			core.NewHcHttpClient(httpclient).
				WithEndpoints([]string{options.Endpoint}).
				WithErrorHandler(&ConvertErrorErrorHandler{}).
				WithCredential(EmptyCredentials{})),
	}
	token, err := c.getToken(ctx)
	if err != nil {
		return nil, err
	}
	c.TokenCache = token
	go c.runRefreshDaemon(ctx, 10*time.Hour)
	return c, nil
}

// ProcessAuthParams implements auth.ICredential.
func (u *UsernamePasswordCredentials) ProcessAuthParams(httpClient *impl.DefaultHttpClient, region string) auth.ICredential {
	return u
}

// ProcessAuthRequest implements auth.ICredential.
func (u *UsernamePasswordCredentials) ProcessAuthRequest(httpClient *impl.DefaultHttpClient, httpRequest *request.DefaultHttpRequest) (*request.DefaultHttpRequest, error) {
	if u.TokenCache != nil && u.TokenCache.XSubjectToken != nil {
		httpRequest.AddHeaderParam("X-Auth-Token", *u.TokenCache.XSubjectToken)
	}
	if projectid := u.Options.ProjectID; projectid != "" {
		httpRequest.AddHeaderParam("X-Project-Id", projectid)
		httpRequest = httpRequest.Builder().AddAutoFilledPathParam("project_id", projectid).Build()
	}
	return httpRequest, nil
}

func (u *UsernamePasswordCredentials) PreRequest(req *http.Request) error {
	if u.TokenCache != nil && u.TokenCache.XSubjectToken != nil {
		req.Header.Set("X-Auth-Token", *u.TokenCache.XSubjectToken)
	}
	if projectid := u.Options.ProjectID; projectid != "" {
		req.Header.Set("X-Project-Id", projectid)
		req.URL.Path = strings.ReplaceAll(req.URL.Path, "{project_id}", projectid)
	}
	return nil
}

func (u *UsernamePasswordCredentials) getToken(_ context.Context) (*iammodel.KeystoneCreateUserTokenByPasswordResponse, error) {
	req := &iammodel.KeystoneCreateUserTokenByPasswordRequest{
		Body: &iammodel.KeystoneCreateUserTokenByPasswordRequestBody{
			Auth: &iammodel.PwdAuth{
				Identity: &iammodel.PwdIdentity{
					Methods: []iammodel.PwdIdentityMethods{
						iammodel.GetPwdIdentityMethodsEnum().PASSWORD,
					},
					Password: &iammodel.PwdPassword{
						User: &iammodel.PwdPasswordUser{
							Name:     u.Options.Username,
							Password: u.Options.Password,
							Domain:   &iammodel.PwdPasswordUserDomain{Name: u.Options.Domain},
						},
					},
				},
			},
		},
	}
	if u.Options.ProjectID != "" {
		req.Body.Auth.Scope = &iammodel.AuthScope{
			Project: &iammodel.AuthScopeProject{Id: &u.Options.ProjectID},
		}
	}
	resp, err := u.IAM.KeystoneCreateUserTokenByPassword(req)
	if err != nil {
		return nil, err
	}
	if resp.HttpStatusCode != 201 {
		return nil, fmt.Errorf("Failed to get token: %d", resp.HttpStatusCode)
	}
	log.Info("get token success", "resp", resp)
	return resp, nil
}

func (u *UsernamePasswordCredentials) runRefreshDaemon(ctx context.Context, refreshDuration time.Duration) {
	timer := time.NewTimer(refreshDuration)
	for {
		select {
		case <-ctx.Done():
			return
		case <-timer.C:
			token, err := u.getToken(ctx)
			if err != nil {
				log.Error(err, "Failed to refresh token")
				timer.Reset(30 * time.Second)
				continue
			}
			u.TokenCache = token
			timer.Reset(refreshDuration)
		}
	}
}

type EmptyCredentials struct{}

func (e EmptyCredentials) ProcessAuthParams(httpClient *impl.DefaultHttpClient, region string) auth.ICredential {
	return e
}

func (e EmptyCredentials) ProcessAuthRequest(httpClient *impl.DefaultHttpClient, httpRequest *request.DefaultHttpRequest) (*request.DefaultHttpRequest, error) {
	return httpRequest, nil
}
