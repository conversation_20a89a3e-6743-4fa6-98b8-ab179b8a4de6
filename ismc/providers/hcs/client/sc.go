package client

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
)

type Order struct {
	// 订单ID
	OrderID string `json:"order_id"`
	// 母订单ID，购物车提交订单存在子母订单关系
	ParentsID string `json:"parents_id"`
	// 订单类型，取值范围为：apply:申请、clone:克隆、modify:修改、delete:删除、delay:延期、softdelete:加入回收站(软删除)、 restore:从回收站中恢复、snapshot:快照，与服务操作定义类型，最大长度64
	Type string `json:"type"`
	// 服务类型
	ServiceType string `json:"service_type"`
	// 规格参数
	Params string `json:"params"`
	// 订单创建者ID
	CreateUserID string `json:"create_user_id"`
	// 订单创建者名称
	CreateUserName string `json:"create_user_name"`
	// 订单所属的VDC ID
	VdcID string `json:"vdc_id"`
	// 订单所属的VDC 名称
	VdcName string `json:"vdc_name"`
	// 订单状态:handling,approving,processing,reSubmitting,partialSuccessed,failed,successed,closed,timeout
	Status OrderStatus `json:"status"`
	// 订单创建时间
	CreateTime OrderTime `json:"create_time"`
	// 订单提交实施时间
	ImplementTime OrderTime `json:"implement_time"`
	// 订单完成时间
	CompleteTime OrderTime `json:"complete_time"`
	// 资源空间ID
	ProjectID string `json:"project_id"`
	// 资源空间名称
	ProjectName string `json:"project_name"`
	// 资源池ID，最大长度64
	CloudInfraID string `json:"cloud_infra_id"`
	// Product ID
	ProductID string `json:"product_id"`
	// Region ID
	RegionID string `json:"region_id"`
	// 产品参数
	ProductDefinationParams string `json:"product_defination_params"`
	// 资源的到期时间
	Tenancy string `json:"tenancy"`
	// 资源的到期时间
	OriginalTenancy string `json:"original_tenancy"`
	// 时区
	TimeZone string `json:"time_zone"`
	// 订单备注
	Comments string `json:"comments"`
	// 订单关联的合同号
	ContractNumber string `json:"contract_number"`
	// 费率
	RateParams string `json:"rate_params"`
	// 租户id
	DomainID string `json:"domain_id"`
	// 订单延时执行时间
	DelayImplementTime string `json:"delay_implement_time"`
	// 订单超时时间
	TimeoutInterval int32 `json:"timeout_interval"`
	// 是否允许手动提交实施
	AllowSubmitImplement bool `json:"allow_submit_implement"`
	// 两级云订单
	OrderScence string `json:"order_scence"`
	// 企业项目Id
	EnterpriseProjectID string `json:"enterprise_project_id"`
	// 企业项目的名称
	EnterpriseProjectName string `json:"enterprise_project_name"`
}

type OrderStatus string

const (
	OrderStatusHandling         OrderStatus = "handling"
	OrderStatusApproving        OrderStatus = "approving"
	OrderStatusProcessing       OrderStatus = "processing"
	OrderStatusReSubmitting     OrderStatus = "reSubmitting"
	OrderStatusPartialSuccessed OrderStatus = "partialSuccessed"
	OrderStatusFailed           OrderStatus = "failed"
	OrderStatusSuccessed        OrderStatus = "successed"
	OrderStatusClosed           OrderStatus = "closed"
	OrderStatusTimeout          OrderStatus = "timeout"
)

type OrderList struct {
	Total  int     `json:"total"`
	Orders []Order `json:"orders"`
}

type ListOrderRequest struct {
	// CreateUserID 订单创建者id,最大长度36。
	CreateUserID string `json:"create_user_id,omitempty"`
	// CreateUserName 订单创建者名称，最大长度256。
	CreateUserName string `json:"create_user_name,omitempty"`
	// VdcID 当前操作的vdcid，最大长度36。
	VdcID string `json:"vdc_id,omitempty"`
	// OrderID 订单id，最大长度36。
	OrderID string `json:"order_id,omitempty"`
	// ServiceType 订单对应的服务类型，最大长度32。
	ServiceType string `json:"service_type,omitempty"`
	// Status 订单状态：handling：处理中,approving：审批中,processing：实施中,reSubmitting：待提交实施,partialSuccessed：部分成功,failed：失败,successed：成功,closed：关闭,timeout：超时。
	Status OrderStatus `json:"status,omitempty"`
	// Type 订单类型，取值范围为：apply:申请、clone:克隆、modify:修改、delete:删除、delay:延期、softdelete:加入回收站(软删除)、 restore:从回收站中恢复、snapshot:快照，与服务操作定义类型，最大长度64。
	Type string `json:"type,omitempty"`
	// StartTime 查询开始时间，时间格式：2017-12-12 12:00:00。
	StartTime time.Time `json:"start_time,omitempty"`
	// EndTime 查询结束时间，时间格式：2017-12-12 12:00:00。
	EndTime time.Time `json:"end_time,omitempty"`
	// ExtendParams 查询条件参数，json格式，最大长度512，包含的键值为：resource_name、resource_id，例如：{"resource_name":xxx,"resource_id":"xxx"}。
	ExtendParams string `json:"extend_params,omitempty"`
	// Limit 分页查询，每页查询数据条数的最大值，1~50 ，默认值：10。 必需
	Limit int `json:"limit"`
	// Start 分页查询，起始值，0~1000000，默认值：0。 必需
	Start int `json:"start"`
	// SortKey 排序关键字,取值：create_time,tenancy。
	SortKey string `json:"sort_key,omitempty"`
	// SortDir 排序方式升序降序 asc或者desc。
	SortDir string `json:"sort_dir,omitempty"`
}

type OrderTime struct {
	time.Time
}

func (t OrderTime) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%s\"", t.Time.Format(OrderTimeFormat))), nil
}

func (t *OrderTime) UnmarshalJSON(data []byte) error {
	if string(data) == "null" || string(data) == "\"\"" {
		return nil
	}
	if len(data) < 2 {
		return fmt.Errorf("invalid time format: %s", string(data))
	}
	// remove the quotes
	data = data[1 : len(data)-1]
	tt, err := time.Parse(OrderTimeFormat, string(data))
	if err != nil {
		return err
	}
	t.Time = tt
	return nil
}

const OrderTimeFormat = "2006-01-02 15:04:05"

func (r ListOrderRequest) ToValues() url.Values {
	values := make(url.Values)
	if r.Limit > 0 {
		values.Set("limit", strconv.Itoa(r.Limit))
	}
	if r.Start > 0 {
		values.Set("start", strconv.Itoa(r.Start))
	}
	if r.CreateUserID != "" {
		values.Set("create_user_id", r.CreateUserID)
	}
	if r.CreateUserName != "" {
		values.Set("create_user_name", r.CreateUserName)
	}
	if r.VdcID != "" {
		values.Set("vdc_id", r.VdcID)
	}
	if r.OrderID != "" {
		values.Set("order_id", r.OrderID)
	}
	if r.ServiceType != "" {
		values.Set("service_type", string(r.ServiceType))
	}
	if r.Status != "" {
		values.Set("status", string(r.Status))
	}
	if r.Type != "" {
		values.Set("type", r.Type)
	}
	if !r.StartTime.IsZero() {
		values.Set("start_time", r.StartTime.Format(OrderTimeFormat))
	}
	if !r.EndTime.IsZero() {
		values.Set("end_time", r.EndTime.Format(OrderTimeFormat))
	}
	if r.ExtendParams != "" {
		values.Set("extend_params", r.ExtendParams)
	}
	if r.SortKey != "" {
		values.Set("sort_key", r.SortKey)
	}
	if r.SortDir != "" {
		values.Set("sort_dir", r.SortDir)
	}
	return values
}

func (c *Client) ListOrders(ctx context.Context, request ListOrderRequest) (*OrderList, error) {
	ret := &OrderList{}
	if err := c.SC_NEXT.
		Get("/rest/order/v3.0/orders").
		Queries(request.ToValues()).
		Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

func (c *Client) GetOrder(ctx context.Context, orderID string) (*Order, error) {
	ret := &Order{}
	if err := c.SC_NEXT.Get(fmt.Sprintf("/rest/order/v3.0/orders/%s", orderID)).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

type OrderResource struct {
	ID           string              `json:"id"`             // ID
	OrderID      string              `json:"order_id"`       // 订单ID
	ResourceID   string              `json:"resource_id"`    // 订单创建的资源ID
	ResourceName string              `json:"name"`           // 资源名称。
	ResourceType string              `json:"resource_type"`  // 资源的类型(如ECS，EVS等)
	CloudInfraID string              `json:"cloud_infra_id"` // 资源所在的资源池ID
	RegionID     string              `json:"region_id"`      // 资源所在的regionID
	AZID         string              `json:"az_id"`          // 资源所在的可用区ID
	ProjectID    string              `json:"project_id"`     // 资源所在的资源空间ID
	RecordTime   string              `json:"record_time"`    // 记录时间
	OrderType    string              `json:"order_type"`     // 订单类型
	Result       OrderResourceResult `json:"result"`         // 资源的实施的结果(successed：成功，failed：失败,partialSuccessed:部分成功)
	ErrorCode    string              `json:"error_code"`     // 错误码。
	ENMessage    string              `json:"en_message"`     // 英文错误信息。
	ZHMessage    string              `json:"zh_message"`     // 中文错误信息。
	Message      string              `json:"message"`        // 错误信息。格式：{"zh_CN":"中文信息","en_US":"英文信息"}。
	ExpiredTime  string              `json:"expired_time"`   // 到期时间,0表示无限期。格式：yyyy-MM-dd HH:59:59。
}

type OrderResourceResult string

const (
	OrderResourceResultSuccessed        OrderResourceResult = "successed"
	OrderResourceResultFailed           OrderResourceResult = "failed"
	OrderResourceResultPartialSuccessed OrderResourceResult = "partialSuccessed"
)

// 当创建虚拟机之后，会返回一个订单ID，此时虚拟机还未创建出来，所以需要通过订单ID查询订单创建的资源详情
// 并使用该ID获取虚拟机详情
// 通过传入的订单ID,查询订单创建的资源的详情
func (c *Client) GetOrderResources(ctx context.Context, orderID string) ([]OrderResource, error) {
	ret := []OrderResource{}
	if err := c.SC_NEXT.Get(fmt.Sprintf("/rest/order/v3.0/orders/%s/resources", orderID)).Return(&ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

type SubscriptionsRequest struct {
	Subscriptions []SubscriptionRequest `json:"subscriptions"` // 数组最大成员个数：500
}

type SubscriptionOperateType string

const (
	SubscriptionOperateTypeApply      SubscriptionOperateType = "apply"
	SubscriptionOperateTypeClone      SubscriptionOperateType = "clone"
	SubscriptionOperateTypeModify     SubscriptionOperateType = "modify"
	SubscriptionOperateTypeDelete     SubscriptionOperateType = "delete"
	SubscriptionOperateTypeDelay      SubscriptionOperateType = "delay"
	SubscriptionOperateTypeSoftDelete SubscriptionOperateType = "softdelete"
	SubscriptionOperateTypeRestore    SubscriptionOperateType = "restore"
	SubscriptionOperateTypeSnapshot   SubscriptionOperateType = "snapshot"
)

type SubscriptionRequest struct {
	// 订单类型，
	// 取值范围为：apply:申请、clone:克隆、modify:修改、delete:删除、delay:延期、softdelete:加入回收站(软删除)、 restore:从回收站中恢复、snapshot:快照，与服务操作定义类型，最大长度64。
	OperateType SubscriptionOperateType `json:"operate_type"`
	// 服务类型,最大长度32。
	ServiceType ServiceType `json:"service_type"`
	// Region ID,最大长度36。
	RegionID string `json:"region_id"`
	// 是否加入购物车，取值范围为:presave:加入购物车。
	OrderType string `json:"order_type,omitempty"`
	// 产品id,订单类型为：apply必须传，最大长度36。
	ProductID string `json:"product_id,omitempty"`
	// 资源空间ID,订单类型为：apply必须传，最大长度36。
	ProjectID    string `json:"project_id,omitempty"`
	CloudInfraID string `json:"cloud_infra_id,omitempty"`
	Comments     string `json:"comments,omitempty"`
	Tenancy      string `json:"tenancy,omitempty"`
	TimeZone     string `json:"time_zone,omitempty"`
	// 敏感参数,例如：密码等，参数结构云服务感知。最大长度3M。
	SecretParams string `json:"secret_params,omitempty"`
	// 参数，云服务的界面输入参数信息放到这个字段里面。最大长度3M。
	// json结构，要求： 1、申请操作，count属性必选，存放申请的实例数量 2、所有操作中display属性必须存在，
	// 参数，云服务的界面输入参数信息放到这个字段里面。最大长度3M。 json结构，要求：
	// 1、申请操作，count属性必选，存放申请的实例数量
	// 2、所有操作中display属性必须存在，ManageOne会取其中的内容，作为订单详情、操作日志详情呈现给用户。
	// 示例： "display": { "en_US": [{ "type": "string", "value": "Shenzhen Guangdong", "label": "province" }，{ "label": "region", "type": "string", "value": "shenzhen-region-1" }], "zh_CN": [{ "type": "string", "value": "广东省深圳市", "label": "省份" }] }
	// 其中label为呈现给用户的参数名称；type为参数值的类型，暂时固定传string；value为参数具体值。需要以操作当时的语言环境做国际化。 如果是变更操作，变更前的参数放在oldDisplay参数中,结构和要求和display一致；变更后的参数放在display参数中。
	// 3、非申请类订单，需要携带 { ids:[{
	// 	"id": "资源id",
	// 	"tenancy": "有效期可选,（延期类订单必选,有效期是具体时间，请按照当前时区计算为yyyy-MM-dd 23:59:59 并转为UTC时间下发）",
	// 	"service_type": "服务类型，如果某资源的服务类型和外层的service_type不一致的话，需要单独制定服务类型",
	// 	"name": "资源实例的名称"
	//   }] }
	Params             string `json:"params,omitempty"`
	OldSubscriptionID  string `json:"old_subscription_id,omitempty"`
	ForceDelay         bool   `json:"force_delay,omitempty"`
	PartialDelay       bool   `json:"partial_delay,omitempty"`
	RateParams         string `json:"rate_params,omitempty"`
	ContractNumber     string `json:"contract_number,omitempty"`
	IsUpdateTenancy    bool   `json:"is_update_tenancy,omitempty"`
	AttachmentKey      string `json:"attachment_key,omitempty"`
	DelayImplementTime int64  `json:"delay_implement_time,omitempty"`
	TimeoutInterval    int32  `json:"timeout_interval,omitempty"`
	CloudServiceForm   string `json:"cloudServiceForm,omitempty"`
	Quotas             string `json:"quotas,omitempty"`
}

// 订购返回结构
type SubscriptionsResponse struct {
	Purchases     []SubscriptionResponse `json:"purchases"`
	ExpectedTotal int32                  `json:"expected_total"`
	ErrorDetails  string                 `json:"error_details"`
}

type SubscriptionResponse struct {
	SubscriptionID string `json:"subscription_id"`
}

type ServiceType string

const (
	ServiceTypeECS ServiceType = "ecs"
	ServiceTypeEVS ServiceType = "evs"
	ServiceTypeVPC ServiceType = "vpc"
	ServiceTypeRDS ServiceType = "rds"
	ServiceTypeDMS ServiceType = "dms"
	ServiceTypeIMS ServiceType = "ims"
	ServiceTypeCTS ServiceType = "cts"
	ServiceTypeELB ServiceType = "elb"
	ServiceTypeDNS ServiceType = "dns"
	ServiceTypeSMN ServiceType = "smn"
	ServiceTypeIAM ServiceType = "iam"
)

type SimpleSubscription struct {
	OperateType SubscriptionOperateType `json:"operateType,omitempty"`
	ServiceType ServiceType             `json:"serviceType,omitempty"`
	// Params 需要传入的参数结构，会自动转为json
	Params any `json:"params,omitempty"`
	// SecretParams 敏感参数，例如密码等，传入的参数结构，会自动转为json
	SecretParams any `json:"secretParams,omitempty"`
}

func (p *Client) CreateSubscriptionSimple(ctx context.Context, sub SimpleSubscription) (*SubscriptionResponse, error) {
	product, err := p.GetProductByServiceTypeCached(ctx, sub.ServiceType)
	if err != nil {
		return nil, err
	}
	paramdata, err := json.Marshal(sub.Params)
	if err != nil {
		return nil, errors.NewBadRequest(err.Error())
	}
	subscription := SubscriptionRequest{
		OperateType: sub.OperateType,
		ServiceType: product.ServiceType,
		ProjectID:   p.DefaultProjectID,
		RegionID:    product.RegionID,
		ProductID:   product.ProductID,
		Params:      string(paramdata),
		Comments:    "auto created by ismc",
		Tenancy:     "0",
		TimeZone:    "GMT+08:00",
	}
	if sub.SecretParams != nil {
		secparamdata, err := json.Marshal(sub.SecretParams)
		if err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		subscription.SecretParams = string(secparamdata)
	}
	resp, err := p.CreateSubscription(ctx, SubscriptionsRequest{Subscriptions: []SubscriptionRequest{subscription}})
	if err != nil {
		return nil, err
	}
	// 返回第一个订单
	for _, purchase := range resp.Purchases {
		return &purchase, nil
	}
	return &SubscriptionResponse{}, nil
}

func (p *Client) GetProductByServiceTypeCached(ctx context.Context, svc ServiceType) (*ProductDTO, error) {
	for _, product := range p.productsCache {
		if product.ServiceType == svc {
			return ptr.To(product), nil
		}
	}
	resp, err := p.GetProductList(ctx, GetProductListRequest{Limit: 100})
	if err != nil {
		return nil, err
	}
	p.productsCache = resp.Products

	for _, product := range p.productsCache {
		if product.ServiceType == svc {
			return ptr.To(product), nil
		}
	}
	return nil, errors.NewNotFound("product", string(svc))
}

// 创建订购,包括创建弹性云服务器，创建云硬盘等操作都是通过这个接口
func (p *Client) CreateSubscription(ctx context.Context, param SubscriptionsRequest) (*SubscriptionsResponse, error) {
	ret := &SubscriptionsResponse{}
	if err := p.SC_NEXT.Post("/rest/subscription/v3.0/subscriptions").JSON(param).Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// 查询可申请的产品列表的请求查询参数
type ApplyProductListReq struct {
	ServiceType string `json:"service_type"`
	Limit       string `json:"limit"`
	Start       string `json:"start"`
	RegionID    string `json:"region_id,omitempty"`
	Name        string `json:"name,omitempty"`
	// 资源池相关的查询参数，用平铺的json字符串表示。例如 [{"resourcePoolId":"resourcepoolId1","azIdList":["azId1","azId2"]}]
	ResourcePoolCondition string `json:"resource_pool_condition,omitempty"`
	ProjectID             string `json:"project_id,omitempty"`
	FilterKey             string `json:"filter_key,omitempty"`
	FilterValues          string `json:"filter_values,omitempty"`
	ProductType           string `json:"product_type,omitempty"`
}

func (a ApplyProductListReq) ToValues() url.Values {
	values := make(url.Values)
	values.Set("service_type", a.ServiceType)
	values.Set("limit", a.Limit)
	values.Set("start", a.Start)
	if a.RegionID != "" {
		values.Set("region_id", a.RegionID)
	}
	if a.Name != "" {
		values.Set("name", a.Name)
	}
	if a.ProductType != "" {
		values.Set("product_type", a.ProductType)
	}
	if a.ProjectID != "" {
		values.Set("project_id", a.ProjectID)
	}
	if a.ResourcePoolCondition != "" {
		values.Set("resource_pool_condition", a.ResourcePoolCondition)
	}
	if a.FilterKey != "" {
		values.Set("filter_key", a.FilterKey)
	}
	if a.FilterValues != "" {
		values.Set("filter_values", a.FilterValues)
	}
	return values
}

type ProductApplyListResultDTO struct {
	Total    int32         `json:"total"`
	Products []ProductBean `json:"products"`
}

// 查询可申请的产品列表
func (p *Client) GetApplyProductList(ctx context.Context, param ApplyProductListReq) (*ProductApplyListResultDTO, error) {
	hcsApplyProduct := ProductApplyListResultDTO{}
	err := p.SC_NEXT.Get("/rest/product/v3.0/apply/products").Queries(param.ToValues()).Return(&hcsApplyProduct).Send(ctx)
	if err != nil {
		return nil, err
	}
	return &hcsApplyProduct, nil
}

type ProductBean struct {
	ProductBase    `json:",inline"`
	Endpoint       string `json:"endpoint"`
	CreateTopVdcID string `json:"create_top_vdc_id"`
}

type ProductBase struct {
	ProductID string `json:"product_id"`
	// 服务类型。
	ServiceType  ServiceType `json:"service_type"`
	CreateVdcID  string      `json:"create_vdc_id"`
	CreateUserID string      `json:"create_user_id"`
	IconID       string      `json:"icon_id"`
	CatalogID    string      `json:"catalog_id"`
	RegionID     string      `json:"region_id"`
	Params       string      `json:"params"`
	// "{\"en_US\":\"EVS\",\"zh_CN\":\"弹性云硬盘\"}"
	Name            string `json:"name"`
	Description     string `json:"description"`
	CreateTime      int64  `json:"create_time"`
	PublishStatus   string `json:"publish_status"`
	DeletableStatus string `json:"deletable_status"`
	IsDefault       bool   `json:"is_default"`
	ResourcePoolID  string `json:"resource_pool_id"`
	ProjectID       string `json:"project_id"`
	AzID            string `json:"az_id"`
	SecretParams    string `json:"secret_params"`
	ProductType     string `json:"product_type"`
	// "[{\"catalog_id\":\"1\",\"level\":1,\"name\":\"{\\\"zh-cn\\\":\\\"基础云服务\\\",\\\"en-us\\\":\\\"Basic cloud services\\\"}\"},{\"catalog_id\":\"10002\",\"level\":2,\"name\":\"{\\\"zh-cn\\\":\\\"存储\\\",\\\"en-us\\\":\\\"Storage\\\"}\"}]"
	CustomCatalogInfo     string `json:"custom_catalog_info"`
	CustomCatalogID       string `json:"custom_catalog_id"`
	Version               string `json:"version"`
	SupportUser           string `json:"support_user"`
	ChargeUser            string `json:"charge_user"`
	ServiceVendor         string `json:"service_vendor"`
	ApprovalParameterMode string `json:"approval_parameter_mode"`
}

// 查询产品详情
func (p *Client) GetProductDetails(ctx context.Context, productID string) (*ProductBean, error) {
	hcsProduct := &ProductBean{}
	if err := p.SC_NEXT.Get("/rest/product/v3.0/products/detail/{product_id}").Return(hcsProduct).Send(ctx); err != nil {
		return nil, err
	}
	return hcsProduct, nil
}

type ProductListResultDTO struct {
	Total    int32        `json:"total"`
	Products []ProductDTO `json:"products"`
}

type ProductDTO struct {
	ProductBase  `json:",inline"`
	PublishScope string `json:"publish_scope"`
	// "[{\"vdcName\":\"all\",\"vdcId\":\"all\"}]"
	OnlineScope         string `json:"online_scope"`
	OnlineStatus        string `json:"online_status"`
	Approval            bool   `json:"approval"`
	Price               string `json:"price"`
	RegisterServiceType string `json:"register_service_type"`
}

// 查询产品列表请求查询参数
type GetProductListRequest struct {
	// Name 产品名称。
	Name string `json:"name,omitempty"`
	// Limit 页数。兼容老版本limit范围0~9999，但是limit过大时存在性能问题， 建议limit使用100。
	// 必需
	Limit int `json:"limit"`
	// Start 页码。范围1~100000
	// 必需
	Start int `json:"start"`
	// Status 草稿（draft）、未发布（unpublish）、发布（publish）、上线（online）、下线（offline）。
	Status string `json:"status,omitempty"`
	// RegionID 产品所属的regionId。
	RegionID string `json:"region_id,omitempty"`
	// ServiceType 服务类型，服务接入时自定义，例如ecs、evs。
	ServiceType ServiceType `json:"service_type,omitempty"`
	// ProjectID The Project ID。
	ProjectID string `json:"project_id,omitempty"`
	// CatalogID 目录ID。
	CatalogID string `json:"catalog_id,omitempty"`
	// OnlineVdcID Online vdc id。
	OnlineVdcID string `json:"online_vdc_id,omitempty"`
	// RequestSource 请求来源，用来标识调用接口的请求来源，ManageOne前端页面调用传入fromPage，此时返回租户内产品列表
	RequestSource string `json:"request_source,omitempty"`
}

func (p *Client) GetProductByServiceType(ctx context.Context, project string, svc ServiceType) (*ProductDTO, error) {
	list, err := p.GetProductList(ctx, GetProductListRequest{ProjectID: p.DefaultProjectID, ServiceType: svc})
	if err != nil {
		return nil, err
	}
	for _, product := range list.Products {
		if product.ServiceType == svc {
			return &product, nil
		}
	}
	return nil, errors.NewNotFound("product", string(svc))
}

// 查询产品列表
func (p *Client) GetProductList(ctx context.Context, param GetProductListRequest) (*ProductListResultDTO, error) {
	hcsProduct := ProductListResultDTO{}
	req := p.SC_NEXT.Get("/rest/product/v3.0/products")
	if param.Limit != 0 {
		req.Query("limit", strconv.Itoa(param.Limit))
	} else {
		req.Query("limit", "100")
	}
	if param.Start != 0 {
		req.Query("start", strconv.Itoa(param.Start))
	} else {
		req.Query("start", "1")
	}
	if param.Status != "" {
		req.Query("status", param.Status)
	}
	if param.RegionID != "" {
		req.Query("region_id", param.RegionID)
	}
	if param.ServiceType != "" {
		req.Query("service_type", string(param.ServiceType))
	}
	if param.Name != "" {
		req.Query("name", param.Name)
	}
	if param.ProjectID != "" {
		req.Query("project_id", param.ProjectID)
	}
	if param.CatalogID != "" {
		req.Query("catalog_id", param.CatalogID)
	}
	if param.OnlineVdcID != "" {
		req.Query("online_vdc_id", param.OnlineVdcID)
	}
	if param.RequestSource != "" {
		req.Query("request_source", param.RequestSource)
	}
	if err := req.Return(&hcsProduct).Send(ctx); err != nil {
		return nil, err
	}
	return &hcsProduct, nil
}

// 15.46 查询资源空间详情
type QueryProjectDetailV31Resp struct {
	Project QueryProjectDetailV31 `json:"project"`
}

type QueryProjectDetailV31 struct {
	Regions []ProjectRegionInfo `json:"regions"`
}
type ProjectRegionInfo struct {
	RegionID     string                  `json:"region_id"`
	RegionType   string                  `json:"region_type"`
	RegionStatus string                  `json:"region_status"`
	CloudInfras  []ProjectCloudInfraInfo `json:"cloud_infras"`
}
type ProjectCloudInfraInfo struct {
	CloudInfraID     string          `json:"cloud_infra_id"`
	CloudInfraName   string          `json:"cloud_infra_name"`
	CloudInfraType   string          `json:"cloud_infra_type"`
	CloudInfraStatus string          `json:"cloud_infra_status"`
	Azs              []ProjectAzInfo `json:"azs"`
}

type ProjectAzInfo struct {
	AzID     string `json:"az_id"`
	AzName   string `json:"az_name"`
	AzStatus string `json:"az_status"`
}

func (p *Client) GetProjectDetails(ctx context.Context) (*QueryProjectDetailV31Resp, error) {
	return p.getProjectDetails(ctx, p.DefaultProjectID)
}

func (p *Client) getProjectDetails(ctx context.Context, projectid string) (*QueryProjectDetailV31Resp, error) {
	hcsProduct := QueryProjectDetailV31Resp{}
	if err := p.SC_NEXT.Get(fmt.Sprintf("/rest/vdc/v3.1/projects/%s", projectid)).
		Return(&hcsProduct).Send(ctx); err != nil {
		return nil, err
	}
	return &hcsProduct, nil
}

type TenantQuotasResponse struct {
	Total    int            `json:"total"`
	Services []ServiceQuota `json:"services"`
}
type ServiceQuota struct {
	ServiceID   string      `json:"service_id"`
	ServiceName string      `json:"service_name"`
	Quotas      []QuotaData `json:"quotas"`
}

type QuotaData struct {
	RegionID              string  `json:"region_id"`
	RegionName            string  `json:"region_name"`
	CloudInfraID          string  `json:"cloud_infra_id"`
	CloudInfraName        string  `json:"cloud_infra_name"`
	AzID                  string  `json:"az_id"`
	AzName                string  `json:"az_name"`
	ParentID              string  `json:"parent_id"`
	ResourceID            string  `json:"resource_id"`
	ResourceName          string  `json:"resource_name"`
	Unit                  string  `json:"unit"`
	QuotaLimit            int32   `json:"quota_limit"`
	QuotaAllocated        int64   `json:"quota_allocated"`
	QuotaUsed             float64 `json:"quota_used"`
	QuotaLeft             int32   `json:"quota_left"`
	CurrentQuotaAllocated int32   `json:"current_quota_allocated"`
	DownQuotaAllocated    int64   `json:"down_quota_allocated"`
	DownQuotaUsed         float64 `json:"down_quota_used"`
	CurrentQuotaLeft      int32   `json:"current_quota_left"`
}

// /rest/vdc/v3.2/vdcs/{vdc_id}/quotas

type GetVDCQuotaOptions struct {
	AzID string `json:"az_id,omitempty"`
}

func (p *Client) GetVDCQuotas(ctx context.Context, vdcID string, options GetVDCQuotaOptions) (*TenantQuotasResponse, error) {
	quotas := TenantQuotasResponse{}
	req := p.SC_NEXT.Get(fmt.Sprintf("/rest/vdc/v3.2/vdcs/{%s}/quotas", vdcID)).Return(&quotas)
	if options.AzID != "" {
		req.Query("az_id", options.AzID)
	}
	if err := req.Send(ctx); err != nil {
		return nil, err
	}
	return &quotas, nil
}

type VdcListResponse struct {
	Total int32     `json:"total"`
	Vdcs  []VdcList `json:"vdcs"`
}

type VdcList struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

func (p *Client) ListVDCs(ctx context.Context) (*VdcListResponse, error) {
	ret := &VdcListResponse{}
	if err := p.SC_NEXT.Get("/rest/vdc/v3.0/vdcs").Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

type MetricsQueryParam struct {
	Namespace    string `json:"namespace"`
	MetricName   string `json:"metric_name"`
	From         int64  `json:"from"`
	To           int64  `json:"to"`
	Period       int    `json:"period"`
	Filter       string `json:"filter"`
	Dim0         string `json:"dim.0"`
	Dim1         string `json:"dim.1,omitempty"`
	Dim2         string `json:"dim.2,omitempty"`
	SequenceType string `json:"sequenceType,omitempty"`
}

func (m MetricsQueryParam) ToValues() url.Values {
	v := make(url.Values)
	v.Set("namespace", m.Namespace)
	v.Set("metric_name", m.MetricName)
	v.Set("from", strconv.FormatInt(m.From, 10))
	v.Set("to", strconv.FormatInt(m.To, 10))
	v.Set("period", strconv.Itoa(m.Period))
	v.Set("filter", m.Filter)
	v.Set("dim.0", m.Dim0)
	if m.Dim1 != "" {
		v.Set("dim.1", m.Dim1)
	}
	if m.Dim2 != "" {
		v.Set("dim.2", m.Dim2)
	}
	if m.SequenceType != "" {
		v.Set("sequenceType", m.SequenceType)
	}
	return v
}

type MetricsQueryResult struct {
	DataPoints []MetricsQueryDataPoint `json:"datapoints"`
	MetricName string                  `json:"metric_name"`
}
type MetricsQueryDataPoint struct {
	Average   float64 `json:"average"`
	Timestamp int64   `json:"timestamp"`
	Unit      string  `json:"unit"`
}

func (p *Client) GetMetrics(ctx context.Context, options MetricsQueryParam) (*MetricsQueryResult, error) {
	return p.getMetrics(ctx, p.DefaultProjectID, options)
}

func (p *Client) getMetrics(ctx context.Context, project string, options MetricsQueryParam) (*MetricsQueryResult, error) {
	ret := &MetricsQueryResult{}
	if err := p.SC_NEXT_PROJECT.Get(fmt.Sprintf("/V1.0/%s/metric-data", project)).
		Queries(options.ToValues()).
		Return(ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

type HostsystemMetrics struct {
	Metrics []HostsystemMetric `json:"metrics"`
}
type HostsystemMetric struct {
	HostsystemID            string  `json:"hostsystem_id"`
	UsedVCPUCores           float64 `json:"used_vcpu_cores"`
	UsedVCPUCoresPercentage float64 `json:"used_vcpu_percentage"`
	UsedMemory              float64 `json:"used_memory"`
	UsedMemoryPercentage    float64 `json:"used_memory_percentage"`
	UsedDiskSpace           float64 `json:"used_disk_space"`
	UsedDiskSpacePercentage float64 `json:"used_disk_space_percentage"`
}

func (p *Client) GetHostsystemMetrics(ctx context.Context) (*HostsystemMetrics, error) {
	hostsystems, err := p.getCMDBResources(ctx, "SYS_PhysicalHost")
	if err != nil {
		return nil, err
	}
	hm := &HostsystemMetrics{}
	for _, hostsystem := range hostsystems.ObjList {
		totalVCPU, _ := hostsystem["totalVcpuCores"].(float64)
		freeVCPU, _ := hostsystem["freeVcpuCores"].(float64)
		totalMemory, _ := hostsystem["totalVmemoryMB"].(float64)
		freeMemory, _ := hostsystem["freeVmemoryMB"].(float64)
		totalDisk, _ := hostsystem["totalDiskSizeMB"].(float64)
		freeDisk, _ := hostsystem["freeDiskSizeMB"].(float64)
		hm.Metrics = append(hm.Metrics, HostsystemMetric{
			HostsystemID:            hostsystem["resId"].(string),
			UsedVCPUCores:           totalVCPU - freeVCPU, // core
			UsedVCPUCoresPercentage: (totalVCPU - freeVCPU) / totalVCPU * 100,
			UsedMemory:              totalMemory - freeMemory, // MB
			UsedMemoryPercentage:    (totalMemory - freeMemory) / totalMemory * 100,
			UsedDiskSpace:           totalDisk - freeDisk, // MB
			UsedDiskSpacePercentage: (totalDisk - freeDisk) / totalDisk * 100,
		})
	}
	return hm, nil
}

type CMDBResources struct {
	ObjList     []map[string]any `json:"objList"`
	TotalNum    int              `json:"totalNum"`
	PageSize    int              `json:"pageSize"`
	TotalPageNo int              `json:"totalPageNo"`
	CurrentPage int              `json:"currentPage"`
}

func (p *Client) getCMDBResources(ctx context.Context, resourceType string) (*CMDBResources, error) {
	ret := &CMDBResources{}
	if err := p.OC_NEXT.Get(fmt.Sprintf("/rest/cmdb/v1/instances/%s", resourceType)).
		Queries(url.Values{
			"pageNo":   []string{"1"},
			"pageSize": []string{"1000"},
		}).
		Return(&ret).Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

// /rest/resource-lifecycle/v3.0/records/basic-resource
type BasicResourcesResult struct {
	Total     int32            `json:"total"`
	Resources []ResourceResult `json:"resources"`
}

type ResourceResult struct {
	ResourceID            string         `json:"resource_id"`
	ResourceName          string         `json:"resource_name"`
	ResourceType          string         `json:"resource_type"`
	ResourceStatus        string         `json:"resource_status"`
	ProjectID             string         `json:"project_id"`
	ProductID             string         `json:"product_id"`
	RegionID              string         `json:"region_id"`
	AzID                  string         `json:"az_id"`
	CloudInfraID          string         `json:"cloud_infra_id"`
	EnterpriseProjectID   string         `json:"enterprise_project_id"`
	EnterpriseProjectName string         `json:"enterprise_project_name"`
	IsDelEpsProjectRel    bool           `json:"is_del_eps_project_rel"`
	ApplicationNameList   []string       `json:"application_name_list"`
	VdcName               string         `json:"vdc_name"`
	Extend                map[string]any `json:"extend"`
}

const AllBasicResourceTypes = "nfviecs,nfvievs,nfvieip,vrmecs,vrmevs,vrmeip,fcecs,fcevs,hwsecs,hwsevs,hwseip,crsecs,crsevs,crseip,ecs,evs,eip,vmwareecs,vmwareevs,hypervecs,hypervevs,vpc,sc"

type BasicResourcesRequest struct {
	// resource_type	是	string	无	资源类型（具体值请在这个接口查询：/rest/application/v3.0/application-configs）。
	ResourceType string `json:"resource_type"`
	// start	否	string	默认值：0	起始分页，默认值为0。
	Start int `json:"start"`
	// limit	否	string	默认值：10	分页查询，每页查询数据条数的最大值，1~50 ，默认值：10。
	Limit int `json:"limit"`
	// filter	否	string	无	过滤参数（格式为：{"name":"123", "id":123"}，并且需要url加密）。
	Filter string `json:"filter"`
	// project_id	否	string	无	资源空间 ID。
	ProjectID string `json:"project_id"`
	// app_id	否	string	无	应用ID（长度32）。
	AppID string `json:"app_id"`
}

func (b BasicResourcesRequest) ToValues() url.Values {
	v := make(url.Values)
	v.Set("resource_type", b.ResourceType)
	if b.Start != 0 {
		v.Set("start", strconv.Itoa(b.Start))
	}
	if b.Limit != 0 {
		v.Set("limit", strconv.Itoa(b.Limit))
	}
	if b.Filter != "" {
		v.Set("filter", b.Filter)
	}
	if b.ProjectID != "" {
		v.Set("project_id", b.ProjectID)
	}
	if b.AppID != "" {
		v.Set("app_id", b.AppID)
	}
	return v
}

// 1.1 查询可添加到应用的资源
func (p *Client) GetBasicResources(ctx context.Context, request BasicResourcesRequest) (*BasicResourcesResult, error) {
	ret := &BasicResourcesResult{}
	if err := p.SC_NEXT.Get("/rest/resource-lifecycle/v3.0/records/basic-resource").
		Queries(request.ToValues()).
		Return(ret).
		Send(ctx); err != nil {
		return nil, err
	}
	return ret, nil
}

func (p *Client) VDCDeleteEnterpriseProject(ctx context.Context, enterpriseProjectID string) error {
	return p.SC_NEXT.Delete(fmt.Sprintf("/rest/vdc/v3.2/enterprise-projects/%s", enterpriseProjectID)).Send(ctx)
}
