package client

const (
	// CLOUD_VM  cpuUsage float  %  CPU使用率
	IndicatorId_CLOUD_VM_CPUUsage IndicatorId = "562958543421441"
	// CLOUD_VM  nicByteIn float  KB/s  网络流入速率
	IndicatorId_CLOUD_VM_NicByteIn IndicatorId = "562958543552537"
	// CLOUD_VM  nicByteOut float  KB/s  网络流出速率
	IndicatorId_CLOUD_VM_NicByteOut IndicatorId = "562958543552538"
	// CLOUD_VM  diskIoIn float  KB/s  云硬盘IO写入
	IndicatorId_CLOUD_VM_DiskIoIn IndicatorId = "562958543618061"
	// CLOUD_VM  diskIoOut float  KB/s  云硬盘IO读出
	IndicatorId_CLOUD_VM_DiskIoOut IndicatorId = "562958543618062"
	// CLOUD_VM  cpu_credit_usage float  Credits  CPU额度使用
	IndicatorId_CLOUD_VM_CPUCreditUsage IndicatorId = "562958543618079"
	// CLOUD_VM  cpu_credit_balance float  Credits  CPU额度余额
	IndicatorId_CLOUD_VM_CPUCreditBalance IndicatorId = "562958543618080"
	// CLOUD_VM  disk_read_requests_rate float  requests/s  磁盘读操作速率
	IndicatorId_CLOUD_VM_DiskReadRequestsRate IndicatorId = "562958543618072"
	// CLOUD_VM  disk_write_requests_rate float  requests/s  磁盘写操作速率
	IndicatorId_CLOUD_VM_DiskWriteRequestsRate IndicatorId = "562958543618073"
	// CLOUD_VM  cpuCreditsRemaining float  Count  剩余CPU信用额度
	IndicatorId_CLOUD_VM_CPUCreditsRemaining IndicatorId = "562958543618081"
	// CLOUD_VM  cpuCreditsConsumed float  Count  已用CPU信用额度
	IndicatorId_CLOUD_VM_CPUCreditsConsumed IndicatorId = "562958543618082"
	// CLOUD_VM  memoryUsage float  %  内存使用率
	IndicatorId_CLOUD_VM_MemoryUsage IndicatorId = "562958543486979"
	// CLOUD_VM  nicByteInOut float  KB/s  网络平均带宽
	IndicatorId_CLOUD_VM_NicByteInOut IndicatorId = "562958543552539"
	// CLOUD_VM  nicDropPercent float  %  网络丢包百分比
	IndicatorId_CLOUD_VM_NicDropPercent IndicatorId = "562958543552550"
	// CLOUD_VM  diskUsage float  %  云硬盘使用率
	IndicatorId_CLOUD_VM_DiskUsage IndicatorId = "562958543618052"
	// CLOUD_VM  diskIowrTicks float  ms  云硬盘平均写时延
	IndicatorId_CLOUD_VM_DiskIowrTicks IndicatorId = "562958543618067"
	// CLOUD_VM  diskIordTicks float  ms  云硬盘平均读时延
	IndicatorId_CLOUD_VM_DiskIordTicks IndicatorId = "562958543618068"
	// CLOUD_VM  diskTotTicks float  %  云硬盘平均IO利用率
	IndicatorId_CLOUD_VM_DiskTotTicks IndicatorId = "562958543618071"
)

const (
	// CLOUD_VOLUME  diskReadRate float  KB/s  磁盘读速率
	IndicatorId_CLOUD_VOLUME_DiskReadRate IndicatorId = "562967133290497"
	// CLOUD_VOLUME  diskWriteRate float  KB/s  磁盘写速率
	IndicatorId_CLOUD_VOLUME_DiskWriteRate IndicatorId = "562967133290498"
	// CLOUD_VOLUME  diskReadOperationRate float  requests/s  磁盘读操作速率
	IndicatorId_CLOUD_VOLUME_DiskReadOperationRate IndicatorId = "562967133290499"
	// CLOUD_VOLUME  diskWriteOperationRate float  requests/s  磁盘写操作速率
	IndicatorId_CLOUD_VOLUME_DiskWriteOperationRate IndicatorId = "562967133290500"
	// CLOUD_VOLUME  burstBalance float  %  突发余额
	IndicatorId_CLOUD_VOLUME_BurstBalance IndicatorId = "562967133290502"
	// CLOUD_VOLUME  disk_util_inband float  %  云硬盘使用率
	IndicatorId_CLOUD_VOLUME_DiskUtilInband IndicatorId = "562967133290501"
)

const (
	// SYS_NetworkDevice  cpuUsage float  %  网络设备平均CPU利用率
	IndicatorId_SYS_NetworkDevice_CPUUsage IndicatorId = "281479271743489"
	// SYS_NetworkDevice  memoryUsage float  %  网络设备平均内存利用率
	IndicatorId_SYS_NetworkDevice_MemoryUsage IndicatorId = "281479271743490"
	// SYS_NetworkDevice  responseTime float  ms  网络设备响应时间
	IndicatorId_SYS_NetworkDevice_ResponseTime IndicatorId = "281479271743491"
	// SYS_NetworkDevice  unreachablePingPercentage float  %  网络设备当日不可达比率
	IndicatorId_SYS_NetworkDevice_UnreachablePingPercentage IndicatorId = "281479271743492"
	// SYS_NetworkDevice  sessionEstablishRate float  record/s  网络设备当前会话新建速率
	IndicatorId_SYS_NetworkDevice_SessionEstablishRate IndicatorId = "281479271809025"
	// SYS_NetworkDevice  sessions float  record  网络设备当前会话总数
	IndicatorId_SYS_NetworkDevice_Sessions IndicatorId = "281479271809026"
	// SYS_NetworkDevice  networkTraffic float  KB/s  网络设备网络流量值
	IndicatorId_SYS_NetworkDevice_NetworkTraffic IndicatorId = "281479271809029"
)

const (
	// SYS_NetworkDevicePort receiveRate	decimal	Mb/s	接口接收速率
	IndicatorId_SYS_NetworkDevicePort_ReceiveRate IndicatorId = "281483566710785"
	// SYS_NetworkDevicePort sendRate	decimal	Mb/s	接口发送速率
	IndicatorId_SYS_NetworkDevicePort_SendRate IndicatorId = "281483566710786"
	// SYS_NetworkDevicePort bandwidthUsageIn	decimal	%	接口流入带宽利用率
	IndicatorId_SYS_NetworkDevicePort_BandwidthUsageIn IndicatorId = "281483566710787"
	// SYS_NetworkDevicePort bandwidthUsageOut	decimal	%	接口流出带宽利用率
	IndicatorId_SYS_NetworkDevicePort_BandwidthUsageOut IndicatorId = "281483566710788"
	// SYS_NetworkDevicePort discardPercentageIn	decimal	%	接口接收包丢弃率
	IndicatorId_SYS_NetworkDevicePort_DiscardPercentageIn IndicatorId = "281483566776321"
	// SYS_NetworkDevicePort discardPercentageOut	decimal	%	接口发送包丢弃率
	IndicatorId_SYS_NetworkDevicePort_DiscardPercentageOut IndicatorId = "281483566776322"
	// SYS_NetworkDevicePort discardPacketOut	decimal	packets	接口丢弃发送包数
	IndicatorId_SYS_NetworkDevicePort_DiscardPacketOut IndicatorId = "281483566776323"
	// SYS_NetworkDevicePort discardPacketIn	decimal	packets	接口丢弃接收包数
	IndicatorId_SYS_NetworkDevicePort_DiscardPacketIn IndicatorId = "281483566776324"
	// SYS_NetworkDevicePort errorPercentageOut	decimal	%	接口发送包错误率
	IndicatorId_SYS_NetworkDevicePort_ErrorPercentageOut IndicatorId = "281483566776325"
	// SYS_NetworkDevicePort errorPercentageIn	decimal	%	接口接收包错误率
	IndicatorId_SYS_NetworkDevicePort_ErrorPercentageIn IndicatorId = "281483566776326"
	// SYS_NetworkDevicePort packetOut	decimal	packets	接口发送包数
	IndicatorId_SYS_NetworkDevicePort_PacketOut IndicatorId = "281483566776327"
	// SYS_NetworkDevicePort packetIn	decimal	packets	接口接收包数
	IndicatorId_SYS_NetworkDevicePort_PacketIn IndicatorId = "281483566776328"
	// SYS_NetworkDevicePort packetRateIn	decimal	packets/s	接口接收包速率
	IndicatorId_SYS_NetworkDevicePort_PacketRateIn IndicatorId = "281483566776329"
	// SYS_NetworkDevicePort packetRateOut	decimal	packets/s	接口发送包速率
	IndicatorId_SYS_NetworkDevicePort_PacketRateOut IndicatorId = "281483566776330"
	// SYS_NetworkDevicePort errorPacketOut	decimal	packets	接口发送错误包数
	IndicatorId_SYS_NetworkDevicePort_ErrorPacketOut IndicatorId = "281483566776331"
	// SYS_NetworkDevicePort errorPacketIn	decimal	packets	接口接收错误包数
	IndicatorId_SYS_NetworkDevicePort_ErrorPacketIn IndicatorId = "281483566776332"
	// SYS_NetworkDevicePort byteOut	decimal	b	接口发送字节数
	IndicatorId_SYS_NetworkDevicePort_ByteOut IndicatorId = "281483566776333"
	// SYS_NetworkDevicePort byteIn	decimal	b	接口接收字节数
	IndicatorId_SYS_NetworkDevicePort_ByteIn IndicatorId = "281483566776334"
)

const (
	// SYS_PhysicalHost cpuUsage float  %  CPU使用率
	IndicatorId_SYS_PhysicalHost_CPUUsage IndicatorId = "1407379178586113"
	// SYS_PhysicalHost memoryUsage float  %  内存使用率
	IndicatorId_SYS_PhysicalHost_MemoryUsage IndicatorId = "1407379178651656"
	// SYS_PhysicalHost nicByteIn float  KB/s  网络流入速率
	IndicatorId_SYS_PhysicalHost_NicByteIn IndicatorId = "1407379178717195"
	// SYS_PhysicalHost nicByteOut float  KB/s  网络流出速率
	IndicatorId_SYS_PhysicalHost_NicByteOut IndicatorId = "1407379178717196"
	// SYS_PhysicalHost nicDropPercent float  %  网络丢包百分比
	IndicatorId_SYS_PhysicalHost_NicDropPercent IndicatorId = "1407379178717197"
	// SYS_PhysicalHost diskIoIn float  KB/s  磁盘IO写入
	IndicatorId_SYS_PhysicalHost_DiskIoIn IndicatorId = "1407379178782738"
	// SYS_PhysicalHost diskIoOut float  KB/s  磁盘IO读出
	IndicatorId_SYS_PhysicalHost_DiskIoOut IndicatorId = "1407379178782739"
	// SYS_PhysicalHost diskUsage float  %  磁盘使用率
	IndicatorId_SYS_PhysicalHost_DiskUsage IndicatorId = "1407379178782740"
	// SYS_PhysicalHost diskIopsWrite float  request/s  磁盘写IOPS
	IndicatorId_SYS_PhysicalHost_DiskIopsWrite IndicatorId = "1407379178782741"
	// SYS_PhysicalHost diskIopsRead float  request/s  磁盘读IOPS
	IndicatorId_SYS_PhysicalHost_DiskIopsRead IndicatorId = "1407379178782742"
	// SYS_PhysicalHost host_disk_files_usage float  %  文件系统利用率
	IndicatorId_SYS_PhysicalHost_HostDiskFilesUsage IndicatorId = "1407379178782743"
	// SYS_PhysicalHost host_disk_files_total float  GB  文件系统总容量
	IndicatorId_SYS_PhysicalHost_HostDiskFilesTotal IndicatorId = "1407379178782744"
	// SYS_PhysicalHost host_disk_files_used float  GB  文件系统已用容量
	IndicatorId_SYS_PhysicalHost_HostDiskFilesUsed IndicatorId = "1407379178782745"
	// SYS_PhysicalHost nicPkgSend float  r/s  网络发送包速
	IndicatorId_SYS_PhysicalHost_NicPkgSend IndicatorId = "1407379178717198"
	// SYS_PhysicalHost nicPkgRcv float  r/s  网络接收包速
	IndicatorId_SYS_PhysicalHost_NicPkgRcv IndicatorId = "1407379178717199"
)
