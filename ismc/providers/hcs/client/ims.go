package client

import (
	"context"
	"fmt"
	"io"
	"net/textproto"
	"reflect"
	"unsafe"

	ims "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ims/v2"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ims/v2/model"
	"xiaoshiai.cn/common/httpclient"
)

func NewGlanceCreateImageMetadataRequestBodyDiskFormat(typ string) model.GlanceCreateImageMetadataRequestBodyDiskFormat {
	ret := model.GlanceCreateImageMetadataRequestBodyDiskFormat{}
	field := reflect.ValueOf(&ret).Elem().Field(0)
	reflect.NewAt(field.Type(), unsafe.Pointer(field.UnsafeAddr())).Elem().SetString(typ)
	return ret
}

type ListImagesResponse struct {
	Images         []ImageInfo `json:"images,omitempty"`
	HttpStatusCode int         `json:"-"`
}

type ImageInfo struct {
	model.ImageInfo `json:",inline"`
	// <PERSON>ze overloads the Size field in ImageInfo which is a pointer to int32
	// image size too large to overflow int32
	Size int64 `json:"size,omitempty"`

	// VirtualSize overloads the VirtualSize field in ImageInfo
	VirtualSize int64 `json:"virtual_size,omitempty"`
}

func (c *Client) ListImages(request *model.ListImagesRequest) (*ListImagesResponse, error) {
	requestDef := ims.GenReqDefForListImages()
	requestDef.Response = &ListImagesResponse{}
	resp, err := c.IMS.HcClient.Sync(request, requestDef)
	if err != nil {
		return nil, err
	}
	return resp.(*ListImagesResponse), nil
}

type UploadImageFileRequest struct {
	ImageId       string
	Content       io.Reader
	Filename      string
	ContentType   string
	ContentLength int64
}

func (c *Client) UploadImageFile(ctx context.Context, request *UploadImageFileRequest) error {
	fileheader := make(textproto.MIMEHeader)
	fileheader.Set("Content-Type", request.ContentType)
	if request.ContentLength > 0 {
		fileheader.Set("Content-Length", fmt.Sprintf("%d", request.ContentLength))
	}
	return c.IMS_NEXT.
		Put(fmt.Sprintf("/v2/images/%s/file", request.ImageId)).
		MultiFormDataStream([]httpclient.MultiFormPart{
			{
				FieldName: "file",
				FileName:  request.Filename,
				Reader:    request.Content,
				Header:    fileheader,
			},
		}).
		Send(ctx)
}

// GlanceCreateImageMetadataRequest Request Object
type GlanceCreateImageMetadataRequest struct {
	Body *GlanceCreateImageMetadataRequestBody `json:"body,omitempty"`
}

type GlanceCreateImageMetadataRequestBody struct {
	// 操作系统类型，目前取值Linux，Windows，Other。
	OSType string `json:"__os_type,omitempty"`

	// 镜像的操作系统具体版本,如果未指定__os_version，则默认设置为Other Linux(64 bit)，不保证该镜像能成功创建虚拟机以及通过该镜像创建的虚拟机能够正常使用。
	OsVersion *string `json:"__os_version,omitempty"`

	// 容器格式。默认取值为bare。
	ContainerFormat *string `json:"container_format,omitempty"`

	// 镜像文件格式。目前支持vhd，zvhd、zvhd2、raw，qcow2。默认取值为vhd
	DiskFormat *model.GlanceCreateImageMetadataRequestBodyDiskFormat `json:"disk_format,omitempty"`

	// 镜像运行需要的最小磁盘，单位为GB 。必须大于镜像系统盘容量，否则创建云主机云服务器可能失败。
	MinDisk *int32 `json:"min_disk,omitempty"`

	// 镜像运行需要的最小内存，单位为MB。参数取值依据云主机云服务器的规格限制。默认取值为0。
	MinRam *int32 `json:"min_ram,omitempty"`

	// 镜像名称，如果未指定name的取值，则默认为空，但是使用该镜像创建虚拟机会失败。名称的长度为1-255位。
	Name *string `json:"name,omitempty"`

	// 镜像是否被保护，保护后的镜像不可删除。默认取值为false。
	Protected *bool `json:"protected,omitempty"`

	// 镜像标签列表。长度为1-255位。默认为空。
	Tags *[]string `json:"tags,omitempty"`

	// 其他租户是否可见。默认取值为private。创建镜像元数据时，visibility取值只能为private。
	Visibility *string `json:"visibility,omitempty"`

	// 	Platform String 镜像平台分类。
	// 枚举值：
	// ● Windows
	// ● Ubuntu
	// ● RedHat
	// ● SUSE
	// ● CentOS
	// ● Debian
	// ● OpenSUSE
	// ● Oracle Linux
	// ● Fedora
	// ● Other
	// ● CoreOS
	// ● EulerOS
	Platform string `json:"__platform,omitempty"`

	// 操作系统位数，一般取值为“32”或者“64”。
	OSBit string `json:"__os_bit,omitempty"`

	// CPU架构类型，取值为x86_64或aarch64。
	Architecture string `json:"architecture,omitempty"`

	// 镜像的密级，取值为secret(机密)、confidential(秘密)、internal(内部)。
	// 	枚举值：
	// 	● secret
	// 	● confidential
	// 	● internal
	SecurityLevel string `json:"security_level,omitempty"`
}

// GlanceCreateImageMetadata 创建镜像元数据（OpenStack原生）
//
// 创建镜像元数据。调用创建镜像元数据接口成功后，只是创建了镜像的元数据，镜像对应的实际镜像文件并不存在
//
// Please refer to HUAWEI cloud API Explorer for details.
func (c *Client) GlanceCreateImageMetadata(request *GlanceCreateImageMetadataRequest) (*model.GlanceCreateImageMetadataResponse, error) {
	requestDef := ims.GenReqDefForGlanceCreateImageMetadata()

	if resp, err := c.IMS.HcClient.Sync(request, requestDef); err != nil {
		return nil, err
	} else {
		return resp.(*model.GlanceCreateImageMetadataResponse), nil
	}
}
