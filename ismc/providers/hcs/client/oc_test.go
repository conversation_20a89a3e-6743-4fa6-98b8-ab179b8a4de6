package client

import (
	"context"
	"fmt"
	"testing"
)

func TestClient_ListSystemResourceModel(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	ret := map[string]any{}

	err := cli.ListSystemResourceModel(ctx, SYS_PhysicalHost, ListResourceModelRequest{}, &ret)
	if err != nil {
		fmt.Println(err)
		return
	}
	t.Logf("list: %v", ret)
}

func TestClient_ListSysStoragePool(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()
	ret, err := cli.SysStoragePool(ctx, ListResourceModelRequest{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(ret)
}

func TestClient_ListCloudVM(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	vmlist, err := cli.ListCloudVM(ctx, ListResourceModelRequest{})
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Logf("list: %v", vmlist)
}

func TestClient_ListSystemPhysicalHost(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	hosts, err := cli.ListSystemPhysicalHost(ctx, ListResourceModelRequest{})
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Logf("list: %v", hosts)
}

func TestClient_ListCloudVolume(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	vols, err := cli.ListCloudVolume(ctx, ListResourceModelRequest{})
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Logf("list: %v", vols)
}

func TestClient_StatCloudResource(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	resp, err := cli.StatCloudResource(ctx, AnalysisDatasetsOptions{})
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Logf("list: %v", resp)
}

func TestClient_TenantResourceStatistics(t *testing.T) {
	cli := setupTestClient(t)
	ctx := context.Background()

	resp, err := cli.StatisticsTenantResource(ctx, CLOUD_VOLUME, StatisticsTenantResourceRequest{})
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Logf("list: %v", resp)
}
