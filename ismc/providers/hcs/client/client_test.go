package client

import (
	"context"
	"testing"
)

func setupTestClient(t *testing.T) *Client {
	options := Options{
		Region:         "majnoon-dccloud-1",
		GlobalDomain:   "majnoon-dccloud.com",
		Domain:         "xiaoshiai",
		UserName:       "xiaoshiai",
		Password:       "",
		Proxy:          "http://192.168.0.40:8089",
		DefaultProject: "majnoon-dccloud-1_xiaoshiai",
	}
	ctx := context.Background()
	cli, err := NewClient(ctx, &options)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("failed to create client: %v", err)
		t.FailNow()
	}
	return cli
}

func TestNewClient(t *testing.T) {
	ctx := context.Background()
	cli := setupTestClient(t)
	if cli == nil {
		t.Error("client is nil")
	}
	info, err := cli.CurrentUserInfo(ctx)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("failed to get current user info: %v", err)
		t.<PERSON>ail<PERSON>ow()
	}
	t.Logf("current user info: %v", info)
}
