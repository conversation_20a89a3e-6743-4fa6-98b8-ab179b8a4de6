package bob

import (
	"context"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

var _ common.ZoneOperation = &Provider{}

// GetZone implements common.RegionOperation.
func (p *Provider) GetZone(ctx context.Context, id string) (*common.Zone, error) {
	zoneslist, err := p.ListZones(ctx, common.ListZoneOptions{})
	if err != nil {
		return nil, err
	}
	for _, zone := range zoneslist.Items {
		if zone.ID == id {
			return &zone, nil
		}
	}
	return nil, errors.NewNotFound("zone", id)
}

// ListZones implements common.RegionOperation.
func (p *Provider) ListZones(ctx context.Context, option common.ListZoneOptions) (common.List[common.Zone], error) {
	azs, err := p.Client.GetProjectDetails(ctx)
	if err != nil {
		return common.List[common.Zone]{}, err
	}
	var as []client.ProjectAzInfo
	for _, region := range azs.Project.Regions {
		for _, ci := range region.CloudInfras {
			as = append(as, ci.Azs...)
		}
	}
	zones := make([]common.Zone, len(as))
	for i, zone := range as {
		zones[i] = coverHCSZoneToZone(zone)
	}
	return common.PageList(zones, option.ListOptions), nil
}

func coverHCSZoneToZone(hcs client.ProjectAzInfo) common.Zone {
	return common.Zone{
		Descripter: common.Descripter{
			ID:   hcs.AzID,
			Name: hcs.AzName,
		},
		Status: common.ZoneStatus{
			Ready: hcs.AzStatus == "normal",
		},
	}
}
