package bob

import (
	"context"
	"fmt"
	"time"

	modelv2 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v2/model"
	modelv3 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v3/model"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

const AllEnterpriseProject = "all_granted_eps"

var _ common.VirtualNetworkOperation = &Provider{}

var _ common.SecurityGroupOperation = &Provider{}

// enterprise_project_id必须填
func (p *Provider) ListVirtualNetworks(ctx context.Context, options common.ListVirtualNetworkOptions) (common.List[common.VirtualNetwork], error) {
	req := &client.ListVpcsRequest{
		Limit:               ptr.To(int32(2000)),
		EnterpriseProjectID: getEnterpriceProject(options.ResourceGroup),
	}
	// we do not know total count, even use page list
	resp, err := p.Client.ListVpcs(req)
	if err != nil {
		return common.List[common.VirtualNetwork]{}, err
	}
	if resp.Vpcs == nil {
		return common.List[common.VirtualNetwork]{}, nil
	}
	networks := make([]common.VirtualNetwork, len(*resp.Vpcs))
	for i, vpc := range *resp.Vpcs {
		networks[i] = ConvertVPC(vpc)
	}
	if options.Sort == "" {
		// do not sort by default
		options.Sort = "none"
	}
	return common.PageList(networks, options.ListOptions), nil
}

// GetVirtualNetwork implements common.Provider.
func (p *Provider) GetVirtualNetwork(ctx context.Context, id string) (*common.VirtualNetwork, error) {
	resp, err := p.Client.VPCV3.ShowVpc(&modelv3.ShowVpcRequest{VpcId: id})
	if err != nil {
		return nil, err
	}
	_ = resp
	network := ConvertVPC(*resp.Vpc)
	return &network, nil
}

func (p *Provider) getVirtualNetworkByIDOrName(ctx context.Context, nameorid string) (*common.VirtualNetwork, error) {
	resp, err := p.ListVirtualNetworks(ctx, common.ListVirtualNetworkOptions{})
	if err != nil {
		return nil, err
	}
	for _, network := range resp.Items {
		if network.ID == nameorid || network.Name == nameorid {
			return &network, nil
		}
	}
	return nil, fmt.Errorf("no network found with name or id %s", nameorid)
}

func ConvertVPC(vpc modelv3.Vpc) common.VirtualNetwork {
	network := common.VirtualNetwork{
		Descripter: common.Descripter{
			ID:          vpc.Id,
			Name:        vpc.Name,
			Description: vpc.Description,
		},
	}
	if vpc.EnterpriseProjectId != "" && vpc.EnterpriseProjectId != "0" {
		network.ResourceGroup = vpc.EnterpriseProjectId
	}
	if creation := vpc.CreatedAt; creation != nil {
		network.CreationTimestamp = time.Time(*creation)
	}
	switch vpc.Status {
	case "PENDING":
		network.Status.Phase = common.VirtualNetworkPhasePending
	case "ACTIVE", "OK":
		network.Status.Phase = common.VirtualNetworkPhaseReady
	default:
		network.Status.Phase = common.VirtualNetworkPhaseUnknown
	}
	if vpc.Cidr != "" {
		network.IPv4Blocks = append(network.IPv4Blocks, common.CIDR(vpc.Cidr))
	}
	for _, item := range vpc.ExtendCidrs {
		network.IPv4Blocks = append(network.IPv4Blocks, common.CIDR(item))
	}
	return network
}

// DeleteVirtualNetwork implements common.Provider.
func (p *Provider) DeleteVirtualNetwork(ctx context.Context, name string) error {
	resp, err := p.Client.VPCV2.DeleteVpc(&modelv2.DeleteVpcRequest{VpcId: name})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

// CreateVirtualNetwork implements common.Provider.
func (p *Provider) CreateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) (*common.Descripter, error) {
	newvpc := &modelv2.CreateVpcOption{
		Name:        &network.Name,
		Description: &network.Description,
	}
	if len(network.IPv4Blocks) == 0 {
		return nil, errors.NewBadRequest("ipv4 block is required")
	}
	mainblock, extendblocks := network.IPv4Blocks[0], network.IPv4Blocks[1:]
	newvpc.Cidr = ptr.To(string(mainblock))
	if network.ResourceGroup != "" {
		newvpc.EnterpriseProjectId = &network.ResourceGroup
	}
	resp, err := p.Client.VPCV2.CreateVpc(&modelv2.CreateVpcRequest{
		Body: &modelv2.CreateVpcRequestBody{Vpc: newvpc},
	})
	if err != nil {
		return nil, err
	}
	if len(extendblocks) > 0 {
		if err := p.addExtendCidrs(ctx, resp.Vpc.Id, extendblocks); err != nil {
			return nil, err
		}
	}
	_ = resp
	return &common.Descripter{}, nil
}

// UpdateVirtualNetwork implements common.Provider.
func (p *Provider) UpdateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) error {
	if len(network.IPv4Blocks) == 0 {
		return errors.NewBadRequest("ipv4 block is required")
	}
	exists, err := p.GetVirtualNetwork(ctx, network.ID)
	if err != nil {
		return err
	}
	maincidr, extendcidrs := network.IPv4Blocks[0], network.IPv4Blocks[1:]

	update := &modelv2.UpdateVpcOption{
		Name:        &network.Name,
		Description: &network.Description,
		Cidr:        ptr.To(string(maincidr)),
	}
	resp, err := p.Client.VPCV2.UpdateVpc(&modelv2.UpdateVpcRequest{
		VpcId: network.ID,
		Body:  &modelv2.UpdateVpcRequestBody{Vpc: update},
	})
	if err != nil {
		return err
	}
	// update extend cidrs
	var existsextendcidrs []common.CIDR
	if len(exists.IPv4Blocks) > 0 {
		existsextendcidrs = exists.IPv4Blocks[1:]
	}
	adds, dels := diffCidrs(existsextendcidrs, extendcidrs)
	// 先删除再添加,避免添加时超过数量限制
	if len(dels) > 0 {
		if err := p.delExtendCidrs(ctx, network.ID, dels); err != nil {
			return err
		}
	}
	if len(adds) > 0 {
		if err := p.addExtendCidrs(ctx, network.ID, adds); err != nil {
			return err
		}
	}
	_ = resp
	return nil
}

func diffCidrs(from, to []common.CIDR) (add, del []common.CIDR) {
	a, b := sets.New(from...), sets.New(to...)
	add = b.Difference(a).UnsortedList()
	del = a.Difference(b).UnsortedList()
	return add, del
}

func (p *Provider) addExtendCidrs(_ context.Context, vpcid string, cirds []common.CIDR) error {
	extendcidrs := []string{}
	for _, block := range cirds {
		extendcidrs = append(extendcidrs, string(block))
	}
	extendresp, err := p.Client.VPCV3.AddVpcExtendCidr(&modelv3.AddVpcExtendCidrRequest{
		VpcId: vpcid,
		Body:  &modelv3.AddVpcExtendCidrRequestBody{Vpc: &modelv3.AddExtendCidrOption{ExtendCidrs: extendcidrs}},
	})
	if err != nil {
		return err
	}
	_ = extendresp
	return nil
}

func (p *Provider) delExtendCidrs(_ context.Context, vpcid string, cirds []common.CIDR) error {
	extendcidrs := []string{}
	for _, block := range cirds {
		extendcidrs = append(extendcidrs, string(block))
	}
	extendresp, err := p.Client.VPCV3.RemoveVpcExtendCidr(&modelv3.RemoveVpcExtendCidrRequest{
		VpcId: vpcid,
		Body: &modelv3.RemoveVpcExtendCidrRequestBody{
			Vpc: &modelv3.RemoveExtendCidrOption{
				ExtendCidrs: extendcidrs,
			},
		},
	})
	if err != nil {
		return err
	}
	_ = extendresp
	return nil
}

// ListVirtualSubnetwork implements common.Provider.
func (p *Provider) ListVirtualSubnetwork(ctx context.Context, network string, options common.ListVirtualSubnetworkOptions) (common.List[common.VirtualSubnetwork], error) {
	resp, err := p.Client.VPCV2.ListSubnets(&modelv2.ListSubnetsRequest{VpcId: &network})
	if err != nil {
		return common.List[common.VirtualSubnetwork]{}, err
	}
	if resp.Subnets == nil {
		return common.List[common.VirtualSubnetwork]{}, nil
	}
	subnetworks := []common.VirtualSubnetwork{}
	for _, subnet := range *resp.Subnets {
		subnetworks = append(subnetworks, ConvertSubnet(subnet))
	}
	return common.PageList(subnetworks, options.ListOptions), nil
}

func ConvertSubnet(subnet modelv2.Subnet) common.VirtualSubnetwork {
	subnetwork := common.VirtualSubnetwork{
		Descripter: common.Descripter{
			ID:          subnet.Id,
			Name:        subnet.Name,
			Description: subnet.Description,
		},
		Zone:           subnet.AvailabilityZone,
		VirtualNetwork: subnet.VpcId,
		IPv4Blocks:     []string{subnet.Cidr},
		GateWay:        subnet.GatewayIp,
		GateWay6:       subnet.GatewayIpV6,
		DHCP:           common.VirtualSubnetworkDHCP{Enabled: subnet.DhcpEnable},
	}
	switch subnet.Status {
	case modelv2.GetSubnetStatusEnum().ACTIVE:
		subnetwork.Status.Phase = common.VirtualSwitchPhaseReady
	case modelv2.GetSubnetStatusEnum().ERROR:
		subnetwork.Status.Phase = common.VirtualSwitchPhaseError
	case modelv2.GetSubnetStatusEnum().UNKNOWN:
		subnetwork.Status.Phase = common.VirtualSwitchPhaseUnknown
	}
	subnetwork.DNSServers = append(subnetwork.DNSServers, subnet.DnsList...)
	if subnet.CidrV6 != "" {
		subnetwork.IPv6Blocks = append(subnetwork.IPv6Blocks, subnet.CidrV6)
	}
	if subnet.CreatedAt != nil {
		subnetwork.CreationTimestamp = time.Time(*subnet.CreatedAt)
	}
	return subnetwork
}

// ListVirtualSubnetworkAllocations implements common.Provider.
func (p *Provider) ListVirtualSubnetworkAllocations(ctx context.Context, network string, subnet string, options common.ListVirtualSwitchAllocationOptions) (common.List[common.VirtualSubnetworkAllocation], error) {
	req := &modelv2.ListPortsRequest{
		NetworkId:           &subnet,
		EnterpriseProjectId: getEnterpriceProject(options.ResourceGroup),
	}
	req.Limit, req.Marker = getLimitMarker(options.ListOptions)

	resp, err := p.Client.VPCV2.ListPorts(req)
	if err != nil {
		return common.List[common.VirtualSubnetworkAllocation]{}, err
	}
	if resp.Ports == nil {
		return common.List[common.VirtualSubnetworkAllocation]{}, nil
	}
	allocations := []common.VirtualSubnetworkAllocation{}
	for _, item := range *resp.Ports {
		allocations = append(allocations, ConvertNetworkPort(item))
	}
	return toContinueList(allocations, options.ListOptions), nil
}

func ConvertNetworkPort(port modelv2.Port) common.VirtualSubnetworkAllocation {
	ret := common.VirtualSubnetworkAllocation{
		Descripter: common.Descripter{
			ID:   port.Id,
			Name: port.Name,
		},
		Zone: port.ZoneId,
		MAC:  port.MacAddress,
	}
	if len(port.FixedIps) > 0 {
		if fixip := port.FixedIps[0].IpAddress; fixip != nil {
			ret.IPv4 = *fixip
		}
		if subnet := port.FixedIps[0].SubnetId; subnet != nil {
			ret.VirtualSubnetwork = *subnet
		}
	}
	return ret
}

// GetVirtualSubnetwork implements common.Provider.
func (p *Provider) GetVirtualSubnetwork(ctx context.Context, network string, subnetid string) (*common.VirtualSubnetwork, error) {
	resp, err := p.Client.VPCV2.ShowSubnet(&modelv2.ShowSubnetRequest{SubnetId: subnetid})
	if err != nil {
		return nil, err
	}
	subnetwork := ConvertSubnet(*resp.Subnet)
	return &subnetwork, nil
}

// CreateVirtualSubnetwork implements common.Provider.
func (p *Provider) CreateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) (*common.Descripter, error) {
	subnet := &modelv2.CreateSubnetOption{
		Name:        vswitch.Name,
		Description: &vswitch.Description,
		VpcId:       network,
		GatewayIp:   vswitch.GateWay,
		DhcpEnable:  &vswitch.DHCP.Enabled,
		DnsList:     &vswitch.DNSServers,
	}
	if len(vswitch.IPv4Blocks) > 0 {
		subnet.Cidr = vswitch.IPv4Blocks[0]
	}
	resp, err := p.Client.VPCV2.CreateSubnet(&modelv2.CreateSubnetRequest{Body: &modelv2.CreateSubnetRequestBody{Subnet: subnet}})
	if err != nil {
		return nil, err
	}
	created := ConvertSubnet(*resp.Subnet)
	return &created.Descripter, nil
}

// UpdateVirtualSubnetwork implements common.Provider.
func (p *Provider) UpdateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) error {
	resp, err := p.Client.VPCV2.UpdateSubnet(&modelv2.UpdateSubnetRequest{
		VpcId:    network,
		SubnetId: vswitch.ID,
		Body: &modelv2.UpdateSubnetRequestBody{
			Subnet: &modelv2.UpdateSubnetOption{
				Name:        vswitch.Name,
				Description: &vswitch.Description,
				DhcpEnable:  &vswitch.DHCP.Enabled,
				DnsList:     &vswitch.DNSServers,
			},
		},
	})
	if err != nil {
		return err
	}
	_ = resp.Subnet
	return nil
}

// DeleteVirtualSubnetwork implements common.Provider.
func (p *Provider) DeleteVirtualSubnetwork(ctx context.Context, network string, name string) error {
	resp, err := p.Client.VPCV2.DeleteSubnet(&modelv2.DeleteSubnetRequest{VpcId: network, SubnetId: name})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}
