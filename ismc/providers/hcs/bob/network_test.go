package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestListVirtualNetworks(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListVirtualNetworks(ctx, common.ListVirtualNetworkOptions{
		ResourceGroup: "0b431e1a-3daf-466c-bc3e-0cb66d94a896",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestGetVirtualNetwork(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetVirtualNetwork(ctx, "4ca4f1e0-39a7-4729-b041-578e77209fc1")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestListSecurityGroups(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListSecurityGroups(ctx, common.ListSecurityGroupOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestGetSecurityGroup(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetSecurityGroup(ctx, "1b019942-7219-4bcc-ac33-2755aede0ffa")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUpdateSecurityGroup(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	sg := &common.SecurityGroup{
		Descripter: common.Descripter{
			ID:          "1b019942-7219-4bcc-ac33-2755aede0ffa",
			Description: "ismc",
			Name:        "default",
		},
	}
	err := globalProvider.UpdateSecurityGroup(ctx, sg)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestGetSubNetID(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()
	id, err := globalProvider.ListVirtualSubnetwork(ctx, "4ca4f1e0-39a7-4729-b041-578e77209fc1", common.ListVirtualSubnetworkOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(id)
}

func TestCreateNetwork(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	_, err := globalProvider.CreateVirtualNetwork(ctx, &common.VirtualNetwork{
		Descripter: common.Descripter{
			Name:        "test-ismc",
			Description: "test",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}
