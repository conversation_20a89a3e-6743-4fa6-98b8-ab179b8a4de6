package bob

import (
	"context"
	"fmt"
	"path/filepath"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ims/v2/model"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

const (
	KB = 1024
	MB = 1024 * 1024
	GB = MB * 1024
	TB = GB * 1024
)

var _ common.ImageOperation = &Provider{}

var _ common.InstanceTypeOperation = &Provider{}

// ListImages implements common.Provider.
func (p *Provider) ListImages(ctx context.Context, options common.ListImageOptions) (common.List[common.Image], error) {
	req := &model.ListImagesRequest{}
	if options.ResourceGroup != "" {
		req.EnterpriseProjectId = getEnterpriceProject(options.ResourceGroup)
	}
	req.Sort<PERSON>ey, req.SortDir = getSortImage(options.Sort)

	switch options.Source {
	case common.ImageSourcePublic:
		req.Imagetype = ptr.To(model.GetListImagesRequestImagetypeEnum().GOLD)
	case common.ImageSourceCustom:
		req.Imagetype = ptr.To(model.GetListImagesRequestImagetypeEnum().PRIVATE)
	case common.ImageSourceShared:
		req.Imagetype = ptr.To(model.GetListImagesRequestImagetypeEnum().SHARED)
	case common.ImageSourceCommunity:
		req.Imagetype = ptr.To(model.GetListImagesRequestImagetypeEnum().MARKET)
	}

	resp, err := p.Client.ListImages(req)
	if err != nil {
		return common.List[common.Image]{}, err
	}
	images := make([]common.Image, len(resp.Images))
	for i, hcsImage := range resp.Images {
		if options.Name != "" && hcsImage.Name != options.Name {
			continue
		}
		images[i] = HcsImageToCommonImage(hcsImage)
	}
	// 由于根据名称过滤，可能导致返回的列表长度不一致，需要重新分页
	// 原接口使用的 continue 格式分页，这里也可以转为 page size 格式分页
	// 在华为云公有云上，image 会全部被list 出来进行前端分页的
	return common.PageList(images, options.ListOptions), nil
}

func getSortImage(sort string) (*model.ListImagesRequestSortKey, *model.ListImagesRequestSortDir) {
	if sort == "" {
		return nil, nil
	}
	sorts := common.ParseSort(sort)
	if len(sorts) == 0 {
		return nil, nil
	}
	var sortkey *model.ListImagesRequestSortKey
	var sortdir *model.ListImagesRequestSortDir
	sort0 := sorts[0]
	switch sort0.Field {
	case "name":
		sortkey = ptr.To(model.GetListImagesRequestSortKeyEnum().NAME)
	case "time":
		sortkey = ptr.To(model.GetListImagesRequestSortKeyEnum().CREATED_AT)
	case "size":
		sortkey = ptr.To(model.GetListImagesRequestSortKeyEnum().SIZE)
	}
	switch sort0.Direction {
	case common.SortASC:
		sortdir = ptr.To(model.GetListImagesRequestSortDirEnum().ASC)
	case common.SortDESC:
		sortdir = ptr.To(model.GetListImagesRequestSortDirEnum().DESC)
	}
	return sortkey, sortdir
}

func HcsImageToCommonImage(img client.ImageInfo) common.Image {
	created, _ := time.Parse(time.RFC3339, img.CreatedAt)

	source := common.ImageSourceCommunity
	switch img.Imagetype {
	case model.GetImageInfoImagetypeEnum().GOLD:
		source = common.ImageSourcePublic
	case model.GetImageInfoImagetypeEnum().PRIVATE:
		source = common.ImageSourceCustom
	case model.GetImageInfoImagetypeEnum().SHARED:
		source = common.ImageSourceShared
	}

	public := false
	switch img.Visibility {
	case model.GetImageInfoVisibilityEnum().PRIVATE:
		public = false
	case model.GetImageInfoVisibilityEnum().PUBLIC:
		public = true
	}

	arch := common.ArchitectureAmd64
	if osbit := img.OsBit; osbit != nil {
		switch *osbit {
		case model.GetImageInfoOsBitEnum().E_32:
			arch = common.ArchitectureX86
		case model.GetImageInfoOsBitEnum().E_64:
			arch = common.ArchitectureAmd64
		}
	}

	bootMode := common.BootModeBIOS
	if hwtype := img.HwFirmwareType; hwtype != nil {
		switch *hwtype {
		case model.GetImageInfoHwFirmwareTypeEnum().BIOS:
			bootMode = common.BootModeBIOS
		case model.GetImageInfoHwFirmwareTypeEnum().UEFI:
			bootMode = common.BootModeUEFI
		}
	}

	features := make([]string, 0)
	switch img.VirtualEnvType {
	case model.GetImageInfoVirtualEnvTypeEnum().FUSION_COMPUTE:
		features = append(features, "FUSION_COMPUTE")
	case model.GetImageInfoVirtualEnvTypeEnum().IRONIC:
		features = append(features, "IRONIC")
	case model.GetImageInfoVirtualEnvTypeEnum().DATA_IMAGE:
		features = append(features, "DATA_IMAGE")
	}
	if ptr.Deref(img.SupportKvm, "") == "true" {
		features = append(features, "KVM")
	}
	if ptr.Deref(img.SupportXen, "") == "true" {
		features = append(features, "XEN")
	}
	if ptr.Deref(img.SupportXenHana, "") == "true" {
		features = append(features, "XEN_HANA")
	}

	phase := common.ImagePhaseUnknown
	switch img.Status {
	case model.GetImageInfoStatusEnum().QUEUED:
		phase = common.ImagePhasePending
	case model.GetImageInfoStatusEnum().SAVING:
		phase = common.ImagePhaseImporting
	case model.GetImageInfoStatusEnum().DELETED:
		phase = common.ImagePhaseUnknown
	case model.GetImageInfoStatusEnum().ACTIVE:
		phase = common.ImagePhaseReady
	}

	return common.Image{
		Descripter: common.Descripter{
			Name:              img.Name,
			ID:                img.Id,
			CreationTimestamp: created,
		},
		Tags:         img.Tags,
		Source:       source,
		Architecture: arch,
		Features:     features,
		Public:       public,
		MinDisk:      *resource.NewQuantity(int64(img.MinDisk)*GB, resource.BinarySI),
		MinMemory:    *resource.NewQuantity(int64(img.MinRam)*MB, resource.BinarySI),
		OS:           common.OSType(img.OsType.Value()),
		OSVersion:    ptr.Deref(img.OsVersion, ""),
		BootMode:     bootMode,
		Status: common.ImageStatus{
			Phase: phase,
			Size:  *resource.NewQuantity(int64(img.Size), resource.BinarySI),
		},
	}
}

// GetImage implements common.Provider.
func (p *Provider) GetImage(ctx context.Context, id string) (*common.Image, error) {
	resp, err := p.Client.ListImages(&model.ListImagesRequest{Id: &id})
	if err != nil {
		return nil, err
	}
	if len(resp.Images) == 0 {
		return nil, errors.NewNotFound("image", id)
	}
	return ptr.To(HcsImageToCommonImage(resp.Images[0])), nil
}

// UpdateImage implements common.Provider.
func (p *Provider) UpdateImage(ctx context.Context, image *common.Image) error {
	exists, err := p.GetImage(ctx, image.ID)
	if err != nil {
		return err
	}
	if exists.ID != image.ID {
		return errors.NewNotFound("image", image.ID)
	}
	patches := []model.UpdateImageRequestBody{}
	if image.Name != exists.Name {
		patches = append(patches, model.UpdateImageRequestBody{
			Op:    model.GetUpdateImageRequestBodyOpEnum().REPLACE,
			Path:  "/name",
			Value: image.Name,
		})
	}
	resp, err := p.Client.IMS.UpdateImage(&model.UpdateImageRequest{ImageId: image.ID, Body: &patches})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

// DeleteImage implements common.Provider.
func (p *Provider) DeleteImage(ctx context.Context, id string) error {
	resp, err := p.Client.IMS.GlanceDeleteImage(&model.GlanceDeleteImageRequest{ImageId: id})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

// ImportImage implements common.Provider.
func (p *Provider) ImportImage(ctx context.Context, image *common.Image, file common.FileContent) (*common.Descripter, error) {
	var diskformat model.GlanceCreateImageMetadataRequestBodyDiskFormat
	switch ext := filepath.Ext(file.FileName); ext {
	case ".vhd":
		diskformat = model.GetGlanceCreateImageMetadataRequestBodyDiskFormatEnum().VHD
	case ".vmdk":
		diskformat = client.NewGlanceCreateImageMetadataRequestBodyDiskFormat("vmdk")
	case ".zvhd":
		diskformat = model.GetGlanceCreateImageMetadataRequestBodyDiskFormatEnum().ZVHD
	case ".qcow2", ".img":
		diskformat = model.GetGlanceCreateImageMetadataRequestBodyDiskFormatEnum().QCOW2
	default:
		return nil, errors.NewBadRequest(fmt.Sprintf("unsupported image format: %s", ext))
	}

	reader := file.Content
	defer reader.Close()
	log := log.FromContext(ctx).WithValues("image", image.Name)
	progresscontent := common.NewSimpleProgressReader(reader, file.ContentLength, log)

	// check if has the same image metadata
	resp, err := p.Client.ListImages(&model.ListImagesRequest{})
	if err != nil {
		return nil, err
	}
	var imageID string
	for _, img := range resp.Images {
		if img.Name == image.Name {
			if img.Status == model.GetImageInfoStatusEnum().QUEUED {
				imageID = img.Id
				break
			} else {
				return nil, errors.NewAlreadyExists("image", image.Name)
			}
		}
	}
	if imageID == "" {
		req := &client.GlanceCreateImageMetadataRequestBody{
			Name:       &image.Name,
			Visibility: ptr.To("private"),
			DiskFormat: ptr.To(diskformat),
		}
		if image.MinDisk.Value() > 0 {
			req.MinDisk = ptr.To(ToGB(image.MinDisk.Value()))
		}
		if !image.MinMemory.IsZero() {
			req.MinRam = ptr.To(int32(image.MinMemory.Value() / MB))
		}
		switch image.Architecture {
		case common.ArchitectureAmd64:
			req.Architecture = "x86_64"
		case common.ArchitectureArm64:
			req.Architecture = "aarch64"
		}
		// Failed to create image, the  property __os_type is not allowed!
		// Failed to create image, the  property __os_bit is not allowed!

		// ● Windows
		// ● Ubuntu
		// ● RedHat
		// ● SUSE
		// ● CentOS
		// ● Debian
		// ● OpenSUSE
		// ● Oracle Linux
		// ● Fedora
		// ● Other
		// ● CoreOS
		// ● EulerOS

		// inject managed tag
		req.Tags = injectManagedTag(req.Tags)
		resp, err := p.Client.GlanceCreateImageMetadata(&client.GlanceCreateImageMetadataRequest{Body: req})
		if err != nil {
			return nil, err
		}
		imageID = *resp.Id
	}
	log.Info("start upload image file", "filename", file.FileName, "length", file.ContentLength)
	if err := p.Client.UploadImageFile(ctx, &client.UploadImageFileRequest{
		ImageId:       imageID,
		Content:       progresscontent,
		Filename:      file.FileName,
		ContentType:   file.ContentType,
		ContentLength: file.ContentLength,
	}); err != nil {
		log.Error(err, "upload image file failed")
		return nil, err
	}
	log.Info("upload image file success")
	return &common.Descripter{ID: imageID}, nil
}
