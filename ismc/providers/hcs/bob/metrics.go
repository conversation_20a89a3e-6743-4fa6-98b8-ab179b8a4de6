package bob

import (
	"context"
	"maps"
	"slices"
	"strconv"
	"strings"
	"sync"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
	sdkclient "xiaoshiai.cn/core/ismc/providers/hcs/client"
)

func (p *Provider) ListDiskMetrics(ctx context.Context) ([]*common.DiskMetrics, error) {
	disks, err := p.ListDisks(ctx, common.ListDiskOptions{})
	if err != nil {
		return nil, err
	}
	return BatchExecut(ctx, disks.Items, 50, p.completeDiskMetrics)
}

// GetDiskMetrics implements common.Provider.
func (p *Provider) GetDiskMetrics(ctx context.Context, id string) (*common.DiskMetrics, error) {
	disk, err := p.GetDisk(ctx, id)
	if err != nil {
		return nil, err
	}
	disks := []common.Disk{*disk}
	metrics, err := p.completeDiskMetrics(ctx, disks)
	if err != nil {
		return nil, err
	}
	if len(metrics) == 0 {
		return nil, errors.NewNotFound("disk metrics", id)
	}
	return metrics[0], nil
}

func (p *Provider) completeDiskMetrics(ctx context.Context, disks []common.Disk) ([]*common.DiskMetrics, error) {
	metricsmap := make(map[string]*common.DiskMetrics, len(disks))
	diskids := make([]string, 0, len(disks))
	for _, disk := range disks {
		diskids = append(diskids, disk.ID)
		metrics := &common.DiskMetrics{
			ID:             disk.ID,
			Name:           disk.Name,
			ResourceGroup:  p.regourceGroups.GetOrDefault(ctx, disk.ResourceGroup),
			Labels:         map[string]string{},
			DiskTotalBytes: float64(disk.Size.Value()),
		}
		if len(disk.Status.Mounts) > 0 {
			metrics.Labels["vm"] = disk.Status.Mounts[0].VirtualMachine
		}
		metricsmap[disk.ID] = metrics
	}
	req := client.ListResourceModelRequest{
		// 使用 nativeId in (id1, id2, id3) 查询提示不支持，只能换个方式
		Condition:       client.ListResourceModelRequestConstraintsIn("nativeId", diskids),
		ContentSelector: []string{"nativeId", "id"},
	}
	vollist, err := p.Client.ListCloudVolume(ctx, req)
	if err != nil {
		return nil, err
	}
	volidmap := make(map[string]string, len(vollist.ObjList))
	volids := make([]string, 0, len(vollist.ObjList))
	for _, vol := range vollist.ObjList {
		volidmap[vol.ID] = vol.NativeId
		volids = append(volids, vol.ID)
	}
	datas, err := p.QueryLatestCloudVolumeHistoryData(ctx, QueryLatestHistoryDataRequest{IDs: volids})
	if err != nil {
		log.FromContext(ctx).Error(err, "query latest cloud volume data failed", "ids", volids)
	}
	for id, data := range datas {
		if val, ok := metricsmap[volidmap[id]]; ok {
			val.DiskUsedPercent = data.DiskUtilInband
			val.DiskUsedBytes = data.DiskUtilInband * val.DiskTotalBytes / 100
			val.DiskReadBytesPerSecond = data.DiskReadRate * KB
			val.DiskWriteBytesPerSecond = data.DiskWriteRate * KB
			val.DiskReadOperationsPerSecond = data.DiskReadOperationRate
			val.DiskWriteOperationsPerSecond = data.DiskWriteOperationsPerSecond
		}
	}
	retlist := slices.Collect(maps.Values(metricsmap))
	slices.SortFunc(retlist, func(a, b *common.DiskMetrics) int {
		return strings.Compare(a.ID, b.ID)
	})
	return retlist, nil
}

// ListVirtualMachineMetrics implements common.MetricsOperations.
func (p *Provider) ListVirtualMachineMetrics(ctx context.Context, options common.ListVirtualMachineMetricsOptions) ([]*common.VirtualMachineMetrics, error) {
	opts := common.ListVirtualMachineOptions{
		ListOptions:   options.ListOptions,
		ResourceGroup: options.ResourceGroup,
		Zone:          options.Zone,
	}
	fields := "basic,flavor,scheduler_hints,image_meta,flavor_detail,metadata,addresses,tags,capabilities,resize_or_migrate,cdrom,device_limit,vmtools_detail,operation_rights,action_rights,block_device,vcpu_model,advanced_properties,os_hostname,enterprise_project_id"
	// make sure .enterpriseProject is returned
	vmlist, err := p.listVirtualMachinesv1WithFields(ctx, opts, fields)
	if err != nil {
		return nil, err
	}
	if len(vmlist.Items) == 0 {
		return []*common.VirtualMachineMetrics{}, nil
	}
	return BatchExecut(ctx, vmlist.Items, 50, p.listVMMetricsFrom)
}

func (p *Provider) listVMMetricsFrom(ctx context.Context, vms []common.VirtualMachine) ([]*common.VirtualMachineMetrics, error) {
	ids := make([]string, 0, len(vms))
	enterpriceProjectMap := make(map[string]*common.VirtualMachine, len(vms))
	for i, vm := range vms {
		ids = append(ids, vm.ID)
		enterpriceProjectMap[vm.ID] = &vms[i]
	}
	getResourceGroupFunc := func(ctx context.Context, id *common.VirtualMachineMetrics) {
		vm, ok := enterpriceProjectMap[id.ID]
		if !ok {
			return
		}
		id.ResourceGroup = p.regourceGroups.GetOrDefault(ctx, vm.ResourceGroup)
		if id.Labels == nil {
			id.Labels = map[string]string{}
		}
		maps.Copy(id.Labels, vm.Labels)
	}
	return p.listVMMetricsFromIDsWith(ctx, ids, getResourceGroupFunc)
}

// GetVirtualMachineMetrics implements common.MetricsOperations.
func (p *Provider) GetVirtualMachineMetrics(ctx context.Context, id string) (*common.VirtualMachineMetrics, error) {
	list, err := p.listVMMetricsFromIDs(ctx, []string{id})
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		// return a empty metrics
		return &common.VirtualMachineMetrics{ID: id}, nil
	}
	return list[0], nil
}

func (p *Provider) listVMMetricsFromIDs(ctx context.Context, vmids []string) ([]*common.VirtualMachineMetrics, error) {
	return p.listVMMetricsFromIDsWith(ctx, vmids, nil)
}

func (p *Provider) listVMMetricsFromIDsWith(ctx context.Context, vmids []string, completeFunc func(context.Context, *common.VirtualMachineMetrics)) ([]*common.VirtualMachineMetrics, error) {
	options := client.ListResourceModelRequest{
		// 使用 nativeId in (id1, id2, id3) 查询提示不支持，只能换个方式
		Condition: client.ListResourceModelRequestConstraintsIn("nativeId", vmids),
		ContentSelector: []string{
			"nativeId", "id", "name", "flavorVcpu", "flavorRamSize", "status", "vmState",
		},
	}
	// limit to this project
	options.Condition = append(options.Condition, client.ListResourceModelRequestConstraint{
		LogOp: "and",
		Simple: client.ListResourceModelRequestSimple{
			Name: "projectId", Operator: "equal", Value: p.Client.DefaultProjectID,
		},
	})

	list, err := p.Client.ListCloudVM(ctx, options)
	if err != nil {
		return nil, err
	}
	vmlist := list.ObjList

	vmmetricsmap := make(map[string]*common.VirtualMachineMetrics, len(vmlist))
	activeids := []string{}
	for _, vm := range vmlist {
		flavorvcpu, _ := strconv.ParseInt(vm.FlavorVcpu, 10, 64)
		flavorvram, _ := strconv.ParseInt(vm.FlavorRamSize, 10, 64)

		metrics := &common.VirtualMachineMetrics{
			ID:               vm.NativeId,
			Name:             vm.Name,
			CPUTotalCores:    float64(flavorvcpu),
			MemoryTotalBytes: flavorvram * MB,
			Up:               vm.Status == "active",
			Health:           vm.Status == "active", // TODO: check health
		}
		if completeFunc != nil {
			completeFunc(ctx, metrics)
		}
		vmmetricsmap[vm.Id] = metrics
		if vm.Status == "active" {
			activeids = append(activeids, vm.Id)
		}
	}
	datas, err := p.QueryLatestCloudVMHistoryData(ctx, QueryLatestHistoryDataRequest{IDs: activeids})
	if err != nil {
		log.FromContext(ctx).Error(err, "query latest cloud vm data failed", "ids", activeids)
	}
	for id, data := range datas {
		if val, ok := vmmetricsmap[id]; ok {
			val.CPUUsedPercent = data.CPUUsage
			val.CPUUsedCores = data.CPUUsage * val.CPUTotalCores / 100

			val.MemoryUsedPercent = data.MemoryUsage
			val.MemoryUsedBytes = int64(data.MemoryUsage * float64(val.MemoryTotalBytes) / 100)

			val.NetworkTXBytesPerSecond = data.NicByteOut * KB
			val.NetworkRXBytesPerSecond = data.NicByteIn * KB

			val.DiskReadBytesPerSecond = data.DiskIoIn * KB
			val.DiskWriteBytesPerSecond = data.DiskIoOut * KB
		}
	}
	return slices.Collect(maps.Values(vmmetricsmap)), nil
}

// ListZoneMetrics implements common.MetricsOperations.
func (p *Provider) ListZoneMetrics(ctx context.Context) ([]*common.ZoneMetrics, error) {
	resp, err := p.Client.GetProjectDetails(ctx)
	if err != nil {
		return nil, err
	}
	var as []client.ProjectAzInfo
	for _, region := range resp.Project.Regions {
		for _, ci := range region.CloudInfras {
			as = append(as, ci.Azs...)
		}
	}
	zm := make([]*common.ZoneMetrics, 0)
	for _, a := range as {
		zoneMetrics, err := p.GetZoneMetrics(ctx, a.AzID)
		if err != nil {
			return nil, err
		}
		zm = append(zm, zoneMetrics)
	}
	return zm, nil
}

// GetZoneMetrics implements common.MetricsOperations.
func (p *Provider) GetZoneMetrics(ctx context.Context, id string) (*common.ZoneMetrics, error) {
	return &common.ZoneMetrics{}, nil
}

// GetHostMetrics implements common.Provider.
func (p *Provider) GetHostMetrics(ctx context.Context, id string) (*common.HostMetrics, error) {
	req := client.ListResourceModelRequest{
		Condition: []client.ListResourceModelRequestConstraint{
			client.ListResourceModelRequestConstraintEqual("nativeId", id),
		},
	}
	list, err := p.listHostMetrics(ctx, req)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return nil, errors.NewNotFound("host", id)
	}
	return list[0], nil
}

// ListHostMetrics implements common.Provider.
func (p *Provider) ListHostMetrics(ctx context.Context) ([]*common.HostMetrics, error) {
	return p.listHostMetrics(ctx, client.ListResourceModelRequest{PageSize: 1000})
}

func (p *Provider) listHostMetrics(ctx context.Context, options client.ListResourceModelRequest) ([]*common.HostMetrics, error) {
	phyhostlist, err := p.Client.ListSystemPhysicalHost(ctx, options)
	if err != nil {
		return nil, err
	}
	ids := make([]string, 0, len(phyhostlist.ObjList))
	metricsmap := make(map[string]*common.HostMetrics, len(phyhostlist.ObjList))
	for _, host := range phyhostlist.ObjList {
		ids = append(ids, host.Id)
		metricsmap[host.Id] = &common.HostMetrics{
			ID:     host.Id,
			Labels: map[string]string{"host": host.Name},
			Name:   host.Name,
			// hard
			MemoryTotalBytes: int64(host.TotalRamSizeMB) * MB,
			CPUTotalCores:    float64(host.CpuQuantityForVirtualization),
			DiskTotalBytes:   int64(host.TotalDiskSizeMB) * MB,
			DiskUsedBytes:    int64(host.AllocatedDiskSizeMB) * MB,

			// vm allocated
			VCPUUsedCores:     float64(host.AllocatedVcpuCores),
			VCPUTotalCores:    float64(host.TotalVcpuCores),
			VMemoryUsedBytes:  int64(host.AllocatedVmemoryMB) * MB,
			VMemoryTotalBytes: int64(host.TotalVmemoryMB) * MB,
		}
	}
	datas, err := p.QueryLatestPhysicalServerData(ctx, QueryLatestHistoryDataRequest{IDs: ids})
	if err != nil {
		log.FromContext(ctx).Error(err, "query latest physical server data failed", "ids", ids)
	}
	for id, data := range datas {
		if val, ok := metricsmap[id]; ok {
			val.CPUUsedPercent = data.CPUUsage
			val.CPUUsedCores = data.CPUUsage * val.CPUTotalCores / 100

			val.MemoryUsedPercent = data.MemoryUsage
			val.MemoryUsedBytes = int64(data.MemoryUsage * float64(val.MemoryTotalBytes) / 100)

			val.DiskReadOperationsPerSecond = data.DiskIopsRead
			val.DiskWriteOperationsPerSecond = data.DiskIopsWrite

			val.NetworkRXBytesPerSecond = data.NicByteIn * KB
			val.NetworkTXBytesPerSecond = data.NicByteOut * KB
		}
	}
	return slices.Collect(maps.Values(metricsmap)), nil
}

var _ common.MetricsOperations = &Provider{}

type QueryLatestHistoryDataRequest struct {
	IDs []string
}

type DiskHistoryData struct {
	DiskUtilInband               float64 // %
	DiskReadRate                 float64 // KB/s
	DiskWriteRate                float64 // KB/s
	DiskReadOperationRate        float64 // requests/s
	DiskWriteOperationsPerSecond float64 // requests/s
}

func (p *Provider) QueryLatestCloudVolumeHistoryData(ctx context.Context, options QueryLatestHistoryDataRequest) (map[string]DiskHistoryData, error) {
	req := client.QueryHistoryDataRequest{
		ObjTypeId: client.ObjectTypeId_CLOUD_VOLUME,
		IndicatorIds: []client.IndicatorId{
			client.IndicatorId_CLOUD_VOLUME_DiskUtilInband,
			client.IndicatorId_CLOUD_VOLUME_DiskReadRate,
			client.IndicatorId_CLOUD_VOLUME_DiskWriteRate,
			client.IndicatorId_CLOUD_VOLUME_DiskReadOperationRate,
			client.IndicatorId_CLOUD_VOLUME_DiskWriteOperationRate,
		},
		ObjIds:   options.IDs,
		Interval: client.QueryHistoryDataInterval_MINUTE,
		Range:    client.QueryHistoryDataRange_LAST_1_HOUR,
	}
	resp, err := p.Client.QueryHistoryDataAll(ctx, req)
	if err != nil {
		return nil, err
	}
	ret := make(map[string]DiskHistoryData)
	for id, item := range resp {
		ret[id] = DiskHistoryData{
			DiskUtilInband:               getFirstSeriesData(item[client.IndicatorId_CLOUD_VOLUME_DiskUtilInband]),
			DiskReadRate:                 getFirstSeriesData(item[client.IndicatorId_CLOUD_VOLUME_DiskReadRate]),
			DiskWriteRate:                getFirstSeriesData(item[client.IndicatorId_CLOUD_VOLUME_DiskWriteRate]),
			DiskReadOperationRate:        getFirstSeriesData(item[client.IndicatorId_CLOUD_VOLUME_DiskReadOperationRate]),
			DiskWriteOperationsPerSecond: getFirstSeriesData(item[client.IndicatorId_CLOUD_VOLUME_DiskWriteOperationRate]),
		}
	}
	return ret, nil
}

type VMHistoryData struct {
	CPUUsage    float64 // %
	MemoryUsage float64 // %
	DiskIoOut   float64 // KB/s
	DiskIoIn    float64 // KB/s
	NicByteIn   float64 // KB/s
	NicByteOut  float64 // KB/s
}

func (p *Provider) QueryLatestCloudVMHistoryData(ctx context.Context, options QueryLatestHistoryDataRequest) (map[string]VMHistoryData, error) {
	req := client.QueryHistoryDataRequest{
		ObjTypeId: client.ObjectTypeId_CLOUD_VM,
		IndicatorIds: []client.IndicatorId{
			client.IndicatorId_CLOUD_VM_CPUUsage,
			client.IndicatorId_CLOUD_VM_MemoryUsage,

			client.IndicatorId_CLOUD_VM_NicByteIn,
			client.IndicatorId_CLOUD_VM_NicByteOut,

			client.IndicatorId_CLOUD_VM_DiskIoIn,
			client.IndicatorId_CLOUD_VM_DiskIoOut,
		},
		ObjIds:   options.IDs,
		Interval: client.QueryHistoryDataInterval_MINUTE,
		Range:    client.QueryHistoryDataRange_LAST_5_MINUTE,
	}
	// 当使用 LAST_5_MINUTE 时，会出现diskIoIn和diskIoOut的数据为空的情况
	// 或者返回的结果中没有对应 id 的数据(时段内未上报)

	// 使用 LAST_1_HOUR 并选取第一个非空数据
	req.Range = client.QueryHistoryDataRange_LAST_1_HOUR

	resp, err := p.Client.QueryHistoryDataAll(ctx, req)
	if err != nil {
		return nil, err
	}
	ret := make(map[string]VMHistoryData)
	for id, item := range resp {
		ret[id] = VMHistoryData{
			CPUUsage:    getFirstSeriesData(item[client.IndicatorId_CLOUD_VM_CPUUsage]),
			MemoryUsage: getFirstSeriesData(item[client.IndicatorId_CLOUD_VM_MemoryUsage]),
			NicByteIn:   getFirstSeriesData(item[client.IndicatorId_CLOUD_VM_NicByteIn]),
			NicByteOut:  getFirstSeriesData(item[client.IndicatorId_CLOUD_VM_NicByteOut]),
			DiskIoIn:    getFirstSeriesData(item[client.IndicatorId_CLOUD_VM_DiskIoIn]),
			DiskIoOut:   getFirstSeriesData(item[client.IndicatorId_CLOUD_VM_DiskIoOut]),
		}
	}
	return ret, nil
}

type ServerHistoryData struct {
	CPUUsage      float64 // %
	MemoryUsage   float64 // %
	DiskIopsWrite float64 // request/s
	DiskIopsRead  float64 // request/s
	NicByteIn     float64 // KB/s
	NicByteOut    float64 // KB/s
}

func (p *Provider) QueryLatestPhysicalServerData(ctx context.Context, options QueryLatestHistoryDataRequest) (map[string]ServerHistoryData, error) {
	req := client.QueryHistoryDataRequest{
		ObjTypeId: client.ObjectTypeId_SYS_PhysicalServer,
		IndicatorIds: []client.IndicatorId{
			client.IndicatorId_SYS_PhysicalHost_CPUUsage,
			client.IndicatorId_SYS_PhysicalHost_MemoryUsage,
			// client.IndicatorId_SYS_PhysicalHost_DiskIopsWrite,
			// client.IndicatorId_SYS_PhysicalHost_DiskIopsRead,
			// client.IndicatorId_SYS_PhysicalHost_NicByteIn,
			// client.IndicatorId_SYS_PhysicalHost_NicByteOut,
		},
		ObjIds:   options.IDs,
		Interval: client.QueryHistoryDataInterval_MINUTE,
		Range:    client.QueryHistoryDataRange_LAST_5_MINUTE,
	}
	// interval 为 5 分钟时，会出现数据为空的情况
	req.Range = client.QueryHistoryDataRange_LAST_1_HOUR

	resp, err := p.Client.QueryHistoryDataAll(ctx, req)
	if err != nil {
		return nil, err
	}
	ret := make(map[string]ServerHistoryData, len(resp))
	for id, item := range resp {
		ret[id] = ServerHistoryData{
			CPUUsage:      getFirstSeriesData(item[client.IndicatorId_SYS_PhysicalHost_CPUUsage]),
			MemoryUsage:   getFirstSeriesData(item[client.IndicatorId_SYS_PhysicalHost_MemoryUsage]),
			DiskIopsWrite: getFirstSeriesData(item[client.IndicatorId_SYS_PhysicalHost_DiskIopsWrite]),
			DiskIopsRead:  getFirstSeriesData(item[client.IndicatorId_SYS_PhysicalHost_DiskIopsRead]),
			NicByteIn:     getFirstSeriesData(item[client.IndicatorId_SYS_PhysicalHost_NicByteIn]),
			NicByteOut:    getFirstSeriesData(item[client.IndicatorId_SYS_PhysicalHost_NicByteOut]),
		}
	}
	return ret, nil
}

func getFirstSeriesData(data client.HistoryDataItem) float64 {
	if len(data.Series) == 0 {
		return 0
	}
	for _, v := range data.Series[0] {
		if v != "" {
			value, _ := strconv.ParseFloat(v, 64)
			return value
		}
	}
	return 0
}

func newResourceGroupsCache(ctx context.Context, cli *sdkclient.Client) (common.KVCache[string, string], error) {
	onRefresh := func(ctx context.Context) (map[string]string, error) {
		list, err := cli.ListEnterpriseProject(ctx, client.ListEnterpriseProjectOptions{})
		if err != nil {
			return nil, err
		}
		ret := make(map[string]string, len(*list.EnterpriseProjects))
		for _, item := range *list.EnterpriseProjects {
			ret[item.Id] = item.Name
		}
		return ret, nil
	}
	onMiss := func(ctx context.Context, id string) (string, error) {
		if id == "" {
			return "", nil
		}
		if id == "default" {
			return "default", nil
		}
		if id == AllEnterpriseProject {
			return "default", nil
		}
		item, err := cli.GetEnterpriseProject(ctx, id)
		if err != nil {
			return "", err
		}
		return item.Name, nil
	}
	options := common.CacheOptions[string, string]{
		OnRefresh: onRefresh,
		OnMiss:    onMiss,
	}
	return common.NewMemKVCache(ctx, options)
}

func BatchExecut[T, F any](ctx context.Context, items []T, batchSize int, fn func(ctx context.Context, batch []T) ([]F, error)) ([]F, error) {
	eg, ctx := errgroup.WithContext(ctx)
	result := make([]F, 0, len(items))
	mu := sync.Mutex{}
	for i := 0; i < len(items); i += batchSize {
		batch := items[i:min(i+batchSize, len(items))]
		eg.Go(func() error {
			batchResult, err := fn(ctx, batch)
			if err != nil {
				return err
			}
			mu.Lock()
			defer mu.Unlock()
			result = append(result, batchResult...)
			return err
		})
	}
	if err := eg.Wait(); err != nil {
		return result, err
	}
	return result, nil
}
