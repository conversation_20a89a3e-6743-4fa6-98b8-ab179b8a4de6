package bob

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

// ListInstanceTypes implements common.Provider.
func (p *Provider) ListInstanceTypes(ctx context.Context, options common.ListInstanceTypeOptions) (common.List[common.InstanceType], error) {
	flavors, err := p.Client.ListFlavors(ctx, client.ListFlavorsOptions{
		AvailabilityZone: options.Zone,
	})
	if err != nil {
		return common.List[common.InstanceType]{}, err
	}
	list := make([]common.InstanceType, 0, len(flavors))
	for _, flavor := range flavors {
		list = append(list, coverHCSInstanceTypeToInstanceType(flavor))
	}
	return common.PageList(list, options.ListOptions), nil
}

func coverHCSInstanceTypeToInstanceType(t client.Flavor) common.InstanceType {
	ret := common.InstanceType{
		Descripter: common.Descripter{
			ID:   t.Id,
			Name: t.Name,
		},
		Resources: map[common.ResourceType]resource.Quantity{
			common.ResourceMemory: *resource.NewQuantity(int64(t.Ram)*MB, resource.BinarySI),
			common.ResourceCPU:    *resource.NewQuantity(int64(t.Vcpus), resource.DecimalSI),
			common.ResourceDisk:   *resource.NewQuantity(int64(t.Disk)*GB, resource.BinarySI),
		},
		Architecture: common.ArchitectureAmd64,
	}
	if ptr.Deref(t.OsExtraSpecs.EcsinstanceArchitecture, "") == "arm64" {
		ret.Architecture = common.ArchitectureArm64
	}
	switch t.OsExtraSpecs.CapabilitiesCpuInfoArch {
	case "x86_64":
		ret.Architecture = common.ArchitectureAmd64
	case "arm64":
		ret.Architecture = common.ArchitectureArm64
	}
	features := make([]string, 0)
	if t.OsExtraSpecs.ResourceType != nil {
		features = append(features, *t.OsExtraSpecs.ResourceType)
	}
	if t.OsExtraSpecs.EcsvirtualizationEnvTypes != nil {
		features = append(features, *t.OsExtraSpecs.EcsvirtualizationEnvTypes)
	}
	if t.OsExtraSpecs.Ecsperformancetype != nil {
		features = append(features, *t.OsExtraSpecs.Ecsperformancetype)
	}
	ret.Features = features
	return ret
}

// GetInstanceType implements common.Provider.
func (p *Provider) GetInstanceType(ctx context.Context, id string) (*common.InstanceType, error) {
	resp, err := p.Client.ShowFlavor(&client.ShowFlavorRequest{FlavorId: id})
	if err != nil {
		return nil, err
	}
	instancetype := coverHCSInstanceTypeToInstanceType(resp.Flavor)
	return &instancetype, nil
}

// CreateInstanceType implements common.Provider.
// only admin can create instance type
func (p *Provider) CreateInstanceType(ctx context.Context, instanceType *common.InstanceType) (*common.Descripter, error) {
	flavor := client.FlavorReq{
		Name:                   instanceType.Name,
		OsFlavoraccessIsPublic: true,
	}
	if cpu, ok := instanceType.Resources[common.ResourceCPU]; ok {
		flavor.Vcpus = int(cpu.Value())
	}
	if mem, ok := instanceType.Resources[common.ResourceMemory]; ok {
		flavor.Ram = int(mem.Value() / MB)
	}
	if disk, ok := instanceType.Resources[common.ResourceDisk]; ok {
		flavor.Disk = int(disk.Value() / GB)
	}
	_, err := p.Client.CreateFlavor(&client.CreateFlavorRequest{Body: &client.CreateFlavorRequestBody{Flavor: flavor}})
	if err != nil {
		return nil, err
	}
	return &common.Descripter{}, nil
}

// UpdateInstanceType implements common.Provider.
func (p *Provider) UpdateInstanceType(ctx context.Context, instanceType *common.InstanceType) error {
	return errUnsupportMethod
}

// DeleteInstanceType implements common.Provider.
func (p *Provider) DeleteInstanceType(ctx context.Context, id string) error {
	return errUnsupportMethod
}
