package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

func setupProvider(t *testing.T) *Provider {
	ctx := context.Background()
	provider, err := NewProvider(ctx, &Options{
		Options: client.Options{
			GlobalDomain:   "majnoon-dccloud.com",
			Region:         "majnoon-dccloud-1",
			Domain:         "xiaoshiai",
			UserName:       "xiaoshiai",
			Proxy:          "http://************:8089",
			DefaultProject: "majnoon-dccloud-1_xiaoshiai",
			Password:       "",
			Debug:          true,
		},
		UseSubscription: true,
	})
	if err != nil {
		t.Fatal(err)
	}
	return provider
}
