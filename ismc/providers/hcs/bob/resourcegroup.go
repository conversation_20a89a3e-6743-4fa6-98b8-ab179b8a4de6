package bob

import (
	"context"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

var _ common.ResourceGroupOperation = &Provider{}

func (p *Provider) ListResourceGroups(ctx context.Context, options common.ListResourceGroupOptions) (common.List[common.ResourceGroup], error) {
	req := client.ListEnterpriseProjectOptions{}
	req.Limit, req.Offset = getLimitOffset(options.ListOptions)
	req.SortDir, req.SortKey = getSortKeyDir(options.ListOptions)
	if options.Name != "" {
		req.Name = options.Name
	}
	if options.Search != "" {
		req.Name = options.Search
	}
	resp, err := p.Client.ListEnterpriseProject(ctx, req)
	if err != nil {
		return common.List[common.ResourceGroup]{}, err
	}
	if resp == nil {
		return common.List[common.ResourceGroup]{}, nil
	}
	resourceGroups := make([]common.ResourceGroup, 0, len(*resp.EnterpriseProjects))

	for _, ep := range *resp.EnterpriseProjects {
		resourceGroups = append(resourceGroups, coverHCSEPToResourceGroup(ep))
	}
	return toPageSizeList(resourceGroups, resp.TotalCount, options.ListOptions), nil
}

// UpdateResourceGroup implements common.ResourceGroupOperation.
func (p *Provider) UpdateResourceGroup(ctx context.Context, id string, resourceGroup *common.ResourceGroup) error {
	return nil
}

func (p *Provider) CreateResourceGroup(ctx context.Context, resourceGroup *common.ResourceGroup) (*common.Descripter, error) {
	resp, err := p.Client.CreateEnterpriseProject(&client.CreateEnterpriseProjectRequest{
		Body: &client.EnterpriseProject{
			Name:        resourceGroup.Name,
			Description: &resourceGroup.Description,
		},
	})
	if err != nil {
		return nil, err
	}
	if resp != nil && resp.EnterpriseProject != nil {
		resourceGroup.ID = resp.EnterpriseProject.Id
		resourceGroup.CreationTimestamp = time.Time(*resp.EnterpriseProject.CreatedAt)
	}
	desc := resourceGroup.Descripter
	return &desc, nil
}

func (p *Provider) GetResourceGroup(ctx context.Context, id string) (*common.ResourceGroup, error) {
	resp, err := p.Client.GetEnterpriseProject(ctx, id)
	if err != nil {
		return nil, err
	}
	if resp == nil {
		return nil, errors.NewNotFound("resource group", id)
	}
	rg := coverHCSEPToResourceGroup(*resp)
	return &rg, nil
}

func (p *Provider) DeleteResourceGroup(ctx context.Context, id string) error {
	return p.Client.DeleteEnterpriseProject(ctx, id)
}

func coverHCSEPToResourceGroup(ep client.EpDetail) common.ResourceGroup {
	rg := common.ResourceGroup{
		Descripter: common.Descripter{
			ID:                ep.Id,
			Name:              ep.Name,
			Description:       ep.Description,
			CreationTimestamp: ep.CreatedAt,
		},
		Status: common.ResourceGroupStatus{
			Disabled: ep.Status == 2,
		},
	}
	return rg
}
