package bob

import (
	"context"
	"fmt"
	"strings"
	"time"

	modelv2 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v2/model"
	modelv3 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/vpc/v3/model"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

// ListSecurityGroups implements common.Provider.
func (p *Provider) ListSecurityGroups(ctx context.Context, options common.ListSecurityGroupOptions) (common.List[common.SecurityGroup], error) {
	if options.Sort == "" {
		options.Sort = "time-"
	}
	return p.listSecurityGroupsV2(ctx, options)
}

func (p *Provider) listSecurityGroupsV3(_ context.Context, options common.ListSecurityGroupOptions) (common.List[common.SecurityGroup], error) {
	req := &modelv3.ListSecurityGroupsRequest{}
	req.Limit, req.Marker = getLimitMarker(options.ListOptions)
	req.EnterpriseProjectId = getEnterpriceProject(options.ResourceGroup)

	resp, err := p.Client.VPCV3.ListSecurityGroups(req)
	if err != nil {
		return common.List[common.SecurityGroup]{}, err
	}
	securityGroups := []common.SecurityGroup{}
	for _, item := range *resp.SecurityGroups {
		securityGroups = append(securityGroups, coverV3SecurityGroup(item))
	}
	return toContinueList(securityGroups, options.ListOptions), nil
}

func (p *Provider) listSecurityGroupsV2(ctx context.Context, options common.ListSecurityGroupOptions) (common.List[common.SecurityGroup], error) {
	req := client.ListSecurityGroupsOptions{}
	if options.Search != "" {
		req.Name = fmt.Sprintf(`(?i)(%s)`, options.Search)
		req.Regexpress = true
	}
	req.Limit, req.Marker = getLimitMarker(options.ListOptions)
	req.SortKey, req.SortDir = getSortKeyDir(options.ListOptions)
	req.EnterpriseProjectId = getEnterpriceProject(options.ResourceGroup)
	if options.Network != "" {
		req.VpcId = &options.Network
	}
	resp, err := p.Client.ListSecurityGroups(ctx, req)
	if err != nil {
		return common.List[common.SecurityGroup]{}, err
	}
	securityGroups := []common.SecurityGroup{}
	for _, item := range resp.SecurityGroups {
		securityGroups = append(securityGroups, covertV2SecurityGroup(item))
	}
	return toContinueList(securityGroups, options.ListOptions), nil
}

// GetSecurityGroup implements common.Provider.
func (p *Provider) GetSecurityGroup(ctx context.Context, id string) (*common.SecurityGroup, error) {
	resp, err := p.Client.VPCV3.ShowSecurityGroup(&modelv3.ShowSecurityGroupRequest{SecurityGroupId: id})
	if err != nil {
		return nil, err
	}
	if resp == nil {
		return nil, errors.NewNotFound("security group", id)
	}
	secgroup := covertV3SecurityGroupInfo(*resp.SecurityGroup)
	return &secgroup, nil
}

func covertV2SecurityGroup(hcs client.SecurityGroup) common.SecurityGroup {
	secgroup := common.SecurityGroup{
		Descripter: common.Descripter{
			ID:            hcs.Id,
			Name:          hcs.Name,
			Description:   ptr.Deref(hcs.Description, ""),
			ResourceGroup: ptr.Deref(hcs.EnterpriseProjectId, ""),
		},
		VirtualNetwork: ptr.Deref(hcs.VpcId, ""),
	}
	if hcs.CreatedAt != nil {
		secgroup.CreationTimestamp = time.Time(*hcs.CreatedAt)
	}
	if hcs.EnterpriseProjectId == nil {
		for _, tag := range hcs.SysTags {
			if strings.HasPrefix(tag, "_sys_enterprise_project_id=") {
				secgroup.ResourceGroup = strings.TrimPrefix(tag, "_sys_enterprise_project_id=")
				break
			}
		}
	} else {
		secgroup.ResourceGroup = *hcs.EnterpriseProjectId
	}
	return secgroup
}

func convertNeutronSecurityGroup(hcs modelv2.NeutronSecurityGroup) common.SecurityGroup {
	secgroup := common.SecurityGroup{
		Descripter: common.Descripter{
			ID:                hcs.Id,
			Name:              hcs.Name,
			Description:       hcs.Description,
			CreationTimestamp: time.Time(*hcs.CreatedAt),
		},
	}
	return secgroup
}

func coverV3SecurityGroup(hcs modelv3.SecurityGroup) common.SecurityGroup {
	secgroup := common.SecurityGroup{
		Descripter: common.Descripter{
			ID:                hcs.Id,
			Name:              hcs.Name,
			Description:       hcs.Description,
			CreationTimestamp: time.Time(*hcs.CreatedAt),
			ResourceGroup:     hcs.EnterpriseProjectId,
		},
	}
	return secgroup
}

func covertV3SecurityGroupInfo(hcs modelv3.SecurityGroupInfo) common.SecurityGroup {
	secgroup := common.SecurityGroup{
		Descripter: common.Descripter{
			ID:                hcs.Id,
			Name:              hcs.Name,
			Description:       hcs.Description,
			CreationTimestamp: time.Time(*hcs.CreatedAt),
			ResourceGroup:     hcs.EnterpriseProjectId,
		},
	}
	return secgroup
}

// CreateSecurityGroup implements common.Provider.
func (p *Provider) CreateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) (*common.Descripter, error) {
	if false {
		// v1
		create := &modelv2.CreateSecurityGroupOption{
			Name:  sg.Name,
			VpcId: &sg.VirtualNetwork,
		}
		if sg.ResourceGroup != "" {
			create.EnterpriseProjectId = &sg.ResourceGroup
		}
		resp, err := p.Client.VPCV2.CreateSecurityGroup(&modelv2.CreateSecurityGroupRequest{
			Body: &modelv2.CreateSecurityGroupRequestBody{SecurityGroup: create},
		})
		if err != nil {
			return nil, err
		}
		created := covertV2SecurityGroup(client.SecurityGroup{SecurityGroup: *resp.SecurityGroup})
		return &created.Descripter, nil
	}
	// v3
	if false {
		create := &modelv3.CreateSecurityGroupOption{
			Name:                sg.Name,
			Description:         &sg.Description,
			EnterpriseProjectId: getEnterpriceProject(sg.ResourceGroup),
		}
		resp, err := p.Client.VPCV3.CreateSecurityGroup(&modelv3.CreateSecurityGroupRequest{
			Body: &modelv3.CreateSecurityGroupRequestBody{SecurityGroup: create},
		})
		if err != nil {
			return nil, err
		}
		created := covertV3SecurityGroupInfo(*resp.SecurityGroup)
		return &created.Descripter, nil
	}
	// v2
	resp, err := p.Client.VPCV2.NeutronCreateSecurityGroup(&modelv2.NeutronCreateSecurityGroupRequest{
		Body: &modelv2.NeutronCreateSecurityGroupRequestBody{
			SecurityGroup: &modelv2.NeutronCreateSecurityGroupOption{
				Description: &sg.Description,
				Name:        &sg.Name,
			},
		},
	})
	if err != nil {
		return nil, err
	}
	created := convertNeutronSecurityGroup(*resp.SecurityGroup)
	return &created.Descripter, nil
}

// UpdateSecurityGroup implements common.Provider.
func (p *Provider) UpdateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) error {
	resp, err := p.Client.VPCV2.NeutronUpdateSecurityGroup(&modelv2.NeutronUpdateSecurityGroupRequest{
		SecurityGroupId: sg.ID,
		Body: &modelv2.NeutronUpdateSecurityGroupRequestBody{
			SecurityGroup: &modelv2.NeutronUpdateSecurityGroupOption{
				Description: &sg.Description,
				Name:        &sg.Name,
			},
		},
	})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

// DeleteSecurityGroup implements common.Provider.
func (p *Provider) DeleteSecurityGroup(ctx context.Context, id string) error {
	resp, err := p.Client.VPCV2.DeleteSecurityGroup(&modelv2.DeleteSecurityGroupRequest{SecurityGroupId: id})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

// ListSecurityGroupRules implements common.Provider.
func (p *Provider) ListSecurityGroupRules(ctx context.Context, securityGroup string, options common.ListSecurityGroupRuleOptions) (common.List[common.SecurityGroupRule], error) {
	req := &modelv2.ListSecurityGroupRulesRequest{
		SecurityGroupId: &securityGroup,
	}
	req.Limit, req.Marker = getLimitMarker(options.ListOptions)

	resp, err := p.Client.VPCV2.ListSecurityGroupRules(req)
	if err != nil {
		return common.List[common.SecurityGroupRule]{}, err
	}
	if resp.SecurityGroupRules == nil {
		return common.List[common.SecurityGroupRule]{}, nil
	}
	rules := []common.SecurityGroupRule{}
	for _, item := range *resp.SecurityGroupRules {
		rules = append(rules, coverHcsSecurityGroupRuleToSecurityGroupRule(item))
	}
	return toContinueList(rules, options.ListOptions), nil
}

// UpdateSecurityGroupRule implements common.Provider.
func (p *Provider) UpdateSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return errUnsupportMethod
}

// DeleteSecurityGroupRule implements common.Provider.
func (p *Provider) DeleteSecurityGroupRule(ctx context.Context, securityGroupID string, ruleID string) error {
	_, err := p.Client.VPCV2.DeleteSecurityGroupRule(&modelv2.DeleteSecurityGroupRuleRequest{SecurityGroupRuleId: ruleID})
	return err
}

type ICMPPortRange struct {
	From int
	To   int
}

var ICMPPortList = map[string]ICMPPortRange{
	"Any":                  {From: -1, To: -1},
	"Echo":                 {From: 8, To: 0},
	"Echo reply":           {From: 0, To: 0},
	"Fragment need DF set": {From: 3, To: 4},
	"Host redirect":        {From: 5, To: 1},
	"Host TOS redirect":    {From: 5, To: 3},
	"Host unreachable":     {From: 3, To: 1},
	"Information reply":    {From: 16, To: 0},
	"Information request":  {From: 15, To: 0},
	"Net redirect":         {From: 5, To: 0},
	"Net TOS redirect":     {From: 5, To: 2},
	"Net unreachable":      {From: 3, To: 0},
	"Parameter problem":    {From: 12, To: 0},
	"Port unreachable":     {From: 3, To: 3},
	"Protocol unreachable": {From: 3, To: 2},
	"Reassembly timeout":   {From: 11, To: 1},
	"Source quench":        {From: 4, To: 0},
	"Source route failed":  {From: 3, To: 5},
	"Timestamp reply":      {From: 14, To: 0},
	"Timestamp request":    {From: 13, To: 0},
	"TTL exceeded":         {From: 11, To: 0},
}

// AddSecurityGroupRule implements common.Provider.
func (p *Provider) AddSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	req := &modelv2.CreateSecurityGroupRuleOption{
		SecurityGroupId: securityGroup,
		Description:     &rule.Description,
	}
	switch rule.Protocol {
	case common.ICMP, common.TCP, common.UDP:
		req.Protocol = ptr.To(strings.ToLower(string(rule.Protocol)))
	case "": // any
	default:
		req.Protocol = ptr.To(strings.ToLower(string(rule.Protocol)))
	}
	switch rule.Famlily {
	case common.IPv4:
		req.Ethertype = ptr.To("IPv4")
	case common.IPv6:
		req.Ethertype = ptr.To("IPv6")
	default:
	}
	// 功能说明：远端IP地址，
	// 当direction是egress时为虚拟机访问端的地址，
	// 当direction是ingress时为访问虚拟机的地址
	// 取值范围：IP地址，或者cidr格式 约束：和remote_group_id，remote_address_group_id互斥
	switch rule.Direction {
	case common.TrafficDirrectionIngress:
		req.Direction = "ingress"
		req.RemoteIpPrefix = ptr.To(string(rule.Src))
		if rule.SrcPort.Min > 0 {
			req.PortRangeMin = &rule.SrcPort.Min
		}
		if rule.SrcPort.Max > 0 {
			req.PortRangeMax = &rule.SrcPort.Max
		}
	case common.TrafficDirrectionEgress:
		req.Direction = "egress"
		req.RemoteIpPrefix = ptr.To(string(rule.Dest))
		if rule.DestPort.Min > 0 {
			req.PortRangeMin = &rule.DestPort.Min
		}
		if rule.DestPort.Max > 0 {
			req.PortRangeMax = &rule.DestPort.Max
		}
	}
	resp, err := p.Client.VPCV2.CreateSecurityGroupRule(&modelv2.CreateSecurityGroupRuleRequest{
		Body: &modelv2.CreateSecurityGroupRuleRequestBody{SecurityGroupRule: req},
	})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

func coverHcsSecurityGroupRuleToSecurityGroupRule(hcs modelv2.SecurityGroupRule) common.SecurityGroupRule {
	rule := common.SecurityGroupRule{
		Descripter: common.Descripter{
			ID:          hcs.Id,
			Name:        hcs.Description,
			Description: hcs.Description,
		},
		Protocol:      protocolToProtocol(hcs.Protocol),
		SecurityGroup: hcs.SecurityGroupId,
		Famlily:       common.IPFamily(hcs.Ethertype),
	}
	switch hcs.Direction {
	case "ingress":
		rule.Direction = common.TrafficDirrectionIngress
		rule.Src = common.CIDR(hcs.RemoteIpPrefix)
		rule.SrcPort = common.PortRange{Min: hcs.PortRangeMin, Max: hcs.PortRangeMax}
	case "egress":
		rule.Direction = common.TrafficDirrectionEgress
		rule.Dest = common.CIDR(hcs.RemoteIpPrefix)
		rule.DestPort = common.PortRange{Min: hcs.PortRangeMin, Max: hcs.PortRangeMax}
	}
	return rule
}

func protocolToProtocol(protocol string) common.IPProtocol {
	switch protocol {
	case "tcp":
		return common.TCP
	case "udp":
		return common.UDP
	case "icmp":
		return common.ICMP
	default:
		return common.IPProtocol(strings.ToUpper(protocol))
	}
}
