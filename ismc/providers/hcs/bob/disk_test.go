package bob

import (
	"context"
	"encoding/json"
	"testing"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
)

func TestCreateDisk(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	disk := &common.Disk{
		Descripter: common.Descripter{
			Name:        "test-disk-sync2",
			Description: "test create by ismc",
		},
		Zone:        "az1.dc1",
		DiskClass:   "LDC_SAS_Volume",
		Size:        resource.MustParse("20Gi"),
		DiskCluster: "FUSION_CLOUD_majnoon-dccloud-1", // 资源池ID
	}
	_, err := globalProvider.CreateDisk(ctx, disk, common.CreateDiskOptions{
		Wait: true,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestUpdateDisk(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	disk := &common.Disk{
		Descripter: common.Descripter{
			ID: "036f4488-8106-488e-a4d8-c54654508c17",
		},
		Size: resource.MustParse("40Gi"),
	}
	err := globalProvider.UpdateDisk(ctx, disk)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestGetDisk(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	disk, err := globalProvider.GetDisk(ctx, "036f4488-8106-488e-a4d8-c54654508c17")
	if err != nil {
		t.Fatal(err)
	}
	data, _ := json.Marshal(disk)
	t.Log(string(data))
}

func TestListDisks(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	disks, err := globalProvider.ListDisks(ctx, common.ListDiskOptions{
		Zone: "az1.dc1",
	})
	if err != nil {
		t.Fatal(err)
	}
	for _, disk := range disks.Items {
		data, _ := json.Marshal(disk)
		t.Log(string(data))
	}
}

func TestDeleteDisk(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	err := globalProvider.DeleteDisk(ctx, "036f4488-8106-488e-a4d8-c54654508c17", common.DeleteDiskOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestListDiskClass(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListDiskClass(ctx, common.ListDiskClassOptions{
		Zone: "az1.dc1",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestListDiskSnapshots(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	_, err := globalProvider.ListDiskSnapshots(ctx, common.ListDiskSnapshotOption{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestProvider_resizeDiskUseSubscription(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	diskid := "5ccd1530-35dd-4d78-9546-990490d87caf"
	if err := globalProvider.resizeDiskUseSubscription(ctx, diskid, 41, true); err != nil {
		t.Fatal(err)
	}
}
