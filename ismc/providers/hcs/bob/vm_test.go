package bob

import (
	"context"
	"strconv"
	"testing"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

func TestListVirtualMachines(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListVirtualMachines(ctx, common.ListVirtualMachineOptions{
		ListOptions: common.ListOptions{
			Page: 1,
			Size: 10,
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestParse(t *testing.T) {
	qota := resource.MustParse("50Gi")
	t.Log(qota.Value() >> 30)
}

func TestCreateVirtualMachine(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.CreateVirtualMachine(ctx, &common.VirtualMachine{
		Descripter: common.Descripter{
			Name: "testvm-alpine-4",
			Labels: map[string]string{
				"instance": "c7bd6df9d23123",
			},
		},
		InstanceType: common.VirtualMachineInstanceTypeRef{
			ID: "7b94e87c-7152-459b-aae0-74ea8277420e",
		},
		Image: common.VirtualMachineImageRef{
			ID: "8bb364f4-71b8-4f40-9d06-0681158f9b21",
		},
		Zone:     "az1.dc1",
		Password: "Demo!@#admin1234",
		Power:    common.PowerStateOn,
		Disks: []common.VirtualMachineDiskRef{
			{
				Disk: common.Disk{
					Size:      resource.MustParse("40Gi"),
					DiskClass: "LDC_SAS_Volume",
				},
			},
		},
		Interfaces: []common.VirtualMachineInterfaceRef{
			{
				ID: "4ca4f1e0-39a7-4729-b041-578e77209fc1",
			},
		},
	}, common.CreateVirtualMachineOptions{
		Wait: true,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUpdateVirtualMachine(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	err := globalProvider.UpdateVirtualMachine(ctx, &common.VirtualMachine{
		Descripter: common.Descripter{
			ID:          "da3a15ad-4fd1-4faa-8f8c-4390e9d57701",
			Name:        "update-name",
			Description: "aaaaaaaa",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestDeleteVirtualMachine(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	err := globalProvider.DeleteVirtualMachine(ctx, "2fb2b77d-247c-478f-a9ba-d354776d9840", common.DeleteVirtualMachineOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestGetVirtualMachineRemoteConnectionInfo(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetVirtualMachineRemoteConnectionInfo(ctx, "79820403-a2f0-4a4f-a270-e242bcc3c2a4")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestGetVirtualMachine(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetVirtualMachine(ctx, "79820403-a2f0-4a4f-a270-e242bcc3c2a4")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestUpdateVirtualMachineLabels(t *testing.T) {
	p := setupProvider(t)
	serverid := "53f671e7-b083-4403-8a6b-8fd32682b8a4"
	resp, err := p.Client.ECS.UpdateServerMetadata(&model.UpdateServerMetadataRequest{
		ServerId: serverid,
		Body: &model.UpdateServerMetadataRequestBody{
			Metadata: map[string]string{
				"application.xiaoshiai.cn/config-hash": "c7bd6df9d",
			},
		},
	})
	if err != nil {
		return
	}
	t.Log(resp)
}

func Test_escapeTagKey(t *testing.T) {
	tests := []struct {
		k string
	}{
		{
			k: "application.kubernetes.io/name",
		},
	}
	for _, tt := range tests {
		t.Run(tt.k, func(t *testing.T) {
			got := escapeTagKey(tt.k)
			key := unescapeTagKey(got)
			if key != tt.k {
				t.Errorf("unescapeTagKey() = %v, want %v", key, tt.k)
			}
		})
	}
}

func Test_parseExtrainfo(t *testing.T) {
	example := "pcibridge:2,iohang_timeout:720,system_serial_number:6149f842-7188-4aea-b259-96592610f037,uefi_mode_sysinfo_fields:version_serial_uuid_family_asset_product_manufacturer,max_mem:4194304,num_of_mem_plug:0,org_mem:2048,current_mem:2048,max_cpu:2,org_cpu:2,current_cpu:2,cpu_num_for_one_plug:1,xml_support_live_resize:False"
	got := parseExtrainfo(example)
	t.Log(got)
}

func Test_createVirtualMachineUseSubscription(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	count := 10

	prepaid := client.PrePaidServer{
		PrePaidServer: model.PrePaidServer{
			ImageRef:  "2aeb2050-e138-4e0b-876d-fb66577a2363",
			FlavorRef: "19ebd6f0-b310-492c-9865-7d0f3b66cf66",
			AdminPass: ptr.To("P@ssw0rd"),
			Vpcid:     "4ca4f1e0-39a7-4729-b041-578e77209fc1",
			Nics: []model.PrePaidServerNic{
				{SubnetId: "0afd9ab8-fbf5-4dcf-bbb8-5df9e1023ac6"},
			},
			AvailabilityZone: ptr.To("az2.dc2"),
			RootVolume: &model.PrePaidServerRootVolume{
				Volumetype: client.NewPrePaidServerRootVolumeVolumetype("O3B_SAS_Volume"),
				Size:       ptr.To(int32(40)),
			},
			Extendparam: &model.PrePaidServerExtendParam{
				RegionID:            ptr.To("majnoon-dccloud-1"),
				EnterpriseProjectId: ptr.To("0"),
				ChargingMode:        ptr.To(model.GetPrePaidServerExtendParamChargingModeEnum().PRE_PAID),
			},
			SecurityGroups: &[]model.PrePaidServerSecurityGroup{
				{Id: ptr.To("1b019942-7219-4bcc-ac33-2755aede0ffa")},
			},
			Metadata: map[string]string{
				"productId": "acd6d1c04a9545409f94075d3437708a",
			},
		},
	}

	for i := 0; i < count; i++ {
		prepaid.Name = "testvm-" + strconv.Itoa(i)
		resp, err := globalProvider.createVirtualMachineUseSubscription(ctx, prepaid, common.CreateVirtualMachineOptions{})
		if err != nil {
			t.Errorf("create vm %s failed: %v", prepaid.Name, err)
			continue
		}
		t.Logf("create vm %s send success: %v", prepaid.Name, resp)
	}
}
// ListVirtualMachineNetworkInterfaces
func TestListVirtualMachineNetworkInterfaces(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListVirtualMachineNetworkInterfaces(ctx, "d92de84a-e691-4771-83db-67dea8ca14e8", common.ListVirtualMachineNetworkOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}