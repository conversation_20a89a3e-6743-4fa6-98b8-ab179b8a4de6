package bob

import (
	"context"
	"strings"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

var _ common.StatisticsOperation = &Provider{}

// GetSystemStatistics implements common.StatisticsOperation.
func (p *Provider) GetSystemStatistics(ctx context.Context) (*common.SystemStatistics, error) {
	var (
		resources    = make([]client.ResourceResult, 0)
		start, limit = 0, 10
	)
	for {
		result, err := p.Client.GetBasicResources(ctx, client.BasicResourcesRequest{
			ResourceType: client.AllBasicResourceTypes,
			Start:        start,
			Limit:        limit,
		})
		if err != nil {
			return nil, err
		}
		resources = append(resources, result.Resources...)
		if len(result.Resources) < limit {
			break
		}
		start += limit
	}
	return covertResourcesToSystemStatistics(resources), nil
}

func covertResourcesToSystemStatistics(resources []client.ResourceResult) *common.SystemStatistics {
	var (
		zoneMap     = make(map[string]struct{})
		resourceMap = make(map[string]int)
		volTypes    = make(map[string]struct{})
		diskTotal   float64
	)
	for _, resource := range resources {
		resourceMap[resource.ResourceType] += 1
		if resource.AzID != "" {
			zoneMap[resource.AzID] = struct{}{}
		}
		switch resource.ResourceType {
		case "vpc":
			if value, ok := resource.Extend["CIDR"].(string); ok {
				_ = value
			}
			if value, ok := resource.Extend["SERVICE_AVAILABILITY_ZONES"].(string); ok {
				// "az2.dc2,az1.dc1"
				for _, zone := range strings.Split(value, ",") {
					if zone == "" {
						continue
					}
					zoneMap[zone] = struct{}{}
				}
			}
		case "evs":
			if value, ok := resource.Extend["DISK_SIZE"].(float64); ok {
				diskTotal += value
			}
			if voltype, ok := resource.Extend["VOLUME_TYPE"].(string); ok {
				volTypes[voltype] = struct{}{}
			}
		}
	}
	return &common.SystemStatistics{
		ZoneCount:           len(zoneMap),
		VirtualMachineCount: resourceMap["ecs"],
		VirtualNetworkCount: resourceMap["vpc"],
		DiskClusterCount:    len(volTypes),
		DiskCount:           resourceMap["evs"],
		StorageUsed:         *resource.NewQuantity(int64(diskTotal)<<30, resource.BinarySI),
	}
}
