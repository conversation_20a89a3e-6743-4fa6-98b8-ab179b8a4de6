package bob

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/evs/v2/model"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

var (
	_ common.DiskOperation         = &Provider{}
	_ common.DiskClusterOperation  = &Provider{}
	_ common.DiskSnapshotOperation = &Provider{}
)

// ListDiskClass implements common.Provider.
// 查询云硬盘类型
func (p *Provider) ListDiskClass(ctx context.Context, options common.ListDiskClassOptions) (common.List[common.DiskClass], error) {
	resp, err := p.Client.EVS.CinderListVolumeTypes(&model.CinderListVolumeTypesRequest{})
	if err != nil {
		return common.List[common.DiskClass]{}, err
	}
	if resp.VolumeTypes == nil {
		return common.List[common.DiskClass]{}, nil
	}
	class := make([]common.DiskClass, 0, len(*resp.VolumeTypes))
	for _, d := range *resp.VolumeTypes {
		class = append(class, coverHcsDiskClassToDiskClass(d))
	}
	if options.Zone != "" {
		class = slices.DeleteFunc(class, func(c common.DiskClass) bool {
			return !slices.Contains(c.AvailableZones, options.Zone)
		})
	}
	return common.PageList(class, options.ListOptions), nil
}

// GetDiskClass implements common.Provider.
func (p *Provider) GetDiskClass(ctx context.Context, id string) (*common.DiskClass, error) {
	resp, err := p.Client.EVS.CinderListVolumeTypes(&model.CinderListVolumeTypesRequest{})
	if err != nil {
		return nil, err
	}
	if resp.VolumeTypes == nil {
		return nil, errors.NewNotFound("disk class", id)
	}
	for _, d := range *resp.VolumeTypes {
		if d.Name == id {
			dc := coverHcsDiskClassToDiskClass(d)
			return &dc, nil
		}
	}
	return nil, errors.NewNotFound("disk class", id)
}

// 使用stack api查询云硬盘列表
func (p *Provider) ListDisks(ctx context.Context, options common.ListDiskOptions) (common.List[common.Disk], error) {
	req := &model.ListVolumesRequest{}
	if options.Zone != "" {
		req.AvailabilityZone = &options.Zone
	}
	if options.Name != "" {
		req.Name = &options.Name
	} else {
		if options.Search != "" {
			req.Name = &options.Search
		}
	}
	if options.VirtualMachine != "" {
		req.ServerId = &options.VirtualMachine
	}
	if options.Size > 0 {
		page := options.Page
		if page < 1 {
			page = 1
		}
		req.Offset = ptr.To(int32((page - 1) * options.Size))
		req.Limit = ptr.To(int32(options.Size))
	}
	// 返回结果按该关键字排序，支持id，status，size，created_at等关键字，默认为“created_at”。
	// 返回结果按照降序或升序排列，默认为“desc”。 降序：desc 升序：asc
	req.SortKey, req.SortDir = getSortKeyDir(options.ListOptions)

	resp, err := p.Client.ListVolumes(req)
	if err != nil {
		return common.List[common.Disk]{}, err
	}
	if resp.Volumes == nil {
		return common.List[common.Disk]{}, nil
	}
	disks := make([]common.Disk, 0, len(*resp.Volumes))
	for _, d := range *resp.Volumes {
		disks = append(disks, coverHCSVolumeToDisk(d.VolumeDetail))
	}
	return common.List[common.Disk]{
		Items: disks,
		Page:  options.Page,
		Size:  options.Size,
		Total: int(ptr.Deref(resp.Count, 0)),
	}, nil
}

const EVSTimeLayout = "2006-01-02T15:04:05.000000"

func coverHCSVolumeToDisk(hcsDisk model.VolumeDetail) common.Disk {
	createTime, _ := time.Parse(EVSTimeLayout, hcsDisk.CreatedAt)
	disk := common.Disk{
		Descripter: common.Descripter{
			ID:                hcsDisk.Id,
			Name:              hcsDisk.Name,
			CreationTimestamp: createTime,
			ResourceGroup:     ptr.Deref(hcsDisk.EnterpriseProjectId, ""),
			Annotations:       map[string]string{},
		},
		Zone:      hcsDisk.AvailabilityZone,
		DiskClass: hcsDisk.VolumeType,
		Bootable:  hcsDisk.Bootable == "true",
		Size:      *resource.NewQuantity(int64(hcsDisk.Size)*GB, resource.BinarySI),
		Status: common.DiskStatus{
			Phase: hcsDisk.Status,
			// Device: hcsDisk.OsVolHostAttrhost,
		},
	}
	mounts := make([]common.DiskMountInfo, len(hcsDisk.Attachments))
	for i, attachment := range hcsDisk.Attachments {
		mountTime, _ := time.Parse(EVSTimeLayout, attachment.AttachedAt)
		mounts[i] = common.DiskMountInfo{
			Device:         attachment.Device,
			MountTime:      &mountTime,
			VirtualMachine: attachment.ServerId, // 此处使用serverID，因为attachment.HostName为空
		}
	}
	disk.Status.Mounts = mounts
	return disk
}

func (p *Provider) CreateDisk(ctx context.Context, disk *common.Disk, options common.CreateDiskOptions) (*common.Descripter, error) {
	vol := &model.CreateVolumeOption{
		Name:        &disk.Name,
		Description: &disk.Description,
		Metadata: map[string]string{
			"hw:passthrough": "false",
		},
		AvailabilityZone: disk.Zone,
		Size:             int32(disk.Size.Value() / GB),
		VolumeType:       client.NewCreateVolumeOptionVolumeType(disk.DiskClass),
		Count:            ptr.To(int32(1)),
	}
	if disk.From != nil {
		if disk.From.Image != "" {
			vol.ImageRef = &disk.From.Image
		}
		if disk.From.Snapshot != "" {
			vol.SnapshotId = &disk.From.Snapshot
		}
	}
	resp, err := p.Client.EVS.CreateVolume(&model.CreateVolumeRequest{
		Body: &model.CreateVolumeRequestBody{Volume: vol},
	})
	if err != nil {
		return nil, err
	}
	p.tasksCache.AddEvsJobToWatch(ctx, resp.JobId, resp.OrderId)

	created := common.Descripter{Name: disk.Name}
	if resp.VolumeIds != nil && len(*resp.VolumeIds) > 0 {
		created.ID = (*resp.VolumeIds)[0]
	}
	if !options.Wait {
		return &created, nil
	}
	if resp.JobId == nil || *resp.JobId == "" {
		return &created, nil
	}
	jobid := *resp.JobId

	jobresult, err := p.pollEVSJob(ctx, jobid)
	if err != nil {
		return nil, errors.NewInternalError(err)
	}
	diskid := ""
	if jobresult.Entities != nil {
		if jobresult.Entities.VolumeId != nil {
			diskid = *jobresult.Entities.VolumeId
		} else if jobresult.Entities.SubJobs != nil {
			for _, subjob := range *jobresult.Entities.SubJobs {
				if subjob.Entities != nil && subjob.Entities.VolumeId != nil {
					diskid = *subjob.Entities.VolumeId
					break
				}
			}
		}
	}
	created.ID = diskid
	log.Info("evs created disk", "disk", created)
	return &created, nil
}

func (p *Provider) pollEVSJob(ctx context.Context, jobid string) (*model.ShowJobResponse, error) {
	var lastresp *model.ShowJobResponse
	err := wait.PollUntilContextCancel(ctx, 5*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log := log.FromContext(ctx).WithValues("jobid", jobid)
		resp, err := p.Client.EVS.ShowJob(&model.ShowJobRequest{JobId: jobid})
		if err != nil {
			return false, err
		}
		lastresp = resp

		log.Info("evs show job", "job", resp)
		if resp.Status == nil {
			return false, nil
		}
		switch *resp.Status {
		case model.GetShowJobResponseStatusEnum().FAIL:
			return false, fmt.Errorf("job failed: %s", ptr.Deref(resp.FailReason, ""))
		case model.GetShowJobResponseStatusEnum().SUCCESS:
			return true, nil
		case model.GetShowJobResponseStatusEnum().RUNNING:
			log.Info("evs job still running")
			return false, nil
		default:
			log.Info("evs job status", *resp.Status)
			return false, nil
		}
	})
	return lastresp, err
}

// UpdateDisk implements common.Provider.
func (p *Provider) UpdateDisk(ctx context.Context, disk *common.Disk) error {
	log := log.FromContext(ctx)
	exists, err := p.GetDisk(ctx, disk.ID)
	if err != nil {
		return err
	}
	existssizeGB := int32(exists.Size.Value() >> 30) // G
	targetSizeGB := int32(disk.Size.Value() >> 30)   // G

	if targetSizeGB < existssizeGB {
		return errors.NewBadRequest(fmt.Sprintf("can't shrink disk size from %d to %d", existssizeGB, targetSizeGB))
	}
	if existssizeGB != targetSizeGB {
		log.Info("resize disk", disk.ID, "from", existssizeGB, "to", targetSizeGB)
		if p.UseSubscription {
			if err := p.resizeDiskUseSubscription(ctx, disk.ID, int(targetSizeGB), false); err != nil {
				return err
			}
		} else {
			if err := p.resizeDisk(ctx, disk.ID, int(targetSizeGB), false); err != nil {
				return err
			}
		}
	}
	if disk.Name != exists.Name || disk.Description != exists.Description {
		log.Info("update disk", disk.ID, "name", disk.Name, "description", disk.Description)
		resp, err := p.Client.UpdateVolume(ctx, disk.ID, client.UpdateVolumeRequest{
			Name: disk.Name, Description: disk.Description,
		})
		if err != nil {
			return err
		}
		_ = resp
	}
	return nil
}

func (p *Provider) GetDisk(ctx context.Context, id string) (*common.Disk, error) {
	resp, err := p.Client.ShowVolume(&model.ShowVolumeRequest{VolumeId: id})
	if err != nil {
		return nil, err
	}
	if resp.Volume == nil {
		return nil, errors.NewNotFound("disk", id)
	}
	disk := coverHCSVolumeToDisk(resp.Volume.VolumeDetail)
	return &disk, nil
}

// DeleteDisk implements common.Provider.
func (p *Provider) DeleteDisk(ctx context.Context, id string, options common.DeleteDiskOptions) error {
	log := log.FromContext(ctx)
	log.Info("evs delete disk", "disk", id)
	// DeleteDisk use subscription, so no jobid
	sub := client.SimpleSubscription{
		OperateType: client.SubscriptionOperateTypeDelete,
		ServiceType: client.ServiceTypeEVS,
		Params: map[string]any{
			"action": "delete",
			"ids": []map[string]any{
				{"id": id},
			},
		},
	}
	resp, err := p.Client.CreateSubscriptionSimple(ctx, sub)
	if err != nil {
		return err
	}
	// add to watch
	p.tasksCache.AddSubscriptionToWatch(ctx, resp.SubscriptionID)
	if !options.Wait {
		return nil
	}
	jobresult, err := p.pollSubscription(ctx, resp.SubscriptionID)
	if err != nil {
		return errors.NewInternalError(err)
	}
	log.Info("evs delete disk", "job", jobresult)
	return nil

	// resp, err := p.Client.EVS.DeleteVolume(&model.DeleteVolumeRequest{VolumeId: id})
	// if err != nil {
	// 	return err
	// }
	// add to watch
	// p.tasksCache.AddEvsJobToWatch(ctx, resp.JobId, nil)

	// if resp.JobId == nil || *resp.JobId == "" || !options.Wait {
	// 	return nil
	// }
	// jobresult, err := p.pollEVSJob(ctx, *resp.JobId)
	// if err != nil {
	// 	return errors.NewInternalError(err)
	// }
	// log.Info("evs delete disk", "job", jobresult)
	// return nil
}

func coverHcsDiskClassToDiskClass(d model.VolumeType) common.DiskClass {
	dc := common.DiskClass{
		Descripter: common.Descripter{
			ID:          d.Name, // vm creation use volumeType as diskclass so use name as "id"
			UID:         d.Id,
			Name:        d.Name,
			Annotations: map[string]string{},
			Description: ptr.Deref(d.Description, ""),
		},
		Public: ptr.Deref(d.IsPublic, false),
	}
	if ext := d.ExtraSpecs; ext != nil {
		if ext.VolumeBackendName != nil {
			dc.Annotations["volume_backend_name"] = *ext.VolumeBackendName
		}
		if ext.AvailabilityZone != nil {
			dc.AvailableZones = append(dc.AvailableZones, *ext.AvailabilityZone)
		}
		if ext.HWavailabilityZone != nil {
			dc.AvailableZones = append(dc.AvailableZones, *ext.HWavailabilityZone)
		}
		if ext.OsVendorExtendedsoldOutAvailabilityZones != nil {
			dc.Annotations["os-vendor-extended:sold_out_availability_zones"] = *ext.OsVendorExtendedsoldOutAvailabilityZones
		}
		if ext.RESKEYavailabilityZones != nil {
			dc.Annotations["RESKEY:availability_zones"] = *ext.RESKEYavailabilityZones
		}
	}
	return dc
}

// ResizeDisk implements common.DiskOperation.
func (p *Provider) ResizeDisk(ctx context.Context, id string, size resource.Quantity, options common.ResizeDiskOptions) error {
	targetSizeGB := ToGB(size.Value())
	log := log.FromContext(ctx)
	log.Info("resize disk", id, "to", targetSizeGB)
	if p.UseSubscription {
		if err := p.resizeDiskUseSubscription(ctx, id, int(targetSizeGB), false); err != nil {
			return err
		}
	} else {
		if err := p.resizeDisk(ctx, id, int(targetSizeGB), false); err != nil {
			return err
		}
	}
	return nil
}

// ReInitalizeDisk implements common.DiskOperation.
func (p *Provider) ReInitalizeDisk(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

func (p *Provider) resizeDisk(ctx context.Context, id string, sizeGB int, wait bool) error {
	log := log.FromContext(ctx)
	log.V(2).Info("use evs api to resize disk", "disk", id, "size", sizeGB, "wait", wait)

	resp, err := p.Client.EVS.ResizeVolume(&model.ResizeVolumeRequest{
		VolumeId: id,
		Body:     &model.ResizeVolumeRequestBody{OsExtend: &model.OsExtend{NewSize: int32(sizeGB)}},
	})
	if err != nil {
		return err
	}
	// add to watch
	p.tasksCache.AddEvsJobToWatch(ctx, resp.JobId, resp.OrderId)

	if !wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobresult, err := p.pollEVSJob(ctx, *resp.JobId)
	if err != nil {
		return errors.NewInternalError(err)
	}
	log.Info("evs resize disk", "job", jobresult)
	return nil
}

func (p *Provider) resizeDiskUseSubscription(ctx context.Context, id string, sizeGB int, wait bool) error {
	log := log.FromContext(ctx)
	log.V(2).Info("use subscription to resize disk", "disk", id, "size", sizeGB, "wait", wait)

	sub := client.SimpleSubscription{
		OperateType: client.SubscriptionOperateTypeModify,
		ServiceType: client.ServiceTypeEVS,
		Params: map[string]any{
			"action": "extend",
			"ids": []map[string]any{
				{"id": id, "new_size": sizeGB},
			},
		},
	}
	resp, err := p.Client.CreateSubscriptionSimple(ctx, sub)
	if err != nil {
		return err
	}
	// add to watch
	p.tasksCache.AddSubscriptionToWatch(ctx, resp.SubscriptionID)
	if !wait {
		return nil
	}
	jobresult, err := p.pollSubscription(ctx, resp.SubscriptionID)
	if err != nil {
		return errors.NewInternalError(err)
	}
	log.Info("evs resize disk", "job", jobresult)
	return nil
}
