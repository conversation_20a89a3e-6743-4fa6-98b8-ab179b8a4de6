package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

func Test_tasksCache_startWatchSubscription(t *testing.T) {
	provider := setupProvider(t)
	ctx := context.Background()

	orders, err := provider.Client.ListOrders(ctx, client.ListOrderRequest{})
	if err != nil {
		t.Fatal(err)
	}
	if len(orders.Orders) == 0 {
		t.Fatal("no orders")
	}
	order0 := orders.Orders[0]

	provider.tasksCache.AddSubscriptionToWatch(ctx, order0.OrderID)

	onEvent := func(ctx context.Context, event common.WatchEvent[common.Task]) error {
		t.Logf("event: %v", event)
		return nil
	}
	if err := provider.WactchTasks(ctx, onEvent, common.WactchTaskOptions{}); err != nil {
		t.Fatal(err)
	}
}
