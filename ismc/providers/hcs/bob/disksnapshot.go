package bob

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/evs/v2/model"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
)

// ListDiskSnapshots implements common.Provider.
func (p *Provider) ListDiskSnapshots(ctx context.Context, options common.ListDiskSnapshotOption) (common.List[common.DiskSnapshot], error) {
	req := &model.ListSnapshotsRequest{}
	if options.Zone != "" {
		req.AvailabilityZone = &options.Zone
	}
	resp, err := p.Client.EVS.ListSnapshots(req)
	if err != nil {
		return common.List[common.DiskSnapshot]{}, err
	}
	if resp.Snapshots == nil {
		return common.List[common.DiskSnapshot]{}, nil
	}
	snapshots := make([]common.DiskSnapshot, 0, len(*resp.Snapshots))
	for _, d := range *resp.Snapshots {
		snapshots = append(snapshots, coverHCSVolumeToDiskSnapshot(d))
	}
	return common.List[common.DiskSnapshot]{
		Items: snapshots,
		Total: int(ptr.Deref(resp.Count, 0)),
		Page:  options.Page,
		Size:  options.Size,
	}, nil
}

func coverHCSVolumeToDiskSnapshot(hcsDisk model.SnapshotList) common.DiskSnapshot {
	createTime, _ := time.Parse(EVSTimeLayout, hcsDisk.CreatedAt)
	disk := common.DiskSnapshot{
		Descripter: common.Descripter{
			ID:                hcsDisk.Id,
			Name:              ptr.Deref(hcsDisk.Name, ""),
			Description:       ptr.Deref(hcsDisk.Description, ""),
			CreationTimestamp: createTime,
			Annotations:       map[string]string{},
			Labels:            hcsDisk.Metadata,
		},
		Source: common.DiskSnapshotSource{
			Disk: hcsDisk.VolumeId,
		},
	}
	if progress := hcsDisk.OsExtendedSnapshotAttributesprogress; progress != "100%" && progress != "" {
		disk.Status.Message = fmt.Sprintf("progress: %s", progress)
	}
	switch hcsDisk.Status {
	case "creating":
		disk.Status.Phase = common.DiskSnapshotPhasePending
	case "available":
		disk.Status.Phase = common.DiskSnapshotPhaseReady
	}
	return disk
}

var notAllowedPrefix = []string{"autobk_snapshot", "manualbk_snapshot", "sys_snapshot"}

// 快照名称不能以autobk_snapshot, manualbk_snapshot及sys_snapshot作为前缀
// CreateDiskSnapshot implements common.Provider.
func (p *Provider) CreateDiskSnapshot(ctx context.Context, snapshot *common.DiskSnapshot) error {
	for _, prefix := range notAllowedPrefix {
		if strings.HasPrefix(snapshot.Name, prefix) {
			return errors.NewBadRequest(fmt.Sprintf("snapshot name can not start with %v", notAllowedPrefix))
		}
	}
	if snapshot.Source.Disk == "" {
		return errors.NewBadRequest(".source.disk is required")
	}
	resp, err := p.Client.EVS.CreateSnapshot(&model.CreateSnapshotRequest{
		Body: &model.CreateSnapshotRequestBody{
			Snapshot: &model.CreateSnapshotOption{
				Name:        &snapshot.Name,
				Description: &snapshot.Description,
				VolumeId:    snapshot.Source.Disk,
				Metadata:    snapshot.Labels,
			},
		},
	})
	if err != nil {
		return err
	}
	if created := resp.Snapshot; created != nil {
		created := common.Descripter{ID: *created.Id}
		_ = created
	}
	return nil
}

// RemoveDiskSnapshot implements common.Provider.
func (p *Provider) RemoveDiskSnapshot(ctx context.Context, id string) error {
	_, err := p.Client.EVS.DeleteSnapshot(&model.DeleteSnapshotRequest{SnapshotId: id})
	return err
}
