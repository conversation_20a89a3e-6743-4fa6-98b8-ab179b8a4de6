package bob

import (
	"context"
	"encoding/json"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestListZones(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	zones, err := globalProvider.ListZones(ctx, common.ListZoneOptions{})
	if err != nil {
		t.<PERSON>(err)
	}
	for _, zone := range zones.Items {
		data, _ := json.Marshal(zone)
		t.Log(string(data))
	}
}

func TestGetZone(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	zone, err := globalProvider.GetZone(ctx, "az1.dc1")
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	data, _ := json.Marshal(zone)
	t.Log(string(data))
}

func TestGetSystemStatistics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetSystemStatistics(ctx)
	if err != nil {
		t.<PERSON><PERSON>(err)
	}
	t.Log(*resp)
}
