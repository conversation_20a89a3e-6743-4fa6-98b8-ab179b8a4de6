package bob

import (
	"context"
	"reflect"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	evsmodel "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/evs/v2/model"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	sdkclient "xiaoshiai.cn/core/ismc/providers/hcs/client"
)

// ListTasks implements common.Provider.
func (p *Provider) ListTasks(ctx context.Context, opts common.ListTaskOptions) (common.List[common.Task], error) {
	if opts.Sort == "" {
		opts.Sort = "time-"
	}
	return p.tasksCache.tasksList(opts), nil
}

func (p *Provider) GetTask(ctx context.Context, id string) (*common.Task, error) {
	task := p.tasksCache.get(id)
	if task == nil {
		return nil, errors.NewNotFound("task", id)
	}
	return task, nil
}

// RemoveTask implements common.Provider.
func (p *Provider) RemoveTask(ctx context.Context, id string) error {
	if ok := p.tasksCache.remove(id); !ok {
		return errors.NewNotFound("task", id)
	}
	return nil
}

// RestartTask implements common.Provider.
func (p *Provider) RestartTask(ctx context.Context, id string) error {
	return nil
}

// StopTask implements common.Provider.
func (p *Provider) StopTask(ctx context.Context, id string) error {
	return nil
}

// WactchTasks implements common.Provider.
func (p *Provider) WactchTasks(ctx context.Context, on common.OnTaskEvent, opts common.WactchTaskOptions) error {
	return p.tasksCache.Watch(ctx, on, opts)
}

func newTasksCache(ctx context.Context, cli *sdkclient.Client) *tasksCache {
	cap := 128
	tasks := &tasksCache{
		cli:      cli,
		basectx:  ctx,
		list:     make([]common.Task, 0, cap),
		cap:      cap,
		changes:  make(chan common.WatchEvent[common.Task], 32),
		watchers: make(map[string]*taskWatcher),
	}
	go tasks.run(ctx)
	return tasks
}

type tasksCache struct {
	basectx context.Context
	cli     *sdkclient.Client

	cap  int
	list []common.Task
	mu   sync.RWMutex

	changes chan common.WatchEvent[common.Task]

	watchers map[string]*taskWatcher
	watchmu  sync.RWMutex
}

func (t *tasksCache) AddSubscriptionToWatch(_ context.Context, subid string) {
	if subid == "" {
		return
	}
	go t.startWatchSubscription(subid)
}

func (t *tasksCache) AddEcsJobToWatch(_ context.Context, jobid *string, orderid *string) {
	if jobid != nil && *jobid != "" {
		go t.startWatchEcsJob(*jobid)
		return
	}
	if orderid != nil && *orderid != "" {
		go t.startWatchSubscription(*orderid)
	}
}

func (t *tasksCache) AddEvsJobToWatch(_ context.Context, jobid *string, orderid *string) {
	if jobid != nil && *jobid != "" {
		go t.startWatchEvsJob(*jobid)
		return
	}
	if orderid != nil && *orderid != "" {
		go t.startWatchSubscription(*orderid)
	}
}

func (t *tasksCache) startWatchEvsJob(jobid string) {
	watchTask(t.basectx, t, "disks", jobid, func(ctx context.Context, id string) (*common.Task, error) {
		job, err := t.cli.EVS.ShowJob(&evsmodel.ShowJobRequest{JobId: id})
		if err != nil {
			return nil, err
		}
		return convertEvsJobToTask(job), nil
	})
}

func (t *tasksCache) startWatchEcsJob(jobid string) {
	watchTask(t.basectx, t, "virtualmachines", jobid, func(ctx context.Context, id string) (*common.Task, error) {
		job, err := t.cli.ECS.ShowJob(&model.ShowJobRequest{JobId: id})
		if err != nil {
			return nil, err
		}
		return convertEcsJobToTask(job), nil
	})
}

func watchTask(ctx context.Context, t *tasksCache, resourcetype string, jobid string, checkTask func(ctx context.Context, id string) (*common.Task, error)) {
	log := log.FromContext(ctx).WithValues("jobid", jobid, "resourcetype", resourcetype)

	log.Info("start watch job")
	defer log.Info("stop watch job")

	err := wait.PollUntilContextTimeout(ctx, 5*time.Second, 10*time.Minute, true, func(ctx context.Context) (bool, error) {
		task, err := checkTask(ctx, jobid)
		if err != nil {
			log.Error(err, "get job failed")
			return false, err
		}
		if task.ResourceType != "" && resourcetype == "" {
			resourcetype = task.ResourceType
		}
		if task.ResourceType == "" {
			task.ResourceType = resourcetype
		}
		// update task
		t.onTask(*task)
		switch task.Status.Phase {
		case common.TaskPhaseFailed:
			log.Info("job failed", "message", task.Status.Message)
			return true, nil // stop watch
		case common.TaskPhaseSuccess:
			log.Info("job success")
			return true, nil
		default:
			log.Info("job still running")
			return false, nil
		}
	})
	if err != nil {
		log.Info("watch job error", "error", err)
		unknown := common.Task{
			Descripter:   common.Descripter{ID: jobid},
			ResourceType: resourcetype,
			Status: common.TaskStatus{
				Phase:   common.TaskPhaseUnknown,
				Message: err.Error(),
			},
		}
		t.onTask(unknown)
	}
}

func convertEvsJobToTask(job *evsmodel.ShowJobResponse) *common.Task {
	task := &common.Task{
		Descripter: common.Descripter{
			ID:   ptr.Deref(job.JobId, ""),
			Name: ptr.Deref(job.JobId, ""),
		},
		Type:         getJobType(ptr.Deref(job.JobType, "")),
		ServiceType:  "evs",
		ResourceType: "disks",
		Status:       common.TaskStatus{},
	}
	if job.Status != nil {
		switch *job.Status {
		case evsmodel.GetShowJobResponseStatusEnum().INIT:
			task.Status.Phase = common.TaskPhaseQueued
		case evsmodel.GetShowJobResponseStatusEnum().RUNNING:
			task.Status.Phase = common.TaskPhaseRunning
		case evsmodel.GetShowJobResponseStatusEnum().SUCCESS:
			task.Status.Phase = common.TaskPhaseSuccess
		case evsmodel.GetShowJobResponseStatusEnum().FAIL:
			task.Status.Phase = common.TaskPhaseFailed
		default:
			task.Status.Phase = common.TaskPhase(job.Status.Value())
		}
	}
	if job.FailReason != nil {
		task.Status.Message = *job.FailReason
	}
	if job.BeginTime != nil {
		begintime, _ := time.Parse(time.RFC3339, *job.BeginTime)
		task.Status.StartTimestamp = &begintime
		task.CreationTimestamp = begintime
	}
	if job.EndTime != nil && *job.EndTime != "" {
		endtime, _ := time.Parse(time.RFC3339, *job.EndTime)
		task.Status.Progress = 100
		task.Status.EndTimestamp = &endtime
	}

	if job.Entities != nil && job.Entities.SubJobs != nil {
		for _, entity := range *job.Entities.SubJobs {
			subtask := common.SubTask{
				ID:      entity.JobId,
				Phase:   common.TaskPhase(entity.Status.Value()),
				Message: entity.FailReason,
			}
			begintime, _ := time.Parse(time.RFC3339, entity.BeginTime)
			subtask.StartTimestamp = &begintime

			endtime, _ := time.Parse(time.RFC3339, entity.EndTime)
			subtask.EndTimestamp = &endtime

			if subtask.Message != "" {
				task.Status.Message += ", " + subtask.Message
			}
			task.Status.SubTasks = append(task.Status.SubTasks, subtask)
		}
	}
	return task
}

func (t *tasksCache) Watch(ctx context.Context, on common.OnTaskEvent, opts common.WactchTaskOptions) error {
	log := log.FromContext(ctx)
	if err := t.sendInitTasks(ctx, on, opts); err != nil {
		log.Error(err, "send init tasks failed")
		return err
	}
	w := &taskWatcher{
		parent:  t,
		uid:     uuid.NewString(),
		options: opts,
		result:  make(chan common.WatchEvent[common.Task], 12),
	}
	t.mu.Lock()
	t.watchers[w.uid] = w
	t.mu.Unlock()

	defer func() {
		t.removeWatcher(w.uid)
	}()

	for {
		select {
		case <-ctx.Done():
			return nil
		case e := <-w.result:
			if err := on(ctx, e); err != nil {
				return err
			}
		}
	}
}

func (t *tasksCache) sendInitTasks(ctx context.Context, on common.OnTaskEvent, opts common.WactchTaskOptions) error {
	if opts.Sort == "" {
		opts.Sort = "time"
	}
	tasks := t.tasksList(common.ListTaskOptions{
		ListOptions:  common.ListOptions{Size: opts.Size, Sort: opts.Sort},
		ResourceType: opts.ResourceType,
		Phase:        opts.Phase,
	})
	sort.Slice(tasks.Items, func(i, j int) bool {
		return tasks.Items[i].CreationTimestamp.After(tasks.Items[j].CreationTimestamp)
	})
	for _, task := range tasks.Items {
		e := common.WatchEvent[common.Task]{Type: common.WatchEventTypeAdd, Data: task}
		if err := on(ctx, e); err != nil {
			return err
		}
	}
	return nil
}

func (t *tasksCache) run(ctx context.Context) {
	log := log.FromContext(ctx)
	log.Info("start tasks cache")
	defer log.Info("stop tasks cache")
	go t.initOrders(ctx)
	for {
		select {
		case <-ctx.Done():
			return
		case task := <-t.changes:
			watchers := t.currentWatchers(task.Data.ResourceType)
			for _, watcher := range watchers {
				// send task to watcher
				select {
				case watcher.result <- task:
				case <-ctx.Done():
					return
				default:
					log.V(5).Info("task watcher buffer full", "task", task)
				}
			}
		}
	}
}

func (t *tasksCache) removeWatcher(id string) error {
	t.watchmu.Lock()
	defer t.watchmu.Unlock()
	delete(t.watchers, id)
	return nil
}

func (t *tasksCache) initOrders(ctx context.Context) {
	log := log.FromContext(ctx)
	log.Info("init orders cache")
	defer log.Info("init orders cache done")

	orders, err := t.cli.ListOrders(ctx, sdkclient.ListOrderRequest{})
	if err != nil {
		log.Error(err, "list orders failed")
		return
	}
	for _, order := range orders.Orders {
		go t.startWatchSubscription(order.OrderID)
	}
}

func (t *tasksCache) startWatchSubscription(orderid string) {
	watchTask(t.basectx, t, "", orderid, func(ctx context.Context, id string) (*common.Task, error) {
		order, err := t.cli.GetOrder(ctx, id)
		if err != nil {
			return nil, err
		}
		resources, err := t.cli.GetOrderResources(ctx, id)
		if err != nil {
			log.FromContext(ctx).Error(err, "get order resources failed")
		}
		return orderToTask(*order, resources), nil
	})
}

func getTaskType(typ string) string {
	typetoaction := map[string]string{
		"apply":  "create",
		"modify": "update",
	}
	if action, ok := typetoaction[typ]; ok {
		return action
	}
	return typ
}

func getTaskResourceType(typ string) string {
	svcToRsourceType := map[string]string{
		"ecs": "virtualmachines",
		"evs": "disks",
		"vpc": "networks",
		"elb": "loadbalancers",
		"ims": "images",
	}
	if val := svcToRsourceType[typ]; val != "" {
		return val
	}
	return typ
}

func orderToTask(order sdkclient.Order, resources []sdkclient.OrderResource) *common.Task {
	resourcetype := getTaskResourceType(order.ServiceType)
	action := getTaskType(order.Type)

	task := &common.Task{
		Descripter: common.Descripter{
			ID:                order.OrderID,
			Name:              order.OrderID,
			CreationTimestamp: order.CreateTime.Time,
		},
		Pramas:       order.Params,
		Type:         action + " " + strings.TrimSuffix(resourcetype, "s"),
		ResourceType: resourcetype,
		ServiceType:  order.ServiceType,
		Status: common.TaskStatus{
			StartTimestamp: ptr.To(order.ImplementTime.Time),
		},
	}
	if task.Status.StartTimestamp.IsZero() {
		task.Status.StartTimestamp = ptr.To(order.CreateTime.Time)
	}

	if !order.CompleteTime.IsZero() {
		task.Status.EndTimestamp = &order.CompleteTime.Time
		task.Status.Progress = 100
	}
	var msg string
	for _, res := range resources {
		taskres := common.TaskResource{
			ID:       res.ResourceID,
			Name:     res.ResourceName,
			Resource: res.ResourceType,
		}
		if res.Message != "" {
			msg += res.Message + ", "
		}
		task.Status.Resources = append(task.Status.Resources, taskres)
	}
	switch order.Status {
	case sdkclient.OrderStatusFailed:
		task.Status.Phase = common.TaskPhaseFailed
	case sdkclient.OrderStatusSuccessed, sdkclient.OrderStatusPartialSuccessed:
		task.Status.Phase = common.TaskPhaseSuccess
		return task
	case sdkclient.OrderStatusClosed:
		task.Status.Phase = common.TaskPhaseStopped
	case sdkclient.OrderStatusHandling:
		task.Status.Phase = common.TaskPhaseQueued
	case sdkclient.OrderStatusApproving, sdkclient.OrderStatusReSubmitting:
		task.Status.Phase = common.TaskPhaseQueued
	case sdkclient.OrderStatusProcessing:
		task.Status.Phase = common.TaskPhaseRunning
	default:
		// unknown
		task.Status.Phase = common.TaskPhase(order.Status)
	}
	task.Status.Message = msg
	return task
}

func getJobType(typ string) string {
	actionmaps := map[string]string{
		"attachVolume":      "attach disk",
		"detachVolume":      "detach disk",
		"deleteVMs":         "delete virtualmachine",
		"createServer":      "create virtualmachine",
		"createEmptyVolume": "create disk",
		"changeOSV2":        "change image",
		"reinstallOsV2":     "reinstall image",
		"resizeServer":      "resize virtualmachine",
		"addNicsForServer":  "attach network interface",
		"delNicsForServer":  "detach network interface",
	}
	if action, ok := actionmaps[typ]; ok {
		return action
	}
	return typ
}

func convertEcsJobToTask(job *model.ShowJobResponse) *common.Task {
	task := &common.Task{
		Descripter: common.Descripter{
			ID:   ptr.Deref(job.JobId, ""),
			Name: ptr.Deref(job.JobId, ""),
		},
		Type:         getJobType(ptr.Deref(job.JobType, "")),
		ServiceType:  "ecs",
		ResourceType: "virtualmachines",
		Status: common.TaskStatus{
			Message: ptr.Deref(job.Message, ""),
		},
	}
	if job.Status != nil {
		switch *job.Status {
		case model.GetShowJobResponseStatusEnum().INIT:
			task.Status.Phase = common.TaskPhaseQueued
		case model.GetShowJobResponseStatusEnum().RUNNING:
			task.Status.Phase = common.TaskPhaseRunning
		case model.GetShowJobResponseStatusEnum().SUCCESS:
			task.Status.Phase = common.TaskPhaseSuccess
		case model.GetShowJobResponseStatusEnum().FAIL:
			task.Status.Phase = common.TaskPhaseFailed
		default:
			task.Status.Phase = common.TaskPhase(job.Status.Value())
		}
	}
	if job.FailReason != nil {
		task.Status.Message = *job.FailReason
	}
	if job.BeginTime != nil {
		begintime, _ := time.Parse(time.RFC3339, *job.BeginTime)
		task.Status.StartTimestamp = &begintime
		task.CreationTimestamp = begintime
	}
	if job.EndTime != nil && *job.EndTime != "" {
		endtime, _ := time.Parse(time.RFC3339, *job.EndTime)
		task.Status.Progress = 100
		task.Status.EndTimestamp = &endtime
	}

	if job.Entities != nil && job.Entities.SubJobs != nil {
		for _, entity := range *job.Entities.SubJobs {
			subtask := common.SubTask{
				ID:      ptr.Deref(entity.JobId, ""),
				Phase:   common.TaskPhase(entity.Status.Value()),
				Message: ptr.Deref(entity.FailReason, ""),
			}
			if entity.BeginTime != nil {
				begintime, _ := time.Parse(time.RFC3339, *entity.BeginTime)
				subtask.StartTimestamp = &begintime
			}
			if entity.EndTime != nil {
				endtime, _ := time.Parse(time.RFC3339, *entity.EndTime)
				subtask.EndTimestamp = &endtime
			}
			if subtask.Message != "" {
				task.Status.Message += ", " + subtask.Message
			}
			task.Status.SubTasks = append(task.Status.SubTasks, subtask)
		}
	}
	return task
}

func (t *tasksCache) onTask(task common.Task) {
	exists := t.get(task.ID)
	if exists != nil {
		// check if task is updated
		if reflect.DeepEqual(exists, task) {
			return // no change
		}
		// update existing task
		*exists = task

		// notify watchers
		t.changes <- common.WatchEvent[common.Task]{
			Type: common.WatchEventTypeUpdate,
			Data: task,
		}
		return
	}
	// add to list
	t.mu.Lock()
	defer t.mu.Unlock()

	// remove old tasks
	if len(t.list) >= t.cap*2 {
		t.list = append([]common.Task{}, t.list[t.cap:]...)
	}
	t.list = append(t.list, task)

	// notify watchers
	t.changes <- common.WatchEvent[common.Task]{
		Type: common.WatchEventTypeAdd,
		Data: task,
	}
}

func (t *tasksCache) currentWatchers(resource string) []*taskWatcher {
	t.mu.RLock()
	defer t.mu.RUnlock()
	var watchers []*taskWatcher
	for _, w := range t.watchers {
		if w.options.ResourceType != "" && w.options.ResourceType != resource {
			continue
		}
		watchers = append(watchers, w)
	}
	return watchers
}

func (t *tasksCache) get(id string) *common.Task {
	t.mu.RLock()
	defer t.mu.RUnlock()
	for i := range t.list {
		if t.list[i].ID == id {
			return &t.list[i]
		}
	}
	return nil
}

type listTaskOpts struct {
	common.ListOptions
	ResourceType string
	Phase        common.TaskPhase
}

func (t *tasksCache) tasksList(opt common.ListTaskOptions) common.List[common.Task] {
	t.mu.RLock()
	defer t.mu.RUnlock()

	var tasks []common.Task
	for _, task := range t.list {
		if opt.ResourceType != "" && task.ResourceType != opt.ResourceType {
			continue
		}
		if opt.Phase != "" && task.Status.Phase != opt.Phase {
			continue
		}
		tasks = append(tasks, task)
	}
	return common.PageList(tasks, opt.ListOptions)
}

func (t *tasksCache) remove(id string) bool {
	t.mu.Lock()
	defer t.mu.Unlock()
	t.list = slices.DeleteFunc(t.list, func(i common.Task) bool {
		return i.ID == id
	})
	return true
}

type taskWatcher struct {
	parent  *tasksCache
	uid     string
	options common.WactchTaskOptions
	result  chan common.WatchEvent[common.Task]
}

// Close implements common.Watcher.
func (t *taskWatcher) Close() error {
	return t.parent.removeWatcher(t.uid)
}

// Events implements common.Watcher.
func (t *taskWatcher) Events() <-chan common.WatchEvent[common.Task] {
	return t.result
}
