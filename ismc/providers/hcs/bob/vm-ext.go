package bob

import (
	"context"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

// ListVirtualMachineDisks implements common.Provider.
func (p *Provider) ListVirtualMachineDisks(ctx context.Context, id string, options common.ListVirtualMachineDiskOptions) (common.List[common.Disk], error) {
	if true {
		if options.Sort == "" {
			options.Sort = "name"
		}
		return p.ListDisks(ctx, common.ListDiskOptions{
			ListOptions:    options.ListOptions,
			VirtualMachine: id,
		})
	}
	resp, err := p.Client.ECS.ListServerBlockDevices(&model.ListServerBlockDevicesRequest{ServerId: id})
	if err != nil {
		return common.List[common.Disk]{}, err
	}
	if resp.VolumeAttachments == nil {
		return common.List[common.Disk]{}, nil
	}
	disks := make([]common.Disk, 0, len(*resp.VolumeAttachments))
	for _, attachment := range *resp.VolumeAttachments {
		vol := common.Disk{
			Descripter: common.Descripter{
				ID:   ptr.Deref(attachment.Id, ""),
				Name: ptr.Deref(attachment.Device, ""),
			},
		}

		disks = append(disks, vol)
	}
	return common.PageList(disks, options.ListOptions), nil
}

// AttachVirtualMachineDisk implements common.Provider.
func (p *Provider) AttachVirtualMachineDisk(ctx context.Context, serverid string, options common.AttachVirtualMachineDiskOptions) error {
	log := log.FromContext(ctx).WithName("attach disk")

	req := &model.AttachServerVolumeRequest{
		ServerId: serverid,
		Body: &model.AttachServerVolumeRequestBody{
			VolumeAttachment: &model.AttachServerVolumeOption{
				VolumeId: options.Disk.ID,
				Device:   ptr.To(options.Disk.Device),
			},
		},
	}
	resp, err := p.Client.ECS.AttachServerVolume(req)
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("attach disk job result", "job", jobResult)
	return nil
}

// DetachVirtualMachineDisk implements common.Provider.
func (p *Provider) DetachVirtualMachineDisk(ctx context.Context, serverid string, diskid string, options common.DetachVirtualMachineDiskOptions) error {
	log := log.FromContext(ctx).WithName("dettachVirtualMachineDisk")
	req := &model.DetachServerVolumeRequest{
		ServerId: serverid,
		VolumeId: diskid,
	}
	resp, err := p.Client.ECS.DetachServerVolume(req)
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("detach disk job result", "job", jobResult)
	return nil
}

// ListVirtualMachineNetworkInterfaces implements common.Provider.
func (p *Provider) ListVirtualMachineNetworkInterfaces(ctx context.Context, id string, options common.ListVirtualMachineNetworkOptions) (common.List[common.NetworkInterface], error) {
	log := log.FromContext(ctx)
	resp, err := p.Client.ECS.ListServerInterfaces(&model.ListServerInterfacesRequest{ServerId: id})
	if err != nil {
		return common.List[common.NetworkInterface]{}, err
	}
	if resp.InterfaceAttachments == nil {
		return common.List[common.NetworkInterface]{}, nil
	}
	cachedSubnets := map[string]common.VirtualSubnetwork{}
	cachedNetworks := map[string]common.VirtualNetwork{}

	nics := make([]common.NetworkInterface, 0, len(*resp.InterfaceAttachments))
	for _, attachment := range *resp.InterfaceAttachments {
		nic := common.NetworkInterface{
			Descripter: common.Descripter{
				ID:   ptr.Deref(attachment.PortId, ""),
				Name: ptr.Deref(attachment.PortId, ""),
			},
			MAC: ptr.Deref(attachment.MacAddr, ""),
		}
		// NetId is the "subnet" id in huawei cloud
		if subnetid := ptr.Deref(attachment.NetId, ""); subnetid != "" {
			nic.VirtualSubnetwork.ID = subnetid
			// complete subnetwork
			if val, ok := cachedSubnets[subnetid]; ok {
				nic.VirtualSubnetwork = val
			} else {
				if network, err := p.GetVirtualSubnetwork(ctx, "", subnetid); err != nil {
					log.Error(err, "get subnet failed", "subnet", subnetid)
				} else {
					nic.VirtualSubnetwork = *network
					cachedSubnets[subnetid] = *network
				}
			}
			// complete network
			if network, ok := cachedNetworks[nic.VirtualSubnetwork.VirtualNetwork]; ok {
				nic.VirtualNetwork = network
			} else {
				if network, err := p.GetVirtualNetwork(ctx, nic.VirtualSubnetwork.VirtualNetwork); err != nil {
					log.Error(err, "get network failed", "network", nic.VirtualSubnetwork.VirtualNetwork)
				} else {
					nic.VirtualNetwork = *network
					cachedNetworks[nic.VirtualSubnetwork.VirtualNetwork] = *network
				}
			}
		}
		// the subnetid in fixedips is the subnetid of openstack
		if attachment.FixedIps != nil && len(*attachment.FixedIps) > 0 {
			for _, ip := range *attachment.FixedIps {
				nic.IPv4s = append(nic.IPv4s, ptr.Deref(ip.IpAddress, ""))
			}
		}
		nics = append(nics, nic)
	}
	// complete ports
	if err := p.completePorts(ctx, nics); err != nil {
		log.Error(err, "complete ports failed")
	}
	if options.Sort == "" {
		options.Sort = "time"
	}
	return common.PageList(nics, options.ListOptions), nil
}

func (p *Provider) completePorts(ctx context.Context, nics []common.NetworkInterface) error {
	portids := make([]string, 0, len(nics))
	for _, mic := range nics {
		portids = append(portids, mic.ID)
	}
	ports, err := p.Client.ListPorts(ctx, client.ListVPCPortsOptions{ID: portids})
	if err != nil {
		return err
	}
	for _, port := range ports {
		for i, nic := range nics {
			if nic.ID == port.ID {
				if nics[i].Descripter.CreationTimestamp.IsZero() {
					nics[i].Descripter.CreationTimestamp = port.CreatedAt.Time
				}
				if port.BindingProfile != nil {
					if isprimary, _ := port.BindingProfile["primary_interface"].(bool); isprimary {
						nics[i].IsPrimary = true
					}
				}
			}
		}
	}
	return nil
}

// AttachVirtualMachineNetworkInterface implements common.Provider.
func (p *Provider) AttachVirtualMachineNetworkInterface(ctx context.Context, serverid string, options common.AttachVirtualMachineNetworkInterfaceOptions) error {
	network := options.Interface

	log := log.FromContext(ctx).WithName("attachVirtualMachineNic")

	if network.SubNetwork == "" {
		return errors.NewBadRequest("subnet is required")
	}
	nic := model.BatchAddServerNicOption{
		SubnetId: network.SubNetwork,
	}
	if network.IPv4 != "" {
		nic.IpAddress = &network.IPv4
	}
	resp, err := p.Client.ECS.BatchAddServerNics(&model.BatchAddServerNicsRequest{
		ServerId: serverid,
		Body: &model.BatchAddServerNicsRequestBody{
			Nics: []model.BatchAddServerNicOption{nic},
		},
	})
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("attach nic job result", "job", jobResult)
	return nil
}

// DetachVirtualMachineNetworkInterface implements common.Provider.
func (p *Provider) DetachVirtualMachineNetworkInterface(ctx context.Context, serverid string, nicid string, options common.DetachVirtualMachineNetworkInterfaceOptions) error {
	log := log.FromContext(ctx).WithName("detachVirtualMachineNic")
	resp, err := p.Client.ECS.BatchDeleteServerNics(&model.BatchDeleteServerNicsRequest{
		ServerId: serverid,
		Body: &model.BatchDeleteServerNicsRequestBody{
			Nics: []model.BatchDeleteServerNicOption{{Id: nicid}},
		},
	})
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("detach nic job result", "job", jobResult)
	return nil
}

// ChangeVirtualMachineImage implements common.Provider.
func (p *Provider) ChangeVirtualMachineImage(ctx context.Context, id string, options common.ChangeVirtualMachineImageOptions) error {
	return p.changeVirtualMachineImage(ctx, id, options)
}

func (p *Provider) changeVirtualMachineImage(ctx context.Context, id string, options common.ChangeVirtualMachineImageOptions) error {
	log := log.FromContext(ctx).WithName("change image")
	oschange := &model.ChangeServerOsWithCloudInitOption{
		Imageid: options.Image.ID,
	}
	if options.Password != "" {
		oschange.Adminpass = &options.Password
	}
	if len(options.KeyPairs) > 0 {
		oschange.Keyname = &options.KeyPairs[0].ID
		// TODO: set usedId?
		// 用户ID。 说明 如果使用秘钥方式切换操作系统，则该字段为必选字段。
		oschange.Userid = nil
	}
	if options.Force {
		oschange.Mode = ptr.To(WithStopServer)
	}
	if options.CloudInit.UserData != "" {
		oschange.Metadata.UserData = &options.CloudInit.UserData
	}
	resp, err := p.Client.ECS.ChangeServerOsWithCloudInit(&model.ChangeServerOsWithCloudInitRequest{
		ServerId: id,
		Body:     &model.ChangeServerOsWithCloudInitRequestBody{OsChange: oschange},
	})
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("change image job result", "job", jobResult)
	return nil
}

// mode取值为withStopServer时，对开机状态的云服务器执行变更规格操作，系统自动对云服务器先执行关机，再变更规格，变更成功后再执行开机。
const WithStopServer = "withStopServer"

// ChangeVirtualMachineInstanceType implements common.Provider.
func (p *Provider) ChangeVirtualMachineInstanceType(ctx context.Context, id string, options common.ChangeVirtualMachineInstanceTypeOptions) error {
	log := log.FromContext(ctx).WithName("change instance type")

	if options.InstanceType.ID == "" {
		return errors.NewBadRequest("new instance type is required")
	}
	resize := &model.ResizePrePaidServerOption{
		FlavorRef: options.InstanceType.ID,
	}
	if options.Force {
		resize.Mode = ptr.To(WithStopServer)
	}
	resp, err := p.Client.ECS.ResizeServer(&model.ResizeServerRequest{
		ServerId: id,
		Body:     &model.ResizeServerRequestBody{Resize: resize},
	})
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("change instance type job result", "job", jobResult)
	return nil
}

// ReInstallVirtualMachine implements common.Provider.
func (p *Provider) ReInstallVirtualMachine(ctx context.Context, id string, options common.ReInstallVirtualMachineOptions) error {
	log := log.FromContext(ctx).WithName("reinstall vm")
	reintall := &model.ReinstallServerWithCloudInitOption{}
	if options.Password != "" {
		reintall.Adminpass = &options.Password
	}
	if len(options.KeyPairs) > 0 {
		reintall.Keyname = &options.KeyPairs[0].ID
		// TODO: set usedId?
		// 用户ID。 说明 如果使用秘钥方式切换操作系统，则该字段为必选字段。
		reintall.Userid = nil
	}
	if options.Force {
		reintall.Mode = ptr.To(WithStopServer)
	}
	if options.CloudInit.UserData != "" {
		reintall.Metadata.UserData = &options.CloudInit.UserData
	}
	resp, err := p.Client.ECS.ReinstallServerWithCloudInit(&model.ReinstallServerWithCloudInitRequest{
		ServerId: id,
		Body:     &model.ReinstallServerWithCloudInitRequestBody{OsReinstall: reintall},
	})
	if err != nil {
		return err
	}
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("reinstall vm job result", "job", jobResult)
	return nil
}

// ChangeVirtualMachineCloudInit implements common.Provider.
func (p *Provider) ChangeVirtualMachineCloudInit(ctx context.Context, id string, options common.ResetVirtualMachineCloudInitOptions) error {
	return nil
}

// ResetVirtualMachinePassword implements common.Provider.
func (p *Provider) ResetVirtualMachinePassword(ctx context.Context, id string, options common.ResetVirtualMachinePasswordOptions) error {
	_, err := p.Client.ECS.ResetServerPassword(&model.ResetServerPasswordRequest{
		ServerId: id,
		Body: &model.ResetServerPasswordRequestBody{
			ResetPassword: &model.ResetServerPasswordOption{
				NewPassword: options.Password,
			},
		},
	})
	if err != nil {
		return err
	}
	return nil
}
