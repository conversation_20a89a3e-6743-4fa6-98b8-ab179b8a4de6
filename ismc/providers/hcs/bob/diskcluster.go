package bob

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

// ListDiskClusters implements common.Provider.
func (p *Provider) ListDiskClusters(ctx context.Context, options common.ListDiskClusterOptions) (common.List[common.DiskCluster], error) {
	req := client.ListResourceModelRequest{
		PageNo:   options.Page,
		PageSize: options.Size,
	}
	pools, err := p.Client.SysStoragePool(ctx, req)
	if err != nil {
		return common.List[common.DiskCluster]{}, err
	}
	items := make([]common.DiskCluster, 0, len(pools.Datas))
	for _, item := range pools.Datas {
		items = append(items, storagePoolToDiskCluster(item))
	}
	return common.List[common.DiskCluster]{
		Items: items,
		Size:  pools.PageSize,
		Total: pools.TotalSize,
		Page:  pools.PageNo,
	}, nil
}

func storagePoolToDiskCluster(pool client.SysStoragePool) common.DiskCluster {
	ret := common.DiskCluster{
		Descripter: common.Descripter{
			ID:   pool.RegionId,
			Name: pool.Name,
		},
		Status: common.DiskClusterStatus{
			Phase: common.DiskClusterPhaseReady,
			Total: *resource.NewQuantity(int64(pool.TotalCapacity*TB), resource.BinarySI),
			Used:  *resource.NewQuantity(int64(pool.UsedCapacity*TB), resource.BinarySI),
		},
	}
	return ret
}

// GetDiskCluster implements common.Provider.
func (p *Provider) GetDiskCluster(ctx context.Context, id string) (*common.DiskCluster, error) {
	return nil, errUnsupportMethod
}

// DeleteDiskCluster implements common.Provider.
func (p *Provider) DeleteDiskCluster(ctx context.Context, id string) error {
	return errUnsupportMethod
}

// CreateDiskCluster implements common.Provider.
func (p *Provider) CreateDiskCluster(ctx context.Context, cluster *common.DiskCluster) (*common.Descripter, error) {
	return nil, errUnsupportMethod
}
