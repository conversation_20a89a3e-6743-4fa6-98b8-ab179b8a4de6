package bob

import (
	"context"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestGetVirtualMachineMetrics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetVirtualMachineMetrics(ctx, "d92de84a-e691-4771-83db-67dea8ca14e8")
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log(resp)
}

func TestListVirtualMachineMetrics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListVirtualMachineMetrics(ctx, common.ListVirtualMachineMetricsOptions{})
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log(resp)
}

func TestGetZoneResourceTypeMetrics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListZoneMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}
	for _, ele := range resp {
		t.Log(*ele)
	}
}

func TestProvider_ListHostMetrics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListHostMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestProvider_ListDiskMetrics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListDiskMetrics(ctx)
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestProvider_GetDiskMetrics(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetDiskMetrics(ctx, "9d513516-5c7e-454f-a408-c4d0382c42bf")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}
