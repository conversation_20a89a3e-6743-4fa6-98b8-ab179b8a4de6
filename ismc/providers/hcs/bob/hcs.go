package bob

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
	sdkclient "xiaoshiai.cn/core/ismc/providers/hcs/client"
)

var _ common.Provider = &Provider{}

type Options struct {
	sdkclient.Options `json:",inline"`
	UseSubscription   bool `json:"useSubscription" description:"use subscription mode, create/modify vm/image/disk use subscription api"`
	ProxyVNC          bool `json:"proxyVNC" description:"proxy VNC request"`
}

func NewDefaultOptions() *Options {
	return &Options{
		Options:         *sdkclient.NewDefaultOptions(),
		UseSubscription: true,
		ProxyVNC:        true,
	}
}

type Provider struct {
	Client          *sdkclient.Client
	UseSubscription bool     // 使用订阅模式，创建/修改 虚拟机/镜像/磁盘 时走订阅模式的接口
	IsPublicCloud   bool     // 是否是公有云，公有云的接口和私有云的接口有差异
	ProxyVNCAddr    *url.URL // 是否代理 VNC 请求
	tasksCache      *tasksCache

	regourceGroups common.KVCache[string, string]
}

func NewProvider(ctx context.Context, opts *Options) (*Provider, error) {
	sdkcli, err := sdkclient.NewClient(ctx, &opts.Options)
	if err != nil {
		return nil, err
	}
	resourcegroupCache, err := newResourceGroupsCache(ctx, sdkcli)
	if err != nil {
		return nil, err
	}
	provider := &Provider{
		Client:          sdkcli,
		UseSubscription: opts.UseSubscription,
		tasksCache:      newTasksCache(ctx, sdkcli),
		regourceGroups:  resourcegroupCache,
	}
	if opts.ProxyVNC {
		vncproxyurl, err := url.Parse(client.GetServiceEndpoint("console", "", opts.GlobalDomain))
		if err != nil {
			return nil, fmt.Errorf("parse vnc proxy url failed:%w", err)
		}
		provider.ProxyVNCAddr = vncproxyurl
	}
	return provider, nil
}

func getLimitOffset(options common.ListOptions) (*int32, *int32) {
	if options.Size <= 0 {
		return nil, nil
	}
	page := options.Page
	if page <= 0 {
		page = 1
	}
	offset := int32((page - 1) * options.Size)
	return ptr.To(int32(options.Size)), &offset
}

func getLimitMarker(options common.ListOptions) (*int32, *string) {
	if options.Size <= 0 {
		return nil, nil
	}
	if options.Continue == "" {
		return ptr.To(int32(options.Size)), nil
	}
	return ptr.To(int32(options.Size)), &options.Continue
}

func getSortKeyDir(options common.ListOptions) (*string, *string) {
	if options.Sort != "" {
		switch options.Sort {
		case "name":
			return ptr.To("name"), ptr.To("asc")
		case "name-":
			return ptr.To("name"), ptr.To("desc")
		case "time":
			return ptr.To("created_at"), ptr.To("asc")
		case "time-":
			return ptr.To("created_at"), ptr.To("desc")
		case "size":
			return ptr.To("size"), ptr.To("asc")
		case "size-":
			return ptr.To("size"), ptr.To("desc")
		}
	}
	return nil, nil
}

func getEnterpriceProject(resourcegroup string) *string {
	if resourcegroup == "" {
		return ptr.To(AllEnterpriseProject)
	}
	return &resourcegroup
}

type CanGetID interface {
	GetID() string
}

func toContinueList[T CanGetID](list []T, options common.ListOptions) common.List[T] {
	return common.List[T]{
		Size:     options.Size,
		Items:    list,
		Continue: getContinueKey(list, options.Size),
	}
}

func getContinueKey[T CanGetID](list []T, size int) string {
	if size == 0 || len(list) == 0 {
		return ""
	}
	if len(list) < size {
		return ""
	}
	return list[len(list)-1].GetID()
}

func toPageSizeList[T CanGetID](list []T, total *int32, options common.ListOptions) common.List[T] {
	return common.List[T]{
		Size:  options.Size,
		Items: list,
		Page:  options.Page,
		Total: int(ptr.Deref(total, 0)),
	}
}

func (p *Provider) pollSubscription(ctx context.Context, subid string) (*client.OrderResource, error) {
	log := log.FromContext(ctx)
	var lastResp *client.OrderResource
	err := wait.PollUntilContextCancel(ctx, 5*time.Second, true, func(ctx context.Context) (bool, error) {
		resources, err := p.Client.GetOrderResources(ctx, subid)
		if err != nil {
			return false, err
		}
		if len(resources) == 0 {
			log.V(2).Info("no resources found")
			return false, nil
		}
		log.Info("get order resources", "resources", resources)
		resource0 := resources[0]
		lastResp = &resource0
		switch resource0.Result {
		case client.OrderResourceResultFailed:
			log.Info("order failed", "message", resource0.Message)
			return false, fmt.Errorf("order failed:%s", resource0.Message)
		case client.OrderResourceResultSuccessed:
			log.Info("order success")
			return true, nil
		case client.OrderResourceResultPartialSuccessed:
			log.Info("order partial success")
			return true, nil
		default:
			log.Info("order still running")
			return false, nil
		}
	})
	if err != nil {
		return nil, err
	}
	return lastResp, nil
}

const ismcTag = common.LabelIsmcManaged + defaultTagValueSuffix

func injectManagedTag(tags *[]string) *[]string {
	if tags == nil {
		return &[]string{ismcTag}
	}
	for _, tag := range *tags {
		if tag == ismcTag {
			return tags
		}
	}
	*tags = append(*tags, ismcTag)
	return tags
}
