package bob

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	libproxy "xiaoshiai.cn/common/rest/proxy"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

const defaultTagValueSuffix = ".true"

var _ common.VirtualMachineOperation = &Provider{}

var (
	lowerCase   = regexp.MustCompile(`[a-z]`)
	upperCase   = regexp.MustCompile(`[A-Z]`)
	number      = regexp.MustCompile(`\d`)
	specialChar = regexp.MustCompile(`[@$!%*?&]`)
)

// password must contains lower, upper, number and special characters and length >= 8
func isPasswordValid(password string) bool {
	if len(password) < 8 {
		return false
	}
	return lowerCase.MatchString(password) &&
		upperCase.MatchString(password) &&
		number.MatchString(password) &&
		specialChar.MatchString(password)
}

// CreateVirtualMachine implements common.Provider.
func (p *Provider) CreateVirtualMachine(ctx context.Context, vm *common.VirtualMachine, options common.CreateVirtualMachineOptions) (*common.Descripter, error) {
	log := log.FromContext(ctx)
	if vm.Password != "" && !isPasswordValid(vm.Password) {
		return nil, errors.NewBadRequest("password must at least 8 characters long and contain lower, upper, number and special characters")
	}
	instanceType, err := p.parseInstanceType(ctx, vm, vm.InstanceType)
	if err != nil {
		return nil, err
	}
	image, err := p.parseImage(ctx, vm, vm.Image)
	if err != nil {
		return nil, err
	}
	rootVolume, dataVolumes, err := parseDiskRefs(vm.Disks)
	if err != nil {
		return nil, err
	}
	vpc, nics, err := p.parseNetwork(ctx, vm.Interfaces)
	if err != nil {
		return nil, err
	}
	server := client.PrePaidServer{
		PrePaidServer: model.PrePaidServer{
			ImageRef:         image.ID,
			Description:      ptr.To(vm.Description),
			FlavorRef:        instanceType.ID,
			Name:             vm.Name,
			Vpcid:            vpc.ID,
			Nics:             nics,
			Tags:             &vm.Tags,
			RootVolume:       rootVolume,
			AvailabilityZone: ptr.To(vm.Zone),
			Extendparam: &model.PrePaidServerExtendParam{
				ChargingMode: ptr.To(model.GetPrePaidServerExtendParamChargingModeEnum().POST_PAID),
			},
			Count: ptr.To(int32(1)),
		},
	}
	// Metadata 不能设置自定义值,无法作为labels使用,使用tag作为labels
	if p.IsPublicCloud {
		if tags := mapToServerTags(vm.Labels); len(tags) > 0 {
			server.ServerTags = &tags
		}
	} else {
		tags, err := mapToTags(vm.Labels)
		if err != nil {
			return nil, fmt.Errorf("invalid labels:%w", err)
		}
		if len(tags) > 0 {
			server.Tags = &tags
		}
	}
	if len(dataVolumes) > 0 {
		server.DataVolumes = &dataVolumes
	}
	if vm.Password != "" {
		server.AdminPass = &vm.Password
	}
	if vm.ResourceGroup != "" {
		server.Extendparam.EnterpriseProjectId = &vm.ResourceGroup
	}
	if len(vm.KeyPairs) > 0 {
		server.KeyName = &vm.KeyPairs[0].ID
	}
	if vm.CloudInit.UserData != "" {
		server.UserData = ptr.To(vm.CloudInit.UserData)
	}
	secgroups := []model.PrePaidServerSecurityGroup{}
	for i, sec := range vm.SecurityGroups {
		if sec.ID == "" {
			return nil, errors.NewBadRequest(fmt.Sprintf(".securityGroups[%d].id is empty", i))
		}
		secgroups = append(secgroups, model.PrePaidServerSecurityGroup{Id: &sec.ID})
	}
	if len(secgroups) > 0 {
		server.SecurityGroups = &secgroups
	}
	// inject managed tag
	server.Tags = injectManagedTag(server.Tags)
	log.Info("create ecs vm", "server", server)
	if false && p.UseSubscription {
		log.V(2).Info("create ecs vm use subscription")
		return p.createVirtualMachineUseSubscription(ctx, server, options)
	} else {
		log.V(2).Info("create ecs vm use api")
		return p.createVirtualMachine(ctx, server, options)
	}
}

// 标签的key值只能包含大写字母（A~Z）、小写字母（a~z）、数字（0-9）、下划线（_）、中划线（-）以及中文字符。
// 标签的value值只能包含大写字母（A~Z）、小写字母（a~z）、数字（0-9）、下划线（_）、中划线（-）、小数点（.）以及中文字符。

var (
	tagKeyReplacer     = strings.NewReplacer(".", "0x2e", "/", "0x2f", ":", "0x3a")
	tagKeyUnreplacer   = strings.NewReplacer("0x2e", ".", "0x2f", "/", "0x3a", ":")
	tagValueReplacer   = strings.NewReplacer("/", "0x2f", ":", "0x3a")
	tagValueUnreplacer = strings.NewReplacer("0x2f", "/", "0x3a", ":")
)

func escapeTagKey(k string) string {
	return tagKeyReplacer.Replace(k)
}

func unescapeTagKey(k string) string {
	return tagKeyUnreplacer.Replace(k)
}

func escapeTagValue(v string) string {
	return tagValueReplacer.Replace(v)
}

func unescapeTagValue(v string) string {
	return tagValueUnreplacer.Replace(v)
}

// 弹性云服务器的标签。
// 标签的格式为“key.value”。其中，key的长度不超过36个字符，value的长度不超过43个字符。
// 标签命名时，需满足如下要求：
// - 标签的key值只能包含大写字母（A~Z）、小写字母（a~z）、数字（0-9）、下划线（_）、中划线（-）以及中文字符。
// - 标签的value值只能包含大写字母（A~Z）、小写字母（a~z）、数字（0-9）、下划线（_）、中划线（-）、小数点（.）以及中文字符。
// > 说明：
// >  > 创建弹性云服务器时，一台弹性云服务器最多可以添加10个标签。
// > 公有云新增server_tags字段，该字段与tags字段功能相同，支持的key、value取值范围更广，建议使用server_tags字段。
var (
	tagKeyRegexp   = regexp.MustCompile(`^[A-Za-z0-9_\-]{1,36}$`)
	tagValueRegexp = regexp.MustCompile(`^[A-Za-z0-9_\-\.]{1,43}$`)
)

func mapToTags(kvs map[string]string) ([]string, error) {
	if len(kvs) == 0 {
		return nil, nil
	}
	tags := []string{}
	for k, v := range kvs {
		if v == "" {
			continue
		}
		key, val := escapeTagKey(k), escapeTagValue(v)
		if !tagKeyRegexp.MatchString(key) {
			return nil, fmt.Errorf("invalid tag key:%s, can only contain A-Z, a-z, 0-9, _, -, and chinese characters", key)
		}
		if !tagValueRegexp.MatchString(val) {
			return nil, fmt.Errorf("invalid tag value:%s, can only contain A-Z, a-z, 0-9, _, -, ., and chinese characters", val)
		}
		tags = append(tags, fmt.Sprintf("%s.%s", key, val))
	}
	return tags, nil
}

func tagsToMap(tags *[]string) map[string]string {
	if tags == nil {
		return nil
	}
	kvs := map[string]string{}
	for _, tag := range *tags {
		s := strings.SplitN(tag, ".", 2)
		if len(s) != 2 {
			continue
		}
		kvs[unescapeTagKey(s[0])] = unescapeTagValue(s[1])
	}
	return kvs
}

func tagsToTags(tags *[]string) []string {
	if tags == nil {
		return nil
	}
	ret := make([]string, len(*tags))
	for i, tag := range *tags {
		tag = strings.TrimSuffix(tag, defaultTagValueSuffix)
		ret[i] = tag
	}
	return ret
}

// 弹性云服务器的标签。
// > 说明： >  > 创建弹性云服务器时，一台弹性云服务器最多可以添加10个标签。
// > 公有云新增server_tags字段，该字段与tags字段功能相同，支持的key、value取值范围更广，建议使用server_tags字段。
func mapToServerTags(kvs map[string]string) []model.PrePaidServerTag {
	tags := []model.PrePaidServerTag{}
	for k, v := range kvs {
		tags = append(tags, model.PrePaidServerTag{Key: k, Value: v})
	}
	return tags
}

func serverTagsToMap(tags *[]model.ServerSystemTag) map[string]string {
	if tags == nil {
		return nil
	}
	kvs := map[string]string{}
	for _, tag := range *tags {
		if tag.Key == nil || tag.Value == nil {
			continue
		}
		kvs[*tag.Key] = *tag.Value
	}
	return kvs
}

func (p *Provider) createVirtualMachine(ctx context.Context, vm client.PrePaidServer, options common.CreateVirtualMachineOptions) (*common.Descripter, error) {
	resp, err := p.Client.CreateServers(&client.CreateServersRequest{
		Body: &client.CreateServersRequestBody{Server: &vm},
	})
	if err != nil {
		return nil, err
	}
	// add to watch
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, resp.OrderId)

	created := &common.Descripter{
		Name: vm.Name,
	}
	if resp.ServerIds != nil {
		for _, id := range *resp.ServerIds {
			created.ID = id
			break
		}
	}
	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return created, nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return nil, err
	}
	log.Info("job result", "job", jobResult)

	vmid := ""
	if jobResult.Entities != nil {
		if jobResult.Entities.ServerId != nil {
			vmid = *jobResult.Entities.ServerId
		} else if jobResult.Entities.SubJobs != nil {
			for _, subjob := range *jobResult.Entities.SubJobs {
				if subjob.Entities != nil && subjob.Entities.ServerId != nil {
					vmid = *subjob.Entities.ServerId
					break
				}
			}
		}
	}
	created.ID = vmid
	log.Info("ecs vm created", "vm", created)
	return created, nil
}

func (p *Provider) createVirtualMachineUseSubscription(ctx context.Context, vm client.PrePaidServer, options common.CreateVirtualMachineOptions) (*common.Descripter, error) {
	log := log.FromContext(ctx)
	secparam := map[string]any{}
	if vmpass := vm.AdminPass; vmpass != nil {
		// secparam["metadata"] = map[string]string{"admin_pass": *vm.AdminPass}
		// 文档中和实际web上的请求不一致，以下是实际web上的请求
		secparam["admin_password"] = *vmpass
		vm.AdminPass = nil
	}
	if vm.KeyName != nil {
		secparam["key_name"] = *vm.KeyName
		vm.KeyName = nil
	}
	subresp, err := p.Client.CreateSubscriptionSimple(ctx, client.SimpleSubscription{
		OperateType:  client.SubscriptionOperateTypeApply,
		ServiceType:  "ecs",
		Params:       vm,
		SecretParams: secparam,
	})
	if err != nil {
		return nil, err
	}
	// add to watch
	p.tasksCache.AddSubscriptionToWatch(ctx, subresp.SubscriptionID)

	created := &common.Descripter{}
	if subresp.SubscriptionID == "" || !options.Wait {
		return created, nil
	}
	subResult, err := p.pollSubscription(ctx, subresp.SubscriptionID)
	if err != nil {
		return created, err
	}
	log.Info("subscription result", "sub", subResult)
	if subResult.ResourceID != "" {
		created.ID = subResult.ResourceID
	}
	return created, nil
}

func (p *Provider) parseInstanceType(ctx context.Context, vm *common.VirtualMachine, ref common.VirtualMachineInstanceTypeRef) (*common.InstanceType, error) {
	if ref.ID != "" {
		return p.GetInstanceType(ctx, ref.ID)
	}
	if ref.Name != "" {
		list, err := p.ListInstanceTypes(ctx, common.ListInstanceTypeOptions{
			Zone: vm.Zone,
		})
		if err != nil {
			return nil, err
		}
		for _, item := range list.Items {
			if item.Name == ref.Name || item.ID == ref.Name {
				return &item, nil
			}
		}
		return nil, fmt.Errorf("instance type %s not found", ref.Name)
	}
	return nil, fmt.Errorf("instance type is empty")
}

func (p *Provider) parseImage(ctx context.Context, vm *common.VirtualMachine, ref common.VirtualMachineImageRef) (*common.Image, error) {
	if ref.ID != "" {
		return p.GetImage(ctx, ref.ID)
	}
	if ref.Name != "" {
		list, err := p.ListImages(ctx, common.ListImageOptions{
			Zone:          vm.Zone,
			ResourceGroup: vm.ResourceGroup,
			Name:          ref.Name,
		})
		if err != nil {
			return nil, err
		}
		for _, item := range list.Items {
			if item.Name == ref.Name || item.ID == ref.Name {
				return &item, nil
			}
		}
		return nil, fmt.Errorf("image %s not found", ref.Name)
	}
	return nil, fmt.Errorf("image is empty")
}

func (p *Provider) pollECSJob(ctx context.Context, jobID string) (*model.ShowJobResponse, error) {
	var lastResp *model.ShowJobResponse
	err := wait.PollUntilContextCancel(ctx, 5*time.Second, true, func(ctx context.Context) (bool, error) {
		resp, err := p.Client.ECS.ShowJob(&model.ShowJobRequest{JobId: jobID})
		if err != nil {
			return false, err
		}
		lastResp = resp

		log.Info("show ecs job", "job", resp)
		if resp == nil {
			return false, nil
		}
		switch *resp.Status {
		case model.GetShowJobResponseStatusEnum().FAIL:
			if resp.FailReason != nil {
				return false, fmt.Errorf("job failed:%s", *resp.FailReason)
			}
			return false, fmt.Errorf("job failed")
		case model.GetShowJobResponseStatusEnum().SUCCESS:
			return true, nil
		case model.GetShowJobResponseStatusEnum().RUNNING:
			log.Info("ecs job still running")
			return false, nil
		}
		return false, nil
	})
	if err != nil {
		return nil, err
	}
	return lastResp, nil
}

/*
可选参数。
若使用的镜像类型为windows，请选择admin_pass参数注入用户密码；若选择密钥对登陆，需要填写正确的密钥对名称
*/
type SecretParam struct {
	Metadata      SecretParamMetadata `json:"metadata,omitempty"`
	AdminPassword string              `json:"admin_password"`
}

type SecretParamMetadata struct {
	AdminPass string `json:"admin_pass"`
}

func (p *Provider) parseNetwork(ctx context.Context, networks []common.VirtualMachineInterfaceRef) (*common.VirtualNetwork, []model.PrePaidServerNic, error) {
	if len(networks) == 0 {
		return nil, nil, fmt.Errorf("at least one interface required")
	}
	var vnet *common.VirtualNetwork
	nics := []model.PrePaidServerNic{}
	for _, item := range networks {
		if item.Network == "" {
			return nil, nil, fmt.Errorf("network is empty")
		}
		thisvnet, err := p.getVirtualNetworkByIDOrName(ctx, item.Network)
		if err != nil {
			return nil, nil, err
		}
		if vnet != nil && vnet.ID != thisvnet.ID {
			return nil, nil, fmt.Errorf("interface must be in the same network")
		}
		vnet = thisvnet

		thisnic := model.PrePaidServerNic{}
		if item.IPv4 != "" {
			thisnic.IpAddress = &item.IPv4
		}
		// select an default subnet
		if item.SubNetwork == "" {
			subnetlist, err := p.ListVirtualSubnetwork(ctx, thisvnet.ID, common.ListVirtualSubnetworkOptions{})
			if err != nil {
				return nil, nil, err
			}
			if len(subnetlist.Items) == 0 {
				return nil, nil, fmt.Errorf("no subnet found in network:%s", thisvnet.Name)
			}
			thisnic.SubnetId = subnetlist.Items[0].ID
		} else {
			thisnic.SubnetId = item.SubNetwork
		}
		nics = append(nics, thisnic)
	}
	return vnet, nics, nil
}

func ToGB(size int64) int32 {
	return int32(size >> 30)
}

func parseDiskRefs(disks []common.VirtualMachineDiskRef) (*model.PrePaidServerRootVolume, []client.PrePaidServerDataVolume, error) {
	if len(disks) == 0 {
		return nil, nil, fmt.Errorf("no disk provided,must defined system volume")
	}
	rootVolume, dataVolumes := disks[0], disks[1:]
	root := &model.PrePaidServerRootVolume{
		Volumetype: client.NewPrePaidServerRootVolumeVolumetype(rootVolume.Disk.DiskClass),
	}
	if rootVolume.Disk.Size.Value() > 0 {
		root.Size = ptr.To(ToGB(rootVolume.Disk.Size.Value()))
	}
	datavols := make([]client.PrePaidServerDataVolume, len(dataVolumes))
	for i, disk := range dataVolumes {
		vol := client.PrePaidServerDataVolume{
			PrePaidServerDataVolume: model.PrePaidServerDataVolume{
				Size:                ToGB(disk.Disk.Size.Value()),
				Volumetype:          client.NewPrePaidServerDataVolumeVolumetype(disk.Disk.DiskClass),
				DeleteOnTermination: ptr.To(disk.DeleteWithVirtualMchine),
			},
		}
		if disk.UseExisting {
			vol.VolumeId = disk.ID
			vol.Label = disk.Name
		}
		if disk.From != nil {
			if disk.From.Image != "" {
				vol.DataImageId = &disk.From.Image
			}
		}
		datavols[i] = vol
	}
	return root, datavols, nil
}

type UpdateVirtualMachineFlavorParam struct {
	Resize UpdateVirtualMachineParamResize `json:"resize"`
	IDs    []UpdateVirtualMachineParamID   `json:"ids"`
}

type UpdateVirtualMachineParamResize struct {
	DedicatedHostID string `json:"dedicated_host_id,omitempty"`
	FlavorRef       string `json:"flavorRef"`
}

type UpdateVirtualMachineParamID struct {
	Name        string `json:"name"`
	ServiceType string `json:"service_type"`
	ID          string `json:"id"`
	Tenancy     string `json:"tenancy,omitempty"`
}

func (p *Provider) UpdateVirtualMachine(ctx context.Context, vm *common.VirtualMachine) error {
	resp, err := p.Client.ECS.UpdateServer(&model.UpdateServerRequest{
		ServerId: vm.ID,
		Body: &model.UpdateServerRequestBody{
			Server: &model.UpdateServerOption{
				Name:        &vm.Name,
				Description: &vm.Description,
			},
		},
	})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}

type DeleteVirtualMachineParam struct {
	DeletePublicip bool                          `json:"delete_publicip,omitempty"`
	DeleteVolume   bool                          `json:"delete_volume,omitempty"`
	IDs            []DeleteVirtualMachineParamID `json:"ids"`
}
type DeleteVirtualMachineParamID struct {
	ID string `json:"id"`
}

// DeleteVirtualMachine implements common.Provider.
func (p *Provider) DeleteVirtualMachine(ctx context.Context, id string, options common.DeleteVirtualMachineOptions) error {
	exists, err := p.GetVirtualMachine(ctx, id)
	if err != nil {
		return errors.IgnoreNotFound(err)
	}
	if exists.DeletionTimestamp != nil {
		return nil
	}
	log := log.FromContext(ctx)

	body := &model.DeleteServersRequestBody{
		Servers: []model.ServerId{{Id: id}},
	}
	if options.WithDisks {
		body.DeleteVolume = ptr.To(true)
	}
	resp, err := p.Client.ECS.DeleteServers(&model.DeleteServersRequest{Body: body})
	if err != nil {
		return err
	}
	// add to watch
	p.tasksCache.AddEcsJobToWatch(ctx, resp.JobId, nil)

	if !options.Wait || resp.JobId == nil || *resp.JobId == "" {
		return nil
	}
	jobResult, err := p.pollECSJob(ctx, *resp.JobId)
	if err != nil {
		return err
	}
	log.Info("remove ecs vm job result", "job", jobResult)
	return nil
}

// GetVirtualMachine implements common.Provider.
func (p *Provider) GetVirtualMachine(ctx context.Context, id string) (*common.VirtualMachine, error) {
	if false {
		return p.getVirtualMachineNova(ctx, id)
	}
	if false {
		return p.getVirtualMachineV1_1(ctx, id)
	}
	return p.getVirtualMachineV1(ctx, id)
}

func (p *Provider) getVirtualMachineV1(_ context.Context, id string) (*common.VirtualMachine, error) {
	resp, err := p.Client.ECS.ShowServer(&model.ShowServerRequest{ServerId: id})
	if err != nil {
		return nil, err
	}
	vm := coverVmV1(client.ServerDetail{ServerDetail: *resp.Server}, p.IsPublicCloud)
	if vm.DeletionTimestamp != nil {
		return nil, errors.NewNotFound("virtualmachine", id)
	}
	return &vm, nil
}

func (p *Provider) getVirtualMachineV1_1(_ context.Context, id string) (*common.VirtualMachine, error) {
	resp, err := p.Client.ECS.ListCloudServers(&model.ListCloudServersRequest{Id: &id})
	if err != nil {
		return nil, err
	}
	if resp.Servers == nil || len(*resp.Servers) == 0 {
		return nil, errors.NewNotFound("virtualmachine", id)
	}
	server0 := (*resp.Servers)[0]
	vm := covnertCloudServer(server0)
	return &vm, nil
}

func (p *Provider) getVirtualMachineNova(_ context.Context, id string) (*common.VirtualMachine, error) {
	resp, err := p.Client.ECS.NovaShowServer(&model.NovaShowServerRequest{ServerId: id})
	if err != nil {
		return nil, err
	}
	vm := covnertNovaServer(*resp.Server)
	return &vm, nil
}

// GetVirtualMachineRemoteConnectionInfo implements common.Provider.
func (p *Provider) GetVirtualMachineRemoteConnectionInfo(ctx context.Context, id string) (*common.RemoteConnectionInfo, error) {
	resp, err := p.Client.ShowServerRemoteConsole(&client.ShowServerRemoteConsoleRequest{
		ServerId: id,
		Body: &client.ShowServerRemoteConsoleRequestBody{
			RemoteConsole: &client.GetServerRemoteConsoleOption{
				Protocol:         model.GetGetServerRemoteConsoleOptionProtocolEnum().VNC,
				Type:             model.GetGetServerRemoteConsoleOptionTypeEnum().NOVNC,
				ExpectConsoleUrl: ptr.To("true"),
			},
		},
	})
	if err != nil {
		return nil, err
	}
	if resp != nil {
		return &common.RemoteConnectionInfo{URL: resp.RemoteConsole.ConsoleUrl}, nil
	}
	return &common.RemoteConnectionInfo{}, nil
}

// ListVirtualMachines implements common.Provider.
func (p *Provider) ListVirtualMachines(ctx context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	if false {
		return p.listVirtualMachinesNova(ctx, options)
	}
	if false {
		return p.listVirtualMachinesV1_1(ctx, options)
	}
	return p.listVirtualMachinesv1(ctx, options)
}

func (p *Provider) listVirtualMachinesv1(ctx context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	return p.listVirtualMachinesv1WithFields(ctx, options, "")
}

func (p *Provider) listVirtualMachinesv1WithFields(_ context.Context, options common.ListVirtualMachineOptions, fields string) (common.List[common.VirtualMachine], error) {
	req := &client.ListServersDetailsRequest{
		EnterpriseProjectId: getEnterpriceProject(options.ResourceGroup),
	}
	if fields != "" {
		req.ExpectFields = &fields
	}
	if options.Name != "" {
		req.Name = &options.Name
	}
	if options.Search != "" {
		req.Name = &options.Search
	}
	if len(options.Tags) > 0 {
		tag := options.Tags[0]
		if !strings.Contains(tag, ".") {
			// add default tag value
			tag += defaultTagValueSuffix
		}
		req.Tags = ptr.To(tag)
	}
	if options.Size > 0 {
		req.Limit = ptr.To(int32(options.Size))
	}
	if options.Page > 0 {
		// cautions: offset is page, not offset as usual
		req.Offset = ptr.To(int32(options.Page))
	}
	if req.Limit == nil {
		// limit must set
		req.Limit = ptr.To(int32(1000))
		req.Offset = ptr.To(int32(0))
	}
	resp, err := p.Client.ListServersDetails(req)
	if err != nil {
		return common.List[common.VirtualMachine]{}, err
	}
	if resp.Servers == nil {
		return common.List[common.VirtualMachine]{}, nil
	}
	machines := make([]common.VirtualMachine, 0, len(*resp.Servers))
	for _, hcs := range *resp.Servers {
		machines = append(machines, coverVmV1(hcs, p.IsPublicCloud))
	}
	return common.List[common.VirtualMachine]{
		Items: machines,
		Total: int(ptr.Deref(resp.Count, 0)),
		Page:  options.Page,
		Size:  options.Size,
	}, nil
}

// Deprecated: This api has been removed in HCS
func (p *Provider) listVirtualMachinesV1_1(_ context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	req := &model.ListCloudServersRequest{}
	if options.Name != "" {
		req.Name = &options.Name
	}
	if options.Search != "" {
		req.Name = &options.Search
	}
	req.Limit, req.Marker = getLimitMarker(options.ListOptions)
	resp, err := p.Client.ECS.ListCloudServers(req)
	if err != nil {
		return common.List[common.VirtualMachine]{}, err
	}
	if resp.Servers == nil {
		return common.List[common.VirtualMachine]{}, nil
	}
	machines := make([]common.VirtualMachine, 0, len(*resp.Servers))
	for _, item := range *resp.Servers {
		machines = append(machines, covnertCloudServer(item))
	}
	return toContinueList(machines, options.ListOptions), nil
}

func (p *Provider) listVirtualMachinesNova(_ context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	req := &model.NovaListServersDetailsRequest{}
	if options.Name != "" {
		req.Name = &options.Name
	}
	if options.Search != "" {
		req.Name = &options.Search
	}
	req.Limit, req.Marker = getLimitMarker(options.ListOptions)
	if req.Limit == nil {
		req.Limit = ptr.To(int32(1000))
	}
	resp, err := p.Client.ECS.NovaListServersDetails(req)
	if err != nil {
		return common.List[common.VirtualMachine]{}, err
	}
	if resp.Servers == nil {
		return common.List[common.VirtualMachine]{}, nil
	}
	machines := make([]common.VirtualMachine, 0, len(*resp.Servers))
	for _, item := range *resp.Servers {
		machines = append(machines, covnertNovaServer(item))
	}
	return toContinueList(machines, options.ListOptions), nil
}

// 2019-05-22T07:48:19.000000
const NovaTimeLayout = time.RFC3339

func covnertNovaServer(server model.NovaServer) common.VirtualMachine {
	vm := common.VirtualMachine{
		Descripter: common.Descripter{
			ID:          server.Id,
			Name:        server.Name,
			Annotations: server.Metadata,
			Description: ptr.Deref(server.Description, ""),
		},
		Zone: server.OSEXTAZavailabilityZone,
		Status: common.VirtualMachineStatus{
			Phase:     server.Status.Value(),
			Resources: map[common.ResourceType]resource.Quantity{},
		},
	}
	vm.CreationTimestamp, _ = time.Parse(NovaTimeLayout, server.Created)
	if server.OSSRVUSGlaunchedAt != "" {
		vm.Status.BootTime, _ = time.Parse(NovaTimeLayout, server.OSSRVUSGlaunchedAt)
	}
	vm.Labels = tagsToMap(&server.Tags)
	if server.Image != nil {
		vm.Image.ID = server.Image.Id
	}
	if metadata := server.Metadata; metadata != nil {
		extrainfo := parseExtrainfo(metadata["cascaded.instance_extrainfo"])
		if cpu := extrainfo["org_cpu"]; cpu != "" {
			cpucores, _ := strconv.Atoi(cpu)
			vm.Status.Resources[common.ResourceCPU] = *resource.NewQuantity(int64(cpucores), resource.DecimalSI)
		}
		if mem := extrainfo["org_mem"]; mem != "" {
			memsize, _ := strconv.Atoi(mem)
			vm.Status.Resources[common.ResourceMemory] = *resource.NewQuantity(int64(memsize)*MB, resource.BinarySI)
		}
	}
	if server.Flavor != nil {
		vm.InstanceType.ID = ptr.Deref(server.Flavor.Id, "")
	}
	if server.KeyName != "" {
		vm.KeyPairs = []common.VirtualMachineObjectRef{{ID: server.KeyName}}
	}
	for _, sec := range server.SecurityGroups {
		vm.SecurityGroups = append(vm.SecurityGroups, common.VirtualMachineObjectRef{
			ID: ptr.Deref(sec.Name, ""),
		})
	}
	if server.OSEXTSRVATTRuserData != nil {
		vm.CloudInit.UserData = *server.OSEXTSRVATTRuserData
	}
	for _, vol := range server.OsExtendedVolumesvolumesAttached {
		vm.Disks = append(vm.Disks, common.VirtualMachineDiskRef{
			Disk: common.Disk{
				Descripter:              common.Descripter{ID: vol.Id},
				DeleteWithVirtualMchine: ptr.Deref(vol.DeleteOnTermination, false),
			},
		})
	}
	for subnetname, addrs := range server.Addresses {
		_ = subnetname
		for _, addr := range addrs {
			vm.Status.Guest.Interfaces = append(vm.Status.Guest.Interfaces, common.VirtualMachineNetworkInterface{
				Connected: true,
				IPs:       []string{addr.Addr},
				MAC:       addr.OSEXTIPSMACmacAddr,
				Type:      addr.OSEXTIPStype,
			})
		}
	}
	vm.Status.PowerState = mapPowerState(server.OSEXTSTSpowerState)
	// use the power state from the status
	vm.Power = vm.Status.PowerState
	return vm
}

func parseExtrainfo(info string) map[string]string {
	if info == "" {
		return nil
	}
	kvs := map[string]string{}
	for _, kv := range strings.Split(info, ",") {
		parts := strings.SplitN(kv, ":", 2)
		if len(parts) != 2 {
			continue
		}
		kvs[parts[0]] = parts[1]
	}
	return kvs
}

func covnertCloudServer(server model.CloudServer) common.VirtualMachine {
	vm := common.VirtualMachine{
		Descripter: common.Descripter{
			ID:            server.Id,
			Name:          server.Name,
			Annotations:   server.Metadata,
			Description:   ptr.Deref(server.Description, ""),
			ResourceGroup: ptr.Deref(server.EnterpriseProjectId, ""),
		},
		Zone: server.AvailabilityZone,
		Status: common.VirtualMachineStatus{
			Phase: server.Status,
		},
	}
	vm.CreationTimestamp, _ = time.Parse(time.RFC3339, server.Created)
	if server.LaunchedAt != nil {
		vm.Status.BootTime, _ = time.Parse(time.RFC3339, *server.LaunchedAt)
	}
	vm.Labels = tagsToMap(server.Tags)
	if server.Image != nil {
		vm.Image.ID = server.Image.Id
	}
	if metadata := server.Metadata; metadata != nil {
		vm.Image.Name = metadata["image_name"]
		if vm.Image.ID == "" {
			vm.Image.ID = metadata["metering.image_id"]
		}
		vpciid := metadata["vpc_id"]
		vm.Interfaces = []common.VirtualMachineInterfaceRef{
			{
				Network: vpciid,
			},
		}
	}
	if flavor := server.Flavor; flavor != nil {
		vm.InstanceType.ID = ptr.Deref(flavor.Id, "")
		vm.InstanceType.Name = ptr.Deref(flavor.Name, "")
		if flavor.Disk != nil {
			// pass
		}
		if flavor.Vcpus != nil {
			vm.Status.Resources[common.ResourceCPU] = *resource.NewQuantity(int64(*flavor.Vcpus), resource.DecimalSI)
			vm.Status.CPU.Cores = int(*flavor.Vcpus)
		}
		if flavor.Ram != nil {
			ram := int64(*flavor.Ram)
			vm.Status.Resources[common.ResourceMemory] = *resource.NewQuantity(ram*MB, resource.BinarySI)
		}
		if flavor.Gpus != nil && len(*flavor.Gpus) != 0 {
			gpus := int64(0)
			gpumemMB := int64(0)
			for _, gpu := range *flavor.Gpus {
				gpus += int64(ptr.Deref(gpu.Count, 0))
				gpumemMB += int64(ptr.Deref(gpu.MemoryMb, 0))
			}
			if gpus > 0 {
				vm.Status.Resources[common.ResourceGPU] = *resource.NewQuantity(gpus, resource.DecimalSI)
			}
		}
		if flavor.AsicAccelerators != nil {
			cnt := int64(0)
			memMB := int64(0)
			for _, asic := range *flavor.AsicAccelerators {
				cnt += int64(ptr.Deref(asic.Count, 0))
				memMB += int64(ptr.Deref(asic.MemoryMb, 0))
			}
			if memMB > 0 {
				vm.Status.Resources[common.ResourceType("asic-memory")] = *resource.NewQuantity(memMB*MB, resource.BinarySI)
			}
			if cnt > 0 {
				vm.Status.Resources[common.ResourceType("asic")] = *resource.NewQuantity(cnt, resource.DecimalSI)
			}
		}
	}
	if server.KeyName != nil {
		vm.KeyPairs = []common.VirtualMachineObjectRef{{
			ID:   *server.KeyName,
			Name: *server.KeyName,
		}}
	}
	if server.SecurityGroups != nil {
		for _, sec := range *server.SecurityGroups {
			vm.SecurityGroups = append(vm.SecurityGroups, common.VirtualMachineObjectRef{
				ID:   ptr.Deref(sec.Id, ""),
				Name: ptr.Deref(sec.Name, ""),
			})
		}
	}
	if server.VolumesAttached != nil {
		for _, vol := range *server.VolumesAttached {
			vm.Disks = append(vm.Disks, common.VirtualMachineDiskRef{
				Disk: common.Disk{
					Descripter:              common.Descripter{ID: vol.Id},
					Bootable:                ptr.Deref(vol.BootIndex, "") == "0",
					Status:                  common.DiskStatus{Device: ptr.Deref(vol.Device, "")},
					DeleteWithVirtualMchine: ptr.Deref(vol.DeleteOnTermination, false),
					Size:                    *resource.NewQuantity(int64(ptr.Deref(vol.Size, 0)), resource.BinarySI),
				},
			})
		}
	}
	if server.PowerState != nil {
		vm.Status.PowerState = mapPowerState(*server.PowerState)
	}
	// use the power state from the status
	vm.Power = vm.Status.PowerState
	vm.Status.Phase = server.Status
	return vm
}

func coverVmV1(hcs client.ServerDetail, userServerTags bool) common.VirtualMachine {
	vm := common.VirtualMachine{
		Descripter: common.Descripter{
			ID:            hcs.Id,
			Name:          hcs.Name,
			Annotations:   hcs.Metadata,
			Tags:          tagsToTags(hcs.Tags),
			ResourceGroup: ptr.Deref(hcs.EnterpriseProjectId, ""),
		},
		Zone:     hcs.OSEXTAZavailabilityZone,
		HostName: hcs.OSEXTSRVATTRhostname,
		Status: common.VirtualMachineStatus{
			Phase: hcs.Status,
			Host:  hcs.OSEXTSRVATTRhost,
			CPU: common.VirtualMachineCPU{
				Architecture: hcs.Architecture,
				Vendor:       hcs.CpuVendor,
				Sockets:      1, // default to 1
			},
			Resources: map[common.ResourceType]resource.Quantity{},
		},
	}
	if userServerTags {
		vm.Labels = serverTagsToMap(hcs.SysTags)
	} else {
		vm.Labels = tagsToMap(hcs.Tags)
	}
	vm.CreationTimestamp, _ = time.Parse(time.RFC3339, hcs.Created)

	if hcs.OSSRVUSGlaunchedAt != "" {
		vm.Status.BootTime, _ = time.Parse(EVSTimeLayout, hcs.OSSRVUSGlaunchedAt)
	}
	if hcs.OSSRVUSGterminatedAt != "" {
		deltime, _ := time.Parse(EVSTimeLayout, hcs.OSSRVUSGterminatedAt)
		vm.DeletionTimestamp = &deltime
	}

	if img := hcs.Image; img != nil {
		vm.Image.ID = img.Id
	}

	if imagmeta := hcs.ImageMeta; imagmeta != nil {
		if imagmeta.MinDisk != "" {
			minDisk, _ := strconv.ParseInt(imagmeta.MinDisk, 10, 64)
			vm.Status.Resources[common.ResourceDisk] = *resource.NewQuantity(minDisk*GB, resource.BinarySI)
		}
	}

	if vmtool := hcs.VMTools; vmtool != nil {
		if vmtool.HostInstalledVersion == "err_unknown" {
			vm.Status.Guest.VMTool.Version = "unknown"
		} else {
			vm.Status.Guest.VMTool.Version = vmtool.ServerInstalledVersion
		}
	}

	if metadata := hcs.Metadata; metadata != nil {
		vm.Image.Name = metadata["image_name"]
		if vm.Image.ID == "" {
			vm.Image.ID = metadata["metering.image_id"]
		}
		vpciid := metadata["vpc_id"]
		vm.Interfaces = []common.VirtualMachineInterfaceRef{
			{
				Network: vpciid,
			},
		}
		if vm.Status.Guest.OSType == "" {
			vm.Status.Guest.OSType = common.OSType(metadata["os_type"])
		}
	}

	if flavor := hcs.Flavor; flavor != nil {
		vm.InstanceType.ID = flavor.Id
		vm.InstanceType.Name = flavor.Name
		if flavor.Disk != "" {
			// pass
		}
		if flavor.Vcpus != "" {
			vcpu, _ := strconv.ParseInt(flavor.Vcpus, 10, 64)
			vm.Status.Resources[common.ResourceCPU] = *resource.NewQuantity(vcpu, resource.DecimalSI)
			vm.Status.CPU.Cores = int(vcpu)
		}
		if flavor.Ram != "" {
			ram, _ := strconv.ParseInt(flavor.Ram, 10, 64)
			vm.Status.Resources[common.ResourceMemory] = *resource.NewQuantity(ram*MB, resource.BinarySI)
		}
		if len(flavor.Gpus) != 0 {
			vm.Status.Resources[common.ResourceGPU] = *resource.NewQuantity(int64(len(flavor.Gpus)), resource.DecimalSI)
		}
		if len(flavor.AsicAccelerators) != 0 {
			vm.Status.Resources[common.ResourceType("asic-accelerator")] = *resource.NewQuantity(int64(len(flavor.AsicAccelerators)), resource.DecimalSI)
		}
	}

	if hcs.KeyName != "" {
		vm.KeyPairs = []common.VirtualMachineObjectRef{{
			ID:   hcs.KeyName,
			Name: hcs.KeyName,
		}}
	}
	// use the power state from the status
	vm.Status.PowerState = mapPowerState(hcs.OSEXTSTSpowerState)
	vm.Power = vm.Status.PowerState
	vm.Status.Phase = hcs.Status

	for _, disk := range hcs.OsExtendedVolumesvolumesAttached {
		vm.Disks = append(vm.Disks, common.VirtualMachineDiskRef{
			Disk: common.Disk{
				Descripter: common.Descripter{
					ID: disk.Id,
				},
				DeleteWithVirtualMchine: disk.DeleteOnTermination == "true",
				Bootable:                ptr.Deref(disk.BootIndex, "") == "0",
				Status:                  common.DiskStatus{Device: disk.Device},
			},
		})
	}
	for _, volume := range hcs.BlockDevice {
		vm.Disks = append(vm.Disks, common.VirtualMachineDiskRef{
			Disk: common.Disk{
				Descripter: common.Descripter{ID: volume.Id},
				Bootable:   volume.BootIndex == 0,
				Size:       *resource.NewQuantity(int64(volume.Size)*GB, resource.BinarySI),
				Status: common.DiskStatus{
					Device: volume.DeviceName,
				},
			},
			PCIAddress: volume.PciAddress,
			BUSType:    volume.DriverVolumeType,
		})
	}
	// sort disks by name
	slices.SortFunc(vm.Disks, func(i, j common.VirtualMachineDiskRef) int {
		return strings.Compare(i.Disk.Name, j.Disk.Name)
	})

	netif := make([]common.VirtualMachineNetworkInterface, 0, len(hcs.Addresses))
	for subnetname, addrs := range hcs.Addresses {
		_ = subnetname
		for _, addr := range addrs {
			netif = append(netif, common.VirtualMachineNetworkInterface{
				Connected: true,
				IPs:       []string{addr.Addr},
				MAC:       ptr.Deref(addr.OSEXTIPSMACmacAddr, ""),
			})
		}
	}
	vm.Status.Guest.Interfaces = netif

	secgroups := make([]common.VirtualMachineObjectRef, 0, len(hcs.SecurityGroups))
	for _, sec := range hcs.SecurityGroups {
		secgroups = append(secgroups, common.VirtualMachineObjectRef{
			ID:   sec.Id,
			Name: sec.Name,
		})
	}
	vm.SecurityGroups = secgroups

	return vm
}

func mapPowerState(state int32) common.PowerState {
	// 0: NOSTATE
	// 1: RUNNING
	// 3: PAUSED
	// 4: SHUTDOWN
	// 6: CRASHED
	// 7: SUSPENDED
	switch state {
	case 0:
		return common.PowerStateUnknown
	case 1:
		return common.PowerStateOn
	case 3:
		return common.PowerStatePaused
	case 4:
		return common.PowerStateOff
	case 6:
		return common.PowerStateCrashed
	case 7:
		return common.PowerStateSuspended
	default:
		return common.PowerStateUnknown
	}
}

// VNCVirtualMachine implements common.Provider.
func (p *Provider) VNCVirtualMachine(w http.ResponseWriter, r *http.Request, id string, path string) {
	// not proxy vnc
	if p.ProxyVNCAddr == nil {
		info, err := p.GetVirtualMachineRemoteConnectionInfo(r.Context(), id)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		if info.URL == "" {
			http.Error(w, "no vnc url found", http.StatusNotFound)
			return
		}
		// 302
		http.Redirect(w, r, info.URL, http.StatusFound)
		return
	}
	p.proxyVNC(w, r, id, path)
}

func (p *Provider) proxyVNC(w http.ResponseWriter, r *http.Request, id string, path string) {
	if path == "" {
		info, err := p.GetVirtualMachineRemoteConnectionInfo(r.Context(), id)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		if info.URL == "" {
			http.Error(w, "no vnc url found", http.StatusNotFound)
			return
		}
		target, err := url.Parse(info.URL)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
		path = target.Path + "?" + target.RawQuery
		// 302
		http.Redirect(w, r, path, http.StatusFound)
		return
	}
	// proxy to vnc
	proxy := &httputil.ReverseProxy{
		Transport: p.Client.Transport,
		Rewrite: func(pr *httputil.ProxyRequest) {
			pr.Out.URL.Path = path
			// vnc server block the request origin not the same as the server
			pr.Out.Header.Del("Origin")
			pr.SetURL(p.ProxyVNCAddr)
		},
		FlushInterval: -1,
		ErrorHandler:  libproxy.ErrorResponser{}.Error,
	}
	proxy.ServeHTTP(w, r)
}

// SetVirtualMachinePower implements common.Provider.
func (p *Provider) SetVirtualMachinePower(ctx context.Context, id string, power common.PowerAction, options common.VirtualMachinePowerOptions) error {
	rebootType := func(hard bool) *string {
		if hard {
			return ptr.To(client.ActionTypeHARD)
		}
		return ptr.To(client.ActionTypeSOFT)
	}

	req := &client.ServerActionRequestBody{}
	switch power {
	case common.PowerActionOn:
		req.OsStart = &client.ServerActionStartOption{}
	case common.PowerActionOff:
		req.OsStop = &client.ServerActionStopOption{
			Type: rebootType(options.Hard),
		}
	case common.PowerActionReboot:
		req.Reboot = &client.ServerActionRebootOption{
			Type: rebootType(options.Hard),
		}
	default:
		return errors.NewBadRequest(fmt.Sprintf("unsupport power action:%s", power))
	}
	resp, err := p.Client.ServerAction(&client.ServerActionRequest{
		ServerID: id,
		Body:     req,
	})
	if err != nil {
		return err
	}
	_ = resp
	return nil
}
