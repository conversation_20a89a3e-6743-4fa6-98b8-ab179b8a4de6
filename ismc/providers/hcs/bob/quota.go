package bob

import (
	"context"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/ecs/v2/model"
	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.QuotaOperation = &Provider{}

// GetQuota implements common.QuotaOperation.
func (p *Provider) GetQuota(ctx context.Context) (*common.Quota, error) {
	limit, err := p.Client.ECS.ShowServerLimits(&model.ShowServerLimitsRequest{})
	if err != nil {
		return nil, err
	}
	quota := &common.Quota{
		Hard: map[common.ResourceType]resource.Quantity{
			common.ResourceCPU:                        *resource.NewQuantity(int64(limit.Absolute.MaxTotalCores), resource.DecimalSI),
			common.ResourceMemory:                     *resource.NewQuantity(int64(limit.Absolute.MaxTotalRAMSize)*1024*1024, resource.BinarySI),
			common.ResourceType("instances"):          *resource.NewQuantity(int64(limit.Absolute.MaxTotalInstances), resource.DecimalSI),
			common.ResourceType("floatingIP"):         *resource.NewQuantity(int64(limit.Absolute.MaxTotalFloatingIps), resource.DecimalSI),
			common.ResourceType("securityGroup"):      *resource.NewQuantity(int64(limit.Absolute.MaxSecurityGroups), resource.DecimalSI),
			common.ResourceType("securityGroupRules"): *resource.NewQuantity(int64(limit.Absolute.MaxSecurityGroupRules), resource.DecimalSI),
			common.ResourceType("keypairs"):           *resource.NewQuantity(int64(limit.Absolute.MaxTotalKeypairs), resource.DecimalSI),
			common.ResourceType("server"):             *resource.NewQuantity(int64(limit.Absolute.MaxServerMeta), resource.DecimalSI),
			common.ResourceType("image"):              *resource.NewQuantity(int64(limit.Absolute.MaxImageMeta), resource.DecimalSI),
		},
	}
	return quota, nil
}
