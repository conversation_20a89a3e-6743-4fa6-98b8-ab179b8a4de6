package bob

import (
	"context"
	"io"
	"os"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestListImages(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListImages(ctx, common.ListImageOptions{
		ListOptions: common.ListOptions{},
		Source:      common.ImageSourceCustom,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestGetImage(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetImage(ctx, "8bb364f4-71b8-4f40-9d06-0681158f9b21")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestDeleteImages(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	err := globalProvider.DeleteImage(ctx, "4818c5a1-d777-4e07-997e-070e7bc573c8")
	if err != nil {
		t.<PERSON>(err)
	}
	t.Log("success")
}

func readImageFromLocal(_ context.Context) (io.ReadCloser, error) {
	return os.Open("tmp/jammy-server-cloudimg-amd64.img")
}

func TestImportImage(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	content, err := readImageFromLocal(ctx)
	if err != nil {
		t.Fatal(err)
	}
	img := &common.Image{
		Descripter: common.Descripter{
			Name: "ismc-test-image1",
		},
		Architecture: "x86_64",
		BootMode:     "uefi",
	}
	file := common.FileContent{
		FileName: "jammy-server-cloudimg-amd64.img",
		Content:  content,
	}
	_, err = globalProvider.ImportImage(ctx, img, file)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

func TestListInstanceTypes(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.ListInstanceTypes(ctx, common.ListInstanceTypeOptions{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestGetInstanceType(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resp, err := globalProvider.GetInstanceType(ctx, "f6fb37f1-4b29-41bd-9ed3-46d68274b646")
	if err != nil {
		t.Fatal(err)
	}
	t.Log(resp)
}

func TestCreateInstanceType(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	instanceType := &common.InstanceType{
		Descripter: common.Descripter{
			Name: "Flavor_8c8g_ismc",
		},
	}
	_, err := globalProvider.CreateInstanceType(ctx, instanceType)
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}
