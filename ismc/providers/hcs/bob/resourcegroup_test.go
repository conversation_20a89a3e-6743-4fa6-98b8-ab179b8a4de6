package bob

import (
	"context"
	"encoding/json"
	"testing"

	"xiaoshiai.cn/core/ismc/common"
)

func TestListResourceGroups(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	resourceGroups, err := globalProvider.ListResourceGroups(ctx, common.ListResourceGroupOptions{})
	if err != nil {
		t.Fatal(err)
	}
	for _, rg := range resourceGroups.Items {
		data, _ := json.Marshal(rg)
		t.Log(string(data))
	}
}

func TestGetResourceGroup(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	rg, err := globalProvider.GetResourceGroup(ctx, "0b431e1a-3daf-466c-bc3e-0cb66d94a896")
	if err != nil {
		t.Fatal(err)
	}
	data, _ := json.Marshal(rg)
	t.Log(string(data))
}

func TestCreateResourceGroup(t *testing.T) {
	globalProvider := setupProvider(t)
	ctx := context.Background()

	_, err := globalProvider.CreateResourceGroup(ctx, &common.ResourceGroup{
		Descripter: common.Descripter{
			Name:        "ismc-1",
			Description: "this is create by ismc api",
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}
