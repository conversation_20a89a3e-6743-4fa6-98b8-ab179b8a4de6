package bob

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/hcs/client"
)

// GetHost implements common.Provider.
func (p *Provider) GetHost(ctx context.Context, id string) (*common.Host, error) {
	phyhost, err := p.getPhyHost(ctx, id)
	if err != nil {
		return nil, err
	}
	host := phyHostToHost(*phyhost)
	return &host, nil
}

func (p *Provider) getPhyHost(ctx context.Context, id string) (*client.SysPhysicalHost, error) {
	req := client.ListResourceModelRequest{
		Condition: []client.ListResourceModelRequestConstraint{
			client.ListResourceModelRequestConstraintEqual("nativeId", id),
		},
	}
	list, err := p.Client.ListSystemPhysicalHost(ctx, req)
	if err != nil {
		return nil, err
	}
	if len(list.ObjList) == 0 {
		return nil, errors.NewNotFound("host", id)
	}
	return &list.ObjList[0], nil
}

// ListHosts implements common.Provider.
func (p *Provider) ListHosts(ctx context.Context, option common.ListHostOptions) (common.List[common.Host], error) {
	req := client.ListResourceModelRequest{
		PageNo:   option.Page,
		PageSize: option.Size,
	}
	list, err := p.Client.ListSystemPhysicalHost(ctx, req)
	if err != nil {
		return common.List[common.Host]{}, err
	}
	items := make([]common.Host, 0, len(list.ObjList))
	for _, item := range list.ObjList {
		items = append(items, phyHostToHost(item))
	}
	return common.List[common.Host]{
		Items: items,
		Size:  list.PageSize,
		Total: list.TotalNum,
		Page:  list.CurrentPageNo,
	}, nil
}

func phyHostToHost(item client.SysPhysicalHost) common.Host {
	ret := common.Host{
		Descripter: common.Descripter{
			ID:   item.NativeId, // use nativeId as ID, vm status host is nativeId
			UID:  item.Id,
			Name: item.Name,
		},
		Zone: item.AzoneName,
		Status: common.HostStatus{
			Capacity: map[common.ResourceType]resource.Quantity{},
			Used:     map[common.ResourceType]resource.Quantity{},
		},
	}
	// ips
	if item.BmcIp != "" {
		ret.Status.IPs = append(ret.Status.IPs, item.BmcIp)
	}
	if item.IpAddress != "" {
		ret.Status.IPs = append(ret.Status.IPs, item.IpAddress)
	}
	// capacity
	if item.TotalVcpuCores != 0 {
		ret.Status.Capacity[common.ResourceCPU] = *resource.NewQuantity(int64(item.TotalVcpuCores), resource.DecimalSI)
	}
	if item.TotalDiskSizeMB != 0 {
		ret.Status.Capacity[common.ResourceStorage] = *resource.NewQuantity(int64(item.TotalDiskSizeMB*MB), resource.BinarySI)
	}
	if item.TotalVmemoryMB != 0 {
		ret.Status.Capacity[common.ResourceMemory] = *resource.NewQuantity(int64(item.TotalVmemoryMB*MB), resource.BinarySI)
	}
	if item.GpuTotal != 0 {
		ret.Status.Capacity[common.ResourceGPU] = *resource.NewQuantity(int64(item.GpuTotal), resource.DecimalSI)
	}
	// used
	if item.AllocatedVcpuCores != 0 {
		ret.Status.Used[common.ResourceCPU] = *resource.NewQuantity(int64(item.AllocatedVcpuCores), resource.DecimalSI)
	}
	if item.AllocatedDiskSizeMB != 0 {
		ret.Status.Used[common.ResourceStorage] = *resource.NewQuantity(int64(item.AllocatedDiskSizeMB*MB), resource.BinarySI)
	}
	if item.AllocatedVmemoryMB != 0 {
		ret.Status.Used[common.ResourceMemory] = *resource.NewQuantity(int64(item.AllocatedVmemoryMB*MB), resource.BinarySI)
	}
	if item.GpuUsed != 0 {
		ret.Status.Used[common.ResourceGPU] = *resource.NewQuantity(int64(item.GpuUsed), resource.DecimalSI)
	}
	return ret
}

// UpdateHost implements common.Provider.
func (p *Provider) UpdateHost(ctx context.Context, hostname string, host *common.Host) error {
	return common.ErrUnsupported
}
