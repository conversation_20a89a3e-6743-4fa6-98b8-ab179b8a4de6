package bob

import (
	"context"

	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/core/ismc/common"
)

// GetVersion implements common.Provider.
func (p *Provider) GetVersion(ctx context.Context) (*common.Version, error) {
	venderversion := common.VersionInfo{
		Vendor:  "huawei",
		Name:    "hcs",
		Version: "ManageOne 8.3.0.SPC010",
	}
	return &common.Version{
		Agent:  common.VersionInfo{Version: version.Get().GitVersion},
		Vendor: venderversion,
	}, nil
}
