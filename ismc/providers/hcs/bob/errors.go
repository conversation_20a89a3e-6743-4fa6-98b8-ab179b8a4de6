package bob

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/go-resty/resty/v2"
)

var errUnsupportMethod = fmt.Errorf("not support thhis method")

type BaseError struct {
	ExceptionID   string   `json:"exceptionId"`
	ExceptionType string   `json:"exceptionType"`
	DescArgs      []string `json:"descArgs"`
	ReasonArgs    []string `json:"reasonArgs"`
	DetailArgs    []string `json:"detailArgs"`
	AdviceArgs    []string `json:"adviceArgs"`
}

func (e BaseError) Error() string {
	return fmt.Sprintf("ExceptionID:%s\nExceptionType:%s\nDescArgs:%v\nReasonArgs:%v\nDetailArgs:%v\nAdviceArgs:%v",
		e.ExceptionID, e.ExceptionType, e.DescArgs, e.<PERSON>rgs, e.DetailArgs, e.AdviceArgs)
}

func WrapperError(resp *resty.Response) error {
	if resp.IsError() {
		var err error
		switch resp.StatusCode() {
		case http.StatusBadRequest, http.StatusInternalServerError:
			var errorBase BaseError
			err = json.Unmarshal(resp.Body(), &errorBase)
			if err != nil {
				return fmt.Errorf("failed to unmarshal base error body: %w", err)
			}
			return errorBase
		default:
			return fmt.Errorf("unexpected HTTP error: %d - %s", resp.StatusCode(), resp.Status())
		}
	}
	return nil
}
