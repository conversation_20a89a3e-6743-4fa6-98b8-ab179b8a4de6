package providers

import (
	"context"
	"fmt"
	"strings"

	"xiaoshiai.cn/core/ismc/common"
	hcs "xiaoshiai.cn/core/ismc/providers/hcs/bob"
	"xiaoshiai.cn/core/ismc/providers/noop"
	vmwaresdk "xiaoshiai.cn/core/ismc/providers/vmware"
	vmware "xiaoshiai.cn/core/ismc/providers/vmware/bob"
)

type Options struct {
	Type   string             `json:"type,omitempty" description:"provider type"`
	Vmware *vmwaresdk.Options `json:"vmware,omitempty" description:"vmware options"`
	HuaWei *hcs.Options       `json:"huawei,omitempty" description:"huawei options"`
}

func NewDefaultOptions() *Options {
	return &Options{
		Type:   "none",
		Vmware: vmwaresdk.NewDefaultOptions(),
		HuaWei: hcs.NewDefaultOptions(),
	}
}

func NewProvider(ctx context.Context, options *Options) (common.Provider, error) {
	switch strings.ToLower(options.Type) {
	case "vmware":
		vmCli, err := vmwaresdk.NewClient(ctx, *options.Vmware)
		if err != nil {
			return nil, err
		}
		return vmware.NewProvider(ctx, vmCli)
	case "huawei", "hcs":
		return hcs.NewProvider(ctx, options.HuaWei)
	case "none":
		return noop.NewProvider(ctx), nil
	default:
		return nil, fmt.Errorf("unknown provider type %s", options.Type)
	}
}
