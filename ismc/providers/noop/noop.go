package noop

import (
	"context"
	"io"
	"net/http"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/ismc/common"
)

var _ common.Provider = &NoopProvider{}

func NewProvider(_ context.Context) common.Provider {
	return &NoopProvider{}
}

type NoopProvider struct{}

// UpdateResourceGroup implements common.Provider.
func (n *NoopProvider) UpdateResourceGroup(ctx context.Context, id string, resourceGroup *common.ResourceGroup) error {
	return common.ErrUnsupported
}

// ListVirtualMachineDisks implements common.Provider.
func (n *NoopProvider) ListVirtualMachineDisks(ctx context.Context, id string, options common.ListVirtualMachineDiskOptions) (common.List[common.Disk], error) {
	return common.List[common.Disk]{}, nil
}

// ListVirtualMachineNetworkInterfaces implements common.Provider.
func (n *NoopProvider) ListVirtualMachineNetworkInterfaces(ctx context.Context, id string, options common.ListVirtualMachineNetworkOptions) (common.List[common.NetworkInterface], error) {
	return common.List[common.NetworkInterface]{}, nil
}

// ReInitalizeDisk implements common.Provider.
func (n *NoopProvider) ReInitalizeDisk(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// AttachVirtualMachineDisk implements common.Provider.
func (n *NoopProvider) AttachVirtualMachineDisk(ctx context.Context, id string, options common.AttachVirtualMachineDiskOptions) error {
	return common.ErrUnsupported
}

// AttachVirtualMachineNetworkInterface implements common.Provider.
func (n *NoopProvider) AttachVirtualMachineNetworkInterface(ctx context.Context, id string, options common.AttachVirtualMachineNetworkInterfaceOptions) error {
	return common.ErrUnsupported
}

// ChangeVirtualMachineImage implements common.Provider.
func (n *NoopProvider) ChangeVirtualMachineImage(ctx context.Context, id string, options common.ChangeVirtualMachineImageOptions) error {
	return common.ErrUnsupported
}

// ChangeVirtualMachineInstanceType implements common.Provider.
func (n *NoopProvider) ChangeVirtualMachineInstanceType(ctx context.Context, id string, options common.ChangeVirtualMachineInstanceTypeOptions) error {
	return common.ErrUnsupported
}

// DetachVirtualMachineDisk implements common.Provider.
func (n *NoopProvider) DetachVirtualMachineDisk(ctx context.Context, id string, diskid string, options common.DetachVirtualMachineDiskOptions) error {
	return common.ErrUnsupported
}

// DetachVirtualMachineNetworkInterface implements common.Provider.
func (n *NoopProvider) DetachVirtualMachineNetworkInterface(ctx context.Context, id string, nicid string, options common.DetachVirtualMachineNetworkInterfaceOptions) error {
	return common.ErrUnsupported
}

// ReInstallVirtualMachine implements common.Provider.
func (n *NoopProvider) ReInstallVirtualMachine(ctx context.Context, id string, options common.ReInstallVirtualMachineOptions) error {
	return common.ErrUnsupported
}

// ChangeVirtualMachineCloudInit implements common.Provider.
func (n *NoopProvider) ChangeVirtualMachineCloudInit(ctx context.Context, id string, options common.ResetVirtualMachineCloudInitOptions) error {
	return common.ErrUnsupported
}

// ResetVirtualMachinePassword implements common.Provider.
func (n *NoopProvider) ResetVirtualMachinePassword(ctx context.Context, id string, options common.ResetVirtualMachinePasswordOptions) error {
	return common.ErrUnsupported
}

// ================================== HostOperation ==================================

// GetHost implements common.Provider.
func (n *NoopProvider) GetHost(ctx context.Context, hostname string) (*common.Host, error) {
	return nil, common.ErrUnsupported
}

// ListHosts implements common.Provider.
func (n *NoopProvider) ListHosts(ctx context.Context, option common.ListHostOptions) (common.List[common.Host], error) {
	return common.List[common.Host]{}, nil
}

// UpdateHost implements common.Provider.
func (n *NoopProvider) UpdateHost(ctx context.Context, hostname string, host *common.Host) error {
	return common.ErrUnsupported
}

// GetTask implements common.Provider.
func (n *NoopProvider) GetTask(ctx context.Context, id string) (*common.Task, error) {
	return nil, common.ErrUnsupported
}

// ================================== TaskOperation ==================================
// ListTasks implements common.Provider.
func (n *NoopProvider) ListTasks(ctx context.Context, opts common.ListTaskOptions) (common.List[common.Task], error) {
	return common.List[common.Task]{}, nil
}

// RemoveTask implements common.Provider.
func (n *NoopProvider) RemoveTask(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// RestartTask implements common.Provider.
func (n *NoopProvider) RestartTask(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// StopTask implements common.Provider.
func (n *NoopProvider) StopTask(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// WactchTasks implements common.Provider.
func (n *NoopProvider) WactchTasks(ctx context.Context, on common.OnTaskEvent, opts common.WactchTaskOptions) error {
	return common.ErrUnsupported
}

// ================================== DiskOperation ==================================

// ResizeDisk implements common.Provider.
func (n *NoopProvider) ResizeDisk(ctx context.Context, id string, size resource.Quantity, options common.ResizeDiskOptions) error {
	return common.ErrUnsupported
}

// ListDiskMetrics implements common.Provider.
func (n *NoopProvider) ListDiskMetrics(ctx context.Context) ([]*common.DiskMetrics, error) {
	return nil, common.ErrUnsupported
}

// GetDiskClass implements common.Provider.
func (n *NoopProvider) GetDiskClass(ctx context.Context, id string) (*common.DiskClass, error) {
	return nil, common.ErrUnsupported
}

// GetVirtualMachineMetrics implements common.Provider.
func (n *NoopProvider) GetVirtualMachineMetrics(ctx context.Context, id string) (*common.VirtualMachineMetrics, error) {
	return nil, common.ErrUnsupported
}

// GetZoneMetrics implements common.Provider.
func (n *NoopProvider) GetZoneMetrics(ctx context.Context, id string) (*common.ZoneMetrics, error) {
	return nil, common.ErrUnsupported
}

// ListVirtualMachineMetrics implements common.Provider.
func (n *NoopProvider) ListVirtualMachineMetrics(ctx context.Context, options common.ListVirtualMachineMetricsOptions) ([]*common.VirtualMachineMetrics, error) {
	return nil, common.ErrUnsupported
}

// ListZoneMetrics implements common.Provider.
func (n *NoopProvider) ListZoneMetrics(ctx context.Context) ([]*common.ZoneMetrics, error) {
	return nil, common.ErrUnsupported
}

// GetDiskMetrics implements common.Provider.
func (n *NoopProvider) GetDiskMetrics(ctx context.Context, id string) (*common.DiskMetrics, error) {
	return nil, common.ErrUnsupported
}

// GetHostMetrics implements common.Provider.
func (n *NoopProvider) GetHostMetrics(ctx context.Context, id string) (*common.HostMetrics, error) {
	return nil, common.ErrUnsupported
}

// ListHostMetrics implements common.Provider.
func (n *NoopProvider) ListHostMetrics(ctx context.Context) ([]*common.HostMetrics, error) {
	return nil, common.ErrUnsupported
}

// SetVirtualMachinePower implements common.Provider.
func (n *NoopProvider) SetVirtualMachinePower(ctx context.Context, id string, power common.PowerAction, options common.VirtualMachinePowerOptions) error {
	return common.ErrUnsupported
}

// ================================== StatisticsOperation ==================================

// GetSystemStatistics implements common.StatisticsOperation.
func (n *NoopProvider) GetSystemStatistics(ctx context.Context) (*common.SystemStatistics, error) {
	return nil, common.ErrUnsupported
}

// ================================== QuotaOperation ==================================

// GetQuota implements common.QuotaOperation.
func (n *NoopProvider) GetQuota(ctx context.Context) (*common.Quota, error) {
	return nil, common.ErrUnsupported
}

// ================================== DiskSnapshotOperation ==================================

// CreateDiskSnapshot implements common.DiskSnapshotOperation.
func (n *NoopProvider) CreateDiskSnapshot(ctx context.Context, snapshot *common.DiskSnapshot) error {
	return common.ErrUnsupported
}

// ListDiskSnapshots implements common.DiskSnapshotOperation.
func (n *NoopProvider) ListDiskSnapshots(ctx context.Context, options common.ListDiskSnapshotOption) (common.List[common.DiskSnapshot], error) {
	return common.List[common.DiskSnapshot]{}, nil
}

// RemoveDiskSnapshot implements common.DiskSnapshotOperation.
func (n *NoopProvider) RemoveDiskSnapshot(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// ================================== SecurityGroupOperation ==================================

// AddSecurityGroupRule implements common.SecurityGroupOperation.
func (n *NoopProvider) AddSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return common.ErrUnsupported
}

// CreateSecurityGroup implements common.SecurityGroupOperation.
func (n *NoopProvider) CreateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteSecurityGroup implements common.SecurityGroupOperation.
func (n *NoopProvider) DeleteSecurityGroup(ctx context.Context, name string) error {
	return common.ErrUnsupported
}

// DeleteSecurityGroupRule implements common.SecurityGroupOperation.
func (n *NoopProvider) DeleteSecurityGroupRule(ctx context.Context, securityGroup string, ruleName string) error {
	return common.ErrUnsupported
}

// GetSecurityGroup implements common.SecurityGroupOperation.
func (n *NoopProvider) GetSecurityGroup(ctx context.Context, name string) (*common.SecurityGroup, error) {
	return nil, common.ErrUnsupported
}

// ListSecurityGroupRules implements common.SecurityGroupOperation.
func (n *NoopProvider) ListSecurityGroupRules(ctx context.Context, securityGroup string, options common.ListSecurityGroupRuleOptions) (common.List[common.SecurityGroupRule], error) {
	return common.List[common.SecurityGroupRule]{}, nil
}

// ListSecurityGroups implements common.SecurityGroupOperation.
func (n *NoopProvider) ListSecurityGroups(ctx context.Context, options common.ListSecurityGroupOptions) (common.List[common.SecurityGroup], error) {
	return common.List[common.SecurityGroup]{}, nil
}

// UpdateSecurityGroup implements common.SecurityGroupOperation.
func (n *NoopProvider) UpdateSecurityGroup(ctx context.Context, sg *common.SecurityGroup) error {
	return common.ErrUnsupported
}

// UpdateSecurityGroupRule implements common.SecurityGroupOperation.
func (n *NoopProvider) UpdateSecurityGroupRule(ctx context.Context, securityGroup string, rule *common.SecurityGroupRule) error {
	return common.ErrUnsupported
}

// ================================== InstanceTypeOperation ==================================

// CreateInstanceType implements common.InstanceTypeOperation.
func (n *NoopProvider) CreateInstanceType(ctx context.Context, instanceType *common.InstanceType) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteInstanceType implements common.InstanceTypeOperation.
func (n *NoopProvider) DeleteInstanceType(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// GetInstanceType implements common.InstanceTypeOperation.
func (n *NoopProvider) GetInstanceType(ctx context.Context, id string) (*common.InstanceType, error) {
	return nil, common.ErrUnsupported
}

// ListInstanceTypes implements common.InstanceTypeOperation.
func (n *NoopProvider) ListInstanceTypes(ctx context.Context, options common.ListInstanceTypeOptions) (common.List[common.InstanceType], error) {
	return common.List[common.InstanceType]{}, nil
}

// UpdateInstanceType implements common.InstanceTypeOperation.
func (n *NoopProvider) UpdateInstanceType(ctx context.Context, instanceType *common.InstanceType) error {
	return common.ErrUnsupported
}

// ================================== VirtualNetworkOperation ==================================

// CreateVirtualNetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) CreateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// CreateVirtualSubnetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) CreateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteVirtualNetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) DeleteVirtualNetwork(ctx context.Context, name string) error {
	return common.ErrUnsupported
}

// DeleteVirtualSubnetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) DeleteVirtualSubnetwork(ctx context.Context, network string, name string) error {
	return common.ErrUnsupported
}

// GetVirtualNetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) GetVirtualNetwork(ctx context.Context, name string) (*common.VirtualNetwork, error) {
	return nil, common.ErrUnsupported
}

// GetVirtualSubnetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) GetVirtualSubnetwork(ctx context.Context, network string, name string) (*common.VirtualSubnetwork, error) {
	return nil, common.ErrUnsupported
}

// ListVirtualNetworks implements common.VirtualNetworkOperation.
func (n *NoopProvider) ListVirtualNetworks(ctx context.Context, options common.ListVirtualNetworkOptions) (common.List[common.VirtualNetwork], error) {
	return common.List[common.VirtualNetwork]{}, nil
}

// ListVirtualSubnetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) ListVirtualSubnetwork(ctx context.Context, network string, options common.ListVirtualSubnetworkOptions) (common.List[common.VirtualSubnetwork], error) {
	return common.List[common.VirtualSubnetwork]{}, nil
}

// ListVirtualSubnetworkAllocations implements common.VirtualNetworkOperation.
func (n *NoopProvider) ListVirtualSubnetworkAllocations(ctx context.Context, network string, name string, options common.ListVirtualSwitchAllocationOptions) (common.List[common.VirtualSubnetworkAllocation], error) {
	return common.List[common.VirtualSubnetworkAllocation]{}, nil
}

// UpdateVirtualNetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) UpdateVirtualNetwork(ctx context.Context, network *common.VirtualNetwork) error {
	return common.ErrUnsupported
}

// UpdateVirtualSubnetwork implements common.VirtualNetworkOperation.
func (n *NoopProvider) UpdateVirtualSubnetwork(ctx context.Context, network string, vswitch *common.VirtualSubnetwork) error {
	return common.ErrUnsupported
}

// ================================== VirtualMachineOperation ==================================

// CreateVirtualMachine implements common.VirtualMachineOperation.
func (n *NoopProvider) CreateVirtualMachine(ctx context.Context, vm *common.VirtualMachine,
	options common.CreateVirtualMachineOptions,
) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteVirtualMachine implements common.VirtualMachineOperation.
func (n *NoopProvider) DeleteVirtualMachine(ctx context.Context, id string, options common.DeleteVirtualMachineOptions) error {
	return common.ErrUnsupported
}

// GetVirtualMachine implements common.VirtualMachineOperation.
func (n *NoopProvider) GetVirtualMachine(ctx context.Context, id string) (*common.VirtualMachine, error) {
	return nil, common.ErrUnsupported
}

// GetVirtualMachineRemoteConnectionInfo implements common.VirtualMachineOperation.
func (n *NoopProvider) GetVirtualMachineRemoteConnectionInfo(ctx context.Context, id string) (*common.RemoteConnectionInfo, error) {
	return nil, common.ErrUnsupported
}

// ListVirtualMachines implements common.VirtualMachineOperation.
func (n *NoopProvider) ListVirtualMachines(ctx context.Context, options common.ListVirtualMachineOptions) (common.List[common.VirtualMachine], error) {
	return common.List[common.VirtualMachine]{}, nil
}

// UpdateVirtualMachine implements common.VirtualMachineOperation.
func (n *NoopProvider) UpdateVirtualMachine(ctx context.Context, vm *common.VirtualMachine) error {
	return common.ErrUnsupported
}

// VNCVirtualMachine implements common.Provider.
func (n *NoopProvider) VNCVirtualMachine(w http.ResponseWriter, r *http.Request, id string, path string) {
}

// ================================== DiskClusterOperation ==================================

// CreateDiskCluster implements common.DiskClusterOperation.
func (n *NoopProvider) CreateDiskCluster(ctx context.Context, cluster *common.DiskCluster) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteDiskCluster implements common.DiskClusterOperation.
func (n *NoopProvider) DeleteDiskCluster(ctx context.Context, id string) error {
	return common.ErrUnsupported
}

// GetDiskCluster implements common.DiskClusterOperation.
func (n *NoopProvider) GetDiskCluster(ctx context.Context, id string) (*common.DiskCluster, error) {
	return nil, common.ErrUnsupported
}

// ListDiskClass implements common.DiskClusterOperation.
func (n *NoopProvider) ListDiskClass(ctx context.Context, options common.ListDiskClassOptions) (common.List[common.DiskClass], error) {
	return common.List[common.DiskClass]{}, nil
}

// ListDiskClusters implements common.DiskClusterOperation.
func (n *NoopProvider) ListDiskClusters(ctx context.Context, options common.ListDiskClusterOptions) (common.List[common.DiskCluster], error) {
	return common.List[common.DiskCluster]{}, nil
}

// ================================== DiscoveryOperation ==================================

// GetVersion implements common.DiscoveryOperation.
func (n *NoopProvider) GetVersion(ctx context.Context) (*common.Version, error) {
	return &common.Version{}, nil
}

// ================================== ResourceGroupOperation ==================================

// CreateResourceGroup implements common.ResourceGroupOperation.
func (n *NoopProvider) CreateResourceGroup(ctx context.Context, resourceGroup *common.ResourceGroup) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteResourceGroup implements common.ResourceGroupOperation.
func (n *NoopProvider) DeleteResourceGroup(ctx context.Context, name string) error {
	return common.ErrUnsupported
}

// GetResourceGroup implements common.ResourceGroupOperation.
func (n *NoopProvider) GetResourceGroup(ctx context.Context, name string) (*common.ResourceGroup, error) {
	return nil, common.ErrUnsupported
}

// ListResourceGroups implements common.ResourceGroupOperation.
func (n *NoopProvider) ListResourceGroups(ctx context.Context, options common.ListResourceGroupOptions) (common.List[common.ResourceGroup], error) {
	return common.List[common.ResourceGroup]{}, nil
}

// ================================== RegionOperation ==================================

// GetZone implements common.RegionOperation.
func (n *NoopProvider) GetZone(ctx context.Context, name string) (*common.Zone, error) {
	return nil, common.ErrUnsupported
}

// ListZones implements common.RegionOperation.
func (n *NoopProvider) ListZones(ctx context.Context, option common.ListZoneOptions) (common.List[common.Zone], error) {
	return common.List[common.Zone]{}, nil
}

// ================================== ImageOperation ==================================

// DeleteImage implements common.ImageOperation.
func (n *NoopProvider) DeleteImage(ctx context.Context, name string) error {
	return common.ErrUnsupported
}

// GetImage implements common.ImageOperation.
func (n *NoopProvider) GetImage(ctx context.Context, name string) (*common.Image, error) {
	return nil, common.ErrUnsupported
}

// ImportImage implements common.ImageOperation.
func (n *NoopProvider) ImportImage(ctx context.Context, image *common.Image, file common.FileContent) (*common.Descripter, error) {
	log := log.DefaultLogger
	log.Info("import image", "image", image)

	log.Info("importing image data", "file", file.FileName)
	content := file.Content
	defer content.Close()

	dest := io.Discard
	readn, err := io.Copy(dest, content)
	if err != nil {
		if err == io.EOF {
			if readn != file.ContentLength {
				return nil, io.ErrUnexpectedEOF
			}
		}
		return nil, err
	}
	log.Info("imported image data", "file", file.FileName, "size", readn)
	desc := &common.Descripter{}
	return desc, nil
}

// ListImages implements common.ImageOperation.
func (n *NoopProvider) ListImages(ctx context.Context, options common.ListImageOptions) (common.List[common.Image], error) {
	return common.List[common.Image]{}, nil
}

// UpdateImage implements common.ImageOperation.
func (n *NoopProvider) UpdateImage(ctx context.Context, image *common.Image) error {
	return common.ErrUnsupported
}

// ================================== DiskOperation ==================================

// CreateDisk implements common.DiskOperation.
func (n *NoopProvider) CreateDisk(ctx context.Context, disk *common.Disk, options common.CreateDiskOptions) (*common.Descripter, error) {
	return nil, common.ErrUnsupported
}

// DeleteDisk implements common.DiskOperation.
func (n *NoopProvider) DeleteDisk(ctx context.Context, id string, options common.DeleteDiskOptions) error {
	return common.ErrUnsupported
}

// GetDisk implements common.DiskOperation.
func (n *NoopProvider) GetDisk(ctx context.Context, id string) (*common.Disk, error) {
	return nil, common.ErrUnsupported
}

// ListDisks implements common.DiskOperation.
func (n *NoopProvider) ListDisks(ctx context.Context, options common.ListDiskOptions) (common.List[common.Disk], error) {
	return common.List[common.Disk]{}, nil
}

// UpdateDisk implements common.DiskOperation.
func (n *NoopProvider) UpdateDisk(ctx context.Context, disk *common.Disk) error {
	return common.ErrUnsupported
}
