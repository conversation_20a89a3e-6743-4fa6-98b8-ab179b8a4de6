package common

import (
	"context"
	"io"

	"k8s.io/apimachinery/pkg/api/resource"
)

type Image struct {
	Descripter    `json:",inline"`
	Source        ImageSource  `json:"source,omitempty"`
	Tags          []string     `json:"tags,omitempty"`
	Architecture  Architecture `json:"architecture,omitempty"`
	OS            OSType       `json:"os,omitempty"`
	OSVersion     string       `json:"osVersion,omitempty"`
	BootMode      BootMode     `json:"bootMode,omitempty"`
	KernelVersion string       `json:"kernelVersion,omitempty"`
	// MinDisk 是镜像所需的最小磁盘空间,当创建虚拟机时，需要保证虚拟机的磁盘空间大于等于镜像的最小磁盘空间
	MinDisk resource.Quantity `json:"minDisk,omitempty"`
	// MinMemory 是镜像所需的最小内存,当创建虚拟机时，需要保证虚拟机的内存大于等于镜像的最小内存
	MinMemory resource.Quantity `json:"minMemory,omitempty"`
	// Features 是镜像的特性
	Features []string    `json:"features,omitempty"`
	Version  string      `json:"version,omitempty"`
	Public   bool        `json:"public,omitempty"`
	Status   ImageStatus `json:"status,omitempty"`
}

type ImageSource string

const (
	ImageSourcePublic    ImageSource = "Public"    // 公共镜像
	ImageSourceCustom    ImageSource = "Custom"    // 自定义镜像
	ImageSourceShared    ImageSource = "Shared"    // 共享镜像, 由其他用户共享
	ImageSourceCommunity ImageSource = "Community" // 社区镜像
)

const (
	ImagePhaseUnknown   ImagePhase = "Unknown"   // 未知状态
	ImagePhasePending   ImagePhase = "Pending"   // 等待中
	ImagePhaseImporting ImagePhase = "Importing" // 导入中
	ImagePhaseFailed    ImagePhase = "Failed"    // 导入失败
	ImagePhaseReady     ImagePhase = "Ready"     // 准备就绪, 可以创建虚拟机
)

type ImagePhase string

type ImageStatus struct {
	Size  resource.Quantity `json:"size,omitempty"`
	Phase ImagePhase        `json:"phase,omitempty"`
}

type ListImageOptions struct {
	ListOptions
	Zone          string // 可用区
	Name          string // 镜像名称,准精确匹配
	ResourceGroup string
	Source        ImageSource
}

type FileContent struct {
	FileName      string
	Content       io.ReadCloser
	ContentType   string
	ContentLength int64
}

type ImageOperation interface {
	ListImages(ctx context.Context, options ListImageOptions) (List[Image], error)
	GetImage(ctx context.Context, id string) (*Image, error)
	UpdateImage(ctx context.Context, image *Image) error
	DeleteImage(ctx context.Context, id string) error
	ImportImage(ctx context.Context, image *Image, file FileContent) (*Descripter, error)
}
