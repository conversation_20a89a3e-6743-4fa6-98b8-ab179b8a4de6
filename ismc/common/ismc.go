package common

import (
	"net/url"
	"slices"
	"strconv"
	"strings"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
)

const (
	// LabelIsmcManaged 使用 ISMC 创建的资源，需要增加此标签，以用于快速区分是否为当前管理的资源
	LabelIsmcManaged = "ismc-managed"
)

var ErrUnsupported = errors.NewNotImplemented("unsupported operation")

type Descripter struct {
	ID                string            `json:"id,omitempty"`
	Name              string            `json:"name,omitempty"`
	ResourceGroup     string            `json:"resourceGroup,omitempty"`
	UID               string            `json:"uid,omitempty"`
	CreationTimestamp time.Time         `json:"creationTimestamp,omitempty"`
	DeletionTimestamp *time.Time        `json:"deletionTimestamp,omitempty"`
	Labels            map[string]string `json:"labels,omitempty"`
	Annotations       map[string]string `json:"annotations,omitempty"`
	Description       string            `json:"description,omitempty"`
	Tags              []string          `json:"tags,omitempty"`
}

type Srotable interface {
	GetName() string
	GetCreationTimestamp() time.Time
}

type ID interface {
	GetID() string
}

func (d Descripter) GetName() string {
	return d.Name
}

func (d Descripter) GetID() string {
	return d.ID
}

func (d Descripter) GetCreationTimestamp() time.Time {
	return d.CreationTimestamp
}

type List[T any] struct {
	Items []T `json:"items"`
	Size  int `json:"size,omitempty"`
	Total int `json:"total,omitempty"`
	Page  int `json:"page,omitempty"`
	// continue token
	// 当返回的数据中 continue 不为空时，表示还有数据未返回，且表示使用 continue 方式分页
	// 进行下一页数据请求时，需要将 continue 参数带上
	Continue string `json:"continue,omitempty"`
}

func PageList[T Srotable](items []T, options ListOptions) List[T] {
	if options.Sort == "" {
		options.Sort = "time-"
	}
	return PageListNoDefault(items, options)
}

func PageListNoDefault[T Srotable](items []T, options ListOptions) List[T] {
	return PageFromListOptions(items, options,
		func(t T) string { return t.GetName() },
		func(t T) time.Time { return t.GetCreationTimestamp() })
}

func PageFromListOptions[T any](list []T, opts ListOptions, namefunc func(item T) string, timefunc func(item T) time.Time) List[T] {
	return PageFrom(list, opts.Page, opts.Size, api.SearchNameFunc(opts.Search, namefunc), SortByFunc(opts.Sort, namefunc, timefunc))
}

func SortByFunc[T any](by string, getname func(T) string, gettime func(T) time.Time) func(a, b T) int {
	switch by {
	case "time", "time+":
		if gettime == nil {
			return nil
		}
		return func(a, b T) int {
			if timcmp := gettime(a).Compare(gettime(b)); timcmp == 0 && getname != nil {
				return strings.Compare(getname(a), getname(b))
			} else {
				return timcmp
			}
		}
	case "time-":
		if gettime == nil {
			return nil
		}
		return func(a, b T) int {
			if timcmp := gettime(b).Compare(gettime(a)); timcmp == 0 && getname != nil {
				return strings.Compare(getname(a), getname(b))
			} else {
				return timcmp
			}
		}
	case "name", "name+":
		if getname == nil {
			return nil
		}
		return func(a, b T) int {
			return strings.Compare(getname(a), getname(b))
		}
	case "name-":
		if getname == nil {
			return nil
		}
		return func(a, b T) int {
			return strings.Compare(getname(b), getname(a))
		}
	default:
		// default as is
		return nil
	}
}

func PageFrom[T any](list []T, page, size int, pickfun func(item T) bool, sortfun func(a, b T) int) List[T] {
	// filter
	if pickfun != nil {
		list = slices.DeleteFunc(list, func(item T) bool {
			return !pickfun(item)
		})
	}
	// sort
	if sortfun != nil {
		slices.SortFunc(list, sortfun)
	}
	// page
	if size == 0 {
		return List[T]{Total: len(list), Items: list}
	}
	page = max(1, page)
	total := len(list)
	startIdx := (page - 1) * size
	if startIdx > total {
		return List[T]{Total: total, Items: []T{}}
	}
	endIdx := min(startIdx+size, total)
	return List[T]{
		Total: total,
		Items: list[startIdx:endIdx],
		Page:  page,
		Size:  size,
	}
}

type ListOptions struct {
	Page   int    `json:"page"`
	Size   int    `json:"size"`
	Sort   string `json:"sort"`
	Search string `json:"search"`

	// continue token
	Continue string `json:"continue"`
}

type SortBy struct {
	Field     string        `json:"field,omitempty"`
	Direction SortDirection `json:"direction,omitempty"`
}

type SortDirection string

const (
	SortASC  SortDirection = "asc"
	SortDESC SortDirection = "desc"
)

// ParseSort parses the sort string into a list of SortBy
// eg. "name" -> [{Key: "name", Dir: "asc"}]
// eg. "name-,time" -> [{Key: "name", Dir: "desc"}, {Key: "time", Dir: "asc"}]
func ParseSort(sort string) []SortBy {
	if sort == "" {
		return nil
	}
	sortbys := []SortBy{}
	for _, s := range strings.Split(sort, ",") {
		s = strings.TrimSpace(s)
		if s == "" {
			continue
		}
		dir := SortASC
		if strings.HasSuffix(s, "-") {
			dir = SortDESC
			s = s[:len(s)-1]
		} else if strings.HasSuffix(s, "+") {
			s = s[:len(s)-1]
		}
		sortbys = append(sortbys, SortBy{Field: s, Direction: dir})
	}
	return sortbys
}

func (l ListOptions) Values() url.Values {
	values := make(url.Values)
	values.Set("page", strconv.Itoa(l.Page))
	values.Set("size", strconv.Itoa(l.Size))
	values.Set("search", l.Search)
	values.Set("sort", l.Sort)
	values.Set("continue", l.Continue)
	return values
}

type ResourceType string

const (
	ResourceCPU     ResourceType = "cpu"     // CPU核数
	ResourceMemory  ResourceType = "memory"  // 内存
	ResourceStorage ResourceType = "storage" // 存储
	ResourceDisk    ResourceType = "disk"    // 磁盘数量
	ResourceNetDev  ResourceType = "netdev"  // 网卡数量
	ResourceGPU     ResourceType = "gpu"     // GPU数量
)

type Provider interface {
	ZoneOperation
	ImageOperation
	VirtualNetworkOperation
	SecurityGroupOperation
	InstanceTypeOperation
	VirtualMachineOperation
	VirtualMachineOperationExtend
	VirtualMachineNetworkOperation
	VirtualMachineDiskOperation
	DiskOperation
	DiskClusterOperation
	DiskSnapshotOperation
	DiscoveryOperation
	QuotaOperation
	StatisticsOperation
	ResourceGroupOperation
	MetricsOperations
	TaskOperation
	HostOperation
}
