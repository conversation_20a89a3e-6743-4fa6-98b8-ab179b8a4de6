package common

import (
	"context"
	"time"
)

type Task struct {
	Descripter `json:",inline"`
	// Type 任务类型 create-vm, delete-vm, create-disk, delete-disk
	Type string `json:"type"`
	// ServiceType 任务关联的服务类型
	ServiceType  string `json:"serviceType"`
	ResourceType string `json:"resourceType"`
	Pramas       string `json:"pramas,omitempty"`
	// Status 任务状态
	Status TaskStatus `json:"status"`
}

type TaskResource struct {
	ID       string `json:"id"`       // 资源ID
	Name     string `json:"name"`     // 资源名称
	Resource string `json:"resource"` // 资源类型
}

type TaskPhase string

const (
	TaskPhaseQueued  TaskPhase = "Queued"
	TaskPhaseRunning TaskPhase = "Running"
	TaskPhaseSuccess TaskPhase = "Success"
	TaskPhaseFailed  TaskPhase = "Failed"
	TaskPhaseUnknown TaskPhase = "Unknown"
	TaskPhaseStopped TaskPhase = "Stopped"
)

type TaskStatus struct {
	Phase          TaskPhase      `json:"phase"`    // 任务状态
	Progress       int            `json:"progress"` // 0-100
	StartTimestamp *time.Time     `json:"startTimestamp"`
	EndTimestamp   *time.Time     `json:"endTimestamp"`
	Message        string         `json:"message,omitempty"`   // 任务消息，例如错误信息
	SubTasks       []SubTask      `json:"subTasks,omitempty"`  // 子任务
	Resources      []TaskResource `json:"resources,omitempty"` // 任务关联的资源
}

type SubTask struct {
	ID             string       `json:"id"` // 子任务ID
	StartTimestamp *time.Time   `json:"startTimestamp"`
	EndTimestamp   *time.Time   `json:"endTimestamp"`
	Phase          TaskPhase    `json:"phase"`
	Progress       int          `json:"progress"` // 0-100
	Resources      TaskResource `json:"resources"`
	Message        string       `json:"message,omitempty"`
}

type ListTaskOptions struct {
	ListOptions `json:",inline"`

	// Resource 资源类型，用于过滤任务
	// 例如：disk, virtualmachine
	ResourceType string `json:"resourceType,omitempty"`

	// Status 任务状态
	Phase TaskPhase `json:"phase,omitempty"`
}

type WactchTaskOptions struct {
	// Resource 资源类型，用于过滤任务
	// 例如：disk, virtualmachine
	ResourceType string `json:"resourceType,omitempty"`
	// Size limit initial list size
	Size int `json:"size"`
	// Sort initial list sort
	Sort string `json:"sort,omitempty"`
	// Phase 任务状态
	Phase TaskPhase `json:"phase,omitempty"`
}

type OnTaskEvent func(ctx context.Context, event WatchEvent[Task]) error

type TaskOperation interface {
	GetTask(ctx context.Context, id string) (*Task, error)
	ListTasks(ctx context.Context, opts ListTaskOptions) (List[Task], error)
	WactchTasks(ctx context.Context, on OnTaskEvent, opts WactchTaskOptions) error

	StopTask(ctx context.Context, id string) error
	RestartTask(ctx context.Context, id string) error
	RemoveTask(ctx context.Context, id string) error
}

type TaskEventType string

const (
	WatchEventTypeAdd    TaskEventType = "Add"
	WatchEventTypeUpdate TaskEventType = "Update"
	WatchEventTypeDelete TaskEventType = "Delete"
)

type WatchEvent[T any] struct {
	Type  TaskEventType `json:"type,omitempty"`
	Data  T             `json:"data,omitempty"`
	Error error         `json:"error,omitempty"`
}
