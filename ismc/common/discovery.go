package common

import "context"

type DiscoveryOperation interface {
	GetVersion(ctx context.Context) (*Version, error)
}

type Version struct {
	Vendor VersionInfo `json:"vendor"`
	Agent  VersionInfo `json:"agent"`
}

type VersionInfo struct {
	Name      string `json:"name"`
	Build     string `json:"build"`
	Vendor    string `json:"vendor"`
	Version   string `json:"version"`
	BuildTime string `json:"buildTime"`
}
