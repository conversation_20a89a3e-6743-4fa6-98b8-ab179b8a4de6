package common

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
)

type SystemStatistics struct {
	ZoneCount           int               `json:"zoneCount"`
	HostCount           int               `json:"hostCount"`
	VirtualMachineCount int               `json:"virtualMachineCount"`
	VirtualNetworkCount int               `json:"virtualNetworkCount"`
	DiskClusterCount    int               `json:"diskClusterCount"`
	DiskCount           int               `json:"diskCount"`
	StorageUsed         resource.Quantity `json:"storageUsed"`
	StorageTotal        resource.Quantity `json:"storageTotal"`
}

type StatisticsOperation interface {
	// GetStatistics get the statistics of the object
	GetSystemStatistics(ctx context.Context) (*SystemStatistics, error)
}
