package common

import (
	"context"
)

type Zone struct {
	Descripter `json:",inline"`
	Status     ZoneStatus `json:"status,omitempty"`
}

type ZoneStatus struct {
	// Ready 该区域是否可用
	Ready bool `json:"ready,omitempty"`

	// Addtional represents the additional information of a zone.
	// 在私有云上，可能会有一些额外的信息，比如网络信息等
	// 使用到的物理机器 CPU, 虚拟化版本等信息。
	Addtional map[string]string `json:"addtional,omitempty"`
}

type ListZoneOptions struct {
	ListOptions
}

type ZoneOperation interface {
	ListZones(ctx context.Context, option ListZoneOptions) (List[Zone], error)
	GetZone(ctx context.Context, id string) (*Zone, error)
}

type ResourceGroup struct {
	Descripter `json:",inline"`
	Status     ResourceGroupStatus `json:"status,omitempty"`
}

type ResourceGroupStatus struct {
	Disabled bool `json:"disabled,omitempty"`
}

type ListResourceGroupOptions struct {
	ListOptions `json:",inline"`
	Name        string `json:"name,omitempty"` // 通过名称查询，精确匹配
}

type ResourceGroupOperation interface {
	ListResourceGroups(ctx context.Context, options ListResourceGroupOptions) (List[ResourceGroup], error)
	CreateResourceGroup(ctx context.Context, resourceGroup *ResourceGroup) (*Descripter, error)
	GetResourceGroup(ctx context.Context, id string) (*ResourceGroup, error)
	DeleteResourceGroup(ctx context.Context, id string) error
	UpdateResourceGroup(ctx context.Context, id string, resourceGroup *ResourceGroup) error
}
