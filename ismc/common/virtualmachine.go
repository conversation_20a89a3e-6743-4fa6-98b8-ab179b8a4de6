package common

import (
	"context"
	"net/http"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
)

type Architecture string

const (
	ArchitectureAmd64 Architecture = "amd64" // x86_64 也是 amd64
	ArchitectureX86   Architecture = "x86"   // x86_32
	ArchitectureArm64 Architecture = "arm64" // arm64
	ArchitectureArm   Architecture = "arm"   // arm32
)

type BootMode string

const (
	BootModeBIOS BootMode = "BIOS"
	BootModeUEFI BootMode = "UEFI"
)

type OSType string

const (
	OSLinux   OSType = "Linux"
	OSWindows OSType = "Windows"
	OSDarwin  OSType = "Darwin"
	OSUnknown OSType = "Unknown"
)

type OSStatus string

const (
	OSStatusUnknown OSStatus = "Unknown"
	OSStatusRunning OSStatus = "Running"
)

type VirtualMachine struct {
	Descripter `json:",inline"`

	// Zone 虚拟机所在的可用区
	// 创建虚拟机时，如果不指定虚拟机所在的可用区，由云服务商自动选择
	Zone string `json:"zone,omitempty"`

	// ImageName 镜像ID
	Image VirtualMachineImageRef `json:"image,omitempty"`

	// HostName 主机名
	HostName string `json:"hostName,omitempty"`

	// InstanceType 实例规格ID
	InstanceType VirtualMachineInstanceTypeRef `json:"instanceType,omitempty"`

	// VirtualNetwork 虚拟网络名称
	// 对于使用多个网络的云服务商，可以指定多个网络
	// 若不支持多个网络，可以只使用第一个网络
	// Interfaces 网络接口
	Interfaces []VirtualMachineInterfaceRef `json:"interfaces,omitempty"`

	// SecurityGroups 安全组
	SecurityGroups []VirtualMachineObjectRef `json:"securityGroups,omitempty"`

	// CloudInit cloud-init 配置
	CloudInit CloudInit `json:"cloudInit,omitempty"`

	// Password 虚拟机登录密码
	Password string `json:"password,omitempty"`

	// KeyPairNames 密钥对名称
	// 创建虚拟机时，可以指定密钥对名称，用于登录虚拟机
	// 若不支持多个密钥对，可以只使用第一个密钥对
	KeyPairs []VirtualMachineObjectRef `json:"keyPairs,omitempty"`

	// Disks 磁盘
	// 默认第一个磁盘为系统盘
	Disks []VirtualMachineDiskRef `json:"disks"`

	// Power 虚拟机电源状态
	Power PowerState `json:"power,omitempty"`

	// Features 虚拟机特性
	// 一些平台特性的配置可以放入这里面
	Features map[string]string `json:"features,omitempty"`

	Status VirtualMachineStatus `json:"status"`
}

type VirtualMachineInstanceTypeRef struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type VirtualMachineImageRef struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type VirtualMachineObjectRef = ObjectRef

type ObjectRef struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type VirtualMachineNetworkRef struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
	// SubNetwork 子网ID, 用于指定虚拟机所在的子网，若不指定，则使用默认子网
	SubNetwork string `json:"subNetwork,omitempty"`
	// IPv4 手动指定虚拟机的IPv4地址
	IPv4 string `json:"ipv4,omitempty"`
	// IPv6 手动指定虚拟机的IPv6地址
	IPv6 string `json:"ipv6,omitempty"`
}

type VirtualMachineDiskRef struct {
	Disk `json:",inline"`
	// UseExisting 表示是否使用已有的磁盘
	// 若为 true, 则表示使用已有的磁盘, 此时 Disk 字段中的ID字段必须有值
	UseExisting bool `json:"useExisting,omitempty"`

	// PCIAddress 磁盘PCI地址
	PCIAddress string `json:"pciAddress,omitempty"`

	// BusType 磁盘总线类型
	// SCSI, SATA, NVMe, IDE
	BUSType string `json:"busType,omitempty"`
}

type PowerState string

const (
	PowerStateUnknown   PowerState = "Unknown"
	PowerStateOn        PowerState = "On"
	PowerStateOff       PowerState = "Off"
	PowerStateSuspended PowerState = "Suspended"
	PowerStateReset     PowerState = "Reset"
	PowerStateReboot    PowerState = "Reboot"
	PowerStatePaused    PowerState = "Paused"
	PowerStateCrashed   PowerState = "Crashed"
	PowerStateStarting  PowerState = "Starting"
)

type PowerAction string

const (
	PowerActionOn      PowerAction = "On"
	PowerActionOff     PowerAction = "Off"
	PowerActionReboot  PowerAction = "Reboot"
	PowerActionSuspend PowerAction = "Suspend"
)

type VirtualMachineStatus struct {
	// Phase 虚拟机状态,与云服务商的状态对应
	Phase string `json:"phase,omitempty"`
	// BootTime 虚拟机启动时间,最近一次启动的时间
	BootTime   time.Time                          `json:"bootTime,omitempty"`
	PowerState PowerState                         `json:"powerState,omitempty"`
	Addtional  map[string]string                  `json:"addtional,omitempty"`
	Resources  map[ResourceType]resource.Quantity `json:"resources,omitempty"`
	BootMode   BootMode                           `json:"bootMode,omitempty"`
	Guest      VirtualMachineGuest                `json:"guest"`
	CPU        VirtualMachineCPU                  `json:"cpu,omitempty"`
	// Host 虚拟机所在的主机
	Host string `json:"host,omitempty"`
}

type VirtualMachineCPU struct {
	Cores        int    `json:"cores,omitempty"`
	Sockets      int    `json:"sockets,omitempty"`
	Architecture string `json:"architecture,omitempty"`
	Vendor       string `json:"vendor,omitempty"`
}

type VirtualMachineGuest struct {
	VMTool     VirtualMachineVMTool             `json:"vmTool,omitempty"`
	OSType     OSType                           `json:"osType,omitempty"`
	OSName     string                           `json:"osName,omitempty"`
	OSVersion  string                           `json:"osVersion,omitempty"`
	HostName   string                           `json:"hostName,omitempty"`
	OSStatus   OSStatus                         `json:"osStatus,omitempty"`
	Interfaces []VirtualMachineNetworkInterface `json:"interfaces"`
}

type VirtualMachineVMTool struct {
	// Installed 是否安装虚拟机工具
	Installed bool `json:"installed,omitempty"`
	// Version 虚拟机工具版本
	Version string `json:"version,omitempty"`
}

type VirtualMachineInterfaceRef struct {
	// ID 网卡ID, 创建虚拟机时，由于没有创建网卡所以没有ID
	ID string `json:"id,omitempty"`
	// Network 网络ID
	Network string `json:"network,omitempty"`
	// SubNetwork 子网ID, 用于指定虚拟机所在的子网，若不指定，则使用默认子网
	SubNetwork string `json:"subNetwork,omitempty"`
	// IPv4 手动指定虚拟机的IPv4地址
	IPv4 string `json:"ipv4,omitempty"`
	// IPv6 手动指定虚拟机的IPv6地址
	IPv6 string `json:"ipv6,omitempty"`
}

type VirtualMachineNetworkInterface struct {
	Connected bool                    `json:"connected,omitempty"`
	Type      string                  `json:"type,omitempty"`
	MAC       string                  `json:"mac,omitempty"`
	IPs       []string                `json:"ips,omitempty"`
	Routes    []NetworkInterfaceRoute `json:"routes,omitempty"`
}

type NetworkInterfaceRoute struct {
	Network string `json:"network,omitempty"`
	Gateway string `json:"gateway,omitempty"`
}

type CloudInit struct {
	UserData string `json:"userData,omitempty"`
}

type RemoteConnectionInfo struct {
	URL     string            `json:"url,omitempty"`
	Headers map[string]string `json:"headers,omitempty"`
}

type ListVirtualMachineOptions struct {
	ListOptions   `json:",inline"`
	ResourceGroup string   `json:"omitempty"`
	Zone          string   `json:"zone,omitempty"`
	Name          string   `json:"name,omitempty"` // 虚拟机名称,精确匹配
	Tags          []string `json:"tags,omitempty"` // 虚拟机标签,支持多个标签
}

type CreateVirtualMachineOptions struct {
	Wait bool `json:"wait,omitempty"` // 是否等待虚拟机创建完成
}

type VirtualMachinePowerOptions struct {
	Hard bool `json:"hard,omitempty"` // 是否强制
	Wait bool `json:"wait,omitempty"` // 是否等待
}

type DeleteVirtualMachineOptions struct {
	Wait      bool `json:"wait,omitempty"`      // 是否等待虚拟机删除完成
	WithDisks bool `json:"withDisks,omitempty"` // 是否删除虚拟机使用的磁盘
	Force     bool `json:"force,omitempty"`     // 是否强制删除
}

type VirtualMachineOperation interface {
	ListVirtualMachines(ctx context.Context, options ListVirtualMachineOptions) (List[VirtualMachine], error)
	// CreateVirtualMachine 创建虚拟机
	// 创建虚拟机完成后，需要实现将虚拟机ID回写到 vm 对象中
	CreateVirtualMachine(ctx context.Context, vm *VirtualMachine, options CreateVirtualMachineOptions) (*Descripter, error)
	// UpdateVirtualMachine 更新虚拟机
	// 更新虚拟机时，可以更新虚拟机的配置，实现时，可以对比传入的虚拟机和已有虚拟机，找出需要更新的配置，然后调用云服务商的接口进行更新
	UpdateVirtualMachine(ctx context.Context, vm *VirtualMachine) error
	// GetVirtualMachine 获取虚拟机
	GetVirtualMachine(ctx context.Context, id string) (*VirtualMachine, error)
	// DeleteVirtualMachine 删除虚拟机
	DeleteVirtualMachine(ctx context.Context, id string, options DeleteVirtualMachineOptions) error

	// GetVirtualMachineRemoteConnectionInfo 获取虚拟机的远程连接信息
	// 一般用于获取虚拟机的远程VNC界面地址
	// 对于私有云，远程地址可能用户无法直接访问，需要进行代理，则在上层判断，若返回的url为IP地址，则需要进行代理
	GetVirtualMachineRemoteConnectionInfo(ctx context.Context, id string) (*RemoteConnectionInfo, error)

	// VNCVirtualMachine 代理虚拟机的VNC连接
	VNCVirtualMachine(w http.ResponseWriter, r *http.Request, id string, path string)

	// SetVirtualMachinePower 设置虚拟机的电源状态
	SetVirtualMachinePower(ctx context.Context, id string, power PowerAction, options VirtualMachinePowerOptions) error
}

type ChangeVirtualMachineInstanceTypeOptions struct {
	// InstanceType 实例规格ID
	InstanceType VirtualMachineInstanceTypeRef `json:"instanceType,omitempty"`
	Force        bool                          `json:"force,omitempty"`
	Wait         bool                          `json:"wait,omitempty"`
}

type ReInstallVirtualMachineOptions struct {
	Force     bool                      `json:"force,omitempty"`
	Wait      bool                      `json:"wait,omitempty"`
	Password  string                    `json:"password,omitempty"`
	KeyPairs  []VirtualMachineObjectRef `json:"keyPairs,omitempty"`
	CloudInit CloudInit                 `json:"cloudInit,omitempty"`
}

type ResetVirtualMachinePasswordOptions struct {
	// Password 新密码
	Password string `json:"password,omitempty"`
	// Type 重置密码的方式
	Type string `json:"type,omitempty"`
}

type ResetVirtualMachineCloudInitOptions struct {
	CloudInit CloudInit `json:"cloudInit,omitempty"`
	Wait      bool      `json:"wait,omitempty"`
}

type AttachVirtualMachineDisk struct {
	ID     string `json:"id,omitempty"`
	Device string `json:"device,omitempty"`
}

type AttachVirtualMachineDiskOptions struct {
	Disk AttachVirtualMachineDisk `json:"disk,omitempty"`
	Wait bool                     `json:"wait,omitempty"`
}

type DetachVirtualMachineDiskOptions struct {
	Wait bool `json:"wait,omitempty"`
}

type AttachVirtualMachineNetworkInterfaceOptions struct {
	Interface VirtualMachineInterfaceRef `json:"interface,omitempty"`
	Wait      bool                       `json:"wait,omitempty"`
}

type DetachVirtualMachineNetworkInterfaceOptions struct {
	Wait bool `json:"wait,omitempty"`
}

type ChangeVirtualMachineImageOptions struct {
	Image     VirtualMachineImageRef    `json:"image,omitempty"`
	Force     bool                      `json:"force,omitempty"`
	Password  string                    `json:"password,omitempty"`
	KeyPairs  []VirtualMachineObjectRef `json:"keyPairs,omitempty"`
	Wait      bool                      `json:"wait,omitempty"`
	CloudInit CloudInit                 `json:"cloudInit,omitempty"`
}

type VirtualMachineOperationExtend interface {
	// ReInstallVirtualMachine 重装虚拟机，重新初始化磁盘
	// Deprecated: 请使用对应磁盘的 ReInitalizeDisk 方法
	ReInstallVirtualMachine(ctx context.Context, id string, options ReInstallVirtualMachineOptions) error

	// ChangeVirtualMachineImage 更改虚拟机的镜像，切换虚拟机的操作系统
	ChangeVirtualMachineImage(ctx context.Context, id string, options ChangeVirtualMachineImageOptions) error

	// ResetVirtualMachinePassword 重置虚拟机密码
	ResetVirtualMachinePassword(ctx context.Context, id string, options ResetVirtualMachinePasswordOptions) error

	// ChangeVirtualMachineCloudInit 更改虚拟机的 cloud-init 配置
	ChangeVirtualMachineCloudInit(ctx context.Context, id string, options ResetVirtualMachineCloudInitOptions) error

	// ChangeVirtualMachineInstanceType 更改虚拟机实例规格
	ChangeVirtualMachineInstanceType(ctx context.Context, id string, options ChangeVirtualMachineInstanceTypeOptions) error
}

type ListVirtualMachineDiskOptions struct {
	ListOptions `json:",inline"`
}

type VirtualMachineDiskOperation interface {
	// ListVirtualMachineDisks 查询虚拟机的磁盘
	ListVirtualMachineDisks(ctx context.Context, id string, options ListVirtualMachineDiskOptions) (List[Disk], error)
	// AttachVirtualMachineDisk 挂载磁盘到虚拟机
	AttachVirtualMachineDisk(ctx context.Context, id string, options AttachVirtualMachineDiskOptions) error
	// DetachVirtualMachineDisk 卸载虚拟机的磁盘
	DetachVirtualMachineDisk(ctx context.Context, id string, diskid string, options DetachVirtualMachineDiskOptions) error
}

type ListVirtualMachineNetworkOptions struct {
	ListOptions `json:",inline"`
}

type NetworkInterface struct {
	Descripter        `json:",inline"`
	Zone              string            `json:"zone,omitempty"`
	VirtualNetwork    VirtualNetwork    `json:"virtualNetwork,omitempty"`
	VirtualSubnetwork VirtualSubnetwork `json:"virtualSubnetwork,omitempty"`
	IPv4s             []string          `json:"ipv4,omitempty"`
	IPv6s             []string          `json:"ipv6,omitempty"`
	MAC               string            `json:"mac,omitempty"`
	IsPrimary         bool              `json:"isPrimary,omitempty"`
}

type VirtualMachineNetworkOperation interface {
	// ListVirtualMachineNetworks 查询虚拟机的网络
	ListVirtualMachineNetworkInterfaces(ctx context.Context, id string, options ListVirtualMachineNetworkOptions) (List[NetworkInterface], error)
	// AttachVirtualMachineNetworkInterface 挂载网络接口到虚拟机
	AttachVirtualMachineNetworkInterface(ctx context.Context, id string, options AttachVirtualMachineNetworkInterfaceOptions) error
	// DetachVirtualMachineNetworkInterface 卸载虚拟机的网络接口
	DetachVirtualMachineNetworkInterface(ctx context.Context, id string, nicid string, options DetachVirtualMachineNetworkInterfaceOptions) error
}
