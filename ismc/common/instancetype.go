package common

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
)

// 实例规格
// 实例规格对应到云服务商的实现上是启动虚拟机时的虚拟硬件配置
// 资源规格在各个 zone 上可能不同,
type InstanceType struct {
	Descripter `json:",inline"`
	Zone       string `json:"zone,omitempty"`
	// Resources 是实例规格的资源配置, 如 CPU, 内存, 磁盘, 网卡, GPU 等
	Resources map[ResourceType]resource.Quantity `json:"resources,omitempty"`
	// Architecture 是实例规格的 CPU 架构
	Architecture Architecture `json:"architecture,omitempty"`
	// Features 是实例规格的特性
	Features []string `json:"features,omitempty"`
	// SupportedBootModes 是实例规格支持的启动模式
	SupportedBootModes []BootMode `json:"supportedBootModes,omitempty"`
	// Status 是实例规格的状态
	Status InstanceTypeStatus `json:"status,omitempty"`
}

type InstanceTypeStatus struct {
	// SoldOut 是实例规格是否售罄
	SoldOut bool `json:"soldOut,omitempty"`
}

type ListZoneTypeOptions struct {
	ListOptions
}

type ListInstanceTypeOptions struct {
	ListOptions
	Zone string
	Vm   string
}

type InstanceTypeOperation interface {
	// ListInstanceTypes 列出某个可用去的所有实例规格
	// 用于创建虚拟机时选择实例规格
	ListInstanceTypes(ctx context.Context, options ListInstanceTypeOptions) (List[InstanceType], error)

	GetInstanceType(ctx context.Context, id string) (*InstanceType, error)
	// CreateInstanceType 创建一个新的实例规格
	// 在公有云上, 实例规格是由云服务商提供的, 不支持创建，则可以返回不支持类型的错误
	CreateInstanceType(ctx context.Context, instanceType *InstanceType) (*Descripter, error)
	// UpdateInstanceType 更新一个实例规格
	UpdateInstanceType(ctx context.Context, instanceType *InstanceType) error
	//  DeleteInstanceType 删除一个实例规格
	DeleteInstanceType(ctx context.Context, id string) error
}
