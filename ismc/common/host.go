package common

import (
	"context"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
)

type Host struct {
	Descripter  `json:",inline"`
	Maintenance bool       `json:"maintenance,omitempty"`
	Zone        string     `json:"zone,omitempty"`
	Status      HostStatus `json:"status,omitempty"`
}

type HostStatus struct {
	PowerState PowerState `json:"powerState,omitempty"`
	// Phase is a simple, high-level summary of where the host is in its lifecycle.
	Phase string `json:"phase,omitempty"`
	// Message is a human-readable message indicating details about why the host is in this state.
	Message string `json:"message,omitempty"`

	BootTime *time.Time `json:"bootTime,omitempty"`

	// Capacity represents the total amount of resources that a zone has.
	Capacity map[ResourceType]resource.Quantity `json:"capacity,omitempty"`

	// Used represents the amount of resources that have been used in real.
	Used map[ResourceType]resource.Quantity `json:"used,omitempty"`

	// Addtional represents the additional information of a zone.
	// 在私有云上，可能会有一些额外的信息，比如网络信息等
	// 使用到的物理机器 CPU, 虚拟化版本等信息。
	Addtional map[string]string `json:"addtional,omitempty"`

	// IPs represents the IP addresses of the zone.
	// 表示该节点的管理IP地址
	IPs []string `json:"ips,omitempty"`

	// OS
	OS string `json:"os"`

	// Hardware is the hardware info
	Hardware HardwareInfo `json:"hardware"`

	// Software is the software of vitual machine manager system.
	Software SoftwareInfo `json:"software"`

	// CPU is the cpu info
	CPU CPUInfo `json:"cpu"`
}

type HardwareInfo struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

type CPUInfo struct {
	Model   string `json:"model,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

type SoftwareInfo struct {
	Name    string `json:"name,omitempty"`
	Version string `json:"version,omitempty"`
	Vendor  string `json:"vendor,omitempty"`
}

type ListHostOptions struct {
	ListOptions `json:",inline"`
}

type HostOperation interface {
	ListHosts(ctx context.Context, option ListHostOptions) (List[Host], error)
	GetHost(ctx context.Context, id string) (*Host, error)
	UpdateHost(ctx context.Context, id string, host *Host) error
}
