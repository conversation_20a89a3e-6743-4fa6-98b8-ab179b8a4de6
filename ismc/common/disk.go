package common

import (
	"context"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
)

type DiskCluster struct {
	Descripter `json:",inline"`
	Zone       string            `json:"zone,omitempty"`
	Type       string            `json:"type,omitempty"`
	Status     DiskClusterStatus `json:"status,omitempty"`
}

type DiskClusterPhase string

const (
	DiskClusterPhaseUnknown DiskClusterPhase = "Unknown"
	DiskClusterPhaseReady   DiskClusterPhase = "Ready"
)

type DiskClusterStatus struct {
	Phase DiskClusterPhase  `json:"phase,omitempty"`
	Total resource.Quantity `json:"total,omitempty"`
	Used  resource.Quantity `json:"used,omitempty"`
}

type ListDiskClusterOptions struct {
	ListOptions `json:",inline"`
	Zone        string `json:"zone,omitempty"`
}

type ListDiskClassOptions struct {
	ListOptions   `json:"listOptions,omitempty"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
	// Zone 根据可用区过滤可用的 DiskClass
	Zone string `json:"zone,omitempty"`
}

type DiskClusterOperation interface {
	// ListDiskClusters 查询可用的存储集群
	// 在公有云上，对应私有存储池，若没有私有存储池，则返回空列表
	// 在私有云上，对应存储池，存储池一般是和zone关联的
	ListDiskClusters(ctx context.Context, options ListDiskClusterOptions) (List[DiskCluster], error)
	GetDiskCluster(ctx context.Context, id string) (*DiskCluster, error)
	DeleteDiskCluster(ctx context.Context, id string) error
	// CreateDiskCluster 创建存储集群
	// 在公有云上，此功能不受支持，需要手动去公有云界面上购买
	// 私有云上，创建存储池的参数也不通用，但实现者可以尝试实现。
	CreateDiskCluster(ctx context.Context, cluster *DiskCluster) (*Descripter, error)

	// ListDiskClass 查询支持的存储类型
	// 在公有云中一般只有这个函数可以使用，除非拥有私有存储池
	ListDiskClass(ctx context.Context, options ListDiskClassOptions) (List[DiskClass], error)

	// GetDiskClass 查询存储类型
	GetDiskClass(ctx context.Context, id string) (*DiskClass, error)
}

type DiskClass struct {
	Descripter `json:",inline,omitempty"`
	// 支持的可用区域
	AvailableZones []string `json:"availableZones,omitempty"`

	Public bool `json:"public,omitempty"`
}

type Disk struct {
	Descripter `json:",inline"`
	// Zone 磁盘所在的可用区
	// 若该字段为空，则表示磁盘在所有可用区都可用
	Zone string            `json:"zone,omitempty"`
	Size resource.Quantity `json:"size,omitempty"`
	// DiskClassName 磁盘类型
	// HDD SSD ESSD 等
	// 各个云服务商的磁盘类型不同
	// 创建磁盘时，根据类型分配不同的存储集群，一个类型可能有多个对应的存储集群
	DiskClass string `json:"diskClass,omitempty"`
	// 指定专属（私有）存储池
	// 专属存储池是一种独享的存储资源
	DiskCluster string `json:"diskCluster,omitempty"`

	// DeleteWithInstance 随实例删除
	DeleteWithVirtualMchine bool `json:"deleteWithVirtualMchine,omitempty"`

	// Source 磁盘的来源, 可以是镜像或快照
	From *DiskSource `json:"from,omitempty"`

	// Bootable 是否是启动盘
	Bootable bool `json:"bootable,omitempty"`

	Status DiskStatus `json:"status,omitempty"`
}

type DiskStatus struct {
	// Phase 磁盘的状态,没有固定值,与云服务商有关
	Phase string `json:"phase,omitempty"`
	// Mounts 磁盘使用信息
	Mounts []DiskMountInfo `json:"mounts,omitempty"`
	// Device 磁盘的设备名
	Device                    string            `json:"device,omitempty"`
	Source                    DiskSource        `json:"source,omitempty"`
	Used                      resource.Quantity `json:"used,omitempty"`
	DeleteWithVirtualMchineID string            `json:"deleteWithVirtualMchineID,omitempty"`
}

type DiskSource struct {
	// Image 磁盘的镜像名称
	// 如果是从镜像创建的磁盘，这个字段会有值
	Image string `json:"image,omitempty"`
	// Disk
	// 如果是从磁盘创建的磁盘，这个字段会有值
	Disk string `json:"disk,omitempty"`
	// Snapshot 磁盘的快照名称
	// 如果是从快照创建的磁盘，这个字段会有值
	Snapshot string `json:"snapshot,omitempty"`
}

type DiskMountInfo struct {
	VirtualMachine string     `json:"virtualMachine,omitempty"`
	Device         string     `json:"device,omitempty"`
	MountTime      *time.Time `json:"mountTime,omitempty"`
}

type ListDiskOptions struct {
	ListOptions   `json:",inline"`
	Zone          string `json:"zone,omitempty"`
	Name          string `json:"name,omitempty"` // 磁盘名称,精确匹配
	ResourceGroup string `json:"resourceGroup,omitempty"`
	// VirtualMachine 跟磁盘绑定的虚拟机，或者虚拟机正在使用的磁盘
	VirtualMachine string `json:"virtualMachine,omitempty"`
}

type CreateDiskOptions struct {
	// Wait 是否等待创建完成
	Wait bool `json:"wait,omitempty"`
}

type DeleteDiskOptions struct {
	// Wait 是否等待删除完成
	Wait bool `json:"wait,omitempty"`
}

type ResizeDiskOptions struct {
	Wait bool `json:"wait,omitempty"`
}

type DiskOperation interface {
	ListDisks(ctx context.Context, options ListDiskOptions) (List[Disk], error)
	CreateDisk(ctx context.Context, disk *Disk, options CreateDiskOptions) (*Descripter, error)
	UpdateDisk(ctx context.Context, disk *Disk) error
	GetDisk(ctx context.Context, id string) (*Disk, error)
	DeleteDisk(ctx context.Context, id string, options DeleteDiskOptions) error

	// ResizeDisk 扩容磁盘
	ResizeDisk(ctx context.Context, id string, size resource.Quantity, options ResizeDiskOptions) error
	// ReInitalizeDisk 重新初始化磁盘,当磁盘来自快照/镜像时,可以重新初始化磁盘
	ReInitalizeDisk(ctx context.Context, id string) error
}

type DiskSnapshot struct {
	Descripter   `json:",inline"`
	ZoneName     string             `json:"zoneName,omitempty"`
	SnapshotType string             `json:"snapshotType,omitempty"`
	Source       DiskSnapshotSource `json:"source,omitempty"`
	RetenionDays int                `json:"retentionDays,omitempty"`
	Status       DiskSnapshotStatus `json:"status,omitempty"`
}

const (
	DiskSnapshotPhaseUnknown DiskSnapshotPhase = "Unknown"
	DiskSnapshotPhasePending DiskSnapshotPhase = "Pending"
	DiskSnapshotPhaseFailed  DiskSnapshotPhase = "Failed"
	DiskSnapshotPhaseReady   DiskSnapshotPhase = "Ready"
)

type DiskSnapshotPhase string

type DiskSnapshotStatus struct {
	Phase   DiskSnapshotPhase `json:"phase,omitempty"`
	Message string            `json:"message,omitempty"`
}

type DiskSnapshotSource struct {
	// Image 磁盘的镜像名称
	// 如果是从镜像创建的磁盘，这个字段会有值
	Image string `json:"image,omitempty"`
	// Disk
	// 如果是从磁盘创建的磁盘，这个字段会有值
	Disk string `json:"disk,omitempty"`
	// Snapshot 磁盘的快照名称
	// 如果是从快照创建的磁盘，这个字段会有值
	Snapshot string            `json:"snapshot,omitempty"`
	Size     resource.Quantity `json:"size,omitempty"`
}

type ListDiskSnapshotOption struct {
	ListOptions
	Zone          string
	ResourceGroup string
	// VirtualMachine 查询虚拟机的快照
	VirtualMachine string `json:"virtualMachineName,omitempty"`
	// Disk 查询磁盘的快照
	Disk string `json:"diskName,omitempty"`
}

type DiskSnapshotOperation interface {
	ListDiskSnapshots(ctx context.Context, options ListDiskSnapshotOption) (List[DiskSnapshot], error)
	CreateDiskSnapshot(ctx context.Context, snapshot *DiskSnapshot) error
	RemoveDiskSnapshot(ctx context.Context, id string) error
}
