package common

import (
	"context"

	"k8s.io/apimachinery/pkg/api/resource"
)

type ListVirtualMachineMetricsOptions struct {
	ListOptions   `json:",inline"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
	Zone          string `json:"zone,omitempty"`
}

type MetricsOperations interface {
	ListVirtualMachineMetrics(ctx context.Context, options ListVirtualMachineMetricsOptions) ([]*VirtualMachineMetrics, error)
	ListZoneMetrics(ctx context.Context) ([]*ZoneMetrics, error)

	GetVirtualMachineMetrics(ctx context.Context, id string) (*VirtualMachineMetrics, error)
	GetZoneMetrics(ctx context.Context, id string) (*ZoneMetrics, error)

	ListDiskMetrics(ctx context.Context) ([]*DiskMetrics, error)
	GetDiskMetrics(ctx context.Context, id string) (*DiskMetrics, error)

	ListHostMetrics(ctx context.Context) ([]*HostMetrics, error)
	GetHostMetrics(ctx context.Context, id string) (*HostMetrics, error)
}

type ZoneMetrics struct {
	Labels map[string]string `json:"labels,omitempty"`
	// extra metrics
	Extra []Metrics `json:"extra,omitempty"`
}

type DiskMetrics struct {
	ID            string            `json:"id,omitempty"`
	Name          string            `json:"name,omitempty"`
	ResourceGroup string            `json:"resourceGroup,omitempty"`
	Labels        map[string]string `json:"labels,omitempty"`

	DiskUsedPercent float64 `json:"diskUsedPercent,omitempty"`
	DiskUsedBytes   float64 `json:"diskUsedBytes,omitempty"`
	DiskTotalBytes  float64 `json:"diskTotalBytes,omitempty"`

	DiskReadBytesPerSecond       float64 `json:"diskReadBytesPerSecond,omitempty"`
	DiskWriteBytesPerSecond      float64 `json:"diskWriteBytesPerSecond,omitempty"`
	DiskReadOperationsPerSecond  float64 `json:"diskReadOperationsPerSecond,omitempty"`
	DiskWriteOperationsPerSecond float64 `json:"diskWriteOperationsPerSecond,omitempty"`
}

type VirtualMachineMetrics struct {
	ID            string            `json:"id,omitempty"`
	Name          string            `json:"name,omitempty"`
	ResourceGroup string            `json:"resourceGroup,omitempty"`
	Labels        map[string]string `json:"labels,omitempty"`

	Up     bool `json:"up,omitempty"`
	Health bool `json:"health,omitempty"`

	CPUUsedPercent float64 `json:"cpuUsedPercent"`
	CPUUsedCores   float64 `json:"cpuUsedCores"`
	CPUTotalCores  float64 `json:"cpuTotalCores"`

	MemoryUsedPercent float64 `json:"memoryUsedPercent"`
	MemoryUsedBytes   int64   `json:"memoryUsedBytes"`
	MemoryTotalBytes  int64   `json:"memoryTotalBytes"`

	NetworkTXBytesPerSecond float64 `json:"networkTXBytesPerSecond,omitempty"`
	NetworkRXBytesPerSecond float64 `json:"networkRXBytesPerSecond,omitempty"`

	DiskUsedBytes   int64   `json:"diskUsedBytes,omitempty"`
	DiskUsedPercent float64 `json:"diskUsedPercent,omitempty"`
	DiskTotalBytes  int64   `json:"diskTotalBytes,omitempty"`

	DiskReadBytesPerSecond  float64 `json:"diskReadBytesPerSecond,omitempty"`
	DiskWriteBytesPerSecond float64 `json:"diskWriteBytesPerSecond,omitempty"`

	// extra metrics
	Extra []Metrics `json:"extra,omitempty"`
}

type MetricsType string

const (
	MetricsTypeGauge     MetricsType = "gauge"
	MetricsTypeCounter   MetricsType = "counter"
	MetricsTypeHistogram MetricsType = "histogram"
)

type Metrics struct {
	Name  string      `json:"name,omitempty"`
	Type  MetricsType `json:"type,omitempty"`
	Desc  string      `json:"desc,omitempty"`
	Value any         `json:"value,omitempty"`
}

type ResourceUsage struct {
	ResourceType string
	Used         resource.Quantity
	Total        resource.Quantity
	Usage        float64
	Unit         string
}

type HostMetrics struct {
	// host id
	ID string `json:"id,omitempty"`
	// readble name
	Name   string            `json:"name,omitempty"`
	Labels map[string]string `json:"labels,omitempty"`

	CPUUsedPercent float64 `json:"cpuUsedPercent,omitempty"`
	CPUUsedCores   float64 `json:"cpuUsedCores,omitempty"`
	CPUTotalCores  float64 `json:"cpuTotalCores,omitempty"`

	MemoryUsedPercent float64 `json:"memoryUsedPercent,omitempty"`
	MemoryUsedBytes   int64   `json:"memoryUsedBytes,omitempty"`
	MemoryTotalBytes  int64   `json:"memoryTotalBytes,omitempty"`

	DiskUsedBytes   int64   `json:"diskUsedBytes,omitempty"`
	DiskUsedPercent float64 `json:"diskUsedPercent,omitempty"`
	DiskTotalBytes  int64   `json:"diskTotalBytes,omitempty"`

	DiskReadBytesPerSecond  float64 `json:"diskReadBytesPerSecond,omitempty"`
	DiskWriteBytesPerSecond float64 `json:"diskWriteBytesPerSecond,omitempty"`

	DiskReadOperationsPerSecond  float64 `json:"diskReadOperationsPerSecond,omitempty"`
	DiskWriteOperationsPerSecond float64 `json:"diskWriteOperationsPerSecond,omitempty"`

	NetworkTXBytesPerSecond float64 `json:"networkTXBytesPerSecond,omitempty"`
	NetworkRXBytesPerSecond float64 `json:"networkRXBytesPerSecond,omitempty"`

	Load1  float64 `json:"load1,omitempty"`
	Load5  float64 `json:"load5,omitempty"`
	Load15 float64 `json:"load15,omitempty"`

	// virtual machine allocation
	VCPUUsedCores  float64 `json:"vcpuUsedCores,omitempty"`
	VCPUTotalCores float64 `json:"vcpuTotalCores,omitempty"`

	VMemoryUsedBytes  int64 `json:"vmemoryUsedBytes,omitempty"`
	VMemoryTotalBytes int64 `json:"vmemoryTotalBytes,omitempty"`

	// extra metrics
	Extra []Metrics `json:"extra,omitempty"`
}
