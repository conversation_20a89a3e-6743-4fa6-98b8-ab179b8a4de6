package common

import (
	"context"
)

// VirtualNetwork 虚拟网络,在公有云中也叫 VPC(Virtual Private Cloud)
type VirtualNetwork struct {
	Descripter    `json:",inline"`
	Zone          string               `json:"zone,omitempty"`          // 虚拟网络所在区域
	ResourceGroup string               `json:"resourceGroup,omitempty"` // 虚拟网络所在资源组
	IsDefault     bool                 `json:"isDefault,omitempty"`     // 是否是默认虚拟网络
	IPv4Blocks    []CIDR               `json:"ipv4Blocks,omitempty"`    // IPv4地址段
	IPv6Blocks    []CIDR               `json:"ipv6Blocks,omitempty"`    // IPv6地址段
	Status        VirtualNetworkStatus `json:"status,omitempty"`
}

type CIDR string

type VirtualNetworkPhase string

const (
	VirtualNetworkPhaseUnknown VirtualNetworkPhase = "Unknown"
	VirtualNetworkPhasePending VirtualNetworkPhase = "Pending"
	VirtualNetworkPhaseReady   VirtualNetworkPhase = "Ready"
	VirtualNetworkPhaseError   VirtualNetworkPhase = "Error"
)

type VirtualNetworkStatus struct {
	Phase VirtualNetworkPhase `json:"phase,omitempty"`
}

type VirtualSubnetwork struct {
	Descripter     `json:",inline,omitempty"`
	Zone           string                `json:"zone,omitempty"`
	IsDefault      bool                  `json:"isDefault,omitempty"`
	VirtualNetwork string                `json:"virtualNetwork,omitempty"`
	IPv4Blocks     []string              `json:"ipv4Blocks,omitempty"`
	IPv6Blocks     []string              `json:"ipv6Blocks,omitempty"`
	GateWay        string                `json:"gateWay,omitempty"`
	GateWay6       string                `json:"gateWay6,omitempty"`
	DHCP           VirtualSubnetworkDHCP `json:"dhcp,omitempty"`
	DNSServers     []string              `json:"dnsServers,omitempty"`
	Status         VirtualSwitchStatus   `json:"status,omitempty"`
}

type VirtualSubnetworkDHCP struct {
	Enabled bool `json:"enabled,omitempty"`
}

type VirtualSwitchPhase string

const (
	VirtualSwitchPhaseUnknown VirtualSwitchPhase = "Unknown"
	VirtualSwitchPhasePending VirtualSwitchPhase = "Pending"
	VirtualSwitchPhaseReady   VirtualSwitchPhase = "Ready"
	VirtualSwitchPhaseError   VirtualSwitchPhase = "Error"
)

type VirtualSwitchStatus struct {
	Phase VirtualSwitchPhase `json:"phase,omitempty"`
}

type VirtualSubnetworkAllocation struct {
	Descripter        `json:",inline,omitempty"`
	Zone              string `json:"zone,omitempty"`
	VirtualSubnetwork string `json:"virtualSubnetwork,omitempty"`
	IPv4              string `json:"ipv4,omitempty"`
	IPv6              string `json:"ipv6,omitempty"`
	MAC               string `json:"mac,omitempty"`
	// NetworkInterfaceName 使用该IP的网卡实例名称
	// 在私有云中，该字段可能为空
	NetworkInterfaceName string `json:"networkInterfaceName,omitempty"`
}

type ListVirtualSubnetworkOptions struct {
	ListOptions   `json:",inline"`
	Zone          string `json:"zone,omitempty"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
}

type ListVirtualSwitchAllocationOptions struct {
	ListOptions   `json:",inline,omitempty"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
}

type VirtualNetworkRouteTable struct {
	Descripter `json:",inline"`
	Entry      []VirtualNetworkRouteTableEntry `json:"entry,omitempty"`
}

type VirtualNetworkRouteTableEntry struct {
	// Dest 目的地址
	Dest string `json:"dest,omitempty"`
	// Via 下一跳地址
	Via string `json:"via,omitempty"`
	// Dev 下一条的设备类型
	// 在公有云中，dev 可以是是弹性网卡类型、网卡类型以及其他类型
	Dev string `json:"dev,omitempty"`
}

type ListVirtualNetworkOptions struct {
	ListOptions   `json:",inline"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
}

type VirtualNetworkOperation interface {
	ListVirtualNetworks(ctx context.Context, options ListVirtualNetworkOptions) (List[VirtualNetwork], error)
	CreateVirtualNetwork(ctx context.Context, network *VirtualNetwork) (*Descripter, error)

	// UpdateVirtualNetwork 更新虚拟网络
	// 在阿里云，更新虚拟机网络仅支持：附加IPv4地址段、删除IPv4地址段、附加IPv6地址段、删除IPv6地址段
	// 在实现时，可以对比传入的网络和已有网络，找出需要更新的地址段，然后调用阿里云的接口进行更新
	UpdateVirtualNetwork(ctx context.Context, network *VirtualNetwork) error
	GetVirtualNetwork(ctx context.Context, name string) (*VirtualNetwork, error)
	DeleteVirtualNetwork(ctx context.Context, name string) error

	ListVirtualSubnetwork(ctx context.Context, network string, options ListVirtualSubnetworkOptions) (List[VirtualSubnetwork], error)
	GetVirtualSubnetwork(ctx context.Context, network, id string) (*VirtualSubnetwork, error)
	CreateVirtualSubnetwork(ctx context.Context, network string, subnetwork *VirtualSubnetwork) (*Descripter, error)
	UpdateVirtualSubnetwork(ctx context.Context, network string, subnetwork *VirtualSubnetwork) error
	DeleteVirtualSubnetwork(ctx context.Context, network string, id string) error

	// GetVirtualSwitchesDetails 获取虚拟交换机的 IP 分配情况
	ListVirtualSubnetworkAllocations(ctx context.Context, network, subnetwork string,
		options ListVirtualSwitchAllocationOptions) (List[VirtualSubnetworkAllocation], error)
}
type SecurityGroup struct {
	Descripter `json:",inline"`

	// VirtualNetwork 虚拟网络名称
	// 不指定时，对于云服务商为默认虚拟网络
	VirtualNetwork string `json:"virtualNetwork,omitempty"`
}

type TrafficDirrection string

const (
	TrafficDirrectionIngress TrafficDirrection = "Ingress"
	TrafficDirrectionEgress  TrafficDirrection = "Egress"
)

type IPProtocol string

const (
	TCP  IPProtocol = "TCP"
	UDP  IPProtocol = "UDP"
	ICMP IPProtocol = "ICMP"
)

type TrafficPolicy string

const (
	TrafficPolicyAllow TrafficPolicy = "Allow"
	TrafficPolicyDeny  TrafficPolicy = "Deny"
	TrafficPolicyDrop  TrafficPolicy = "Drop"
)

type PortRange struct {
	Min int32 `json:"min,omitempty"`
	Max int32 `json:"max,omitempty"`
}

type SecurityGroupRule struct {
	Descripter `json:",inline"`
	// SecurityGroup 安全组ID
	SecurityGroup string `json:"securityGroup,omitempty"`
	// Direction 出入控制方向
	Direction TrafficDirrection `json:"direction,omitempty"`
	// Famlily IP地址族, 默认为 IPv4
	Famlily IPFamily `json:"family,omitempty"`
	Src     CIDR     `json:"src,omitempty"`
	// SrcPort 源端口范围
	// 为空表示支持所有端口
	SrcPort  PortRange `json:"srcPorts,omitempty"`
	Dest     CIDR      `json:"dest,omitempty"`
	DestPort PortRange `json:"destPorts,omitempty"`
	// Protocol IP协议类型
	// 为空表示支持所有协议
	Protocol IPProtocol `json:"protocol,omitempty"`
	Priority string     `json:"priority,omitempty"`
	// Policy 安全组规则策略，默认为 Allow
	Policy TrafficPolicy `json:"policy,omitempty"`
}

type IPFamily string

const (
	IPv4 IPFamily = "IPv4"
	IPv6 IPFamily = "IPv6"
)

type ListSecurityGroupOptions struct {
	ListOptions   `json:",inline"`
	ResourceGroup string `json:"resourceGroup,omitempty"`
	Network       string `json:"network,omitempty"`
}

type ListSecurityGroupRuleOptions struct {
	ListOptions `json:",inline"`
}

type SecurityGroupOperation interface {
	ListSecurityGroups(ctx context.Context, options ListSecurityGroupOptions) (List[SecurityGroup], error)
	GetSecurityGroup(ctx context.Context, id string) (*SecurityGroup, error)
	CreateSecurityGroup(ctx context.Context, sg *SecurityGroup) (*Descripter, error)
	UpdateSecurityGroup(ctx context.Context, sg *SecurityGroup) error
	DeleteSecurityGroup(ctx context.Context, id string) error

	ListSecurityGroupRules(ctx context.Context, securityGroup string, options ListSecurityGroupRuleOptions) (List[SecurityGroupRule], error)
	AddSecurityGroupRule(ctx context.Context, securityGroup string, rule *SecurityGroupRule) error
	UpdateSecurityGroupRule(ctx context.Context, securityGroup string, rule *SecurityGroupRule) error
	DeleteSecurityGroupRule(ctx context.Context, securityGroup, id string) error
}
