package loadbalancer

import (
	"context"
	"encoding/json"
	"io"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/store"
)

type Options struct {
	Server       string `json:"server,omitempty" description:"The server address of the loadbalancer."`
	PortPoolName string `json:"portPoolName,omitempty" description:"The name of the port pool."`
}

func NewDefaultOptions() *Options {
	return &Options{
		Server:       "http://loadbalancer-controller.bob",
		PortPoolName: "",
	}
}

func NewDefaultLoadbalancer(options *Options) (*DefaultLoadbalancer, error) {
	client,err := httpclient.NewClient(options.Server)
	if err != nil {
		return nil, err
	}
	client.OnResponse = StatusOnResponse
	return &DefaultLoadbalancer{Client: client, Options: options}, nil
}

func StatusOnResponse(req *http.Request, resp *http.Response) error {
	if resp.StatusCode < http.StatusBadRequest {
		return nil
	}
	bytes, _ := io.ReadAll(resp.Body)
	statuserr := &errors.Status{}
	if err := json.Unmarshal(bytes, statuserr); err == nil {
		return statuserr
	}
	return errors.NewBadRequest(string(bytes))
}

var _ Loadbalancer = &DefaultLoadbalancer{}

type DefaultLoadbalancer struct {
	Client  *httpclient.Client
	Options *Options
}

// Ensure implements Loadbalancer.
func (d *DefaultLoadbalancer) Ensure(ctx context.Context, lb *LoadbalancerRule) error {
	exists := &store.Unstructured{}
	if err := d.Client.Get("/v1/portforwards/" + lb.Name).Return(exists).Send(ctx); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
		data := map[string]any{
			"name":     lb.Name,
			"pool":     map[string]any{"name": d.Options.PortPoolName},
			"backends": lb.Backends,
		}
		if err := d.Client.Post("/v1/portforwards").JSON(data).Return(lb).Send(ctx); err != nil {
			return err
		}
		return nil
	}
	exists.Object["backends"] = lb.Backends
	exists.Object["pool"] = map[string]any{"name": d.Options.PortPoolName}
	return d.Client.Put("/v1/portforwards/" + lb.Name).JSON(exists).Return(lb).Send(ctx)
}

// List implements Loadbalancer.
func (d *DefaultLoadbalancer) List(ctx context.Context) ([]LoadbalancerRule, error) {
	list := store.List[LoadbalancerRule]{}
	if er := d.Client.Get("/v1/portforwards").Return(&list).Send(ctx); er != nil {
		return nil, er
	}
	return list.Items, nil
}

// Remove implements Loadbalancer.
func (d *DefaultLoadbalancer) Remove(ctx context.Context, lb *LoadbalancerRule) error {
	if err := d.Client.Delete("/v1/portforwards/" + lb.Name).Return(lb).Send(ctx); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	return nil
}
