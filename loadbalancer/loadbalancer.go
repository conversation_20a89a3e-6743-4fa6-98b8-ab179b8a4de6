package loadbalancer

import (
	"context"

	"xiaoshiai.cn/common/store"
)

type LoadbalancerRule struct {
	Name     string                 `json:"name,omitempty"`
	Cluster  store.ObjectReference  `json:"cluster,omitempty"`
	Pool     store.ObjectReference  `json:"pool,omitempty"`
	Backends []LoadbalancerBackend  `json:"backends,omitempty"`
	Status   LoadbalancerRuleStatus `json:"status,omitempty"`
}

type LoadbalancerRuleStatus struct {
	Endpoints []Endpoint `json:"endpoints,omitempty"`
}

type Endpoint struct {
	IPs      []string `json:"ips,omitempty"`
	DNS      string   `json:"dns,omitempty"`
	Port     int      `json:"port,omitempty"`
	Protocol string   `json:"protocol,omitempty"`
}

type LoadbalancerBackend struct {
	IP       string `json:"ip,omitempty"`
	Port     uint32 `json:"port,omitempty"`
	Protocol string `json:"protocol,omitempty"`
}

type Loadbalancer interface {
	List(ctx context.Context) ([]LoadbalancerRule, error)
	Ensure(ctx context.Context, lb *LoadbalancerRule) error
	Remove(ctx context.Context, lb *LoadbalancerRule) error
}
