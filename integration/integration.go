package integration

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/user"
)

type IntergrationOptions struct {
	TokenVerifyAddress string `json:"tokenVerifyAddress,omitempty"`
}

func NewDefaultIntergrationOptions() *IntergrationOptions {
	return &IntergrationOptions{
		TokenVerifyAddress: "https://example.com",
	}
}

type API struct {
	RemoteUser *RemoteUser
}

func NewAPI(base base.API, session user.SessionManager, users auth.Provider, opts *IntergrationOptions) *API {
	u := &RemoteUser{
		remote:  MustNewRemoteBOB(opts.TokenVerifyAddress),
		session: session,
		users:   users,
		API:     base,
	}
	return &API{RemoteUser: u}
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("/integration").
		Tag("Integration").
		SubGroup(
			a.RemoteUser.loginGroup(),
		)
}
