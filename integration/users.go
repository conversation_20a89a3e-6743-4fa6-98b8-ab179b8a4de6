package integration

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/auth"
	"xiaoshiai.cn/core/authdevice"
	"xiaoshiai.cn/core/authn"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/global"
	"xiaoshiai.cn/core/rbac"
	"xiaoshiai.cn/core/user"
)

type UserInfo struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Tenant   string `json:"tenant"`
	Role     string `json:"role"`
}

type RemoteLoginProvider interface {
	GetUserInfo(ctx context.Context, token string) (*UserInfo, error)
}

func NewRemoteBOB(addr string) *RemoteBob {
	cli := &httpclient.Client{Server: addr}
	if proxyaddr := os.Getenv("REMOTE_LOGIN_PROXY"); proxyaddr != "" {
		u, err := url.Parse(proxyaddr)
		if err != nil {
			panic(err)
		}
		log.Info("use remote login proxy", "proxy", u.String())
		tp := &http.Transport{Proxy: http.ProxyURL(u)}
		cli.Client = &http.Client{Transport: tp}
	}
	return &RemoteBob{remote: cli}
}

type RemoteBob struct {
	remote *httpclient.Client
}

func (i *RemoteBob) GetUserInfo(ctx context.Context, token string) (*UserInfo, error) {
	// call remote system to get user info
	type remoteUserinfo struct {
		UserId     string `json:"userId"`     // identified
		Username   string `json:"username"`   // identified unser tenant
		TenantNo   string `json:"tenantNo"`   // identified
		TenantName string `json:"tenantName"` // not identified
		Email      string `json:"email"`
		Role       string `json:"role"`
	}
	type responseWraper struct {
		Message string         `json:"message"`
		Success bool           `json:"success"`
		Data    remoteUserinfo `json:"data"`
	}
	data := responseWraper{}
	err := i.remote.
		Get("/oauth2/customAuth/userInfo").
		Query("idToken", token).
		Header("Isoft-Ca-Key", "ISMC").
		Header("Isoft-Ca-Timestamp", strconv.FormatInt(time.Now().UnixMilli(), 10)).
		Return(&data).
		Send(ctx)
	if err != nil {
		return nil, err
	}
	if !data.Success {
		return nil, errors.NewBadRequest(fmt.Sprintf("remote system not success: %s", data.Message))
	}
	ui := &UserInfo{
		// user is under tenant
		ID: data.Data.UserId,
		// username is unique under tenant only
		Username: func() string {
			username := data.Data.Username
			if data.Data.TenantNo != "" {
				username = data.Data.TenantNo + "-" + username
			}
			return username
		}(),
		Email:  data.Data.Email,
		Tenant: data.Data.TenantNo,
		Role: func() string {
			userrole := data.Data.Role
			roleMap := map[string]string{
				"platform-admin": rbac.RoleAdmin,
				"viewer":         rbac.RoleMember,
			}
			if role, ok := roleMap[userrole]; ok {
				userrole = role
			}
			if userrole == "" {
				return rbac.RoleMember
			}
			return userrole
		}(),
	}
	return ui, nil
}

// RemoteUser acccept idtoken from another system, and auto create user in this system
type RemoteUser struct {
	remote  RemoteLoginProvider
	session user.SessionManager
	users   auth.Provider
	base.API
}

func (i *RemoteUser) RemoteLogin(w http.ResponseWriter, r *http.Request) {
	i.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		idtoken := api.Query(r, "idToken", "")
		if idtoken == "" {
			return nil, errors.NewBadRequest("idToken is required")
		}
		userinfo, err := i.remote.GetUserInfo(ctx, idtoken)
		if err != nil {
			return nil, err
		}
		// ensure user
		if global.FeatureUseBuiltAuthProvider {
			u := &user.User{
				ObjectMeta: store.ObjectMeta{
					Name: userinfo.Username,
				},
				Email: userinfo.Email,
			}
			if err := CreateUserIfNotExists(ctx, storage, u); err != nil {
				return nil, err
			}
		} else {
			profile, err := i.users.GetUser(ctx, userinfo.Username)
			if err != nil {
				if !errors.IsNotFound(err) {
					return nil, err
				}
				// create user
				profile = &auth.UserProfile{}
				profile.Name = userinfo.Username
				profile.Email = userinfo.Email
				if err := i.users.CreateUser(ctx, profile, ""); err != nil {
					return nil, err
				}
			}
		}
		// ensure role
		scopedStorage := storage
		if userinfo.Tenant != "" {
			scopedStorage = storage.Scope(base.ScopeTenant(userinfo.Tenant))
		}
		if userinfo.Role != "" {
			if err := rbac.SetUserRole(ctx, scopedStorage, userinfo.Username, userinfo.Role); err != nil {
				return nil, err
			}
		}
		// login
		if global.FeatureUseBuiltAuthProvider {
			sessionopt := user.OpenSessionOptions{
				Device: authdevice.UserDeviceFromContext(ctx),
			}
			info := api.UserInfo{
				ID:            userinfo.ID,
				Name:          userinfo.Username,
				Email:         userinfo.Email,
				EmailVerified: true,
			}
			session, err := i.session.OpenSession(ctx, info, sessionopt)
			if err != nil {
				return nil, err
			}
			tokenresp := user.SessionTokenResponse(session)
			return tokenresp, nil
		} else {
			session, err := i.users.LoginAsUser(ctx, userinfo.Username)
			if err != nil {
				return nil, err
			}
			api.SetCookie(w, auth.SessionCookieKey, session.Value, session.Expires)
			tokenresp := authn.TokenResponse{
				AcessToken: session.Value,
				ExpiresIn:  int(session.Expires.Sub(time.Now()).Seconds()),
				TokenType:  "Bearer",
				IDToken:    session.Value,
			}
			return RemoteLoginResponse{TokenResponse: tokenresp, Tenant: userinfo.Tenant}, nil
		}
	})
}

type RemoteLoginResponse struct {
	authn.TokenResponse `json:",inline"`
	Tenant              string `json:"tenant"`
}

func CreateUserIfNotExists(ctx context.Context, storage store.Store, u *user.User) error {
	if u.Name == "" {
		return errors.NewBadRequest("User name is required")
	}
	if err := storage.Get(ctx, u.Name, u); err != nil {
		if errors.IsNotFound(err) {
			if _, err := user.CreateUserWithPassword(ctx, storage, *u, ""); err != nil {
				return err
			}
			return nil
		}
		return err
	}
	return nil
}

func (i *RemoteUser) loginGroup() api.Group {
	return api.
		NewGroup("/remote-login").
		Route(
			api.GET("").
				Operation("RemoteLogin").
				Doc("Login via idtoken from another system").
				Param(
					api.QueryParam("idToken", "IDToken from another system"),
				).
				To(i.RemoteLogin),
		)
}
