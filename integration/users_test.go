package integration

import (
	"context"
	"os"
	"testing"
)

func init() {
	os.Setenv("HTTPS_PROXY", "http://192.168.0.138:8080")
}

func TestNewRemoteBOB(t *testing.T) {
	addr := "https://bob-test.majnoon-ifms.com/api"
	remote := MustNewRemoteBOB(addr)

	token := "ed8f4fac-61c6-4313-82a9-a8a2c41e4e171726737683083"
	// token := "wt_b46361e1-5115-42b6-b673-3539408428a81726738694459"
	info, err := remote.GetUserInfo(context.Background(), token)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetUserInfo failed: %v", err)
	}
	if info == nil {
		t.<PERSON><PERSON><PERSON>("GetUserInfo failed: nil")
	}
	t.Log(info)
}
