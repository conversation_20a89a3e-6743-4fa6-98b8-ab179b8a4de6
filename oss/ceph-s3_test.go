package oss

import (
	"bytes"
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/ceph/go-ceph/rgw/admin"
	iampolicy "github.com/minio/pkg/v3/policy"
	"github.com/minio/pkg/v3/policy/condition"
	libs3 "xiaoshiai.cn/common/s3"
)

var (
	testAddress   = "http://************:7480"
	testAccessKey = ""
	testSecretKey = ""
	ctx           = context.Background()
)

func SetupAdminClient(t *testing.T) *CephS3ProviderClient {
	cli, err := NewCephS3ProviderClient(ctx, testAddress, testAccessKey, testSecretKey)
	if err != nil {
		t.Fatalf("NewCephS3ProviderClient() error = %v", err)
	}
	return cli
}
func setupS3Client(t *testing.T) *libs3.Client {
	s3cli, err := libs3.NewClient(ctx, &libs3.Options{
		AccessKey: testAccessKey,
		SecretKey: testSecretKey,
		PathStyle: true,
		Address:   testAddress,
		Region:    "default",
	})
	if err != nil {
		t.Fatal(err)
	}
	return s3cli
}

// 批量创建指定数量的对象
func TestBatchCreateObjects(t *testing.T) {
	s3cli := setupS3Client(t)
	bucketName := "aaabbb"
	// 创建桶
	// _, err := s3cli.CreateBucket(ctx, &s3.CreateBucketInput{
	// 	Bucket: aws.String(bucketName),
	// })
	// if err != nil {
	// 	t.Fatal(err)
	// }
	var (
		concurrentTasks = 100
		objectCount     = 10000
	)
	// 创建对象的内容
	content := []byte("This is a sample object content")
	// 并发上传
	var wg sync.WaitGroup
	taskChan := make(chan struct{}, concurrentTasks)
	for i := 0; i < objectCount; i++ {
		wg.Add(1)
		taskChan <- struct{}{} // 控制并发数
		go func(objectID int) {
			defer wg.Done()
			defer func() { <-taskChan }() // 释放并发槽

			objectKey := fmt.Sprintf("object-%d.test", objectID)
			_, err := s3cli.PutObject(ctx, &s3.PutObjectInput{
				Bucket: aws.String(bucketName),
				Key:    aws.String(objectKey),
				Body:   bytes.NewReader(content),
			})
			if err != nil {
				fmt.Printf("Failed to upload object %s: %v", objectKey, err)
				return
			}
		}(i)
	}
	// 等待所有任务完成
	wg.Wait()
	t.Fatalf("Successfully created %d objects in bucket %s", objectCount, bucketName)
}

// 列举桶的残留对象
func TestListResidueObjects(t *testing.T) {
	// adminCli := setupAdminClient(t)
	// bucketInfo, err := adminCli.Admin.GetBucketInfo(ctx, admin.Bucket{
	// 	Bucket: "abcde",
	// })
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// // 1. 获取桶的ID和shard bucketID: bd03b7ea-a1f6-4a04-9fba-9fe283a4f7e9.134171.11  num_shards: 11
	// bucketID := bucketInfo.ID
	// shards := *bucketInfo.NumShards

	// 2.默认池是 default.rgw.buckets.index

	// todo: 启动一个ceph agent 暴露http接口

	// 3. rados -p default.rgw.buckets.index listomapkeys .dir.bucketID.shardID 此处 0<=shardID<11

	// 4. 过滤3中返回的key,只要是_multipart_开头的key,就返回

	// 5. 可以删除对应的key, rados -p default.rgw.buckets.index rmomapkey .dir.bd03b7ea-a1f6-4a04-9fba-9fe283a4f7e9.134171.11.8 _multipart_gitlab197g.tgz.2~GWliErmAjzEBjXlhuwLYE8boFDyidNk.37

	// 6. 调用GetBucketIndex获取bucket index

	// 7.删除完毕之后需要radosgw-admin bucket check --bucket=three --fix

}

func TestAbortMultipartUpload(t *testing.T) {
	var (
		bucket = "ttt2"
	)
	s3cli := setupS3Client(t)
	multiparts, err := s3cli.ListMultipartUploads(ctx, &s3.ListMultipartUploadsInput{
		Bucket:     aws.String(bucket),
		MaxUploads: aws.Int32(1000),
	})
	if err != nil {
		t.Fatal(err)
	}
	var uploadIDs []MultipartInfo
	//t.Log("-------", *multiparts.UploadIdMarker)
	for _, part := range multiparts.Uploads {
		t.Log("key:", *part.Key)
		t.Log("UploadID:", *part.UploadId)
		t.Log("Initiated:", part.Initiated.Format(time.RFC3339))
		uploadIDs = append(uploadIDs, MultipartInfo{
			Key:      *part.Key,
			UploadID: *part.UploadId,
		})
	}
	t.Log("------")
	for _, uploadID := range uploadIDs {
		_, err := s3cli.AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
			Bucket:   aws.String(bucket),
			Key:      aws.String(uploadID.Key),
			UploadId: aws.String(uploadID.UploadID),
		})
		if err != nil {
			t.Fatal(err)
		}
	}
	t.Log("success")
}

type MultipartInfo struct {
	Key      string
	UploadID string
}

/*
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": "*",
            "Action": "s3:GetObject",
            "Resource": "arn:aws:s3:::my-public-bucket/*"
        }
    ]
}
*/

// 公开桶如果是在租户下的话，需要加上租户ID，像这样/tenant/bucket
// 公开桶如果不在租户下，就直接桶名称了
func TestGeneratePublicBucketPolicy(t *testing.T) {
	var bucketName = "zkkkkkkk"
	policy := &iampolicy.BucketPolicy{
		Version:    iampolicy.DefaultVersion,
		Statements: make([]iampolicy.BPStatement, 0),
	}
	statement := iampolicy.NewBPStatement(
		"public",
		iampolicy.Allow,
		iampolicy.NewPrincipal("*"),
		iampolicy.NewActionSet(iampolicy.GetObjectAction, iampolicy.ListBucketAction),
		iampolicy.NewResourceSet([]iampolicy.Resource{
			iampolicy.NewResource(fmt.Sprintf("%s/*", bucketName)),
			iampolicy.NewResource(bucketName),
		}...),
		condition.NewFunctions(),
	)
	policy.Statements = append(policy.Statements, statement)
	currentPolicyData, err := policy.MarshalJSON()
	if err != nil {
		t.Fatal(err)
	}
	s3Cli := setupS3Client(t)
	_, err = s3Cli.PutBucketPolicy(ctx, &s3.PutBucketPolicyInput{
		Bucket: aws.String(bucketName),
		Policy: aws.String(string(currentPolicyData)),
	})
	if err != nil {
		t.Fatal("bucket policy create err:", err)
	}
	t.Log("success")
}

func TestGetBucketPublicPolicy(t *testing.T) {
	bucketName := "zkkkkkkk"
	s3cli := setupS3Client(t)
	policyData, err := s3cli.GetBucketPolicy(ctx, &s3.GetBucketPolicyInput{Bucket: aws.String(bucketName)})
	if err != nil {
		t.Fatal(err)
	}
	existPolicy, err := iampolicy.ParseBucketPolicyConfig(strings.NewReader(*policyData.Policy), bucketName)
	if err != nil {
		t.Fatal(err)
	}
	for _, policy := range existPolicy.Statements {
		t.Log("policy:", policy)
	}

}

func TestGetAllUsage(t *testing.T) {
	cli := SetupAdminClient(t)
	uid := cli.UID
	if cli.TenantID != "" {
		uid = fmt.Sprintf("%s$%s", cli.TenantID, cli.UID)
	}
	now := time.Now()
	start := now.AddDate(0, -1, 0).Format(time.DateTime)
	usage, err := cli.Admin.GetUsage(ctx, admin.Usage{
		Start:  start,
		End:    now.Format(time.DateTime),
		UserID: uid,
	})
	if err != nil {
		t.Fatal(err)
	}
	//t.Log(usage.Entries)

	for _, entry := range usage.Entries {
		for _, bucket := range entry.Buckets {
			t.Log(bucket)
			t.Log("----")
		}
	}
}

func TestNewCephS3ProviderClient(t *testing.T) {
	cli := SetupAdminClient(t)
	info, err := cli.Admin.GetBucketInfo(ctx, admin.Bucket{
		Bucket: "backup",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(*info.Usage.RgwMain.NumObjects)
	t.Log(*info.Usage.RgwMain.Size)
	t.Log(*info.BucketQuota.MaxSize)

	// uid := cli.UID
	// if cli.TenantID != "" {
	// 	uid = fmt.Sprintf("%s$%s", cli.TenantID, cli.UID)
	// }
	// //2012-09-25 16:00:00
	// now := time.Now()
	// start := now.AddDate(0, -1, 0).Format(time.DateTime)
	// usage, err := cli.Admin.GetUsage(ctx, admin.Usage{
	// 	Start:       start,
	// 	End:         now.Format(time.DateTime),
	// 	UserID:      uid,
	// 	ShowEntries: aws.Bool(true),
	// })
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// t.Log(usage.Entries)
	// for _, sum := range usage.Summary {
	// 	info := ObjectStorageInformationSummary{
	// 		Total: ObjectStorageInformationTotal{
	// 			BytesSent:     sum.Total.BytesSent,
	// 			BytesReceived: sum.Total.BytesReceived,
	// 			Ops:           sum.Total.Ops,
	// 			SuccessfulOps: sum.Total.SuccessfulOps,
	// 		},
	// 	}
	// 	for _, ca := range sum.Categories {
	// 		info.Categories = append(info.Categories, ObjectStorageInformationCategory{
	// 			Category:      ca.Category,
	// 			BytesSent:     ca.BytesSent,
	// 			BytesReceived: ca.BytesReceived,
	// 			Ops:           ca.Ops,
	// 			SuccessfulOps: ca.SuccessfulOps,
	// 		})
	// 	}
	// 	t.Log(info)
	// 	t.Log("------------")
	// 	//osi.Summaries = append(osi.Summaries, info)
	// }
}

func TestGetBucketPolicy(t *testing.T) {
	ctx := context.Background()
	s3cli, err := libs3.NewClient(ctx, &libs3.Options{
		AccessKey: "",
		SecretKey: "",
		PathStyle: true,
		Address:   "http://************:7480",
		Region:    "default",
	})
	if err != nil {
		t.Fatal(err)
	}
	// _, err = s3cli.CreateBucket(ctx, &s3.CreateBucketInput{Bucket: aws.String("test-zk")})
	// if err != nil {
	// 	t.Fatal(err)
	// }
	policyData, err := s3cli.GetBucketPolicy(ctx, &s3.GetBucketPolicyInput{Bucket: aws.String("test")})
	if err != nil {
		if IsS3StorageNotFound(err) {
			t.Log("not found err:", err)
			return
		}
		t.Fatal("other err:", err)
	}
	t.Log(*policyData.Policy)
	// permissions := []ObjectStorageCredentialReference{
	// 	{
	// 		Permission: PermissionsManage,
	// 		UserID:     "ttttt-manager",
	// 	},
	// }
	// newPolicy := GenerateBucketPolicyFromPermission("", "ttttt", permissions)
	// data, _ := newPolicy.MarshalJSON()
	// _, err = s3cli.PutBucketPolicy(ctx, &s3.PutBucketPolicyInput{
	// 	Bucket: aws.String("ttttt"),
	// 	Policy: aws.String(string(data)),
	// })
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// t.Log("success")

	// policy, err := iampolicy.ParseBucketPolicyConfig(strings.NewReader(*policyData.Policy), "ttttt")
	// if err != nil {
	// 	t.Fatal(err)
	// }
	// t.Log(policy)

	// s3cli.PutBucketPolicy(ctx, &s3.PutBucketPolicyInput{
	// 	Bucket: aws.String("ttttt"),
	// 	Policy: aws.String(""),
	// })

	// permissions := []ObjectStorageCredentialReference{
	// 	{
	// 		Permission: PermissionsManage,
	// 		UserID:     "ttttt-manager",
	// 	},
	// }
	// newPolicy := GenerateBucketPolicyFromPermission("", "ttttt", permissions)

	// if policy.Equals(*newPolicy) {
	// 	t.Log("policy equal")
	// 	return
	// }
	// t.Fatal("policy not equal")

	// for _, p := range policy.Statements {
	// 	pri := p.Principal.AWS
	// 	for k := range pri {
	// 		tenant, user, err := findTenantAndUser(k)
	// 		if err != nil {
	// 			t.Fatal(err)
	// 		}
	// 		if tenant == "" {
	// 			t.Log("User:", user)
	// 		} else {
	// 			t.Log("Tenant:", tenant, "User:", user)
	// 		}

	// 	}
	// }
}

func TestGenerateBucketPolicyFromPermission(t *testing.T) {
	permissions := []ObjectStorageCredentialReference{}
	newPolicy := GenerateBucketPolicyFromPermission("", "ttttt", permissions, true)
	currentPolicyData, err := newPolicy.MarshalJSON()
	if err != nil {
		t.Fatal(err)
	}
	t.Log(string(currentPolicyData))
}

func listObjectsByPrefix(ctx context.Context, s3Client *libs3.Client, bucket, prefix string, maxKeys int32) ([]types.Object, error) {
	input := &s3.ListObjectsV2Input{
		Bucket:  aws.String(bucket),
		Prefix:  aws.String(prefix), // 使用前缀过滤
		MaxKeys: aws.Int32(maxKeys), // 限制返回数量
	}

	var allObjects []types.Object
	paginator := s3.NewListObjectsV2Paginator(s3Client, input)

	// 使用分页器处理所有结果
	for paginator.HasMorePages() {
		output, err := paginator.NextPage(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to list objects: %w", err)
		}
		allObjects = append(allObjects, output.Contents...)
	}

	return allObjects, nil
}

// 模糊搜索实现
func listObjectsByPattern(ctx context.Context, s3Client *libs3.Client, bucket, pattern string, maxKeys int32) ([]types.Object, error) {
	// 从pattern中提取前缀，提高查询效率
	prefix := extractSearchPrefix(pattern)

	input := &s3.ListObjectsV2Input{
		Bucket:  aws.String(bucket),
		Prefix:  aws.String(prefix),
		MaxKeys: aws.Int32(maxKeys),
	}

	var matchedObjects []types.Object
	regexPattern := wildcardToRegexp(pattern)
	regex, err := regexp.Compile(regexPattern)
	if err != nil {
		return nil, fmt.Errorf("invalid search pattern: %w", err)
	}

	paginator := s3.NewListObjectsV2Paginator(s3Client, input)

	for paginator.HasMorePages() {
		output, err := paginator.NextPage(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to list objects: %w", err)
		}

		// 对每个对象应用正则匹配
		for _, obj := range output.Contents {
			if regex.MatchString(*obj.Key) {
				matchedObjects = append(matchedObjects, obj)

				// 如果达到最大数量限制，提前返回
				if int32(len(matchedObjects)) >= maxKeys {
					return matchedObjects, nil
				}
			}
		}
	}

	return matchedObjects, nil
}

// extractSearchPrefix 从搜索模式中提取有效前缀
// 例如:
// "folder/test.jpg" -> "folder/test.jpg"
// "folder/*.jpg" -> "folder/"
// "*.jpg" -> ""
// "folder/[0-9]*.png" -> "folder/"
// "folder/?test.jpg" -> "folder/"
func extractSearchPrefix(pattern string) string {
	// 找到第一个通配符或特殊字符的位置
	if pattern == "" || !containsWildcard(pattern) {
		return pattern
	}
	// 使用预编译的正则表达式来匹配特殊字符
	// 这比多次调用 strings.Index 更高效
	specialCharRegex := regexp.MustCompile(`[\*\?\[\\\^\$\(\)]`)
	loc := specialCharRegex.FindStringIndex(pattern)
	if loc == nil {
		return pattern
	}
	// 获取第一个特殊字符之前的所有内容
	prefix := pattern[:loc[0]]
	// 处理特殊情况：如果前缀以目录分隔符结尾，保留它
	// 这样可以确保目录级别的搜索效率
	lastSlash := strings.LastIndex(prefix, "/")
	if lastSlash != -1 && lastSlash+1 == len(prefix) {
		return prefix
	}
	// 如果前缀中包含目录分隔符，返回到最后一个分隔符
	// 这样可以保持目录结构的完整性
	if lastSlash != -1 {
		return prefix[:lastSlash+1]
	}
	return prefix
}
func wildcardToRegexp(pattern string) string {
	var result strings.Builder
	result.WriteString("^") // 添加开始标记
	for i := 0; i < len(pattern); i++ {
		ch := pattern[i]
		switch ch {
		case '*':
			result.WriteString(".*")
		case '?':
			result.WriteString(".")
		case '.', '+', '(', ')', '|', '{', '}', '^', '$':
			// 转义正则表达式的特殊字符
			result.WriteString("\\")
			result.WriteByte(ch)
		default:
			result.WriteByte(ch)
		}
	}
	result.WriteString("$") // 添加结束标记
	return result.String()
}

func TestListObjects(t *testing.T) {
	now := time.Now()
	defer func() {
		t.Log("used time:", time.Since(now))
	}()

	bucket := "aaabbb"
	searchPattern := "object-12"
	maxKeys := int32(1000) // 默认限制
	s3cli := setupS3Client(t)
	var (
		objects []types.Object
		err     error
	)
	if searchPattern != "" {
		// 如果是精确匹配（不包含通配符）
		if !containsWildcard(searchPattern) {
			objects, err = listObjectsByPrefix(ctx, s3cli, bucket, searchPattern, maxKeys)
		} else {
			// 模糊搜索
			objects, err = listObjectsByPattern(ctx, s3cli, bucket, searchPattern, maxKeys)
		}
	} else {
		// 列出所有对象
		objects, err = listObjectsByPrefix(ctx, s3cli, bucket, "", maxKeys)
	}
	if err != nil {
		t.Fatal(err)
	}
	for _, obj := range objects {
		t.Log(*obj.Key)
	}
	t.Log("count:", len(objects))
}

func containsWildcard(s string) bool {
	return strings.ContainsAny(s, "*?[]\\^$()")
}
