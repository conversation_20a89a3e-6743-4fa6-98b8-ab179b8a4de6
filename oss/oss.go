package oss

import (
	"archive/zip"
	"context"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3/types"

	//"log"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go/aws"
	"k8s.io/apimachinery/pkg/api/resource"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	libs3 "xiaoshiai.cn/common/s3"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/tenant"
)

type ObjectStorage struct {
	store.ObjectMeta `json:",inline"`
	Provider         string `json:"provider,omitempty"`
	// 该桶依赖的凭证
	CredentialReferences []ObjectStorageCredentialReference `json:"credentialReferences,omitempty"`
	// 是否公开存储桶
	Public bool                `json:"public,omitempty"`
	Size   *resource.Quantity  `json:"size,omitempty"`
	Cors   *ObjectStorageCors  `json:"cors,omitempty"`
	Status ObjectStorageStatus `json:"status,omitempty"`
}

type ObjectStorageCors struct {
	Enabled bool             `json:"enabled,omitempty"`
	Rules   []BucketCorsRule `json:"rules,omitempty"`
}

func (o ObjectStorage) Contains(credentialname string) bool {
	for _, credential := range o.CredentialReferences {
		if credential.Name == credentialname {
			return true
		}
	}
	return false
}

func (o *ObjectStorage) RemoveCredentialReference(credentialName string) *ObjectStorage {
	for i, credential := range o.CredentialReferences {
		if credential.Name == credentialName {
			o.CredentialReferences = append(o.CredentialReferences[:i], o.CredentialReferences[i+1:]...)
			return o
		}
	}
	return o
}

type ObjectStorageCredentialReference struct {
	Name       string      `json:"name,omitempty"`
	UserID     string      `json:"userID,omitempty"` // 此处对应对象存储中用户的ID,不需要带上租户
	Permission Permissions `json:"permission,omitempty"`
}

const (
	ObjectStorageProviderCephS3 = "ceph-s3"
	ObjectStorageProviderMinio  = "minio-s3"
)

type ObjectStorageStatus struct {
	Phase    ObjectStoragePhaseStatus `json:"phase,omitempty"`
	Users    []ObjectStorageUser      `json:"users,omitempty"`
	Endpoint ObjectStorageEndpoint    `json:"endpoint,omitempty"`
	Message  string                   `json:"message,omitempty"`
}

type ObjectStorageEndpoint struct {
	Endpoint     string `json:"endpoint,omitempty"`
	Bucket       string `json:"bucket,omitempty"`
	Region       string `json:"region,omitempty"`
	UsePathStyle bool   `json:"usePathStyle,omitempty"`
	TenantID     string `json:"tenantID,omitempty"` // 租户的ID,如果公开访问，需要像这样的地址:endpoint/租户的ID/bucket
}

type ObjectStorageUser struct {
	TenantID   string      `json:"tenantID,omitempty"`
	UserID     string      `json:"userID,omitempty"`
	AccessKey  string      `json:"accessKey,omitempty"`
	SecretKey  string      `json:"secretKey,omitempty"`
	Permission Permissions `json:"permission,omitempty"`
}

type ObjectStorageStatics struct {
	Quota       *resource.Quantity `json:"quota"`
	UsedBytes   int64              `json:"usedBytes"`
	ObjectCount int64              `json:"objectCount"`
	CanDeleted  bool               `json:"canDelete"` // 是否能被删除
}

type BucketStatistics struct {
	BucketName string        `json:"bucket_name"`
	Statistics []BucketState `json:"statistics"`
}
type BucketState struct {
	Category      string `json:"category"`
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	Ops           uint64 `json:"ops"`
	SuccessfulOps uint64 `json:"successful_ops"`
}

type ObjectStoragePhaseStatus string

const (
	ObjectStoragePhaseReady  ObjectStoragePhaseStatus = "Ready"
	ObjectStoragePhaseUnkown ObjectStoragePhaseStatus = "Unkown"
)

type ObjectStorageOptions struct {
	Enabled       bool `json:"enabled,omitempty"`
	libs3.Options `json:",inline"`
	Provider      string `json:"provider,omitempty"`
}

type ObjectStorageInformation struct {
	Statics   ObjectStorageStatics              `json:"statics"`
	Summaries []ObjectStorageInformationSummary `json:"summaries"`
}

type ObjectStorageInformationSummary struct {
	Categories []ObjectStorageInformationCategory `json:"categories"`
	Total      ObjectStorageInformationTotal      `json:"total"`
}

type ObjectStorageInformationCategory struct {
	Category      string `json:"category"`
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	Ops           uint64 `json:"ops"`
	SuccessfulOps uint64 `json:"successful_ops"`
}

type ObjectStorageInformationTotal struct {
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	Ops           uint64 `json:"ops"`
	SuccessfulOps uint64 `json:"successful_ops"`
}

type ObjectMultipart struct {
	CreateTime time.Time `json:"createTime"`
	Size       int64     `json:"size"` // 分片的总大小
	ObjectName string    `json:"objectName"`
	UploadID   string    `json:"uploadID"`
	PartNumber int       `json:"partNumber"` // 分片数量
}

func NewDefaultObjectStorageOptions() *ObjectStorageOptions {
	return &ObjectStorageOptions{
		Enabled:  true,
		Provider: ObjectStorageProviderCephS3,
		Options:  *libs3.NewDefaultOptions(),
	}
}

type API struct {
	base.API
	Manager *ObjectStorageService
}

func NewAPI(base base.API, manager *ObjectStorageService) *API {
	return &API{
		API:     base,
		Manager: manager,
	}
}

func ScopesToLabels(scopes []store.Scope) map[string]string {
	labels := map[string]string{}
	for _, scope := range scopes {
		labels[scope.Resource] = scope.Name
	}
	return labels
}

type ObjectStorageUsageInfo struct {
	Name         string                   `json:"name"`
	Organization string                   `json:"organization"`
	Usage        *ObjectStorageStatics    `json:"usage"`
	Phase        ObjectStoragePhaseStatus `json:"phase,omitempty"`
}

func (a *API) GetObjectStorageStatistics(w http.ResponseWriter, r *http.Request) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		objectstorage := api.Path(r, "objectstorage", "")
		if objectstorage == "" {
			return nil, liberrors.NewBadRequest("objectstorage name is required")
		}
		return a.Manager.Provider.GetBucketMetrics(ctx, objectstorage)
	})
}

func (a *API) getObjectStorageFromAll(ctx context.Context, provider, objectName string) (bool, error) {
	tenants := &store.List[tenant.Tenant]{}
	tenantscoped := a.ProviderStore(provider)
	if err := tenantscoped.List(ctx, tenants); err != nil {
		log.Error(err, "failed to list tenants")
		return false, err
	}
	for _, t := range tenants.Items {
		allorgs := &store.List[organization.Organization]{}
		if err := tenantscoped.Scope(base.ScopeTenant(t.Name)).List(ctx, allorgs); err != nil {
			return false, err
		}
		for _, orgItem := range allorgs.Items {
			err := tenantscoped.Scope(base.ScopeTenant(t.Name), base.ScopeOrganization(orgItem.Name)).Get(ctx, objectName, &ObjectStorage{})
			if err == nil {
				return true, nil
			}
		}
	}
	return false, nil
}

// 租户下对象存储统计
func (a *API) ListObjectStoragesUsage(w http.ResponseWriter, r *http.Request) {
	a.onSomeScope(w, r, func(ctx context.Context, provider string, scopes []store.Scope) (any, error) {
		tenantscoped := a.ProviderStore(provider).Scope(scopes...)
		allorgs := &store.List[organization.Organization]{}
		if err := tenantscoped.List(ctx, allorgs); err != nil {
			return nil, err
		}
		var osues []ObjectStorageUsageInfo
		for _, orgItem := range allorgs.Items {
			organizationName := orgItem.Name
			orgStorage := tenantscoped.Scope(base.ScopeOrganization(organizationName))
			orgObjects := &store.List[ObjectStorage]{}
			if err := orgStorage.List(ctx, orgObjects); err != nil {
				return nil, err
			}
			for _, object := range orgObjects.Items {
				osu := ObjectStorageUsageInfo{
					Name:         object.Name,
					Organization: organizationName,
					Phase:        object.Status.Phase,
				}
				objUsage, err := a.Manager.Provider.GetBucketUsage(ctx, object.Name)
				if err != nil {
					// log.Println(err)
					continue
				}
				osu.Usage = objUsage
				osues = append(osues, osu)
			}
		}
		list := api.PageFromListOptions(osues, api.GetListOptions(r), func(o ObjectStorageUsageInfo) string {
			return o.Name
		}, nil)
		return store.List[ObjectStorageUsageInfo]{
			Items: list.Items,
			Total: int(list.Total),
			Page:  int(list.Page),
			Size:  int(list.Size),
		}, nil
	})
}

func (a *API) ListObjectStorages(w http.ResponseWriter, r *http.Request) {
	a.onSomeScope(w, r, func(ctx context.Context, provider string, scopes []store.Scope) (any, error) {
		options := []store.ListOption{
			store.WithMatchLabels(ScopesToLabels(scopes)),
		}
		return base.GenericListWithWatch(w, r, a.ProviderStore(provider).Scope(scopes...), &store.List[ObjectStorage]{}, options...)
	})
}

func (a *API) bucketExists(ctx context.Context, bucket string) (bool, error) {
	_, err := a.Manager.S3Client.HeadBucket(ctx, &s3.HeadBucketInput{Bucket: aws.String(bucket)})
	if err == nil {
		return true, nil
	}
	if IsS3StorageNotFound(err) {
		return false, nil
	}
	return false, err
}

func (a *API) CreateObjectStorage(w http.ResponseWriter, r *http.Request) {
	a.onSomeScope(w, r, func(ctx context.Context, provider string, scopes []store.Scope) (any, error) {
		bucket := &ObjectStorage{}
		if err := api.Body(r, bucket); err != nil {
			return nil, err
		}
		existOss, err := a.bucketExists(ctx, bucket.Name)
		if err != nil {
			return nil, err
		}
		if existOss {
			return nil, fmt.Errorf("The storage service already has a bucket:%s", bucket.Name)
		}
		existStore, err := a.getObjectStorageFromAll(ctx, provider, bucket.Name)
		if err != nil {
			return nil, err
		}
		if existStore {
			return nil, fmt.Errorf("The storage service already has a bucket:%s", bucket.Name)
		}
		if err := a.BeforeBucket(ctx, bucket, scopes); err != nil {
			return nil, err
		}
		// set init cors
		if bucket.Cors == nil {
			bucket.Cors = &ObjectStorageCors{
				Enabled: true,
				Rules: []BucketCorsRule{
					{
						AllowedOrigin: []string{"*"},
						AllowedMethod: []string{
							"GET",
							"PUT",
							"DELETE",
							"HEAD",
							"POST",
						},
						AllowedHeader: []string{"*"},
						MaxAgeSeconds: aws.Int32(3600),
					},
				},
			}
		}
		if err := a.ProviderStore(provider).Scope(scopes...).Create(r.Context(), bucket); err != nil {
			return nil, err
		}
		return bucket, nil
	})
}

func (a *API) GetObjectStorageInfo(w http.ResponseWriter, r *http.Request) {
	a.onSomeScopeBucket(w, r, func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error) {
		obj := &ObjectStorage{}
		if err := a.ProviderStore(provider).Scope(scopes...).Get(r.Context(), name, obj); err != nil {
			return nil, err
		}
		return a.Manager.Provider.GetBucketUsage(ctx, name)
	})
}

func (a *API) UpdateObjectStorage(w http.ResponseWriter, r *http.Request) {
	a.onSomeScopeBucket(w, r, func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error) {
		bucket := &ObjectStorage{}
		if err := api.Body(r, bucket); err != nil {
			return nil, err
		}
		if nameinbody := bucket.GetName(); nameinbody != "" && nameinbody != name {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", nameinbody, name)
		}
		bucket.SetName(name)

		if err := a.BeforeBucket(ctx, bucket, scopes); err != nil {
			return nil, err
		}
		bucket.SetResourceVersion(0)
		if err := a.ProviderStore(provider).Scope(scopes...).Update(r.Context(), bucket); err != nil {
			return nil, err
		}
		return bucket, nil
	})
}

func (a *API) BeforeBucket(ctx context.Context, bucket *ObjectStorage, scopes []store.Scope) error {
	bucketname := GetBucketName(bucket)
	if err := CheckValidBucketName(bucketname, true); err != nil {
		return err
	}
	bucket.SetLabels(ScopesToLabels(scopes))
	return nil
}

func (a *API) DeleteObjectStorage(w http.ResponseWriter, r *http.Request) {
	a.onSomeScopeBucket(w, r, func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error) {
		providerscoped := a.ProviderStore(provider).Scope(scopes...)
		return base.GenericDelete(r, providerscoped, &ObjectStorage{}, name)
	})
}

func (a *API) DownloadObject(w http.ResponseWriter, r *http.Request) {
	a.onSomeScopeBucket(w, r, func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error) {
		obj := &ObjectStorage{}
		if err := a.ProviderStore(provider).Scope(scopes...).Get(r.Context(), name, obj); err != nil {
			return nil, err
		}
		objectname := api.Path(r, "object", "")
		if objectname == "" {
			return nil, liberrors.NewBadRequest("object name is required")
		}
		isFolder := strings.HasSuffix(objectname, "/")
		if isFolder {
			// 处理目录下载
			return a.downloadFolder(ctx, w, name, objectname)
		}
		// 处理单个文件下载
		return a.downloadFile(ctx, w, name, objectname)
	})
}

func (a *API) downloadFile(ctx context.Context, w http.ResponseWriter, bucket, objectKey string) (any, error) {
	output, err := a.Manager.S3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(objectKey),
	})
	if err != nil {
		return nil, err
	}
	defer output.Body.Close()
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(objectKey)))
	_, err = io.Copy(w, output.Body)
	return nil, err
}

func (a *API) downloadFolder(ctx context.Context, w http.ResponseWriter, bucket, prefix string) (any, error) {
	input := &s3.ListObjectsV2Input{
		Bucket: aws.String(bucket),
		Prefix: aws.String(prefix),
	}
	var objects []types.Object
	paginator := s3.NewListObjectsV2Paginator(a.Manager.S3Client, input)
	for paginator.HasMorePages() {
		output, err := paginator.NextPage(ctx)
		if err != nil {
			return nil, err
		}
		objects = append(objects, output.Contents...)
	}
	if len(objects) == 0 {
		return nil, liberrors.NewNotFound("folder", prefix)
	}
	resp, pw := io.Pipe()
	go func() {
		defer pw.Close()
		zipw := zip.NewWriter(pw)
		defer zipw.Close()
		for _, obj := range objects {
			if aws.StringValue(obj.Key) == prefix {
				continue
			}
			output, err := a.Manager.S3Client.GetObject(ctx, &s3.GetObjectInput{
				Bucket: aws.String(bucket),
				Key:    obj.Key,
			})
			if err != nil {
				continue
			}
			modified, _ := time.Parse(time.RFC3339, obj.LastModified.Format(time.RFC3339))
			f, err := zipw.CreateHeader(&zip.FileHeader{
				Name:     aws.StringValue(obj.Key),
				NonUTF8:  false,
				Method:   zip.Deflate,
				Modified: modified,
			})
			if err != nil {
				// Ignore errors, move to next
				continue
			}
			_, err = io.Copy(f, output.Body)
			if err != nil {
				// We have a partial object, report error.
				pw.CloseWithError(err)
				return
			}
		}
	}()
	defer resp.Close()
	filename := filepath.Base(strings.TrimSuffix(prefix, "/"))
	w.Header().Set("Content-Type", "application/octet-stream")
	w.Header().Set("Content-Disposition", fmt.Sprintf(`attachment; filename="%s.zip"`, filename))
	w.Header().Set("Cache-Control", "private, no-cache, no-store, must-revalidate")
	w.Header().Set("Pragma", "no-cache")
	w.Header().Set("Expires", "0")
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.WriteHeader(http.StatusOK)
	_, err := io.Copy(w, resp)
	return nil, err
}

func (a *API) ListObjectMultiparts(w http.ResponseWriter, r *http.Request) {
	a.onSomeScopeBucket(w, r, func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error) {
		obj := &ObjectStorage{}
		if err := a.ProviderStore(provider).Scope(scopes...).Get(r.Context(), name, obj); err != nil {
			return nil, err
		}
		// 无绑定凭据无法查看存储桶的分片
		if len(obj.CredentialReferences) == 0 {
			log.FromContext(ctx).Error(fmt.Errorf("bucket %s has no credential", name), "bucket")
			return nil, nil
		}
		multiparts, err := a.Manager.S3Client.ListMultipartUploads(ctx, &s3.ListMultipartUploadsInput{
			Bucket:     aws.String(name),
			MaxUploads: aws.Int32(1000),
		})
		if err != nil {
			return nil, err
		}
		var objectParts []ObjectMultipart
		for _, part := range multiparts.Uploads {
			if part.Key == nil || part.UploadId == nil || part.Initiated == nil {
				continue
			}
			listPartsInput := &s3.ListPartsInput{
				Bucket:   aws.String(name),
				Key:      part.Key,
				UploadId: part.UploadId,
			}
			var size int64
			partsResult, err := a.Manager.S3Client.ListParts(ctx, listPartsInput)
			if err != nil {
				return nil, err
			}
			for _, pt := range partsResult.Parts {
				size += *pt.Size
			}
			objectParts = append(objectParts, ObjectMultipart{
				CreateTime: *part.Initiated,
				ObjectName: *part.Key,
				UploadID:   *part.UploadId,
				Size:       size,
				PartNumber: len(partsResult.Parts),
			})

		}
		list := api.PageFromListOptions(objectParts, api.GetListOptions(r), nil, nil)
		return store.List[ObjectMultipart]{
			Items: list.Items,
			Total: int(list.Total),
			Page:  int(list.Page),
			Size:  int(list.Size),
		}, nil
	})
}

func (a *API) RemoveObjectMultiparts(w http.ResponseWriter, r *http.Request) {
	a.onSomeScopeBucket(w, r, func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error) {
		obj := &ObjectStorage{}
		if err := a.ProviderStore(provider).Scope(scopes...).Get(r.Context(), name, obj); err != nil {
			return nil, err
		}

		var cloudRemoveParts bool
		for _, cr := range obj.CredentialReferences {
			if cr.Permission == PermissionsManage {
				cloudRemoveParts = true
				break
			}
		}
		if !cloudRemoveParts {
			return nil, fmt.Errorf("bucket %s has no manage permission", name)
		}

		parts := []ObjectMultipart{}
		if err := api.Body(r, &parts); err != nil {
			return nil, err
		}
		for _, part := range parts {
			_, err := a.Manager.S3Client.AbortMultipartUpload(ctx, &s3.AbortMultipartUploadInput{
				Bucket:   aws.String(name),
				Key:      aws.String(part.ObjectName),
				UploadId: aws.String(part.UploadID),
			})
			if err != nil {
				return nil, err
			}
		}
		return nil, nil
	})
}

func (a *API) onSomeScopeBucket(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, provider string, scopes []store.Scope, name string) (any, error)) {
	a.onSomeScope(w, r, func(ctx context.Context, provider string, scopes []store.Scope) (any, error) {
		os := api.Path(r, "objectstorage", "")
		if os == "" {
			return nil, liberrors.NewBadRequest("objectstorage name is required")
		}
		return fn(ctx, provider, scopes, os)
	})
}

func (a *API) ProviderStore(provider string) store.Store {
	if provider == "" {
		return a.Store
	}
	return a.Store.Scope(store.Scope{Resource: "objectstorageproviders", Name: provider})
}

func (a *API) onSomeScope(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, provider string, bucketscopes []store.Scope) (any, error)) {
	base.On(w, r, func(ctx context.Context) (any, error) {
		provider := api.Path(r, "provider", "")

		scopes := []store.Scope{}
		if tenant := api.Path(r, "tenant", ""); tenant != "" {
			scopes = append(scopes, base.ScopeTenant(tenant))
		}
		if org := api.Path(r, "organization", ""); org != "" {
			scopes = append(scopes, base.ScopeOrganization(org))
		}
		return fn(ctx, provider, scopes)
	})
}

func (a *API) Group() api.Group {
	return api.NewGroup("").
		Tag("ObjectStorages").
		SubGroup(
			a.providerGroup(),

			base.NewTenantGroup("credentials").SubGroup(a.credentialTenantGroup()),
			base.NewTenantOrganizationGroup("credentials").SubGroup(a.credentialOrganizationGroup()),
			// base.NewTenantGroup("objectstorages").SubGroup(a.ossGroup()),
			// base.NewTenantOrganizationGroup("objectstorages").SubGroup(a.ossOrganizationGroup()),
			a.ossOrganizationGroup(),
			a.ossTenantGroup(),
		)
}

func (a *API) ossTenantGroup() api.Group {
	return base.
		NewTenantGroup("objectstorages").
		Route(
			api.GET("").
				Doc("List object storages usageinfo").
				Param(api.PageParams...).
				To(a.ListObjectStoragesUsage).
				Response(store.List[ObjectStorageUsageInfo]{}),
			api.GET("/{objectstorage}/statistics").
				Doc("Get object storages statistics").
				To(a.GetObjectStorageStatistics).
				Response(BucketStatistics{}),
		)
}

func (a *API) ossOrganizationGroup() api.Group {
	return base.
		NewTenantOrganizationGroup("objectstorages").
		Route(
			api.GET("").
				Doc("List object storages").
				To(a.ListObjectStorages).
				Response(store.List[ObjectStorage]{}),

			api.POST("").
				Doc("Create object storage").
				To(a.CreateObjectStorage).
				Response(ObjectStorage{}),

			api.GET("/{objectstorage}").
				Doc("Get object storage information").
				Param(api.QueryParam("start", "start time")).
				Param(api.QueryParam("end", "end time")).
				To(a.GetObjectStorageInfo).
				Response(ObjectStorageStatics{}),

			api.PUT("/{objectstorage}").
				Doc("Update object storage").
				To(a.UpdateObjectStorage).
				Param(api.BodyParam("body", ObjectStorage{})),

			api.DELETE("/{objectstorage}").
				Doc("Delete object storage").
				To(a.DeleteObjectStorage),

			api.GET("/{objectstorage}/multiparts").
				Doc("List object multiparts").
				Param(api.PageParams...).
				To(a.ListObjectMultiparts).
				Response(store.List[ObjectMultipart]{}),

			api.DELETE("/{objectstorage}/multiparts").
				Doc("Delete object multiparts").
				Param(api.BodyParam("body", []ObjectMultipart{})).
				To(a.RemoveObjectMultiparts),

			api.GET("/{objectstorage}/download/{object}*").
				Doc("Download object").
				To(a.DownloadObject),
		)
}
