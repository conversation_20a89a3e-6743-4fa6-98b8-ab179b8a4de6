package oss

import (
	"context"
	"fmt"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/organization"
)

type CredentialController struct {
	Service *ObjectStorageService
}

func NewCredentialController(ss *ObjectStorageService) (*controller.Controller, error) {
	rec := &CredentialController{Service: ss}
	br := controller.NewBetterReconciler(rec, ss.Client,
		controller.WithFinalizer("object-storage-credential-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("objectstoragecredentials", br).Watch(
		controller.NewStoreSource(ss.Client, &Credential{}),
	)
	return c, nil
}

func (c *CredentialController) Sync(ctx context.Context, cr *Credential) (controller.Result, error) {
	return base.UnwrapReQueueError(c.sync(ctx, cr))
}

func (c *CredentialController) sync(ctx context.Context, cr *Credential) error {
	tenantname := getCredentialTenant(cr.Scopes)
	username := fmt.Sprintf("%s_%s", tenantname, cr.Name)
	osu, err := c.Service.Provider.CreateUser(ctx, cr.Permissions, username)
	if err != nil {
		cr.Status.Phase = CredentialPhaseError
		cr.Status.Message = err.Error()
		return err
	}
	cr.Status.User = *osu
	// 获取该凭证下绑定的所有组织
	bindorgs := base.ExtractSharedOrganizationFromLabels(cr.GetLabels())
	tenantscoped := c.Service.Client.Scope(cr.Scopes...)
	allorgs := &store.List[organization.Organization]{}
	if err := tenantscoped.List(ctx, allorgs); err != nil {
		cr.Status.Phase = CredentialPhaseError
		return err
	}
	for _, orgItem := range allorgs.Items {
		if contains(bindorgs, orgItem.Name) {
			continue
		}
		// 如果组织不在绑定，需要给组织下面的桶去除绑定的凭证
		// 获取组织下所有的桶，如果桶中CredentialReferences中包含该credential，则在CredentialReferences中删除该credential
		orgStorage := tenantscoped.Scope(base.ScopeOrganization(orgItem.Name))
		orgObjects := &store.List[ObjectStorage]{}
		if err := orgStorage.List(ctx, orgObjects); err != nil {
			cr.Status.Phase = CredentialPhaseError
			return err
		}
		for _, object := range orgObjects.Items {
			if !object.Contains(cr.Name) {
				continue
			}
			// 去除该凭证的绑定
			tmp := &ObjectStorage{}
			if err := orgStorage.Get(ctx, object.Name, tmp); err != nil {
				cr.Status.Phase = CredentialPhaseError
				return err
			}
			if err := orgStorage.Update(ctx, tmp.RemoveCredentialReference(cr.Name)); err != nil {
				cr.Status.Phase = CredentialPhaseError
				return err
			}
		}
	}
	cr.Status.Phase = CredentialPhaseReady
	cr.Status.Message = ""
	return nil
}

func contains[T comparable](items []T, item T) bool {
	for _, o := range items {
		if o == item {
			return true
		}
	}
	return false
}

func (c *CredentialController) Remove(ctx context.Context, cr *Credential) (controller.Result, error) {
	return base.UnwrapReQueueError(c.remove(ctx, cr))
}

func (c *CredentialController) remove(ctx context.Context, cr *Credential) error {
	tenantname := getCredentialTenant(cr.Scopes)
	username := fmt.Sprintf("%s_%s", tenantname, cr.Name)
	return c.Service.Provider.RemoveUser(ctx, username)
}

func getCredentialTenant(scopes []store.Scope) string {
	var tenantname string
	for _, scope := range scopes {
		if scope.Resource == "tenants" {
			tenantname = scope.Name
		}
	}
	return tenantname
}
