package oss

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type Credential struct {
	store.ObjectMeta `json:",inline"`
	Permissions      Permissions      `json:"permissions,omitempty"`
	Status           CredentialStatus `json:"status,omitempty"`
}

type Permissions string

const (
	PermissionsManage   Permissions = "manage"
	PermissionsReadOnly Permissions = "readonly"
)

type CredentialStatus struct {
	Phase   CredentialPhaseStatus `json:"phase,omitempty"`
	User    ObjectStorageUser     `json:"user,omitempty"`
	Message string                `json:"message,omitempty"`
}

type CredentialPhaseStatus string

const (
	CredentialPhaseReady CredentialPhaseStatus = "Ready"
	CredentialPhaseError CredentialPhaseStatus = "Error"
)

func (a *API) credentialOrganizationGroup() api.Group {
	return api.NewGroup("").
		Route(
			api.GET("").
				Doc("list organization could use credentials").
				To(a.ListOrganizationCredentials).
				Response(store.List[Credential]{}),

			api.GET("/{credential}").
				Doc("get organization credential").
				To(a.GetOrganizationCredential).
				Response(Credential{}),
		)
}

func (a *API) ListOrganizationCredentials(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		ret := &store.List[Credential]{}
		tenantscopestorage := a.Store.Scope(base.ScopeTenant(tenant))
		if err := tenantscopestorage.List(ctx, ret); err != nil {
			return nil, err
		}
		bindings := &store.List[Credential]{}
		for _, item := range ret.Items {
			credential, err := base.GetSharedToOrganizationObject(ctx, tenantscopestorage, &Credential{}, org, item.Name)
			if err != nil {
				if errors.IsNotFound(err) {
					continue
				}
				return nil, err
			}
			bindings.Items = append(bindings.Items, *credential)
		}
		return bindings, nil
	})
}

func (a *API) GetOrganizationCredential(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		credential := api.Path(r, "credential", "")
		if credential == "" {
			return nil, errors.NewBadRequest("credential name is required")
		}
		tenantscopestorage := a.Store.Scope(base.ScopeTenant(tenant))
		return base.GetSharedToOrganizationObject(ctx, tenantscopestorage, &Credential{}, org, credential)
	})
}

func (a *API) credentialTenantGroup() api.Group {
	return api.NewGroup("").
		Route(
			api.GET("").
				Doc("list tenant credentials").
				To(a.ListCredentials).
				Response(store.List[Credential]{}),

			api.DELETE("/{credential}").
				Doc("delete tenant credential").
				To(a.DeleteCredential),

			api.POST("").
				Doc("create tenant credential").
				To(a.CreateCredential).
				Response(Credential{}),

			api.PUT("/{credential}").
				Doc("share tenant credential to organizations").
				To(a.ShareCredential).
				Param(api.BodyParam("organizations", []string{})).
				Response([]string{}),
		)
}

func (a *API) ShareCredential(w http.ResponseWriter, r *http.Request) {
	a.onCredential(w, r, func(ctx context.Context, tenant, credential string) (any, error) {
		orgnames := []string{}
		if err := api.Body(r, &orgnames); err != nil {
			return nil, err
		}
		tenantscoped := a.Store.Scope(base.ScopeTenant(tenant))
		if err := base.SetShareddOrganizationToObject(ctx, tenantscoped, &Credential{}, credential, orgnames); err != nil {
			return nil, err
		}
		return orgnames, nil
		// credentialBody := &Credential{}
		// if err := api.Body(r, credentialBody); err != nil {
		// 	return nil, err
		// }
		// store := a.Store.Scope(base.ScopeTenant(tenant))
		// return credentialBody, store.Update(ctx, credentialBody)
	})
}

func (a *API) ListCredentials(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		ret := &store.List[Credential]{}
		return base.GenericList(r, storage, ret)
	})
}

func (a *API) DeleteCredential(w http.ResponseWriter, r *http.Request) {
	a.onCredential(w, r, func(ctx context.Context, tenant, credential string) (any, error) {
		//cr := &store.Unstructured{}
		//cr.SetResource("credentials")
		store := a.Store.Scope(base.ScopeTenant(tenant))
		cr := &Credential{}
		if err := store.Get(ctx, credential, cr); err != nil {
			return nil, err
		}
		exists := len(base.ExtractSharedOrganizationFromLabels(cr.GetLabels())) > 0
		if exists {
			return nil, fmt.Errorf("cannot delete credential %s,organization has bind it", credential)
		}
		return nil, store.Delete(ctx, cr)

		// cr := &Credential{}
		// if err := store.Get(ctx, credential, cr); err != nil {
		// 	if liberrors.IsNotFound(err) {
		// 		return nil, fmt.Errorf("credential %s not found", credential)
		// 	}
		// 	return nil, err
		// }
		// if len(cr.BindOrganizations) == 0 {
		// 	return nil, store.Delete(ctx, cr)
		// }
		// return nil, fmt.Errorf("cannot delete credential %s,organization %v has bind it", credential, cr.BindOrganizations)
		// // root := a.Store.Scope(base.ScopeTenant(tenant))
		// orgs := &store.List[organization.Organization]{}
		// if err := root.List(ctx, orgs); err != nil {
		// 	return nil, err
		// }
		// // 如果有组织绑定了该凭证，则不能删除
		// for _, org := range orgs.Items {
		// 	err := root.Scope(base.ScopeOrganization(org.Name)).Get(ctx, credential, cr)
		// 	if err != nil {
		// 		if liberrors.IsNotFound(err) {
		// 			continue
		// 		}
		// 		return nil, err
		// 	}
		// 	return nil, errors.NewBadRequest(fmt.Sprintf("cannot delete credential %s,organization %s has bind it", credential, org.Name))
		// }
		// return base.GenericDelete(r, root, &Credential{}, credential)
	})
}

func (a *API) CreateCredential(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, _ string) (any, error) {
		credential := &Credential{}
		if err := api.Body(r, credential); err != nil {
			return nil, err
		}
		if err := storage.Create(ctx, credential); err != nil {
			return nil, err
		}
		return credential, nil
	})
}

func (a *API) onCredential(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant string, credential string) (any, error)) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		credential := api.Path(r, "credential", "")
		if credential == "" {
			return nil, fmt.Errorf("credential name is required")
		}
		return fn(ctx, tenant, credential)
	})
}
