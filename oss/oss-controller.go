package oss

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/smithy-go"
	smithyhttp "github.com/aws/smithy-go/transport/http"
	"github.com/ceph/go-ceph/rgw/admin"
	"k8s.io/apimachinery/pkg/api/resource"

	"xiaoshiai.cn/common/controller"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/core/base"

	iampolicy "github.com/minio/pkg/v3/policy"
	"github.com/minio/pkg/v3/policy/condition"
	"xiaoshiai.cn/common/log"
	libs3 "xiaoshiai.cn/common/s3"
	"xiaoshiai.cn/common/store"
)

type ObjectStorageProviderClient interface {
	GetTenantID() string
	CreateUser(ctx context.Context, permission Permissions, userID string) (*ObjectStorageUser, error)
	GetBucketUsage(ctx context.Context, bucketName string) (*ObjectStorageStatics, error)
	GetBucketMetrics(ctx context.Context, bucketName string) (*BucketStatistics, error)
	// impletions should create the user if not exists.
	EnsureUser(ctx context.Context, bucketName string) (*ObjectStorageUser, error)
	RemoveUser(ctx context.Context, userID string) error

	EnsureQuota(ctx context.Context, bucketName string, quota resource.Quantity) error
	RemoveQuota(ctx context.Context, bucketName string) error

	State(ctx context.Context, bucketName string) (ObjectStorageStatics, error)
	BucketUsageInfo(ctx context.Context, start, end, bucketName string) (*ObjectStorageInformation, error)
}

type ObjectStorageService struct {
	Opts     *ObjectStorageOptions
	Provider ObjectStorageProviderClient
	S3Client *libs3.Client
	Client   store.Store
}

func NewObjectStorageService(ctx context.Context, storage store.Store, opt *ObjectStorageOptions) (*ObjectStorageService, error) {
	var (
		providerClient ObjectStorageProviderClient
		err            error
	)
	switch opt.Provider {
	case ObjectStorageProviderCephS3:
		// ceph rgw
		providerClient, err = NewCephS3ProviderClient(ctx, opt.Address, opt.AccessKey, opt.SecretKey)
		if err != nil {
			return nil, err
		}
	case ObjectStorageProviderMinio:
		// minio
		providerClient, err = NewMinioProviderClient(ctx, opt.Address, opt.AccessKey, opt.SecretKey)
		if err != nil {
			return nil, err
		}

	default:
		return nil, fmt.Errorf("unsupported object storage provider %s", opt.Provider)
	}
	s3cli, err := libs3.NewClient(ctx, &opt.Options)
	if err != nil {
		return nil, err
	}
	oss := &ObjectStorageService{
		Opts:     opt,
		Client:   storage,
		Provider: providerClient,
		S3Client: s3cli,
	}
	return oss, nil
}

func (o *ObjectStorageService) GetEndpoint(ctx context.Context, bucketName string) *ObjectStorageEndpoint {
	return &ObjectStorageEndpoint{
		Endpoint:     o.Opts.Address,
		Bucket:       bucketName,
		UsePathStyle: o.Opts.PathStyle,
		Region:       o.Opts.Region,
		TenantID:     o.Provider.GetTenantID(),
	}
}

func NewTenantObjectStorageController(ss *ObjectStorageService) (*controller.Controller, error) {
	rec := &ObjectStorageController{Service: ss}
	br := controller.NewBetterReconciler(rec, ss.Client,
		controller.WithFinalizer("object-storage-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("objectstorages", br).Watch(
		controller.NewStoreSource(ss.Client, &ObjectStorage{}),
	)
	return c, nil
}

type ObjectStorageController struct {
	Service *ObjectStorageService
}

func (o *ObjectStorageController) Sync(ctx context.Context, oss *ObjectStorage) (controller.Result, error) {
	return base.UnwrapReQueueError(formatS3Error(o.sync(ctx, oss)))
}

func (o *ObjectStorageController) sync(ctx context.Context, oss *ObjectStorage) error {
	tenantname, organizationname := base.TenantOrganizationFromScopes(oss.Scopes...)
	if organizationname == "" {
		return nil
	}
	tenantscoped := o.Service.Client.Scope(base.ScopeTenant(tenantname))
	var users []ObjectStorageUser
	for index := range oss.CredentialReferences {
		item := oss.CredentialReferences[index]
		cr := &Credential{}
		if err := tenantscoped.Get(ctx, item.Name, cr); err != nil {
			if !liberrors.IsNotFound(err) {
				return err
			}
			oss.CredentialReferences = slices.Delete(oss.CredentialReferences, index, index+1)
		}
		users = append(users, cr.Status.User)
	}
	bucketName := GetBucketName(oss)
	currentPolicy := GenerateBucketPolicyFromPermission(o.Service.Provider.GetTenantID(), bucketName, oss.CredentialReferences, oss.Public)
	currentPolicyData, err := currentPolicy.MarshalJSON()
	if err != nil {
		return err
	}
	if _, err := o.Service.S3Client.HeadBucket(ctx, &s3.HeadBucketInput{Bucket: aws.String(bucketName)}); err != nil {
		if !IsS3StorageNotFound(err) {
			oss.Status.Phase = ObjectStoragePhaseUnkown
			return err
		}
		if _, err := o.Service.S3Client.CreateBucket(ctx, &s3.CreateBucketInput{Bucket: aws.String(bucketName)}); err != nil {
			oss.Status.Phase = ObjectStoragePhaseUnkown
			return err
		}
		if err := s3.NewBucketExistsWaiter(o.Service.S3Client).
			Wait(ctx, &s3.HeadBucketInput{Bucket: aws.String(bucketName)}, 5*time.Minute); err != nil {
			oss.Status.Phase = ObjectStoragePhaseUnkown
			return err
		}
	}
	// sync quota
	if err := o.syncQuota(ctx, bucketName, oss); err != nil {
		oss.Status.Phase = ObjectStoragePhaseUnkown
		return err
	}
	policyData, err := o.Service.S3Client.GetBucketPolicy(ctx, &s3.GetBucketPolicyInput{Bucket: aws.String(bucketName)})
	if err != nil {
		if !IsS3StorageNotFound(err) {
			oss.Status.Phase = ObjectStoragePhaseUnkown
			return err
		}
		// not found,create bucketpolicy
		_, err := o.Service.S3Client.PutBucketPolicy(ctx, &s3.PutBucketPolicyInput{
			Bucket: aws.String(bucketName),
			Policy: aws.String(string(currentPolicyData)),
		})
		if err != nil {
			oss.Status.Phase = ObjectStoragePhaseUnkown
			return err
		}
	} else {
		existPolicy, err := iampolicy.ParseBucketPolicyConfig(strings.NewReader(*policyData.Policy), bucketName)
		if err != nil {
			oss.Status.Phase = ObjectStoragePhaseUnkown
			return err
		}
		if !existPolicy.Equals(*currentPolicy) {
			// 桶的policy和申明的policy不一致
			_, err := o.Service.S3Client.PutBucketPolicy(ctx, &s3.PutBucketPolicyInput{
				Bucket: aws.String(bucketName),
				Policy: aws.String(string(currentPolicyData)),
			})
			if err != nil {
				oss.Status.Phase = ObjectStoragePhaseUnkown
				return err
			}
		}
	}

	// sync cross
	if err := o.syncCors(ctx, bucketName, oss.Cors); err != nil {
		oss.Status.Phase = ObjectStoragePhaseUnkown
		return err
	}

	// 从store中获取user信息
	address := o.Service.GetEndpoint(ctx, bucketName)
	oss.Status.Endpoint = *address
	oss.Status.Users = users
	oss.Status.Phase = ObjectStoragePhaseReady
	oss.Status.Message = ""
	return nil
}

type BucketCorsRule struct {
	AllowedOrigin []string `json:"allowed_origin"`
	AllowedMethod []string `json:"allowed_method"`
	AllowedHeader []string `json:"allowed_header"`
	ExposeHeader  []string `json:"expose_header"`
	MaxAgeSeconds *int32   `json:"max_age_seconds"`
}

func (o *ObjectStorageController) syncCors(ctx context.Context, bucketName string, cors *ObjectStorageCors) error {
	if cors == nil {
		return nil
	}
	if !cors.Enabled {
		_, err := o.Service.S3Client.DeleteBucketCors(ctx, &s3.DeleteBucketCorsInput{Bucket: aws.String(bucketName)})
		if err != nil {
			return err
		}
		return nil
	}
	awsRules := make([]types.CORSRule, 0, len(cors.Rules))
	for _, rule := range cors.Rules {
		awsRules = append(awsRules, types.CORSRule{
			AllowedMethods: rule.AllowedMethod,
			AllowedHeaders: rule.AllowedHeader,
			ExposeHeaders:  rule.ExposeHeader,
			MaxAgeSeconds:  rule.MaxAgeSeconds,
			AllowedOrigins: rule.AllowedOrigin,
		})
	}
	_, err := o.Service.S3Client.PutBucketCors(ctx, &s3.PutBucketCorsInput{
		Bucket:            aws.String(bucketName),
		CORSConfiguration: &types.CORSConfiguration{CORSRules: awsRules},
	})
	if err != nil {
		return err
	}
	return nil
}

func (o *ObjectStorageController) syncQuota(ctx context.Context, bucketName string, oss *ObjectStorage) error {
	if limtisize := oss.Size; limtisize != nil && limtisize.Value() != 0 {
		// sync quota
		if err := o.Service.Provider.EnsureQuota(ctx, bucketName, *limtisize); err != nil {
			return err
		}
	} else {
		// unset quota
		if err := o.Service.Provider.RemoveQuota(ctx, bucketName); err != nil {
			return err
		}
	}
	return nil
}

func (o *ObjectStorageController) Remove(ctx context.Context, oss *ObjectStorage) (controller.Result, error) {
	if err := o.remove(ctx, oss); err != nil {
		oss.Status.Phase = ObjectStoragePhaseUnkown
		return base.UnwrapReQueueError(formatS3Error(err))
	}
	return controller.Result{}, nil
}

func (o *ObjectStorageController) remove(ctx context.Context, oss *ObjectStorage) error {
	_, organizationname := base.TenantOrganizationFromScopes(oss.Scopes...)
	if organizationname == "" {
		return nil
	}
	bucketName := GetBucketName(oss)
	// remove bucket
	if _, err := o.Service.S3Client.HeadBucket(ctx, &s3.HeadBucketInput{Bucket: aws.String(bucketName)}); err != nil {
		if !IsS3StorageNotFound(err) {
			return err
		}
	} else {
		// 先删除桶下所有的对象
		if err := deleteObjects(ctx, o.Service.S3Client, bucketName); err != nil {
			return err
		}
		if _, err := o.Service.S3Client.DeleteBucket(ctx, &s3.DeleteBucketInput{Bucket: aws.String(bucketName)}); err != nil {
			return err
		}
	}
	// 不需要删除凭证
	return nil
	// remove user
	// return o.Service.Provider.RemoveUser(ctx, generateUserIDOrPolicyNameByBucketName(bucketName))
}

func deleteObjects(ctx context.Context, client *libs3.Client, bucketName string) error {
	var wg sync.WaitGroup
	objectCh := make(chan []types.ObjectIdentifier, 5)
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go deleteObjectsWorker(ctx, client, bucketName, objectCh, &wg)
	}
	err := listAndSendObjects(ctx, client, bucketName, objectCh)
	if err != nil {
		return err
	}
	// 关闭对象通道，等待所有工作 goroutine 完成
	close(objectCh)
	wg.Wait()
	return nil
}

func listAndSendObjects(ctx context.Context, svc *libs3.Client, bucket string, objectCh chan<- []types.ObjectIdentifier) error {
	var continuationToken *string
	for {
		resp, err := svc.ListObjectsV2(ctx, &s3.ListObjectsV2Input{
			Bucket:            aws.String(bucket),
			ContinuationToken: continuationToken,
		})
		if err != nil {
			return fmt.Errorf("can not list %q objects: %v", bucket, err)
		}
		objectsToDelete := make([]types.ObjectIdentifier, 0, 1000)
		for _, item := range resp.Contents {
			objectsToDelete = append(objectsToDelete, types.ObjectIdentifier{
				Key: aws.String(*item.Key),
			})
			if len(objectsToDelete) == 1000 {
				objectCh <- objectsToDelete
				objectsToDelete = make([]types.ObjectIdentifier, 0, 1000)
			}
		}
		if len(objectsToDelete) > 0 {
			objectCh <- objectsToDelete
		}
		if !aws.BoolValue(resp.IsTruncated) {
			break
		}
		continuationToken = resp.NextContinuationToken
	}
	return nil
}

func deleteObjectsWorker(ctx context.Context, svc *libs3.Client, bucket string, objectCh <-chan []types.ObjectIdentifier, wg *sync.WaitGroup) {
	defer wg.Done()
	for objectsToDelete := range objectCh {
		// 批量删除对象
		_, err := svc.DeleteObjects(ctx, &s3.DeleteObjectsInput{
			Bucket: aws.String(bucket),
			Delete: &types.Delete{
				Objects: objectsToDelete,
				Quiet:   aws.Bool(true),
			},
		})
		if err != nil {
			log.Error(err, "delete objects failed")
		}
	}
}

func IsBucketNotFound(err error) bool {
	var apie *smithyhttp.ResponseError
	if errors.As(err, &apie) {
		return apie.HTTPStatusCode() == 404
	}
	return errors.Is(err, admin.ErrNoSuchBucket) || errors.Is(err, admin.ErrNoSuchKey)
}

// formatS3Error only returns the error message for the client fault.
// it avoid return different error message for the "same" error,
// which cause controller run into infinite loop.
func formatS3Error(err error) error {
	if err == nil {
		return nil
	}
	apiErr := &smithy.GenericAPIError{}
	if errors.As(err, &apiErr) {
		switch apiErr.Fault {
		case smithy.FaultClient:
			return liberrors.NewBadRequest(apiErr.Message)
		}
	} else {
		if errors.Is(err, admin.ErrInvalidAccessKey) {
			return admin.ErrInvalidAccessKey
		}
		if errors.Is(err, admin.ErrAccessDenied) {
			return admin.ErrAccessDenied
		}
	}
	// if any other error, omit the error message
	log.Error(err, "format s3 error")
	return fmt.Errorf("failed to sync object storage")
}

func GetBucketName(oss *ObjectStorage) string {
	return oss.Name
}

func IsS3StorageNotFound(err error) bool {
	var apie *smithyhttp.ResponseError
	if errors.As(err, &apie) {
		return apie.HTTPStatusCode() == 404
	}
	return false
}

var (
	validBucketNameStrict = regexp.MustCompile(`^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$`)
	ipAddress             = regexp.MustCompile(`^(\d+\.){3}\d+$`)
)

// 桶名称校验规则
func CheckValidBucketName(bucketName string, strict bool) error {
	if strings.TrimSpace(bucketName) == "" {
		return errors.New("bucket name cannot be empty")
	}
	if len(bucketName) < 3 {
		return errors.New("bucket name cannot be shorter than 3 characters")
	}
	if len(bucketName) > 63 {
		return errors.New("bucket name cannot be longer than 63 characters")
	}
	if ipAddress.MatchString(bucketName) {
		return errors.New("bucket name cannot be an ip address")
	}
	if strings.Contains(bucketName, "..") || strings.Contains(bucketName, ".-") || strings.Contains(bucketName, "-.") {
		return errors.New("bucket name contains invalid characters")
	}
	if !validBucketNameStrict.MatchString(bucketName) {
		return errors.New("bucket name contains invalid characters")
	}
	return nil
}

func GenerateBucketPolicyFromPermission(cephtenant, bucket string, permissions []ObjectStorageCredentialReference, public bool) *iampolicy.BucketPolicy {
	policy := &iampolicy.BucketPolicy{
		Version:    iampolicy.DefaultVersion,
		Statements: make([]iampolicy.BPStatement, 0),
	}
	for _, permission := range permissions {
		principal := fmt.Sprintf("arn:aws:iam::%s:user/%s", cephtenant, permission.UserID)
		var actions []iampolicy.Action
		switch permission.Permission {
		case PermissionsManage:
			actions = append(actions, iampolicy.AllActions)
		case PermissionsReadOnly:
			actions = append(actions, iampolicy.GetBucketLocationAction, iampolicy.GetObjectAction, iampolicy.ListBucketAction)
		}
		resources := []iampolicy.Resource{
			iampolicy.NewResource(fmt.Sprintf("%s/*", bucket)),
			iampolicy.NewResource(bucket),
		}
		statement := iampolicy.NewBPStatement(
			"",
			iampolicy.Allow,
			iampolicy.NewPrincipal(principal),
			iampolicy.NewActionSet(actions...),
			iampolicy.NewResourceSet(resources...),
			condition.NewFunctions(),
		)
		policy.Statements = append(policy.Statements, statement)
	}
	if public {
		// 需要公开的存储桶，添加公开只读的policy
		policy.Statements = append(policy.Statements, getBucketPublicStatement(bucket))
	}
	return policy
}

// 公开桶的时候，需要注意mimetype，不要让浏览器直接下载
// 获取桶的公开Statement
func getBucketPublicStatement(bucketName string) iampolicy.BPStatement {
	return iampolicy.NewBPStatement(
		"public",
		iampolicy.Allow,
		iampolicy.NewPrincipal("*"),
		iampolicy.NewActionSet(iampolicy.GetObjectAction, iampolicy.ListBucketAction),
		iampolicy.NewResourceSet([]iampolicy.Resource{
			iampolicy.NewResource(fmt.Sprintf("%s/*", bucketName)),
			iampolicy.NewResource(bucketName),
		}...),
		condition.NewFunctions(),
	)
}

func HasContainesPublicPolicy(policy *iampolicy.BucketPolicy) bool {
	// for _, statement := range policy.Statements {
	// }
	return false
}
