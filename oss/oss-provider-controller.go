package oss

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
)

func NewObjectStorageProviderController(store store.Store, providerContext *ObjectProviderContext) (*controller.Controller, error) {
	rec := &ObjectStorageProviderReconciler{
		Store:           store,
		ProviderContext: providerContext,
	}
	br := controller.NewBetterReconciler(rec, store,
		controller.WithFinalizer("object-storage-provider-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.NewController("objectstorageproviders", br).Watch(
		controller.NewStoreSource(store, &ObjectStorageProvider{}),
	)
	return c, nil
}

type ObjectStorageProviderReconciler struct {
	Store           store.Store
	ProviderContext *ObjectProviderContext
}

func (r *ObjectStorageProviderReconciler) Initialize(ctx context.Context) error {
	providers := store.List[ObjectStorageProvider]{}
	if err := r.Store.List(ctx, &providers); err != nil {
		return err
	}
	for _, provider := range providers.Items {
		if err := r.ProviderContext.Sync(ctx, provider.Name, provider.Type, provider.Config); err != nil {
			log.FromContext(ctx).Error(err, "init check provider", "name", provider.Name)
		}
	}
	return nil
}

func (r *ObjectStorageProviderReconciler) Sync(ctx context.Context, obj *ObjectStorageProvider) (controller.Result, error) {
	if err := r.ProviderContext.Sync(ctx, obj.Name, obj.Type, obj.Config); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}

func (r *ObjectStorageProviderReconciler) Remove(ctx context.Context, obj *ObjectStorageProvider) (controller.Result, error) {
	if err := r.ProviderContext.Remove(ctx, obj.Name); err != nil {
		return controller.Result{}, err
	}
	return controller.Result{}, nil
}
