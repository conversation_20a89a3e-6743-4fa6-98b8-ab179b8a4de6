package oss

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"io"
	"math/rand"
	"strings"

	crand "crypto/rand"

	"github.com/minio/madmin-go"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"k8s.io/apimachinery/pkg/api/resource"
)

var _ ObjectStorageProviderClient = &MinioProviderClient{}

type MinioProviderClient struct {
	Admin *madmin.AdminClient
	Cli   *minio.Client
}

// GetBucketMetrics implements ObjectStorageProviderClient.
func (m *MinioProviderClient) GetBucketMetrics(ctx context.Context, bucketName string) (*BucketStatistics, error) {
	panic("unimplemented")
}

// GetBucketUsage implements ObjectStorageProviderClient.
func (m *MinioProviderClient) GetBucketUsage(ctx context.Context, bucketName string) (*ObjectStorageStatics, error) {
	panic("unimplemented")
}

// GetTenantID implements ObjectStorageProviderClient.
func (m *MinioProviderClient) GetTenantID() string {
	panic("unimplemented")
}

// CreateTenantUser implements ObjectStorageProviderClient.
func (m *MinioProviderClient) CreateUser(ctx context.Context, permission Permissions, username string) (*ObjectStorageUser, error) {
	panic("unimplemented")
}

// RemoveQuota implements ObjectStorageProviderClient.
func (m *MinioProviderClient) RemoveQuota(ctx context.Context, bucketName string) error {
	return nil
}

func NewMinioProviderClient(ctx context.Context, addr, accesskey, secretKey string) (*MinioProviderClient, error) {
	var endpoint string
	var secure bool
	switch {
	case strings.HasPrefix(addr, "http://"):
		endpoint, secure = strings.TrimPrefix(addr, "http://"), false
	case strings.HasPrefix(addr, "https://"):
		endpoint, secure = strings.TrimPrefix(addr, "https://"), true
	}
	cr := credentials.NewStaticV4(accesskey, secretKey, "")
	adminapi, err := madmin.NewWithOptions(endpoint, &madmin.Options{Creds: cr, Secure: secure})
	if err != nil {
		return nil, fmt.Errorf("unable to create admin client: %v", err)
	}
	ossapi, err := minio.New(endpoint, &minio.Options{Creds: cr, Secure: secure})
	if err != nil {
		return nil, err
	}
	return &MinioProviderClient{Admin: adminapi, Cli: ossapi}, nil
}

// BucketExists implements ObjectStorageProviderClient.
func (m *MinioProviderClient) BucketExists(ctx context.Context, bucketName string) (bool, error) {
	return m.Cli.BucketExists(ctx, bucketName)
}

func (m *MinioProviderClient) BucketUsageInfo(ctx context.Context, start, end, bucketName string) (*ObjectStorageInformation, error) {
	return nil, fmt.Errorf("unsupport")
}

// EnsureUser implements ObjectStorageProviderClient.
func (m *MinioProviderClient) EnsureUser(ctx context.Context, bucketName string) (*ObjectStorageUser, error) {
	userID, err := encrypt(bucketName)
	if err != nil {
		return nil, err
	}
	u, err := m.Admin.GetUserInfo(ctx, userID)
	if err != nil {
		// not found,create
		sk := generateSecretKey(16)
		if err := m.Admin.AddUser(ctx, userID, sk); err != nil {
			return nil, err
		}
		return &ObjectStorageUser{AccessKey: userID, SecretKey: sk, UserID: userID}, nil
	}
	return &ObjectStorageUser{AccessKey: userID, SecretKey: u.SecretKey, UserID: userID}, nil
}

// LinkBucketToUser implements ObjectStorageProviderClient.
func (m *MinioProviderClient) LinkBucketToUser(ctx context.Context, bucketName string) error {
	userID, err := encrypt(bucketName)
	if err != nil {
		return err
	}
	//policyName := generateUserIDOrPolicyNameByBucketName(userID)
	policyName := "todo"
	policy := `
	{
		"Version":"2012-10-17",
		"Statement":[
			{
				"Effect":"Allow",
				"Action":[
					"s3:*"
				],
				"Resource":[
					"arn:aws:s3:::%s/*"
				]
			}
		]
	}`
	policyRaw := []byte(fmt.Sprintf(policy, bucketName))
	if err := m.Admin.AddCannedPolicy(ctx, policyName, policyRaw); err != nil {
		return err
	}
	if err := m.Admin.SetPolicy(ctx, policyName, userID, false); err != nil {
		return err
	}
	return nil
}

// RemoveBucket implements ObjectStorageProviderClient.
func (m *MinioProviderClient) RemoveBucket(ctx context.Context, bucketName string) error {
	return m.Cli.RemoveBucket(ctx, bucketName)
}

// RemoveUser implements ObjectStorageProviderClient.
func (m *MinioProviderClient) RemoveUser(ctx context.Context, userID string) error {
	// if err := m.Admin.RemoveCannedPolicy(ctx, generateUserIDOrPolicyNameByBucketName(userID)); err != nil {
	// 	return err
	// }
	if err := m.Admin.RemoveCannedPolicy(ctx, "todo"); err != nil {
		return err
	}
	return m.Admin.RemoveUser(ctx, userID)
}

// EnsureQuota implements ObjectStorageProviderClient.
func (m *MinioProviderClient) EnsureQuota(ctx context.Context, bucketName string, quota resource.Quantity) error {
	maxsize := quota.Value()
	return m.Admin.SetBucketQuota(ctx, bucketName, &madmin.BucketQuota{
		Quota: uint64(maxsize),
		Type:  madmin.HardQuota,
	})
}

// State implements ObjectStorageProviderClient.
func (m *MinioProviderClient) State(ctx context.Context, bucketName string) (ObjectStorageStatics, error) {
	usage, err := m.Admin.DataUsageInfo(ctx)
	if err != nil {
		return ObjectStorageStatics{}, err
	}
	bucketUsage, ok := usage.BucketsUsage[bucketName]
	if !ok {
		return ObjectStorageStatics{}, nil
	}
	return ObjectStorageStatics{
		ObjectCount: int64(bucketUsage.ObjectsCount),
		UsedBytes:   int64(bucketUsage.Size),
	}, nil
}

const (
	letters   = "0123456789abcdefghijklmnopqrstuvwxyz"
	secretKey = "abcdedfghijlmnop"
)

func generateSecretKey(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	return string(b)
}

// 加密函数
func encrypt(plainText string) (string, error) {
	block, err := aes.NewCipher([]byte(secretKey))
	if err != nil {
		return "", err
	}
	// 填充明文
	plainTextBytes := []byte(plainText)
	blockSize := block.BlockSize()
	padding := blockSize - len(plainTextBytes)%blockSize
	paddedText := append(plainTextBytes, bytes.Repeat([]byte{byte(padding)}, padding)...)
	cipherText := make([]byte, blockSize+len(paddedText))
	iv := cipherText[:blockSize]
	if _, err := io.ReadFull(crand.Reader, iv); err != nil {
		return "", err
	}
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText[blockSize:], paddedText)
	// 使用 URL 安全的 Base64 编码
	encoded := base64.RawURLEncoding.EncodeToString(cipherText)
	return encoded, nil
}

// 解密函数
// func decrypt(cipherText string) (string, error) {
// 	cipherTextBytes, err := base64.RawURLEncoding.DecodeString(cipherText)
// 	if err != nil {
// 		return "", err
// 	}
// 	block, err := aes.NewCipher([]byte(secretKey))
// 	if err != nil {
// 		return "", err
// 	}
// 	blockSize := block.BlockSize()
// 	if len(cipherTextBytes) < blockSize {
// 		return "", fmt.Errorf("ciphertext too short")
// 	}
// 	iv := cipherTextBytes[:blockSize]
// 	cipherTextBytes = cipherTextBytes[blockSize:]

// 	mode := cipher.NewCBCDecrypter(block, iv)
// 	mode.CryptBlocks(cipherTextBytes, cipherTextBytes)
// 	// 去除填充
// 	padding := cipherTextBytes[len(cipherTextBytes)-1]
// 	cipherTextBytes = cipherTextBytes[:len(cipherTextBytes)-int(padding)]
// 	return string(cipherTextBytes), nil
// }
