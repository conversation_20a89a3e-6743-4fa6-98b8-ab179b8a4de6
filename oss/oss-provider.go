package oss

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/resource"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type ObjectStorageProvider struct {
	store.ObjectMeta `json:",inline"`
	Type             ObjectStorageProviderType   `json:"type,omitempty"`
	Config           ObjectStorageProviderConfig `json:"config,omitempty"`
	Status           ObjectStorageProviderStatus `json:"status,omitempty"`
}

type ObjectStorageProviderType string

const (
	ObjectStorageProviderTypeCephS3 ObjectStorageProviderType = "CephS3"
	ObjectStorageProviderTypeMinio  ObjectStorageProviderType = "Minio"
)

type ObjectStorageProviderConfig struct {
	Server             string            `json:"server,omitempty"`
	AccessKey          string            `json:"accessKey,omitempty"`
	SecretKey          string            `json:"secretKey,omitempty"`
	Region             string            `json:"region,omitempty"`
	PathStyle          bool              `json:"pathStyle,omitempty"`
	InsecureSkipVerify bool              `json:"insecureSkipVerify,omitempty"`
	Params             map[string]string `json:"params,omitempty"`
}

type ObjectStorageProviderStatus struct {
	Ready    bool               `json:"ready,omitempty"`
	Message  string             `json:"message,omitempty"`
	Capacity *resource.Quantity `json:"capacity,omitempty"`
}

func (a *API) ListObjectStorageProviders(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return base.GenericList(r, a.Store, &store.List[ObjectStorageProvider]{})
	})
}

func (a *API) CreateObjectStorageProvider(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		req := &ObjectStorageProvider{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		if err := a.Store.Create(ctx, req); err != nil {
			return nil, err
		}
		return req, nil
	})
}

func (a *API) GetObjectStorageProvider(w http.ResponseWriter, r *http.Request) {
	a.onProvider(w, r, func(ctx context.Context, providername string) (any, error) {
		return base.GenericGet(r, a.Store, &ObjectStorageProvider{}, providername)
	})
}

func (a *API) UpdateObjectStorageProvider(w http.ResponseWriter, r *http.Request) {
	a.onProvider(w, r, func(ctx context.Context, providername string) (any, error) {
		return base.GenericUpdate(r, a.Store, &ObjectStorageProvider{}, providername)
	})
}

func (a *API) DeleteObjectStorageProvider(w http.ResponseWriter, r *http.Request) {
	a.onProvider(w, r, func(ctx context.Context, providername string) (any, error) {
		return base.GenericDelete(r, a.Store, &ObjectStorageProvider{}, providername)
	})
}

func (a *API) onProvider(w http.ResponseWriter, r *http.Request, f func(ctx context.Context, providername string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		providername := api.Path(r, "provider", "")
		if providername == "" {
			return nil, errors.NewBadRequest("provider name is required")
		}
		return f(ctx, providername)
	})
}

func (a *API) providerGroup() api.Group {
	return api.NewGroup("/objectstorageproviders").
		Route(
			api.GET("").
				To(a.ListObjectStorageProviders).
				Operation("list object storage providers").
				Response(store.List[ObjectStorageProvider]{}),

			api.POST("").
				To(a.CreateObjectStorageProvider).
				Operation("create object storage provider").
				Param(api.BodyParam("provider", ObjectStorageProvider{})).
				Response(ObjectStorageProvider{}),

			api.GET("/{provider}").
				To(a.GetObjectStorageProvider).
				Operation("get object storage provider").
				Response(ObjectStorageProvider{}),

			api.PUT("/{provider}").
				To(a.UpdateObjectStorageProvider).
				Param(api.BodyParam("provider", ObjectStorageProvider{})).
				Operation("update object storage provider").
				Response(ObjectStorageProvider{}),

			api.DELETE("/{provider}").
				To(a.DeleteObjectStorageProvider).
				Operation("delete object storage provider"),
		)
}
