package observerability

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability/dashboard"
)

type MetricsDashboard struct {
	store.ObjectMeta `json:",inline"`
	Dashboard        dashboard.DashboradConfiguration `json:"dashboard"`
}

type QueryResponse struct {
	ShowName string `json:"showName"`
	Unit     string `json:"unit"`
	Values   any    `json:"values"`
}

func SetPrometheusMonitorDashboard(ctx context.Context, scopedstorage store.Store, name string,
	dashboard dashboard.DashboradConfiguration,
) (*MetricsDashboard, error) {
	exists := &MetricsDashboard{}
	if err := scopedstorage.Get(ctx, name, exists); err != nil {
		if !errors.IsNotFound(err) {
			return nil, err
		}
		newdashboard := &MetricsDashboard{
			ObjectMeta: store.ObjectMeta{Name: name, Description: "Auto created by system"},
			Dashboard:  dashboard,
		}
		if err := scopedstorage.Create(ctx, newdashboard); err != nil {
			return nil, err
		}
		return newdashboard, nil
	}
	exists.Dashboard = dashboard
	if err := scopedstorage.Update(ctx, exists); err != nil {
		return nil, err
	}
	return exists, nil
}

func (a *ApplicationObservabilityAPI) ListPrometheusMonitorDashboards(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store) (any, error) {
		// sort by name
		return base.GenericList(r, storage, &store.List[MetricsDashboard]{}, store.WithSort("name"))
	})
}

func (a *ApplicationObservabilityAPI) CreatePrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store) (any, error) {
		return base.GenericCreate(r, storage, &MetricsDashboard{})
	})
}

func (a *ApplicationObservabilityAPI) UpdatePrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store, dashboard string) (any, error) {
		return base.GenericUpdate(r, storage, &MetricsDashboard{}, dashboard)
	})
}

func (a *ApplicationObservabilityAPI) GetPrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store, dashboard string) (any, error) {
		return base.GenericGet(r, storage, &MetricsDashboard{}, dashboard)
	})
}

func (a *ApplicationObservabilityAPI) DeletePrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store, dashboard string) (any, error) {
		return base.GenericDelete(r, storage, &MetricsDashboard{}, dashboard)
	})
}

func (a *ApplicationObservabilityAPI) QueryPrometheusMonitorDashboard(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store, dashboardname string) (any, error) {
		app, err := application.GetApplicationFromReference(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		info, err := a.CloudInfo.Get(ctx, app.Cluster.ObjectReference)
		if err != nil {
			return nil, err
		}
		op, err := cluster.NewContainerOperation(info)
		if err != nil {
			return nil, err
		}
		config, err := a.getDashboard(ctx, storage, info, dashboardname)
		if err != nil {
			return nil, err
		}
		options := getAppRenderDashboardOptions(r, app)
		return dashboard.RenderDashboardSimple(r, op, config, options)
	})
}

func (a *ApplicationObservabilityAPI) ListDashboardParams(w http.ResponseWriter, r *http.Request) {
	a.onDashboard(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store, dashboardname string) (any, error) {
		app, err := application.GetApplicationFromReference(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		info, err := a.CloudInfo.Get(ctx, app.Cluster.ObjectReference)
		if err != nil {
			return nil, err
		}
		op, err := cluster.NewContainerOperation(info)
		if err != nil {
			return nil, err
		}
		config, err := a.getDashboard(ctx, storage, info, dashboardname)
		if err != nil {
			return nil, err
		}
		options := getAppRenderDashboardOptions(r, app)
		return dashboard.ListDashboardParams(ctx, op, config, options)
	})
}

func (a *ApplicationObservabilityAPI) getDashboard(ctx context.Context, storage store.Store, info cluster.CloudInfo, dashboardname string) (dashboard.DashboradConfiguration, error) {
	// built in dashboards are prefixed to avoid conflict with other dashboards
	record := &MetricsDashboard{}
	if err := storage.Get(ctx, dashboardname, record); err != nil {
		if errors.IsNotFound(err) {
			builtindashboardname := dashboard.GetBuiltApplicationDashboardName(info.Type(), dashboardname)
			if builtin, ok := a.BuiltInDashboards[builtindashboardname]; ok {
				return builtin, nil
			}
		}
		return dashboard.DashboradConfiguration{}, err
	}
	return record.Dashboard, nil
}

func getAppRenderDashboardOptions(r *http.Request, app *application.Application) dashboard.RenderDashbosrdOptions {
	rls := application.GetRelease(app)
	params := map[string]string{
		"instance":  rls.Name,
		"namespace": rls.Namespace,
		// some dashboard use service or job as "instance"
		"job":     rls.Name,
		"service": rls.Name,
		// ingress
		"controller_namespace": rls.Namespace,
		"controller_class":     "gateway.xiaoshiai.cn/" + rls.Name,
	}
	return dashboard.GetRenderDashboardOptions(r, params)
}

func (a *ApplicationObservabilityAPI) onDashboard(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, appref store.ObjectReference, storage store.Store, dashboard string) (any, error)) {
	a.OnApplication(w, r, func(ctx context.Context, appref store.ObjectReference, storage store.Store) (any, error) {
		dashboard := api.Path(r, "dashboard", "")
		if dashboard == "" {
			return nil, errors.NewBadRequest("monitor dashboard name is required")
		}
		return fn(ctx, appref, storage, dashboard)
	})
}

var MetricsDashboradParams = []api.Param{
	api.PathParam("dashboard", "monitor dashboard name").Def("basic"),
	api.QueryParam("from", "start time").Format("datatime").Optional(),
	api.QueryParam("to", "end time").Format("datatime").Optional(),
	api.QueryParam("step", "step duration").Format("duration").Optional(),
	api.QueryParam("params", "query parameters,params='pod=instance-abc-def,foo=bar'").Optional(),
}

func (a *ApplicationObservabilityAPI) ApplicationsMetricsDashboardGroup() api.Group {
	return api.NewGroup("/metrics-dashboards").
		Route(
			api.GET("").Operation("list monitor dashboards").
				To(a.ListPrometheusMonitorDashboards).
				Param(base.PageParams...).
				Response(store.List[MetricsDashboard]{}),

			api.POST("").Operation("import monitor dashboard").
				To(a.CreatePrometheusMonitorDashboard).
				Param(api.BodyParam("dashboard", MetricsDashboard{})).
				Response(MetricsDashboard{}),

			api.GET("/{dashboard}").Operation("get monitor dashboard").
				To(a.GetPrometheusMonitorDashboard).
				Response(MetricsDashboard{}),

			api.GET("/{dashboard}/query").Operation("query monitor dashboard").
				To(a.QueryPrometheusMonitorDashboard).
				Param(MetricsDashboradParams...).
				Response(QueryResponse{}),

			api.GET("/{dashboard}/params").Operation("list available params").
				To(a.ListDashboardParams).
				Response([]dashboard.DashboardParam{}),

			api.PUT("/{dashboard}").Operation("update monitor dashboard").
				To(a.UpdatePrometheusMonitorDashboard).
				Param(api.BodyParam("dashboard", MetricsDashboard{})).
				Response(MetricsDashboard{}),

			api.DELETE("/{dashboard}").Operation("delete monitor dashboard").
				To(a.DeletePrometheusMonitorDashboard),
		)
}
