package observerability

import (
	"context"
	"path"
	"strings"

	"helm.sh/helm/v3/pkg/chart"
	"xiaoshiai.cn/common"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability/dashboard"
)

const AnnotationMetricsDashboardVersion = common.GroupPrefix + "/metrics-dashboard-version"

// NewAutoCreateDashboardFromApplication auto create dashboard from application
// it parse the application chart and create the dashboard
func NewAutoCreateDashboardFromApplication(storage store.Store, cloudinfo cluster.CloudInfoGetter, chart artifact.ChartsProvider) (*controller.Controller, error) {
	rec := &ApplicationDashboardController{
		Storage: storage,
		Clouds:  cloudinfo,
		Charts:  chart,
	}
	c := controller.
		NewController("metrics-dashboard-autocreat", rec).
		Watch(controller.NewStoreSource(storage, &application.Application{}))
	return c, nil
}

type ApplicationDashboardController struct {
	Storage store.Store
	Clouds  cluster.CloudInfoGetter
	Charts  artifact.ChartsProvider
}

func (a *ApplicationDashboardController) Reconcile(ctx context.Context, key controller.ScopedKey) (controller.Result, error) {
	return base.UnwrapReQueueError(a.reconcile(ctx, key))
}

func (a *ApplicationDashboardController) reconcile(ctx context.Context, key controller.ScopedKey) error {
	app := &application.Application{}
	if err := a.Storage.Scope(key.Scopes()...).Get(ctx, key.Name, app); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	annnotations := app.Annotations
	if annnotations == nil {
		annnotations = map[string]string{}
	}
	if app.Product.Version == "" {
		return nil
	}
	curentversion, desiredversion := annnotations[AnnotationMetricsDashboardVersion], app.Product.Version
	if curentversion == desiredversion {
		return nil
	}
	scopedstorage := a.Storage.Scope(key.Scopes()...).Scope(base.ScopeApplication(app.Name))
	if err := a.InstallApplicationDashboards(ctx, scopedstorage, app, desiredversion); err != nil {
		return err
	}
	jsonpointer := store.JSONPointerEscape(AnnotationMetricsDashboardVersion)
	patch := store.RawPatch(store.PatchTypeJSONPatch, []byte(`[{"op":"add","path":"/annotations/`+jsonpointer+`","value":"`+desiredversion+`"}]`))
	if err := a.Storage.Scope(key.Scopes()...).Patch(ctx, app, patch); err != nil {
		return err
	}
	return nil
}

func (a *ApplicationDashboardController) InstallApplicationDashboards(ctx context.Context, scopedstorage store.Store, app *application.Application, version string) error {
	chart, err := a.Charts.DownloadChart(ctx, app.Product.Name, app.Product.Version)
	if err != nil {
		return err
	}
	dashboards, err := ParseHelmChartDashboard(ctx, chart)
	if err != nil {
		return err
	}
	for name, dashboard := range dashboards {
		if _, err := SetPrometheusMonitorDashboard(ctx, scopedstorage, name, dashboard); err != nil {
			return err
		}
	}
	return nil
}

// ParseHelmChartDashboard
// it import "grafana" dashboard from helm chart's dashboards folder
// and parse it to metricsdashboard.DashboradConfiguration
func ParseHelmChartDashboard(ctx context.Context, ch *chart.Chart) (map[string]dashboard.DashboradConfiguration, error) {
	log := log.FromContext(ctx)
	dashboards := map[string]dashboard.DashboradConfiguration{}
	for _, file := range ch.Raw {
		if !strings.HasPrefix(file.Name, "dashboards/") {
			continue
		}
		filename, ext := splitFileExtension(path.Base(file.Name))
		if ext != ".json" && ext != ".yaml" {
			continue
		}
		config, err := dashboard.LoadDashboardConfiguration(file.Data)
		if err != nil {
			log.Error(err, "parse dashboard error", "file", file.Name)
			continue
		}
		dashboards[filename] = *config
	}
	return dashboards, nil
}

func splitFileExtension(path string) (string, string) {
	idx := strings.LastIndexByte(path, '.')
	if idx < 0 {
		return path, ""
	}
	return path[:idx], path[idx:]
}
