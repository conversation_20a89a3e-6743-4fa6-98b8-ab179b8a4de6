package observerability

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"time"

	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability/alertchannel"
	"xiaoshiai.cn/core/resourcequota"
)

func NewAlertRuleController(storage store.Store, mongoStore store.Store, cloudinfo cluster.CloudInfoGetter) (*controller.Controller, error) {
	rec := &AlertRuleReconciler{
		Store:      storage,
		MongoStore: mongoStore,
		Clouds:     cloudinfo,
	}
	better := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("alertrule-controller"),
	)
	c := controller.
		NewController("alertrules", better).
		Watch(
			controller.NewStoreSource(storage, &AlertRule{}),
			controller.NewCustomStoreSource(storage, "resourcequotas", rec.onResourceQuotaSet),
			//controller.NewCustomStoreSource(storage, "clusters", rec.onClusterSet),
		)
	return c, nil
}

func tenantOrganizationAppFromScopes(scopes []store.Scope) (cluster, tenantname, organizationname, app string) {
	for _, scope := range scopes {
		if scope.Resource == "clusters" {
			cluster = scope.Name
		}
		if scope.Resource == "tenants" {
			tenantname = scope.Name
		}
		if scope.Resource == "organizations" {
			organizationname = scope.Name
		}
		if scope.Resource == "applications" {
			app = scope.Name
		}
	}
	return
}

// 自动创建k8s集群的节点离线的告警规则
func (t *AlertRuleReconciler) onClusterSet(ctx context.Context, kind store.WatchEventType, obj store.Object) ([]controller.ScopedKey, error) {
	ref := store.ObjectReference{
		Name:   obj.GetName(),
		Scopes: obj.GetScopes(),
	}
	info, err := t.Clouds.Get(ctx, ref)
	if err != nil {
		return nil, err
	}
	if cluster.ClusterCategoryFrom(info.Type()) != cluster.ClusterCategoryContainer {
		return nil, nil
	}

	storage := t.Store.Scope(ref.Scopes...).Scope(base.ScopeCluster(ref.Name))
	rules := &store.List[AlertRule]{}
	if err := storage.List(ctx, rules); err != nil {
		return nil, err
	}
	ruleName := "auto-generate-node-offline"
	for _, rule := range rules.Items {
		if rule.Name == ruleName {
			return nil, nil
		}
	}
	//not found,so just create it
	rule := &AlertRule{
		ObjectMeta: store.ObjectMeta{
			Name: ruleName,
		},
		AlertLevels: []AlertLevel{
			{
				CompareOp:    "==",
				CompareValue: "0",
				Severity:     "critical",
			},
		},
		For: "1m",
		PromqlGenerator: &PromqlGenerator{
			TemplateGroup:         "Cluster",
			TemplateGroupResource: "Kubernetes",
			TemplateShowName:      "Node Offline",
			TemplateType:          "cluster",
			Resource:              "node",
			Expr:                  `kube_node_status_condition{condition="Ready",status="true"}`,
			Unit:                  "",
			Labels:                []string{"node"},
		},
	}
	return nil, storage.Create(ctx, rule)
}

func (t *AlertRuleReconciler) onResourceQuotaSet(ctx context.Context, kind store.WatchEventType, obj store.Object) ([]controller.ScopedKey, error) {
	tenantname, organizationname := base.TenantOrganizationFromScopes(obj.GetScopes()...)
	// not a tenant scoped resource quota
	if tenantname == "" || organizationname != "" {
		return nil, nil
	}
	resourcequota := &resourcequota.ResourceQuota{}
	if err := t.Store.Scope(obj.GetScopes()...).Get(ctx, obj.GetName(), resourcequota); err != nil {
		return nil, err
	}
	// notify all alert rules for this tenant
	list := store.List[AlertRule]{}
	if err := t.Store.Scope(base.ScopeTenant(tenantname)).List(ctx, &list); err != nil {
		return nil, err
	}
	keys := make([]controller.ScopedKey, 0, len(list.Items))
	for _, rule := range list.Items {
		keys = append(keys, controller.ScopedKeyFromObject(&rule))
	}
	return keys, nil
}

var _ controller.Reconciler[*AlertRule] = &AlertRuleReconciler{}

type AlertRuleReconciler struct {
	Store      store.Store
	MongoStore store.Store
	Clouds     cluster.CloudInfoGetter
}

// Sync implements controller.Reconciler.
func (t *AlertRuleReconciler) Sync(ctx context.Context, rule *AlertRule) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, rule, base.ScopeHandler[*AlertRule]{
		OnTenant:                        t.syncTenantRule,
		OnTenantOrganization:            t.syncTenantOrganizationRule,
		OnTenantOrganizationApplication: t.syncApplicationRule,
		OnTenantOrganizationApplicationSubApplication: t.syncSubApplicationRule,
		OnCluster:       t.syncClusterRule,
		OnTenantCluster: t.syncTenantClusterRule,
	})
}

func publishPrometheusRuleAndAlertConfig(ctx context.Context, op cluster.ContainerOperation, isApp bool, rule *AlertRule, ns string, labels map[string][]string) error {
	log := log.FromContext(ctx)
	cli := op.Info.Client
	if rule.Close {
		silenceID, err := silenceAlert(ctx, op, rule, 300*24*time.Hour)
		if err != nil {
			return err
		}
		rule.SilenceID = silenceID
		return nil
	} else {
		// 关闭静默
		if rule.SilenceID != "" {
			if err := cancelSilenceAlert(ctx, op, rule.SilenceID); err != nil {
				log.Error(err, "failed to cancel silence")
				return err
			}
			rule.SilenceID = ""
			return nil
		}
	}
	// check ns exists
	nsobj := &corev1.Namespace{}
	if err := cli.Get(ctx, client.ObjectKey{Name: ns}, nsobj); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
		log.Info("create ns", "ns", ns)
		if err := cli.Create(ctx, &corev1.Namespace{ObjectMeta: metav1.ObjectMeta{Name: ns}}); err != nil {
			return err
		}
	}
	// 告警规则
	prule := generatePrometheusRule(rule, ns)
	_, err := controllerutil.CreateOrUpdate(ctx, cli, prule, func() error {
		prule.Spec.Groups = []monitoringv1.RuleGroup{generatePromethuesRuleGroup(rule, isApp, ns, labels)}
		return nil
	})
	if err != nil {
		return err
	}
	// 告警配置和通知渠道
	amc := generateAlertManagerConfig(rule, ns)
	_, err = controllerutil.CreateOrUpdate(ctx, cli, amc, func() error {
		amc.Spec = generateAlertManagerConfigSpec(rule, isApp, ns, labels)
		return nil
	})
	return err
}

type AlertManagerSilence struct {
	Matchers  []AlertManagerMatchers `json:"matchers"`
	StartsAt  time.Time              `json:"startsAt"`
	EndsAt    time.Time              `json:"endsAt"`
	CreatedBy string                 `json:"createdBy"`
	Comment   string                 `json:"comment"`
	ID        string                 `json:"id"`
}
type AlertManagerMatchers struct {
	Name    string `json:"name"`
	Value   string `json:"value"`
	IsRegex bool   `json:"isRegex"`
	IsEqual bool   `json:"isEqual"`
}

type AlertManagerSilenceResp struct {
	SilenceID string `json:"silenceID"`
}

// 静默告警
func silenceAlert(ctx context.Context, op cluster.ContainerOperation, rule *AlertRule, duration time.Duration) (string, error) {
	existSilence := &AlertManagerSilence{}
	err := op.AlertManagerGetSilence(ctx, rule.SilenceID, existSilence)
	if err == nil {
		// exist
		return existSilence.ID, nil
	}

	current := time.Now()
	resourceName := rule.GetKubernetesResourceName()
	as := AlertManagerSilence{
		Matchers: []AlertManagerMatchers{
			{
				Name:    "alertname",
				Value:   resourceName,
				IsEqual: true,
				IsRegex: false,
			},
		},
		StartsAt:  current,
		EndsAt:    current.Add(duration),
		CreatedBy: "admin",
		Comment:   "bob silence",
	}
	silenceJSON, err := json.Marshal(as)
	if err != nil {
		return "", fmt.Errorf("failed to marshal silence: %w", err)
	}
	resp := &AlertManagerSilenceResp{}

	if err := op.AlertManagerSilence(ctx, bytes.NewReader(silenceJSON), resp); err != nil {
		return "", err
	}
	return resp.SilenceID, nil
}

// 取消静默
func cancelSilenceAlert(ctx context.Context, op cluster.ContainerOperation, id string) error {
	return op.AlertManagerCancelSilence(ctx, id)
}

func removePrometheusRuleAndAlertConfig(ctx context.Context, op cluster.ContainerOperation, rule *AlertRule, ns string) error {
	// 清理该规则对应的alertmanager静默规则
	cli := op.Info.Client
	_, err := silenceAlert(ctx, op, rule, time.Hour)
	if err != nil {
		return err
	}
	// defer func() {
	// 	if err := cancelSilenceAlert(ctx, op, silenceID); err != nil {
	// 		log.Error(err, "cancel silence alert", "silenceID", rule.SilenceID)
	// 	}
	// }()
	prule := generatePrometheusRule(rule, ns)
	if err := cli.Delete(ctx, prule); err != nil {
		if !apierrors.IsNotFound(err) {
			return err
		}
	}
	amc := generateAlertManagerConfig(rule, ns)
	return client.IgnoreNotFound(cli.Delete(ctx, amc))
}

// 租户的规则需要下发到所管理的所有集群
func (t *AlertRuleReconciler) syncTenantRule(ctx context.Context, tenantname string, rule *AlertRule) (controller.Result, error) {
	_, _, _ = ctx, tenantname, rule
	return controller.Result{}, nil
}

// 组织和租户都需要通过quota获取集群，才能下发rule
func (t *AlertRuleReconciler) syncTenantOrganizationRule(ctx context.Context, tenantname, orgname string, rule *AlertRule) (controller.Result, error) {
	_, _, _, _ = ctx, tenantname, orgname, rule
	return controller.Result{}, nil
}

func (t *AlertRuleReconciler) syncSubApplicationRule(ctx context.Context, tenantname, orgname, app, appname string, rule *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.syncRule(ctx, rule))
}

func (t *AlertRuleReconciler) syncApplicationRule(ctx context.Context, tenantname, orgname, appname string, rule *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.syncRule(ctx, rule))
}

func (t *AlertRuleReconciler) syncRule(ctx context.Context, rule *AlertRule) error {
	app, err := application.GetApplicationFromScopes(ctx, t.Store, rule.Scopes)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	// 获取应用的标签
	var appLabels map[string][]string
	info, err := t.Clouds.Get(ctx, app.Cluster.ObjectReference)
	if err != nil {
		return err
	}
	switch cluster.ClusterCategoryFrom(info.Type()) {
	case cluster.ClusterCategoryVirtualMachine:
		// 虚拟机应用
		vmResources := app.Status.Cloud.Resources
		if len(vmResources) == 0 {
			// no resource deploy
			return base.ReQueue(10 * time.Second)
		}
		appLabels = make(map[string][]string)
		for _, vmResource := range vmResources {
			switch vmResource.Resource {
			case "virtualmachines":
				appLabels["vm"] = append(appLabels["vm"], vmResource.ID)
			case "disks":
				appLabels["disk"] = append(appLabels["disk"], vmResource.ID)
			}
		}
	default:
		// 容器应用
		appLabels = appFilterWithMap(app)

	}
	// 告警规则应该只匹配有该标签的的资源指标
	op, err := cluster.NewContainerOperation(info)
	if err != nil {
		return err
	}
	return publishPrometheusRuleAndAlertConfig(ctx, op, true, rule, app.Cluster.Namespace, appLabels)
}

func (t *AlertRuleReconciler) syncTenantClusterRule(ctx context.Context, tenant, clustername string, rule *AlertRule) (controller.Result, error) {
	ref := store.ObjectReference{
		Scopes: []store.Scope{base.ScopeTenant(tenant)},
		Name:   clustername,
	}
	return base.UnwrapReQueueError(t.syncClusterRuleRef(ctx, ref, rule))
}

func (t *AlertRuleReconciler) syncClusterRule(ctx context.Context, clustername string, rule *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.syncClusterRuleRef(ctx, store.ObjectReference{Name: clustername}, rule))
}

func (t *AlertRuleReconciler) syncClusterRuleRef(ctx context.Context, clusterref store.ObjectReference, rule *AlertRule) error {
	cloud, err := t.Clouds.Get(ctx, clusterref)
	if err != nil {
		return err
	}
	op, err := cluster.NewContainerOperation(cloud)
	if err != nil {
		return err
	}
	return publishPrometheusRuleAndAlertConfig(ctx, op, false, rule, "kubegems-monitoring", nil)
}

// Remove implements controller.Reconciler.
func (t *AlertRuleReconciler) Remove(ctx context.Context, rule *AlertRule) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, rule, base.ScopeHandler[*AlertRule]{
		OnTenant:                        t.removeTenantRule,
		OnTenantOrganization:            t.removeTenantOrganizationRule,
		OnTenantOrganizationApplication: t.removeApplicationRule,
		OnTenantOrganizationApplicationSubApplication: t.removeSubApplicationRule,
		OnCluster:       t.removeClusterRule,
		OnTenantCluster: t.removeTenantClusterRule,
	})
}

func (t *AlertRuleReconciler) removeTenantRule(ctx context.Context, tenantname string, rule *AlertRule) (controller.Result, error) {
	_, _, _ = ctx, tenantname, rule
	return controller.Result{}, nil
}

func (t *AlertRuleReconciler) removeTenantOrganizationRule(ctx context.Context, tenantname, orgname string, rule *AlertRule) (controller.Result, error) {
	_, _, _, _ = ctx, tenantname, orgname, rule
	return controller.Result{}, nil
}

func (t *AlertRuleReconciler) removeSubApplicationRule(ctx context.Context, tenant, org, app, subapp string, rule *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.removeRule(ctx, rule))
}

func (t *AlertRuleReconciler) removeApplicationRule(ctx context.Context, tenant, org, app string, obj *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.removeRule(ctx, obj))
}

func (t *AlertRuleReconciler) removeRule(ctx context.Context, rule *AlertRule) error {
	log.Info("recived remove rule", "name", rule.Name)
	defer func() {
		if err := t.removeRuleAssociationRecords(ctx, rule.Scopes, rule.Name); err != nil {
			log.Error(err, "remove rule association records failed")
		}
	}()
	// 删除告警规则在集群中的配置
	application, err := application.GetApplicationFromScopes(ctx, t.Store, rule.Scopes)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	cloud, err := t.Clouds.Get(ctx, application.Cluster.ObjectReference)
	if err != nil {
		return err
	}
	op, err := cluster.NewContainerOperation(cloud)
	if err != nil {
		return err
	}
	if err := removePrometheusRuleAndAlertConfig(ctx, op, rule, application.Cluster.Namespace); err != nil {
		return err
	}
	return nil
}

func (t *AlertRuleReconciler) removeTenantClusterRule(ctx context.Context, tenantname, clustername string, rule *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.removeClusterRuleRef(ctx, store.ObjectReference{Scopes: []store.Scope{base.ScopeTenant(tenantname)}, Name: clustername}, rule))
}

func (t *AlertRuleReconciler) removeClusterRule(ctx context.Context, clustername string, rule *AlertRule) (controller.Result, error) {
	return base.UnwrapReQueueError(t.removeClusterRuleRef(ctx, store.ObjectReference{Name: clustername}, rule))
}

func (t *AlertRuleReconciler) removeClusterRuleRef(ctx context.Context, clusterref store.ObjectReference, rule *AlertRule) error {
	log.Info("recived remove cluster rule", "name", rule.Name)
	defer func() {
		if err := t.removeRuleAssociationRecords(ctx, rule.Scopes, rule.Name); err != nil {
			log.Error(err, "remove cluster rule association records failed")
		}
	}()
	// 删除告警规则在集群中的配置
	cloud, err := t.Clouds.Get(ctx, clusterref)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	op, err := cluster.NewContainerOperation(cloud)
	if err != nil {
		return err
	}
	if err := removePrometheusRuleAndAlertConfig(ctx, op, rule, "kubegems-monitoring"); err != nil {
		return err
	}
	return nil
}

// 删除规则关联的告警记录
func (t *AlertRuleReconciler) removeRuleAssociationRecords(ctx context.Context, scope []store.Scope, rule string) error {
	return t.MongoStore.DeleteBatch(ctx, &store.List[AlertRecord]{},
		store.WithDeleteBatchLabelRequirements(store.RequirementEqual(alertchannel.AlertOriginNameLabel, rule)),
		store.WithDeleteBatchFieldRequirements(GetAlertRecordFieldsWithScopes(scope)...),
	)
	// storage := t.Store.Scope(scope...)
	// var (
	// 	labelrequirements store.Requirements
	// 	opt               []store.ListOption
	// 	records           = &store.List[AlertRecord]{}
	// )
	// labelrequirements = append(labelrequirements, store.RequirementEqual(alertchannel.AlertOriginNameLabel, rule))
	// opt = append(opt, store.WithLabelRequirements(labelrequirements...))
	// if err := storage.List(ctx, records, opt...); err != nil {
	// 	return err
	// }
	// var errs []error
	// for i := range records.Items {
	// 	item := &records.Items[i]
	// 	if err := storage.Delete(ctx, item); err != nil {
	// 		log.Error(err, "delete alert record failed", "record", item.Name)
	// 		errs = append(errs, err)
	// 	}
	// }
	// return errors.NewAggregate(errs)
}
