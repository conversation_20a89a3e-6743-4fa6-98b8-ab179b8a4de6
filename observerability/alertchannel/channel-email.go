package alertchannel

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"net/smtp"
	"net/url"
	"strconv"
	"strings"
	"text/template"
	"time"

	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/log"
)

var _ AlertChannel = &Email{}

type Email struct {
	SMTPServer   string `json:"smtpServer"`
	RequireTLS   bool   `json:"requireTLS"`
	From         string `json:"from"`
	AuthPassword string `json:"authPassword"`
}

func (e *Email) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeEmail))
	q.Add("smtpServer", e.SMTPServer)
	q.Add("from", e.From)
	q.Add("to", strings.Join(target, ","))
	q.Add("authPassword", e.AuthPassword)
	q.Add("tls", strconv.FormatBool(e.RequireTLS))
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

func (e *Email) ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver {
	u := e.formatURL(target)
	httpConfig := &v1alpha1.HTTPConfig{}
	if useBasicAuth {
		httpConfig.BasicAuth = &monitoringv1.BasicAuth{
			Username: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthUserName,
			},
			Password: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthPassword,
			},
		}
	}
	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
				HTTPConfig:   httpConfig,
			},
		},
	}
}

func (e *Email) Check() error {
	return nil
}

func (e *Email) Test(alert AlertProxyWebhookAlert, target []string) error {
	q := url.Values{}
	q.Add("smtpServer", e.SMTPServer)
	q.Add("from", e.From)
	q.Add("to", strings.Join(target, ","))
	q.Add("tls", fmt.Sprintf("%v", e.RequireTLS))
	q.Add("authPassword", e.AuthPassword)
	for _, al := range alert.Alerts {
		if err := e.DoRequest(q, al, PlatformTypeBOB); err != nil {
			return err
		}
	}
	return nil
}

var _ AlertProxy = &Email{}

// DoRequest implements AlertProxy.
func (e *Email) DoRequest(params url.Values, alert AlertProxyMessage, platform PlatformType) error {
	log.FromContext(context.Background()).Info("alertname", "name", alert.Labels[AlertOriginNameLabel])
	smtpServer := params.Get("smtpServer")
	from := params.Get("from")
	to := params.Get("to")
	tlsStr := params.Get("tls")
	authPassword := params.Get("authPassword")
	enableTls, err := strconv.ParseBool(tlsStr)
	if err != nil {
		return err
	}

	newEmail := &Email{
		SMTPServer:   smtpServer,
		RequireTLS:   enableTls,
		From:         from,
		AuthPassword: authPassword,
	}

	obj := struct {
		Notify NotifyTemplate
	}{
		Notify: alert.GetCommonNotifyTemplate(),
	}

	//生成html模版
	tmpl := template.Must(template.New("email").Parse(e.Template(platform)))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, obj); err != nil {
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), 13*time.Second)
	defer cancel()
	done := make(chan error, 1)
	go func() {
		done <- newEmail.sendEmail(fmt.Sprintf("%s Alert", string(platform)), buf.String(), strings.Split(to, ","))
	}()
	select {
	case err := <-done:
		if err != nil {
			if isQQSmtpError(err) {
				return nil
			}
			return err
		}
	case <-ctx.Done():
		return fmt.Errorf("your email server is responding slowly. It is recommended to check your email configuration or email service provider status")
	}
	return nil
}

func isQQSmtpError(err error) bool {
	return strings.HasPrefix(err.Error(), "short response")
}

func (e *Email) sendEmail(subject, body string, to []string) error {
	if e.SMTPServer == "" || e.From == "" || e.AuthPassword == "" {
		return fmt.Errorf("missing required email parameters")
	}
	// 设置默认端口
	host, port := splitServerAddress(e.SMTPServer)
	// 构建邮件内容
	message := fmt.Sprintf("From: %s\r\n", e.From) +
		fmt.Sprintf("To: %s\r\n", strings.Join(to, ",")) +
		fmt.Sprintf("Subject: %s\r\n", subject) +
		"Content-Type: text/html; charset=UTF-8\r\n\r\n" +
		string(body)

	// 认证信息
	auth := smtp.PlainAuth("", e.From, e.AuthPassword, host)
	// 发送邮件
	if !e.RequireTLS {
		// 普通连接
		return smtp.SendMail(fmt.Sprintf("%s:%s", host, port), auth, e.From, to, []byte(message))
	}

	// 使用TLS连接
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
		ServerName:         host,
	}
	conn, err := tls.Dial("tcp", fmt.Sprintf("%s:%s", host, port), tlsConfig)
	if err != nil {
		return fmt.Errorf("failed to dial SMTP server with TLS: %v", err)
	}
	defer conn.Close()
	client, err := smtp.NewClient(conn, host)
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %v", err)
	}
	defer client.Close()
	if err = client.Auth(auth); err != nil {
		return fmt.Errorf("SMTP authentication failed: %v", err)
	}
	if err = client.Mail(e.From); err != nil {
		return fmt.Errorf("failed to set sender: %v", err)
	}
	for _, addr := range to {
		if err = client.Rcpt(addr); err != nil {
			return fmt.Errorf("failed to set recipient %s: %v", addr, err)
		}
	}
	w, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to get data writer: %v", err)
	}
	_, err = w.Write([]byte(message))
	if err != nil {
		return fmt.Errorf("failed to write message: %v", err)
	}
	err = w.Close()
	if err != nil {
		return fmt.Errorf("failed to close data writer: %v", err)
	}
	return client.Quit()
}
func splitServerAddress(server string) (host, port string) {
	parts := strings.Split(server, ":")
	if len(parts) == 2 {
		return parts[0], parts[1]
	}
	return server, "25" // 默认SMTP端口
}

func (e *Email) Template(platform PlatformType) string {
	return fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: #333;
      padding: 20px;
      line-height: 1.6;
      background-color: #f5f5f5;
      margin: 0;
    }
    .container {
      max-width: 650px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .header {
      background-color: {{ if eq .Notify.Status "firing" }}#dc3545{{ else }}#28a745{{ end }};
      color: white;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 25px;
    }
    .alert-status {
      display: inline-block;
      padding: 8px 16px;
      border-radius: 4px;
      font-weight: bold;
      margin-bottom: 20px;
      background-color: {{ if eq .Notify.Status "firing" }}#f8d7da{{ else }}#d4edda{{ end }};
      color: {{ if eq .Notify.Status "firing" }}#721c24{{ else }}#155724{{ end }};
      border: 1px solid {{ if eq .Notify.Status "firing" }}#f5c6cb{{ else }}#c3e6cb{{ end }};
    }
    .section {
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 15px;
    }
    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .section-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #0056b3;
      font-size: 16px;
    }
    .info-table {
      width: 100%%;
      border-collapse: collapse;
    }
    .info-table td {
      padding: 8px;
      vertical-align: top;
    }
    .info-table td:first-child {
      width: 140px;
      font-weight: bold;
      color: #555;
    }
    .footer {
      text-align: center;
      padding: 15px;
      font-size: 12px;
      color: #777;
      background-color: #f8f9fa;
      border-top: 1px solid #eee;
    }
    .footer a {
      color: #0056b3;
      text-decoration: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>🔥 [%s Observability] - {{ .Notify.TemplateShowName }} {{ .Notify.Action }} {{ .Notify.Threshold }}{{ .Notify.Unit }}</h1>
    </div>
    
    <div class="content">
      <!-- Alert Status Section -->
      <div class="section">
        <div class="alert-status">
          The alarm of your {{ if ne .Notify.TenantID "" }}Enterprise {{ .Notify.TenantID }} {{ else }}Cluster {{ .Notify.ClusterID }} {{ end }} has been {{ if eq .Notify.Status "firing" }}triggered{{ else }}recovery{{ end }}
        </div>
      </div>
      
      <!-- Platform and Resource Info -->
      <div class="section">
        <div class="section-title">Platform & Alert</div>
        <table class="info-table">
          <tr>
            <td>Platform:</td>
            <td>%s</td>
          </tr>
          <tr>
            <td>Alert Object:</td>
            <td>{{ .Notify.AlertObject }}</td>
          </tr>
          <tr>
            <td>Alert Rule:</td>
            <td>{{ .Notify.AlertName }}</td>
          </tr>
		  <tr>
            <td>Alert Level:</td>
            <td>{{ .Notify.Level }}</td>
          </tr>
        </table>
      </div>
      
      <!-- Alert Condition and Value -->
      <div class="section">
        <div class="section-title">Alert Metrics</div>
        <table class="info-table">
          <tr>
            <td>Condition:</td>
            <td>{{ .Notify.TemplateShowName }} {{ .Notify.Action }} {{ .Notify.Threshold }}{{ .Notify.Unit }}</td>
          </tr>
          <tr>
            <td>Current Value:</td>
            <td>{{ .Notify.CurrentValue }}{{ .Notify.Unit }}</td>
          </tr>
        </table>
      </div>
      
      <!-- Time Information -->
      <div class="section">
        <div class="section-title">Time Information</div>
        <table class="info-table">
          <tr>
            <td>Trigger Time:</td>
            <td>{{ .Notify.StartTime }}</td>
          </tr>
          <tr>
            <td>Duration:</td>
            <td>{{ .Notify.KeepTime }}</td>
          </tr>
          {{ if ne .Notify.ResovedTime "" }}
          <tr>
            <td>Recovery Time:</td>
            <td>{{ .Notify.ResovedTime }}</td>
          </tr>
          {{ end }}
        </table>
      </div>

	  <!-- Message Information -->
      <div class="section">
        <div class="section-title">Message Information</div>
        <table class="info-table">
          <tr>
            <td>Alert Message:</td>
            <td>{{ .Notify.Message }}</td>
          </tr>
        </table>
      </div>
    </div>
    <div class="footer">
      <p>You can login to the %s Platform to view the alert history</p>
    </div>
  </div>
</body>
</html>`, string(platform), string(platform), string(platform))
}
