package alertchannel

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"strconv"
	"strings"
	"text/template"
	"time"

	"net/smtp"
	"net/textproto"
	"net/url"

	"github.com/jordan-wright/email"
	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/log"
)

var _ AlertChannel = &Email{}

type Email struct {
	SMTPServer   string `json:"smtpServer"`
	RequireTLS   bool   `json:"requireTLS"`
	From         string `json:"from"`
	AuthPassword string `json:"authPassword"`
}

func (e *Email) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeEmail))
	q.Add("smtpServer", e.SMTPServer)
	q.Add("from", e.From)
	q.Add("to", strings.Join(target, ","))
	q.Add("authPassword", e.AuthPassword)
	q.Add("tls", strconv.FormatBool(e.RequireTLS))
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

func (e *Email) ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver {
	u := e.formatURL(target)
	httpConfig := &v1alpha1.HTTPConfig{}
	if useBasicAuth {
		httpConfig.BasicAuth = &monitoringv1.BasicAuth{
			Username: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthUserName,
			},
			Password: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthPassword,
			},
		}
	}
	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
				HTTPConfig:   httpConfig,
			},
		},
	}
}

func (e *Email) Check() error {
	return nil
}

func (e *Email) Test(alert AlertProxyWebhookAlert, target []string) error {
	if len(alert.Alerts) == 0 {
		return fmt.Errorf("no alert")
	}
	tmpl := template.Must(template.New("email").Parse(e.Template(PlatformTypeBOB)))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, alert.Alerts[0]); err != nil {
		return err
	}

	mail := &email.Email{
		From:    e.From,
		To:      target,
		Subject: "Bob test email",
		HTML:    buf.Bytes(),
		Headers: textproto.MIMEHeader{},
	}
	host, _, err := net.SplitHostPort(e.SMTPServer)
	if err != nil {
		return err
	}
	ctx, cancel := context.WithTimeout(context.Background(), 13*time.Second)
	defer cancel()
	done := make(chan error, 1)
	go func() {
		if e.RequireTLS {
			done <- mail.SendWithTLS(e.SMTPServer, smtp.PlainAuth("", e.From, e.AuthPassword, host),
				&tls.Config{InsecureSkipVerify: false, ServerName: host})
		} else {
			done <- mail.Send(e.SMTPServer, smtp.PlainAuth("", e.From, e.AuthPassword, host))
		}
	}()
	select {
	case err := <-done:
		if err != nil {
			return err
		}
	case <-ctx.Done():
		return fmt.Errorf("your email server is responding slowly. It is recommended to check your email configuration or email service provider status")
	}
	return nil
}

var _ AlertProxy = &Email{}

// DoRequest implements AlertProxy.
func (e *Email) DoRequest(params url.Values, alert AlertProxyMessage, platform PlatformType) error {
	log.FromContext(context.Background()).Info("alertname", "name", alert.Labels[AlertOriginNameLabel])
	smtpServer := params.Get("smtpServer")
	from := params.Get("from")
	to := params.Get("to")
	tlsStr := params.Get("tls")
	authPassword := params.Get("authPassword")
	enableTls, err := strconv.ParseBool(tlsStr)
	if err != nil {
		return err
	}
	//生成html模版
	tmpl := template.Must(template.New("email").Parse(e.Template(platform)))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, alert); err != nil {
		return err
	}

	mail := &email.Email{
		From:    from,
		To:      strings.Split(to, ","),
		Subject: fmt.Sprintf("%s Alert", string(platform)),
		HTML:    buf.Bytes(),
		Headers: textproto.MIMEHeader{},
	}
	host, _, err := net.SplitHostPort(smtpServer)
	if err != nil {
		return err
	}
	if enableTls {
		return mail.SendWithTLS(smtpServer, smtp.PlainAuth("", from, authPassword, host),
			&tls.Config{InsecureSkipVerify: false, ServerName: host})
	}
	return mail.Send(smtpServer, smtp.PlainAuth("", from, authPassword, host))
}
func (e *Email) Template(platform PlatformType) string {
	return fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      color: #333;
      padding: 20px;
      line-height: 1.6;
      background-color: #f5f5f5;
      margin: 0;
    }
    .container {
      max-width: 650px;
      margin: 0 auto;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .header {
      background-color: #0056b3;
      color: white;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 25px;
    }
    .alert-status {
      display: inline-block;
      padding: 6px 12px;
      border-radius: 4px;
      font-weight: bold;
      margin-bottom: 15px;
    }
    .alert-firing {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .alert-resolved {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .section {
      margin-bottom: 20px;
      border-bottom: 1px solid #eee;
      padding-bottom: 15px;
    }
    .section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    .section-title {
      font-weight: bold;
      margin-bottom: 10px;
      color: #0056b3;
    }
    .info-table {
      width: 100vw;
      border-collapse: collapse;
    }
    .info-table td {
      padding: 8px;
      vertical-align: top;
    }
    .info-table td:first-child {
      width: 140px;
      font-weight: bold;
      color: #555;
    }
    .labels-list, .annotations-list {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    .labels-list li, .annotations-list li {
      padding: 5px 0;
      border-bottom: 1px dashed #eee;
    }
    .labels-list li:last-child, .annotations-list li:last-child {
      border-bottom: none;
    }
    .value-highlight {
      font-size: 18px;
      font-weight: bold;
      color: #e63946;
      background-color: #f8f9fa;
      padding: 8px;
      border-radius: 4px;
      display: inline-block;
    }
    .footer {
      text-align: center;
      padding: 15px;
      font-size: 12px;
      color: #777;
      background-color: #f8f9fa;
      border-top: 1px solid #eee;
    }
    .footer a {
      color: #0056b3;
      text-decoration: none;
    }
    .time-info {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      margin-top: 15px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Majnoon %s Group</h1>
    </div>
    
    <div class="content">
      <!-- Alert Status Section -->
      <div class="section">
        <div class="alert-status {{ if eq .Status "firing" }}alert-firing{{ else }}alert-resolved{{ end }}">
          Status: {{ .Status }}
        </div>
        
        <div class="time-info">
          <table class="info-table">
            <tr>
              <td>Started At:</td>
              <td>{{ .StartsAt.Format "2006-01-02 15:04:05" }}</td>
            </tr>
            {{ if and .EndsAt (not .EndsAt.IsZero) }}
            <tr>
              <td>Resolved At:</td>
              <td>{{ .EndsAt.Format "2006-01-02 15:04:05" }}</td>
            </tr>
            {{ end }}
          </table>
        </div>
      </div>
      
      <!-- Alert Details Section -->
      <div class="section">
        <div class="section-title">Alert Details</div>
        <table class="info-table">
          {{ if .Labels.gems_originname }}
          <tr>
            <td>Alert Name:</td>
            <td>{{ .Labels.gems_originname }}</td>
          </tr>
          {{ end }}
          {{ if .Annotations.bob_templategroup }}
          <tr>
            <td>Template Group:</td>
            <td>{{ .Annotations.bob_templategroup }}</td>
          </tr>
          {{ end }}
          {{ if .Labels.cluster }}
          <tr>
            <td>Cluster:</td>
            <td>{{ .Labels.cluster }}</td>
          </tr>
          {{ end }}
          {{ if .Labels.namespace }}
          <tr>
            <td>Namespace:</td>
            <td>{{ .Labels.namespace }}</td>
          </tr>
          {{ end }}
        </table>
      </div>
      
      <!-- Alert Metrics Section -->
      <div class="section">
        <div class="section-title">Metrics Information</div>
        <table class="info-table">
          {{ if .Annotations.bob_alertaction }}
          <tr>
            <td>Condition:</td>
            <td>{{ .Annotations.bob_alertaction }} {{ .Annotations.bob_alerthreshold }}{{ .Annotations.bob_alertunit }}</td>
          </tr>
          {{ end }}
          {{ if .Annotations.value }}
          <tr>
            <td>Current Value:</td>
            <td><span class="value-highlight">{{ .Annotations.value }}{{ .Annotations.bob_alertunit }}</span></td>
          </tr>
          {{ end }}
        </table>
      </div>
      
      <!-- All Labels Section -->
      <div class="section">
        <div class="section-title">All Labels</div>
        <ul class="labels-list">
          {{ range $key, $value := .Labels }}
          <li><strong>{{ $key }}:</strong> {{ $value }}</li>
          {{ end }}
        </ul>
      </div>
      
      <!-- All Annotations Section -->
      <div class="section">
        <div class="section-title">All Annotations</div>
        <ul class="annotations-list">
          {{ range $key, $value := .Annotations }}
          {{ if ne $key "value" }}
          <li><strong>{{ $key }}:</strong> {{ $value }}</li>
          {{ end }}
          {{ end }}
        </ul>
      </div>
    </div>
    
    <div class="footer">
      <p>You can login to the %s Platform  to view the detailed alert</p>
    </div>
  </div>
</body>
</html>`, string(platform), string(platform))
}
