package alertchannel

import (
	"net/url"
	"testing"
	"time"
)

var feishu = &Feishu{
	URL:        "https://open.feishu.cn/open-apis/bot/v2/hook/fc2a8bab-2e6f-4ae5-b6a6-b28489343232",
	SignSecret: "qkG1gci7Vqv8AfzBM6fbsh",
}

// 发送应用的告警通知
func TestSendAppAlertFeishu(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeFeishu))
	q.Add("url", feishu.URL)
	q.Add("at", "all")
	q.Add("signSecret", feishu.SignSecret)

	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "firing",
		Labels: map[string]string{
			AlertScopeTenant:      "abc",
			AlertOriginNameLabel:  "test-feishu",
			AlertScopeApplication: "mds",
			AlertClusterKey:       "test-cluster",
			"namespace":           "default",
			"pod":                 "1-nginx-7fc6f6f695-blz4d",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Container CPU Usage Ratio",
			AlertResource:          "pod",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "20",
		},
		StartsAt: &start,
	}
	if err := feishu.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

// 发送应用的告警恢复
func TestSendAppRecoverdFeishu(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeFeishu))
	q.Add("url", feishu.URL)
	q.Add("at", "all")
	q.Add("signSecret", feishu.SignSecret)

	now := time.Now()
	start := now.Add(-2 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "resolved",
		Labels: map[string]string{
			AlertScopeTenant:      "abc",
			AlertOriginNameLabel:  "test-feishu",
			AlertScopeApplication: "mds",
			AlertClusterKey:       "test-cluster",
			"namespace":           "default",
			"pod":                 "1-nginx-7fc6f6f695-blz4d",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Container CPU Usage Ratio",
			AlertResource:          "pod",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "5",
		},
		StartsAt: &start,
		EndsAt:   &now,
	}
	if err := feishu.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}

// 发送集群的告警通知
func TestSendClusterAlertFeishu(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeFeishu))
	q.Add("url", feishu.URL)
	q.Add("at", "all")
	q.Add("signSecret", feishu.SignSecret)

	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "firing",
		Labels: map[string]string{
			AlertOriginNameLabel: "test-feishu",
			ALertScopeCluster:    "my-cluster",
			AlertClusterKey:      "test-cluster",
			//"namespace":          "default",
			"node": "k8s-node1",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Node CPU Usage Ratio",
			AlertResource:          "node",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "20",
		},
		StartsAt: &start,
	}
	if err := feishu.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

// 发送集群的恢复通知
func TestSendClusterRecoverdFeishu(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeFeishu))
	q.Add("url", feishu.URL)
	q.Add("at", "all")
	q.Add("signSecret", feishu.SignSecret)

	now := time.Now()
	start := now.Add(-2 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "resolved",
		Labels: map[string]string{
			AlertOriginNameLabel: "test-feishu",
			ALertScopeCluster:    "my-cluster",
			AlertClusterKey:      "test-cluster",
			"node":               "k8s-node1",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Node CPU Usage Ratio",
			AlertResource:          "node",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "5",
		},
		StartsAt: &start,
		EndsAt:   &now,
	}
	if err := feishu.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}
