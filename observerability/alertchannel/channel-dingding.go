package alertchannel

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"text/template"
	"time"

	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/log"
)

var _ AlertChannel = &Dingding{}

type Dingding struct {
	URL string `json:"url"` // Dingding robot webhook url
	//AtMobiles  []string `json:"atMobiles"`  // 要@的用户手机号
	SignSecret string `json:"signSecret"` // 签名校验key
}

func (f *Dingding) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeDingding))
	q.Add("url", f.URL)
	q.Add("atMobiles", strings.Join(target, ","))
	q.Add("signSecret", f.SignSecret)
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

func (f *Dingding) ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver {
	u := f.formatURL(target)
	httpConfig := &v1alpha1.HTTPConfig{}
	if useBasicAuth {
		httpConfig.BasicAuth = &monitoringv1.BasicAuth{
			Username: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthUserName,
			},
			Password: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthPassword,
			},
		}
	}
	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
				HTTPConfig:   httpConfig,
			},
		},
	}
}

func (f *Dingding) Check() error {
	if !strings.Contains(f.URL, "oapi.dingtalk.com") {
		return fmt.Errorf("Dingding robot url not valid")
	}
	return nil
}

func (f *Dingding) Test(alert AlertProxyWebhookAlert, target []string) error {
	q := url.Values{}
	q.Add("url", f.URL)
	q.Add("atMobiles", strings.Join(target, ","))
	q.Add("signSecret", f.SignSecret)
	for _, al := range alert.Alerts {
		if err := f.DoRequest(q, al, PlatformTypeBOB); err != nil {
			return err
		}
	}
	return nil
}

var _ AlertProxy = &Dingding{}

func (f *Dingding) Template(platform PlatformType) string {
	return fmt.Sprintf(`
		{
          "at": {
              "atMobiles":[{{ range $index, $element := .AtMobiles }}{{if $index}},{{end}}"{{ $element }}"{{ end }}],
              "isAtAll": false
          },
          "markdown": {
              "title":"🔥 [%s告警中心] - {{ .AlertProxyMessage.Labels.gems_alertname }}",
              "text": "## 🔥 [%s告警中心] - {{ .AlertProxyMessage.Labels.gems_alertname }} \n\n 🕐 开始时间: {{ .AlertProxyMessage.StartsAt.Format "2006-01-02T15:04:05Z07:00" }} \n\n {{ if eq .AlertProxyMessage.Labels.severity "critical" }}🟣{{ else }}🔴{{ end }} 级别: {{ .AlertProxyMessage.Labels.severity }} \n\n ☸️ 集群: {{ .AlertProxyMessage.Labels.cluster }} \n\n 🏠 命名空间: {{ .AlertProxyMessage.Labels.gems_namespace }} \n\n {{ if eq .AlertProxyMessage.Status "firing"  }}🔴{{ else }}🟢{{ end }} 状态: {{ .AlertProxyMessage.Status }} \n\n {{ .AlertProxyMessage.Annotations.message }} \n\n {{ range $index, $element := .AtMobiles }}{{if $index}} {{end}}@{{ $element }}{{ end }}"
          },
          "msgtype":"markdown"
      }
	`, string(platform), string(platform))
}

func (f *Dingding) DoRequest(params url.Values, alert AlertProxyMessage, platform PlatformType) error {
	log.FromContext(context.Background()).Info("alertname", "name", alert.Labels[AlertOriginNameLabel])
	obj := struct {
		AlertProxyMessage
		// for template
		AtMobiles []string
	}{
		AlertProxyMessage: alert,
	}
	u, err := url.Parse(params.Get("url"))
	if err != nil {
		return err
	}
	query := u.Query()
	if params.Get("atMobiles") != "" {
		obj.AtMobiles = strings.Split(params.Get("atMobiles"), ",")
	}
	if params.Get("signSecret") != "" {
		t := time.Now() // 钉钉要求毫秒
		sign, err := genDingdingSign(params.Get("signSecret"), t.UnixMilli())
		if err != nil {
			return err
		}
		query.Add("sign", sign)
		query.Add("timestamp", fmt.Sprintf("%v", t.UnixMilli()))
	}
	u.RawQuery = query.Encode()
	tmpl := template.Must(template.New("dingding").Parse(f.Template(platform)))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, obj); err != nil {
		return err
	}
	req, err := http.NewRequest(http.MethodPost, u.String(), buf)
	if err != nil {
		return err
	}
	req.URL.Query()
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	b := struct {
		Errcode int    `json:"errcode"`
		Errmsg  string `json:"errmsg"`
	}{}
	if err := json.NewDecoder(resp.Body).Decode(&b); err != nil {
		return err
	}
	if b.Errcode != 0 {
		return fmt.Errorf("send to dingding robot failed, code: %d, msg: %s", b.Errcode, b.Errmsg)
	}
	return nil
}
func genDingdingSign(secret string, timestamp int64) (string, error) {
	//timestamp + key 做sha256, 再进行base64 encode
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	h := hmac.New(sha256.New, []byte(secret))
	_, err := h.Write([]byte(stringToSign))
	if err != nil {
		return "", err
	}
	signature := url.QueryEscape(base64.StdEncoding.EncodeToString(h.Sum(nil)))
	return signature, nil
}
