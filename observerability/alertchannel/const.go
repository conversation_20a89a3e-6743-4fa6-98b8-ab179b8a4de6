package alertchannel

const (
	//prometheus-rule
	LabelPrometheusRuleName = "prometheusrule.kubegems.io/name"
	MessageAnnotationsKey   = "message"
	ValueAnnotationKey      = "value"
	ValueAnnotationExpr     = `{{ $value | printf "%.2f" }}`

	AlertNamespaceLabel    = "gems_namespace"
	AlertNameLabel         = "gems_alertname"
	AlertOriginNameLabel   = "gems_originname"
	AlertSeverityLabel     = "severity"
	AlertSeverityError     = "error"    // 错误
	AlertSeverityCritical  = "critical" // 严重
	AlertTemplateGroupName = "bob_templategroup"
	AlertActionKey         = "bob_alertaction"
	AlertThreshold         = "bob_alerthreshold"
	AlertUnit              = "bob_alertunit"
	AlertResource          = "bob_alertresource"

	AlertScopeTenant       = "bob_tenant"
	AlertScopeOrganization = "bob_organization"
	AlertScopeApplication  = "bob_application"
	AlertScopeCluster      = "bob_cluster"

	//alertmanager
	LabelAlertmanagerConfigName      = "alertmanagerconfig.kubegems.io/name"
	AlertManagerConfigWaitOrInternal = "30s"

	//email
	EmailSecretName       = "bobcloud-email-password"
	EmailSecretLabelKey   = "bobcloud"
	EmailSecretLabelValue = "email-secret"
	AlertClusterKey       = "cluster"
)
