package alertchannel

import (
	"net/url"
	"testing"
	"time"
)

var teams = &MSTeams{
	URL: "https://antonoilfieldservices.webhook.office.com/webhookb2/5aef4c1c-071a-4dce-9ec2-d29b7411376c@83da2142-8a19-4b00-b616-ca2c9959e5b0/IncomingWebhook/d9ee5477ba1f4249a15c8a657a44323c/2227e8a4-2dd1-42f7-aae4-2d5e1200a815/V2-MOlydFYGNipA4Y51gEvtJKfNv7NqOLXwQ79k-TTgCM1",
}

func TestSendAppMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "firing",
		Labels: map[string]string{
			AlertScopeTenant:      "abc",
			AlertOriginNameLabel:  "test-msteams",
			AlertScopeApplication: "mds",
			AlertClusterKey:       "test-cluster",
			"namespace":           "default",
			"pod":                 "1-nginx-7fc6f6f695-blz4d",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Container CPU Usage Ratio",
			AlertResource:          "pod",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "20",
		},
		StartsAt: &start,
	}
	if err := teams.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

// 发送应用的告警恢复
func TestSendAppRecoverdMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-2 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "resolved",
		Labels: map[string]string{
			AlertScopeTenant:      "abc",
			AlertOriginNameLabel:  "test-msteams",
			AlertScopeApplication: "mds",
			AlertClusterKey:       "test-cluster",
			"namespace":           "default",
			"pod":                 "1-nginx-7fc6f6f695-blz4d",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Container CPU Usage Ratio",
			AlertResource:          "pod",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "5",
		},
		StartsAt: &start,
		EndsAt:   &now,
	}
	if err := teams.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}

// 发送集群的告警通知
func TestSendClusterAlertMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "firing",
		Labels: map[string]string{
			AlertOriginNameLabel: "test-msteams",
			ALertScopeCluster:    "my-cluster",
			AlertClusterKey:      "test-cluster",
			//"namespace":          "default",
			"node": "k8s-node1",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Node CPU Usage Ratio",
			AlertResource:          "node",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "20",
		},
		StartsAt: &start,
	}
	if err := teams.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")
}

// 发送集群的恢复通知
func TestSendClusterRecoverdMSTeams(t *testing.T) {
	q := url.Values{}
	q.Add("type", string(TypeMSTeams))
	q.Add("url", teams.URL)
	now := time.Now()
	start := now.Add(-2 * time.Hour)
	alertObj := AlertProxyMessage{
		Status: "resolved",
		Labels: map[string]string{
			AlertOriginNameLabel: "test-msteams",
			ALertScopeCluster:    "my-cluster",
			AlertClusterKey:      "test-cluster",
			"node":               "k8s-node1",
		},
		Annotations: map[string]string{
			AlertTemplateGroupName: "Node CPU Usage Ratio",
			AlertResource:          "node",
			AlertActionKey:         ">=",
			AlertThreshold:         "10",
			AlertUnit:              "%",
			ValueAnnotationKey:     "5",
		},
		StartsAt: &start,
		EndsAt:   &now,
	}
	if err := teams.DoRequest(q, alertObj, PlatformTypeBOB); err != nil {
		t.Fatal(err)
	}
	t.Log("success")

}
