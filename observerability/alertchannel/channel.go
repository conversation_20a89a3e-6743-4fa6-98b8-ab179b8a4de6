package alertchannel

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	"xiaoshiai.cn/core/base"
)

const (
	//考虑边缘侧是否能访问中心侧的url
	InternalHttpServiceAddress = "ismc-agent.ismc:80/alert/receive" //test
	BasicAuthSecretName        = "alert-secret"
	BasicAuthUserName          = "username"
	BasicAuthPassword          = "password"
)

type ChannelType string

const (
	TypeWebhook     ChannelType = "webhook"
	TypeEmail       ChannelType = "email"
	TypeFeishu      ChannelType = "feishu"
	TypeDingding    ChannelType = "dingding"
	TypeAliyunMsg   ChannelType = "aliyunMsg"
	TypeAliyunVoice ChannelType = "aliyunVoice"
	TypeMSTeams     ChannelType = "msteams"
)

type PlatformType string

const (
	PlatformTypeBOB  PlatformType = "BOB"
	PlatformTypeISMC PlatformType = "ISMC"
)

type AlertChannel interface {
	ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver
	Check() error
	Test(alert AlertProxyWebhookAlert, target []string) error
}

func BoolPointer(b bool) *bool {
	return &b
}

func StringPointer(s string) *string {
	return &s
}

type AlertProxyMessage struct {
	Status       string            `json:"status"`
	Labels       map[string]string `json:"labels"`
	Annotations  map[string]string `json:"annotations"`
	StartsAt     *time.Time        `json:"startsAt"`
	EndsAt       *time.Time        `json:"endsAt"`
	GeneratorURL string            `json:"generatorURL"`
	Fingerprint  string            `json:"fingerprint"`
}

type NotifyTemplate struct {
	Status           string `json:"status"`           // firing或resolved
	TenantID         string `json:"tenantID"`         //abc
	ClusterID        string `json:"clusterID"`        //aaa
	AlertName        string `json:"alertName"`        // disk_usage
	TemplateShowName string `json:"templateShowName"` // Huawei VirtualMachine CPU Usage
	Action           string `json:"action"`           // >
	Level            string `json:"level"`
	Cluster          string `json:"cluster"`      //aaa
	Namespace        string `json:"namespace"`    //default
	Threshold        string `json:"threshold"`    //20
	Resource         string `json:"resource"`     //pvc
	Unit             string `json:"unit"`         //%
	CurrentValue     string `json:"currentValue"` //30
	AlertObject      string `json:"alertObject"`  // instanceName
	StartTime        string `json:"startTime"`    // 2024-01-01 00:00:00
	KeepTime         string `json:"keepTime"`     // 222222
	ResovedTime      string `json:"resovedTime"`  // 2024-01-01 00:00:00
	Message          string `json:"message"`
}

// 生成通用的告警通知模版
func (a AlertProxyMessage) GetCommonNotifyTemplate() NotifyTemplate {
	now := time.Now().In(base.GetCustomeTimeZone())
	duration := now.Sub(*a.StartsAt)
	resourceDef := a.Annotations[AlertResource]
	nt := NotifyTemplate{
		Status:           a.Status,
		TenantID:         a.Labels[AlertScopeTenant],
		ClusterID:        a.Labels[AlertScopeCluster],
		AlertName:        a.Labels[AlertOriginNameLabel],
		TemplateShowName: a.Annotations[AlertTemplateGroupName],
		Action:           a.Annotations[AlertActionKey],
		Level:            a.Labels[AlertSeverityLabel],
		Cluster:          a.Labels[AlertClusterKey],
		Namespace:        a.Labels["namespace"],
		Threshold:        a.Annotations[AlertThreshold],
		Resource:         a.Labels[resourceDef],
		Unit:             a.Annotations[AlertUnit],
		CurrentValue:     a.Annotations[ValueAnnotationKey],
		StartTime:        a.StartsAt.Format(time.DateTime),
		KeepTime:         DurationToHumanReadable(duration),
		Message:          a.Annotations[MessageAnnotationsKey],
	}
	var builder strings.Builder
	if tenant := a.Labels[AlertScopeTenant]; tenant != "" {
		builder.WriteString(tenant)
		if org := a.Labels[AlertScopeOrganization]; org != "" {
			builder.WriteString(" | " + org)
			if app := a.Labels[AlertScopeApplication]; app != "" {
				builder.WriteString(" | " + app)
			}
		} else if cluster := a.Labels[AlertScopeCluster]; cluster != "" {
			builder.WriteString(" | " + cluster)
		}
	} else if cluster := a.Labels[AlertScopeCluster]; cluster != "" {
		builder.WriteString(cluster)
	}
	nt.AlertObject = builder.String()
	if a.EndsAt != nil {
		if !a.EndsAt.IsZero() {
			nt.ResovedTime = a.EndsAt.Format(time.DateTime)
		}
	}
	return nt
}

// 支持使用alert-proxy发送告警实现该接口
type AlertProxy interface {
	Template(platform PlatformType) string
	DoRequest(params url.Values, alert AlertProxyMessage, platform PlatformType) error
}

type AlertProxyWebhookAlert struct {
	Receiver          string              `json:"receiver"`
	Status            string              `json:"status"`
	Alerts            []AlertProxyMessage `json:"alerts"`
	GroupLabels       map[string]string   `json:"groupLabels"`
	CommonLabels      map[string]string   `json:"commonLabels"`
	CommonAnnotations map[string]string   `json:"commonAnnotations"`
	ExternalURL       string              `json:"externalURL"`
	Version           string              `json:"version"`
	GroupKey          string              `json:"groupKey"`
	// TruncatedAlerts   int64               `json:"truncatedAlerts"`
}

func DurationToHumanReadable(d time.Duration) string {
	years := int(d / (365 * 24 * time.Hour))
	d -= time.Duration(years) * 365 * 24 * time.Hour
	months := int(d / (30 * 24 * time.Hour))
	d -= time.Duration(months) * 30 * 24 * time.Hour
	days := int(d / (24 * time.Hour))
	d -= time.Duration(days) * 24 * time.Hour
	hours := int(d / time.Hour)
	d -= time.Duration(hours) * time.Hour
	minutes := int(d / time.Minute)
	d -= time.Duration(minutes) * time.Minute
	seconds := int(d / time.Second)

	var parts []string
	if years > 0 {
		parts = append(parts, fmt.Sprintf("%d years", years))
	}
	if months > 0 {
		parts = append(parts, fmt.Sprintf("%d months", months))
	}
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%d days", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%d hours", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%d minutes", minutes))
	}
	if seconds > 0 {
		parts = append(parts, fmt.Sprintf("%d seconds", seconds))
	}

	return strings.Join(parts, " ")
}
