package alertchannel

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"text/template"
	"time"

	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	v1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/log"
)

var _ AlertChannel = &Feishu{}

type Feishu struct {
	URL string `json:"url"` // feishu robot webhook url
	//At         []string `json:"at"`         // 要@的用户id，所有人则是 all
	SignSecret string `json:"signSecret"` // 签名校验key
}

func (f *Feishu) formatURL(target []string) string {
	q := url.Values{}
	q.Add("type", string(TypeFeishu))
	q.Add("url", f.URL)
	q.Add("at", strings.Join(target, ","))
	q.Add("signSecret", f.SignSecret)
	return fmt.Sprintf("http://%s?%s", InternalHttpServiceAddress, q.Encode())
}

func (f *Feishu) ToReceiver(name string, useBasicAuth bool, target []string) v1alpha1.Receiver {
	u := f.formatURL(target)
	httpConfig := &v1alpha1.HTTPConfig{}
	if useBasicAuth {
		httpConfig.BasicAuth = &monitoringv1.BasicAuth{
			Username: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthUserName,
			},
			Password: v1.SecretKeySelector{
				LocalObjectReference: v1.LocalObjectReference{
					Name: BasicAuthSecretName,
				},
				Key: BasicAuthPassword,
			},
		}
	}

	return v1alpha1.Receiver{
		Name: name,
		WebhookConfigs: []v1alpha1.WebhookConfig{
			{
				URL:          &u,
				SendResolved: BoolPointer(true),
				HTTPConfig:   httpConfig,
			},
		},
	}
}

func (f *Feishu) Check() error {
	if !strings.Contains(f.URL, "open.feishu.cn") {
		return fmt.Errorf("feishu robot url not valid")
	}
	return nil
}

func (f *Feishu) Test(alert AlertProxyWebhookAlert, target []string) error {
	q := url.Values{}
	q.Add("type", string(TypeFeishu))
	q.Add("url", f.URL)
	q.Add("at", strings.Join(target, ","))
	q.Add("signSecret", f.SignSecret)
	for _, al := range alert.Alerts {
		if err := f.DoRequest(q, al, PlatformTypeBOB); err != nil {
			return err
		}
	}
	return nil
}

var _ AlertProxy = &Feishu{}

func (f *Feishu) Template(platform PlatformType) string {
	return fmt.Sprintf(`
	{
        {{ if ne .Sign "" }}
        "timestamp": "{{ .Timestamp }}",
        "sign": "{{ .Sign }}",
        {{ end }}
        "msg_type": "interactive",
        "card": {
          "config": {
            "wide_screen_mode": true
          },
          "elements": [
            {
              "fields": [
			    {
                  "is_short": false,
                  "text": {
                    "content": "The alarm of your ({{ if ne .Notify.TenantID "" }}Enterprise:{{ .Notify.TenantID }}{{ else }}Cluster:{{ .Notify.ClusterID }}{{ end }}) has been {{ if eq .Notify.Status "firing" }}triggered{{ else }}recovery{{ end }}",
                    "tag": "lark_md"
                  }
                },
				{
                  "tag": "div"
                },
				{
                  "is_short": false,
                  "text": {
                    "content": "Current: {{ .Notify.CurrentValue }}{{ .Notify.Unit }}({{ .Notify.TemplateShowName }})",
                    "tag": "lark_md"
                  }
                },
				{
                  "is_short": false,
                  "text": {
                    "content": "Current: {{ .Notify.CurrentValue }}{{ .Notify.Unit }}({{ .Notify.TemplateShowName }})",
                    "tag": "lark_md"
                  }
                },
				{
                  "is_short": false,
                  "text": {
                    "content": "Alert Rule: {{ .Notify.AlertName }}",
                    "tag": "lark_md"
                  }
                },
				{
                  "is_short": false,
                  "text": {
                    "content": "Alert Object: {{ .Notify.AlertObject }} | {{ .Notify.Resource }} | {{ .Notify.Cluster }} {{ if ne .Notify.Namespace "" }}| {{ .Notify.Namespace }}{{ end }}",
                    "tag": "lark_md"
                  }
                },
				{
                  "tag": "div"
                },
                {
                  "is_short": false,
                  "text": {
                    "content": "Trigger Time: {{ .Notify.StartTime  }}",
                    "tag": "lark_md"
                  }
                },
                {
                  "is_short": false,
                  "text": {
                    "content": "Duration: {{ .Notify.KeepTime }}",
                    "tag": "lark_md"
                  }
                }{{ if ne .Notify.ResovedTime "" }},
				{
                  "is_short": false,
                  "text": {
                    "content": "Recovery Time: {{ .Notify.ResovedTime }}",
                    "tag": "lark_md"
                  }
                }{{ end }},
				{
                  "is_short": false,
                  "text": {
                    "content": "You can login to the %s Platform  to view the detailed alert",
                    "tag": "lark_md"
                  }
                }{{ $length := len .At }}{{ if gt $length 0 }},
                {
                  "is_short": true,
                  "text": {
                    "content": "{{ range .At }}<at id={{ . }}></at>{{ end }}",
                    "tag": "lark_md"
                  }
                }
                {{- end -}}
              ],
			  "tag": "div"
            }
          ],
          "header": {
            "template": "{{ if eq .Notify.Status "firing" }}red{{ else }}green{{ end }}",
            "title": {
              "content": "🔥 [%s Observability] - {{ .Notify.TemplateShowName }} {{ .Notify.Action }} {{ .Notify.Threshold }}{{ .Notify.Unit }}",
              "tag": "plain_text"
            }
          }
        }
      }
	`, string(platform), string(platform))
}

// DoRequest implements AlertProxy.
func (f *Feishu) DoRequest(params url.Values, alert AlertProxyMessage, platform PlatformType) error {
	log.FromContext(context.Background()).Info("alertname", "name", alert.Labels[AlertOriginNameLabel])

	obj := struct {
		//Alert AlertProxyMessage
		// for template
		Notify    NotifyTemplate
		At        []string
		Timestamp int64
		Sign      string
	}{
		//Alert: alert,
		Notify: alert.GetCommonNotifyTemplate(),
	}
	if params.Get("at") != "" {
		obj.At = strings.Split(params.Get("at"), ",")
	}
	if params.Get("signSecret") != "" {
		obj.Timestamp = time.Now().Unix()
		sign, err := genFeishuSign(params.Get("signSecret"), obj.Timestamp)
		if err != nil {
			return err
		}
		obj.Sign = sign
	}
	tmpl := template.Must(template.New("feishu").Parse(f.Template(platform)))
	buf := bytes.NewBuffer([]byte{})
	if err := tmpl.Execute(buf, obj); err != nil {
		return err
	}

	req, err := http.NewRequest(http.MethodPost, params.Get("url"), buf)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	var result FeishuResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}
	if result.Code != 0 {
		return fmt.Errorf("request failed with code %d: %s", result.Code, result.Msg)
	}
	return nil
}

type FeishuResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
	// 兼容字段
	StatusCode    int    `json:"StatusCode"`
	StatusMessage string `json:"StatusMessage"`
}

func genFeishuSign(secret string, timestamp int64) (string, error) {
	//timestamp + key 做sha256, 再进行base64 encode
	stringToSign := fmt.Sprintf("%v", timestamp) + "\n" + secret
	var data []byte
	h := hmac.New(sha256.New, []byte(stringToSign))
	_, err := h.Write(data)
	if err != nil {
		return "", err
	}
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return signature, nil
}
