package observerability

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
)

func (a *ApplicationObservabilityAPI) ApplicationOpenTelementry(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		subpath := api.Path(r, "path", "")
		return op.OpenTelementryProxy(w, r, subpath)
	})
}

func (a *ApplicationObservabilityAPI) ApplicationsOpenTelementryGroup() api.Group {
	return api.
		NewGroup("otel").
		Route(
			api.GET("").To(base.Redirect).Doc("Redirect to the opentelemetry"),
			api.Any("/{path}*").To(a.ApplicationOpenTelementry).Doc("Redirect to the opentelemetry"),
		)
}
