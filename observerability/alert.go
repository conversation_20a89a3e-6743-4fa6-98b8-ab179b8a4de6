package observerability

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	monitoringv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1alpha1"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/observerability/alertchannel"
)

var defaultClusterWebhook = "default-cluster-webhook"

func generatePrometheusRule(rule *AlertRule, namespace string) *monitoringv1.PrometheusRule {
	resourceName := rule.GetKubernetesResourceName()
	prule := &monitoringv1.PrometheusRule{
		TypeMeta: metav1.TypeMeta{
			Kind:       "PrometheusRule",
			APIVersion: "monitoring.coreos.com/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceName,
			Namespace: namespace,
			Labels: map[string]string{
				alertchannel.LabelPrometheusRuleName: resourceName,
			},
		},
	}
	return prule
}

func generateScopeLabels(scopes []store.Scope) map[string]string {
	// 获取告警规则的范围，方便在告警历史总分类
	cluster, tenantname, organizationname, appname := tenantOrganizationAppFromScopes(scopes)
	scopeLabels := make(map[string]string)
	if tenantname != "" {
		scopeLabels[alertchannel.AlertScopeTenant] = tenantname
	}
	if cluster != "" {
		scopeLabels[alertchannel.ALertScopeCluster] = cluster
	}
	if organizationname != "" {
		scopeLabels[alertchannel.AlertScopeOrganization] = organizationname
	}
	if appname != "" {
		scopeLabels[alertchannel.AlertScopeApplication] = appname
	}
	return scopeLabels
}

func generatePromethuesRuleGroup(rule *AlertRule, isApp bool, ns string, labels map[string][]string) monitoringv1.RuleGroup {
	resourceName := rule.GetKubernetesResourceName()
	group := monitoringv1.RuleGroup{
		Name: resourceName,
	}
	useTemplate := rule.PromqlGenerator != nil
	var (
		unitValue        string
		templateShowName = rule.Expr
		resource         = "pod"
	)
	var expression = rule.Expr
	if useTemplate {
		expression = rule.PromqlGenerator.Expr
		unit, err := alertchannel.ParseUnit(rule.PromqlGenerator.Unit)
		if err == nil {
			unitValue = unit.Show
		}
		templateShowName = rule.PromqlGenerator.TemplateShowName
		resource = rule.PromqlGenerator.Resource
	}
	if isApp {
		if strings.Contains(expression, "#") {
			var namespace string
			namespaces := labels["namespace"]
			if len(namespaces) > 0 {
				namespace = fmt.Sprintf(`namespace="%s"`, namespaces[0])
			}
			expression = strings.Replace(expression, "#", namespace, -1)
		} else {
			var parts []string
			for key, value := range labels {
				if len(value) == 1 {
					parts = append(parts, fmt.Sprintf(`%s="%s"`, key, value[0]))
				} else {
					values := "(" + strings.Join(value, "|") + ")"
					parts = append(parts, fmt.Sprintf(`%s=~"%s"`, key, values))
				}
			}
			expression = strings.Replace(expression, "@", strings.Join(parts, ","), -1)
		}
	} else {
		// cluster获取all
		expression = strings.Replace(expression, "@", "", -1)
	}
	for _, level := range rule.AlertLevels {
		labels := generateScopeLabels(rule.Scopes)
		labels[alertchannel.AlertNamespaceLabel] = ns
		labels[alertchannel.AlertNameLabel] = resourceName
		labels[alertchannel.SeverityLabel] = level.Severity
		labels[alertchannel.AlertOriginNameLabel] = rule.Name
		message, _ := genarateMessage(rule)
		ru := monitoringv1.Rule{
			Alert:  resourceName,
			Expr:   intstr.FromString(fmt.Sprintf("%s %s %s", expression, level.CompareOp, level.CompareValue)),
			For:    rule.For,
			Labels: labels,
			Annotations: map[string]string{
				alertchannel.AlertResource:          resource,
				alertchannel.AlertTemplateGroupName: templateShowName,
				alertchannel.AlertActionKey:         level.CompareOp,
				alertchannel.AlertThreshold:         level.CompareValue,
				alertchannel.AlertUnit:              unitValue,
				alertchannel.MessageAnnotationsKey:  message,
				alertchannel.ValueAnnotationKey:     alertchannel.ValueAnnotationExpr,
			},
		}
		group.Rules = append(group.Rules, ru)
	}
	return group
}

func generateAlertManagerConfig(rule *AlertRule, ns string) *v1alpha1.AlertmanagerConfig {
	resourceName := rule.GetKubernetesResourceName()
	aconfig := &v1alpha1.AlertmanagerConfig{
		TypeMeta: metav1.TypeMeta{
			Kind:       "AlertmanagerConfig",
			APIVersion: "monitoring.coreos.com/v1alpha1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceName,
			Namespace: ns,
			Labels: map[string]string{
				alertchannel.LabelAlertmanagerConfigName: resourceName,
			},
		},
	}
	return aconfig
}

// GroupWait - 第一次收到告警后，等待多长时间才发送通知
// GroupInterval - 在发送一组告警后，如果这组告警还在持续触发，间隔多久再次发送通知
func generateAlertManagerConfigSpec(rule *AlertRule, isApp bool, ns string, labels map[string][]string) v1alpha1.AlertmanagerConfigSpec {
	resourceName := rule.GetKubernetesResourceName()
	var (
		//matcher     []v1alpha1.Matcher
		subMatchers []v1alpha1.Matcher
		expression  string
		namespace   string
		cluster     string
	)
	for _, scope := range rule.Scopes {
		if scope.Resource == "clusters" {
			cluster = scope.Name
		}
	}
	ret := v1alpha1.AlertmanagerConfigSpec{
		Route: &v1alpha1.Route{
			Receiver:      "null",
			GroupBy:       []string{alertchannel.AlertNamespaceLabel, alertchannel.AlertNameLabel},
			GroupWait:     alertchannel.AlertManagerConfigWaitOrInternal,
			GroupInterval: alertchannel.AlertManagerConfigWaitOrInternal,
			Routes:        []apiextensionsv1.JSON{},
			//Matchers:      matcher,
			Matchers: []v1alpha1.Matcher{
				{
					Name:  alertchannel.AlertNamespaceLabel,
					Value: ns,
				},
			},
		},
		Receivers: []v1alpha1.Receiver{
			{Name: "null"},
		},
		InhibitRules: []v1alpha1.InhibitRule{},
	}
	if !isApp {
		subMatchers = append(subMatchers, v1alpha1.Matcher{
			Name:  alertchannel.AlertNamespaceLabel,
			Value: ns,
		}, v1alpha1.Matcher{
			Name:  alertchannel.AlertNameLabel,
			Value: resourceName,
		})
		// 如果没有接收器，就使用集群默认的渠道
		if len(rule.Receivers) == 0 {
			// 针对集群级别，没有receiver的情况，配置默认的webhook receiver
			rule.Receivers = append(rule.Receivers, &AlertReceiver{
				AlertChannel: &AlertChannel{
					ObjectMeta: store.ObjectMeta{
						Name: defaultClusterWebhook,
					},
					ChannelType: alertchannel.TypeWebhook,
					//SendResolved: true,
					ChannelWebhook: &alertchannel.Webhook{
						Kind: alertchannel.WebHookKindClusterDefault,
					},
				},
				Interval: alertchannel.AlertManagerConfigWaitOrInternal,
			})
		}
	} else {
		if rule.PromqlGenerator != nil {
			expression = rule.PromqlGenerator.Expr
		}
		namespaces := labels["namespace"]
		if len(namespaces) > 0 {
			namespace = namespaces[0]
		}
		if strings.Contains(expression, "#") {
			subMatchers = append(subMatchers, v1alpha1.Matcher{
				Name:  "namespace",
				Value: namespace,
			})
		} else {
			for k, v := range labels {
				for _, value := range v {
					subMatchers = append(subMatchers, v1alpha1.Matcher{
						Name:  k,
						Value: value,
					})
				}
			}
		}
	}
	for _, rec := range rule.Receivers {
		route := v1alpha1.Route{
			Receiver:       rec.AlertChannel.ReceiverName(cluster),
			RepeatInterval: rec.Interval,
			Continue:       true,
			Matchers:       subMatchers,
		}
		rawRouteData, _ := json.Marshal(route)
		// receiver
		ret.Receivers = append(ret.Receivers, rec.AlertChannel.ToAlertmanagerReceiver(cluster, rec.Targets))
		// route
		ret.Route.Routes = append(ret.Route.Routes, apiextensionsv1.JSON{Raw: rawRouteData})
	}
	// inhibit label
	if len(rule.InhibitLabels) > 0 {
		inhibitrule := v1alpha1.InhibitRule{
			SourceMatch: []v1alpha1.Matcher{
				{
					Name:  alertchannel.AlertNamespaceLabel,
					Value: ns,
				},
				// {
				// 	Name:  alertchannel.AlertNameLabel,
				// 	Value: resourceName,
				// },
				{
					Name:  alertchannel.SeverityLabel,
					Value: alertchannel.SeverityCritical,
				},
				{
					Name:  "namespace",
					Value: ns,
				},
			},
			TargetMatch: []v1alpha1.Matcher{
				{
					Name:  alertchannel.AlertNamespaceLabel,
					Value: ns,
				},
				// {
				// 	Name:  alertchannel.AlertNameLabel,
				// 	Value: resourceName,
				// },
				{
					Name:  alertchannel.SeverityLabel,
					Value: alertchannel.SeverityError,
				},
				{
					Name:  "namespace",
					Value: ns,
				},
			},
			Equal: append(rule.InhibitLabels, alertchannel.AlertNamespaceLabel, alertchannel.AlertNameLabel, "namespace"),
		}
		ret.InhibitRules = append(ret.InhibitRules, inhibitrule)
	}
	return ret
}

/*
inhibit_rules:
  - source_match:        # source_match：匹配当前告警发生后其他告警抑制掉
      severity: 'critical'
    target_match:        # target_match：抑制告警
      severity: 'warning'
    equal: ['alertname'] # equal：只有包含指定标签才可成立规则
以上配置告诉 AlertManager，对于具有相同 alertname 的告警，含 severity: 'critical' 标签的告警将会抑制含 severity: 'warning' 标签的告警。
*/

type AlertRule struct {
	store.ObjectMeta `json:",inline"`
	// Namespace        string           `json:"namespace"`
	Expr            string           `json:"expr"`                // promql表达式，不能包含比较运算符(<, <=, >, >=, ==)
	For             string           `json:"for"`                 // 持续时间, eg. 10s, 1m, 1h
	InhibitLabels   []string         `json:"inhibitLabels"`       // 如果有多个告警级别，需要配置告警抑制的labels
	AlertLevels     AlertLevels      `json:"alertLevels"`         // 告警级别
	Status          string           `json:"status,omitempty"`    // 对应该告警规则在prometheus中的状态 //Inactive,Pending,Firing
	Receivers       []*AlertReceiver `json:"receivers"`           // 接收器, 删除alertrule时级联删除
	Close           bool             `json:"close,omitempty"`     // 是否关闭
	SilenceID       string           `json:"silenceID,omitempty"` // 静默规则ID
	PromqlGenerator *PromqlGenerator `json:"promqlGenerator"`     // 来自模版的表达式
}

func (r *AlertRule) GetKubernetesResourceName() string {
	return r.GetKubernetesResourcePrefix() + r.Name
}

func (r *AlertRule) GetKubernetesResourcePrefix() string {
	var prefix string
	scopes := r.GetScopes()
	if len(scopes) == 0 {
		return prefix
	}
	scope, scopes := scopes[0], scopes[1:]
	switch scope.Resource {
	case "tenants":
		prefix += scope.Name + "-"
		if len(scopes) == 0 {
			return prefix
		}
		scope, scopes := scopes[0], scopes[1:]
		switch scope.Resource {
		case "organizations":
			prefix += scope.Name + "-"
			if len(scopes) == 0 {
				return prefix
			}
			scope := scopes[0]
			switch scope.Resource {
			case "applications":
				prefix += scope.Name + "-"

			}
		}
	case "clusters":
		prefix += scope.Name + "-"
	}
	return prefix

}

type PromqlGenerator struct {
	TemplateGroup         string   `json:"templateGroup" yaml:"templateGroup"`
	TemplateGroupResource string   `json:"templateGroupResource" yaml:"templateGroupResource"`
	TemplateShowName      string   `json:"templateShowName" yaml:"templateShowName"`
	TemplateType          string   `json:"templateType" yaml:"templateType"` // application或cluster或all
	Resource              string   `json:"resource"`                         // 定位资源，方便告警使用
	Expr                  string   `json:"expr"`
	Unit                  string   `json:"unit"`
	Labels                []string `json:"labels"`
}

type (
	AlertLevels []AlertLevel
	AlertLevel  struct {
		CompareOp    string `json:"compareOp,omitempty"`    // >=,==,<=,>,<
		CompareValue string `json:"compareValue,omitempty"` // 支持表达式, eg. 24 * 60
		Severity     string `json:"severity"`               // error, critical
	}
)

type AlertReceiver struct {
	AlertChannel *AlertChannel `json:"alertChannel"`
	Interval     string        `json:"interval"`
	Targets      []string      `json:"targets"` // 可能是电话，邮箱等信息
}

type AlertChannel struct {
	store.ObjectMeta `json:",inline"`
	ChannelType      alertchannel.ChannelType `json:"channelType"` // 通知渠道类型
	ChannelDingding  *alertchannel.Dingding   `json:"dingding,omitempty"`
	ChannelEmail     *alertchannel.Email      `json:"email,omitempty"`
	ChannelFeishu    *alertchannel.Feishu     `json:"feishu,omitempty"`
	ChannelWebhook   *alertchannel.Webhook    `json:"webhook,omitempty"`
	ChannelTeams     *alertchannel.MSTeams    `json:"msteams,omitempty"`
}

func (c *AlertChannel) ReceiverName(cluster string) string {
	var (
		tenantname       = "null"
		organizationname = "null"
	)

	if c.Name == defaultClusterWebhook {
		return fmt.Sprintf("%s-%s", c.ChannelType, c.Name)
	}
	if len(c.Scopes) == 0 {
		return fmt.Sprintf("%s-%s-%s", cluster, c.ChannelType, c.Name)
	}

	for _, scope := range c.Scopes {
		if scope.Resource == "tenants" {
			tenantname = scope.Name
		}
		if scope.Resource == "organizations" {
			organizationname = scope.Name
		}
	}
	return fmt.Sprintf("%s-%s-%s-%s", tenantname, organizationname, c.ChannelType, c.Name)
}

func (c *AlertChannel) ToAlertmanagerReceiver(cluster string, target []string) v1alpha1.Receiver {
	useBasicAuth := false
	empty := v1alpha1.Receiver{Name: "null"}
	var channel alertchannel.AlertChannel
	switch c.ChannelType {
	case alertchannel.TypeDingding:
		if c.ChannelDingding == nil {
			return empty
		}
		channel = c.ChannelDingding
	case alertchannel.TypeEmail:
		if c.ChannelEmail == nil {
			return empty
		}
		channel = c.ChannelEmail
	case alertchannel.TypeFeishu:
		if c.ChannelFeishu == nil {
			return empty
		}
		channel = c.ChannelFeishu
	case alertchannel.TypeWebhook:
		if c.ChannelWebhook == nil {
			return empty
		}
		channel = c.ChannelWebhook
	case alertchannel.TypeMSTeams:
		if c.ChannelTeams == nil {
			return empty
		}
		channel = c.ChannelTeams
	default:
		return empty
	}
	return channel.ToReceiver(c.ReceiverName(cluster), useBasicAuth, target)
}

// TestConnected - 测试联通性
func (c *AlertChannel) TestConnected(targets []string) error {
	now := time.Now()
	start := now.Add(-3 * time.Hour)
	alertObj := alertchannel.AlertProxyWebhookAlert{
		Receiver: c.ReceiverName("test"),
		Status:   "firing",
		Alerts: []alertchannel.AlertProxyMessage{
			{
				Status: "firing",
				Labels: map[string]string{
					alertchannel.AlertScopeTenant:      "test",
					alertchannel.AlertOriginNameLabel:  "test-namespace",
					alertchannel.AlertScopeApplication: "test-application",
					alertchannel.AlertClusterKey:       "test-cluster",
					"namespace":                        "test-default",
					"pod":                              "test-pod",
				},
				Annotations: map[string]string{
					alertchannel.AlertTemplateGroupName: "Test",
					alertchannel.AlertResource:          "pod",
					alertchannel.AlertActionKey:         "!=",
					alertchannel.AlertThreshold:         "1",
					alertchannel.AlertUnit:              "",
					alertchannel.ValueAnnotationKey:     "0",
				},
				StartsAt: &start,
			},
		},
	}
	var channel alertchannel.AlertChannel
	switch c.ChannelType {
	case alertchannel.TypeDingding:
		channel = c.ChannelDingding
	case alertchannel.TypeEmail:
		channel = c.ChannelEmail
	case alertchannel.TypeFeishu:
		channel = c.ChannelFeishu
	case alertchannel.TypeWebhook:
		channel = c.ChannelWebhook
	case alertchannel.TypeMSTeams:
		channel = c.ChannelTeams
	default:
		return fmt.Errorf("unknown type %s", c.ChannelType)
	}
	return channel.Test(alertObj, targets)
}

func genarateMessage(alertrule *AlertRule) (string, error) {
	resourceName := alertrule.GetKubernetesResourceName()
	var ret string
	if alertrule.PromqlGenerator != nil {
		ret = fmt.Sprintf("%s: [cluster:{{ $externalLabels.%s }}] ", resourceName, alertchannel.AlertClusterKey)
		for _, label := range alertrule.PromqlGenerator.Labels {
			ret += fmt.Sprintf("[%s:{{ $labels.%s }}] ", label, label)
		}
		unitValue, err := alertchannel.ParseUnit(alertrule.PromqlGenerator.Unit)
		if err != nil {
			return "", err
		}
		ret += fmt.Sprintf("%s trigger alert, value: %s%s", alertrule.PromqlGenerator.TemplateShowName, alertchannel.ValueAnnotationExpr, unitValue.Show)
	} else {
		ret = fmt.Sprintf("%s: [cluster:{{ $externalLabels.%s }}] trigger alert, value: %s", resourceName, alertchannel.AlertClusterKey, alertchannel.ValueAnnotationExpr)
	}

	return ret, nil
}
