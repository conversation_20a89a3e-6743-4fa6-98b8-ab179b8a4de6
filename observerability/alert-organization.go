package observerability

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/api/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func (a *API) ListOrganizationAlertRules(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		return base.GenericListWithWatch(w, r, storage, &store.List[AlertRule]{})
	})
}

func (a *API) GetOrganizationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnOrganizationAlertRule(w, r, func(ctx context.Context, storage store.Store, tenant, org, rule string) (any, error) {
		return base.GenericGet(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) CreateOrganizationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		return base.GenericCreate(r, storage, &AlertRule{})
	})
}

func (a *API) UpdateOrganizationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnOrganizationAlertRule(w, r, func(ctx context.Context, storage store.Store, tenant, org, rule string) (any, error) {
		return base.GenericUpdate(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) DeleteOrganizationAlertRule(w http.ResponseWriter, r *http.Request) {
	a.OnOrganizationAlertRule(w, r, func(ctx context.Context, storage store.Store, tenant, org, rule string) (any, error) {
		return base.GenericDelete(r, storage, &AlertRule{}, rule)
	})
}

func (a *API) OnOrganizationAlertRule(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, org, rule string) (any, error)) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		rule := api.Path(r, "rule", "")
		if rule == "" {
			return nil, errors.NewBadRequest("rule name is required")
		}
		return fn(ctx, storage, tenant, org, rule)
	})
}

func (a *API) tenantOrganizationAlertRuleGroup() api.Group {
	return base.
		NewTenantWorkspaceGroup("alertrules").
		Route(
			api.GET("").Operation("list alert rules").
				To(a.ListOrganizationAlertRules).
				Param(base.PageParams...).
				Response(store.List[AlertRule]{}),

			api.POST("").Operation("create alert rule").
				To(a.CreateOrganizationAlertRule).
				Param(api.BodyParam("rule", AlertRule{})).
				Response(AlertRule{}),

			api.GET("/{rule}").Operation("get alert rule").
				To(a.GetOrganizationAlertRule).
				Response(AlertRule{}),

			api.PUT("/{rule}").Operation("update alert rule").
				Param(api.BodyParam("rule", AlertRule{})).
				To(a.UpdateOrganizationAlertRule),

			api.DELETE("/{rule}").Operation("delete alert rule").
				To(a.DeleteOrganizationAlertRule),
		)
}

func (a *API) ListOrganizationAlertChannels(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		return base.GenericListWithWatch(w, r, storage, &store.List[AlertChannel]{})
	})
}

func (a *API) GetOrganizationAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnOrganizationAlertChannel(w, r, func(ctx context.Context, storage store.Store, tenant, org, channel string) (any, error) {
		return base.GenericGet(r, storage, &AlertChannel{}, channel)
	})
}

func (a *API) CreateOrganizationAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		return base.GenericCreate(r, storage, &AlertChannel{})
	})
}

func (a *API) UpdateOrganizationAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnOrganizationAlertChannel(w, r, func(ctx context.Context, storage store.Store, tenant, org, channel string) (any, error) {
		return base.GenericUpdate(r, storage, &AlertChannel{}, channel)
	})
}

func (a *API) DeleteOrganizationAlertChannel(w http.ResponseWriter, r *http.Request) {
	a.OnOrganizationAlertChannel(w, r, func(ctx context.Context, storage store.Store, tenant, org, name string) (any, error) {
		channel := &AlertChannel{
			ObjectMeta: store.ObjectMeta{
				Name: name,
			},
		}
		return nil, storage.Delete(ctx, channel)
	})
}

func (a *API) OnOrganizationAlertChannel(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, tenant, org, channel string) (any, error)) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		channel := api.Path(r, "channel", "")
		if channel == "" {
			return nil, errors.NewBadRequest("channel name is required")
		}
		return fn(ctx, storage, tenant, org, channel)
	})
}

func (a *API) organizationAlertChannelGroup() api.Group {
	return base.
		NewTenantWorkspaceGroup("alertchannels").
		Route(
			api.GET("").
				To(a.ListOrganizationAlertChannels).
				Operation("list alert channels").
				Param(base.PageParams...).
				Response(store.List[AlertChannel]{}),

			api.POST("").
				To(a.CreateOrganizationAlertChannel).
				Operation("Create an alert channel").
				Param(
					api.BodyParam("alertchannel", AlertChannel{}),
				),

			api.GET("/{channel}").
				To(a.GetOrganizationAlertChannel).
				Operation("Get an alert channel").
				Response(AlertChannel{}),

			api.PUT("/{channel}").
				Operation("Update an alert channel").
				To(a.UpdateOrganizationAlertChannel).
				Param(api.BodyParam("alertchannel", AlertChannel{})),

			api.DELETE("/{channel}").
				Operation("Delete an alert channel").
				To(a.DeleteOrganizationAlertChannel),
		)
}
