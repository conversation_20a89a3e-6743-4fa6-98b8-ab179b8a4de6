package prometheus

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"xiaoshiai.cn/common/log"
)

type Prometheus interface {
	Query(ctx context.Context, query string, at time.Time) (*ResponseData, error)
	QueryRange(ctx context.Context, query string, start, end time.Time, step time.Duration) (*ResponseData, error)
}
type PromethuesResponse struct {
	Status string       `json:"status"`
	Data   ResponseData `json:"data"`
}

type ResponseData struct {
	Result     json.RawMessage `json:"result"`
	ResultType ResultType      `json:"resultType"`
}

func (d ResponseData) Matrix() (*Matrix, error) {
	if d.ResultType != ResultTypeMatrix {
		return nil, fmt.Errorf("unexpected result type: %s", d.ResultType)
	}
	matrix := &Matrix{ResultType: d.ResultType}
	if err := json.Unmarshal(d.Result, &matrix.Result); err != nil {
		return nil, err
	}
	return matrix, nil
}

func (d ResponseData) Vector() (*Vector, error) {
	if d.ResultType != ResultTypeVector {
		return nil, fmt.Errorf("unexpected result type: %s", d.ResultType)
	}
	vector := &Vector{ResultType: d.ResultType}
	if err := json.Unmarshal(d.Result, &vector.Result); err != nil {
		return nil, err
	}
	return vector, nil
}

func (d ResponseData) Scalar() (*Scalar, error) {
	if d.ResultType != ResultTypeScalar {
		return nil, fmt.Errorf("unexpected result type: %s", d.ResultType)
	}
	scalar := &Scalar{ResultType: d.ResultType}
	if err := json.Unmarshal(d.Result, &scalar.Result); err != nil {
		return nil, err
	}
	return scalar, nil
}

func (d ResponseData) String() (*String, error) {
	if d.ResultType != ResultTypeString {
		return nil, fmt.Errorf("unexpected result type: %s", d.ResultType)
	}
	str := &String{ResultType: d.ResultType}
	if err := json.Unmarshal(d.Result, &str.Result); err != nil {
		return nil, err
	}
	return str, nil
}

func (d ResponseData) Any() (map[string]any, error) {
	var result any
	if err := json.Unmarshal(d.Result, &result); err != nil {
		return nil, err
	}
	return map[string]any{"resultType": string(d.ResultType), "result": result}, nil
}

func (d ResponseData) GetResult() (any, error) {
	switch d.ResultType {
	case ResultTypeVector:
		return d.Vector()
	case ResultTypeMatrix:
		return d.Matrix()
	case ResultTypeScalar:
		return d.Scalar()
	case ResultTypeString:
		return d.String()
	}
	return d, nil
}

type ResultType string

const (
	ResultTypeVector ResultType = "vector"
	ResultTypeMatrix ResultType = "matrix"
	ResultTypeScalar ResultType = "scalar"
	ResultTypeString ResultType = "string"
)

type Scalar struct {
	Result     Value      `json:"result"`
	ResultType ResultType `json:"resultType"`
}

type Vector struct {
	Result     []Sample   `json:"result"`
	ResultType ResultType `json:"resultType"`
}

type Matrix struct {
	Result     []SampleStream `json:"result"`
	ResultType ResultType     `json:"resultType"`
}

type String struct {
	Result     string     `json:"result"`
	ResultType ResultType `json:"resultType"`
}

type Sample struct {
	Metric    map[string]string `json:"metric"`
	Value     Value             `json:"value"`
	Histogram *Histogram        `json:"histogram,omitempty"`
}

type SampleStream struct {
	Metric     map[string]string `json:"metric"`
	Values     []Value           `json:"values"`
	Histograms []Histogram       `json:"histograms,omitempty"`
}

type Histogram struct {
	Count   FloatString       `json:"count"`
	Sum     FloatString       `json:"sum"`
	Buckets []HistogramBucket `json:"buckets"`
}

// Value is a Prometheus value.
// it like:   [1729695907.177, "2"]
type Value struct {
	Timestamp float64 `json:"timestamp"`
	Value     string  `json:"value"`
}

func (v Value) TimestampValue() time.Time {
	return time.Unix(int64(v.Timestamp), 0)
}

func (v Value) FloatValue() float64 {
	f, _ := strconv.ParseFloat(v.Value, 64)
	return f
}

// UnmarshalJSON implements json.Unmarshaler.
func (v *Value) UnmarshalJSON(data []byte) error {
	var raw []any
	if err := json.Unmarshal(data, &raw); err != nil {
		return err
	}
	if len(raw) != 2 {
		return fmt.Errorf("unexpected prometheus value: %v", raw)
	}
	ts, ok := raw[0].(float64)
	if !ok {
		return fmt.Errorf("unexpected prometheus timestamp type: %T", raw[0])
	}
	v.Timestamp = ts
	val, ok := raw[1].(string)
	if !ok {
		return fmt.Errorf("unexpected prometheus value type: %T", raw[1])
	}
	v.Value = val
	return nil
}

func (v Value) MarshalJSON() ([]byte, error) {
	return json.Marshal([]any{v.Timestamp, v.Value})
}

type FloatString float64

func (v FloatString) String() string {
	return strconv.FormatFloat(float64(v), 'f', -1, 64)
}

func (v FloatString) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.String())
}

func (v *FloatString) UnmarshalJSON(b []byte) error {
	if len(b) < 2 || b[0] != '"' || b[len(b)-1] != '"' {
		return fmt.Errorf("float value must be a quoted string")
	}
	f, err := strconv.ParseFloat(string(b[1:len(b)-1]), 64)
	if err != nil {
		return err
	}
	*v = FloatString(f)
	return nil
}

type HistogramBucket struct {
	Boundaries int32
	Lower      FloatString
	Upper      FloatString
	Count      FloatString
}

func (s HistogramBucket) MarshalJSON() ([]byte, error) {
	b, err := json.Marshal(s.Boundaries)
	if err != nil {
		return nil, err
	}
	l, err := json.Marshal(s.Lower)
	if err != nil {
		return nil, err
	}
	u, err := json.Marshal(s.Upper)
	if err != nil {
		return nil, err
	}
	c, err := json.Marshal(s.Count)
	if err != nil {
		return nil, err
	}
	return []byte(fmt.Sprintf("[%s,%s,%s,%s]", b, l, u, c)), nil
}

func (s *HistogramBucket) UnmarshalJSON(buf []byte) error {
	tmp := []any{&s.Boundaries, &s.Lower, &s.Upper, &s.Count}
	wantLen := len(tmp)
	if err := json.Unmarshal(buf, &tmp); err != nil {
		return err
	}
	if gotLen := len(tmp); gotLen != wantLen {
		return fmt.Errorf("wrong number of fields: %d != %d", gotLen, wantLen)
	}
	return nil
}

func QueryMatrix(ctx context.Context, prom Prometheus, expr string, start, end time.Time, step time.Duration) (*Matrix, error) {
	log.FromContext(ctx).V(3).Info("querying prometheus", "query", expr, "start", start, "end", end, "step", step)
	ret, err := prom.QueryRange(ctx, expr, start, end, step)
	if err != nil {
		return nil, err
	}
	return ret.Matrix()
}

func QueryVector(ctx context.Context, prom Prometheus, expr string, at time.Time) (*Vector, error) {
	log.FromContext(ctx).V(3).Info("querying prometheus", "query", expr, "at", at)
	ret, err := prom.Query(ctx, expr, at)
	if err != nil {
		return nil, err
	}
	return ret.Vector()
}
