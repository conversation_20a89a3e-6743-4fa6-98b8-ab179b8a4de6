package observerability

import (
	"context"
	"net/http"
	"strings"

	"k8s.io/apimachinery/pkg/runtime/schema"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability/dashboard"
)

type ClusterMetricsAPI struct {
	base.API
	CloudInfo         cluster.CloudInfoGetter
	BuiltInDashboards map[string]dashboard.DashboradConfiguration
}

var TypedClusterMetricsMapper = map[cluster.ClusterCategory]map[string]string{
	cluster.ClusterCategoryContainer:      ContainerClusterMetricsMapper,
	cluster.ClusterCategoryVirtualMachine: VirtualMachineClusterMetricsMapper,
}

var ContainerClusterMetricsMapper = map[string]string{
	"etcd_request_persecond":        "sum(irate(etcd_request_duration_seconds_count[5m]))",
	"etcd_latency_seconds_p95":      `histogram_quantile(0.95, sum(irate(etcd_request_duration_seconds_bucket[5m])) by (le, operation)) * 1000 * 1000`,
	"etcd_latency_seconds_p99":      `histogram_quantile(0.99, sum(irate(etcd_request_duration_seconds_bucket[5m])) by (le, operation)) * 1000 * 1000`,
	"apiserver_latency_seconds_p95": `histogram_quantile(0.95, sum(irate(apiserver_request_duration_seconds_bucket{verb!~"WATCH|CONNECT"}[5m])) by (le, verb)) * 1000 * 1000`,
	"apiserver_latency_seconds_p99": `histogram_quantile(0.99, sum(irate(apiserver_request_duration_seconds_bucket{verb!~"WATCH|CONNECT"}[5m])) by (le, verb)) * 1000 * 1000`,
	"apiserver_request_persecond":   `sum(irate(apiserver_request_total[5m]))by(code)`,
	"cpu_used_cores":                `round(sum(irate(node_cpu_seconds_total{mode!="idle"}[5m])), 0.001)`,
	"memory_used_bytes":             `sum(node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes)`,
	"disk_used_bytes":               `max(node_filesystem_size_bytes{device=~"/dev/.*", device!~"/dev/loop\\d+"} - node_filesystem_avail_bytes{device=~"/dev/.*", device!~"/dev/loop\\d+"})by(node, device)`,
	"pod_running_count":             `count(kube_pod_status_phase{phase="Running"})`,
}

var VirtualMachineClusterMetricsMapper = map[string]string{
	// realused
	"cpu_used_cores":      `sum(ismc_host_cpu_used_cores)`,
	"cpu_total_cores":     `sum(ismc_host_cpu_total_cores)`,
	"memory_used_bytes":   `sum(ismc_host_memory_used_bytes)`,
	"memory_total_bytes":  `sum(ismc_host_memory_total_bytes)`,
	"storage_used_bytes":  `sum(ismc_host_disk_used_bytes)`,
	"storage_total_bytes": `sum(ismc_host_disk_total_bytes)`,
	// allocated
	"cpu_allocated_cores":    `sum(ismc_host_vcpu_used_cores)`,
	"memory_allocated_bytes": `sum(ismc_host_vmemory_used_bytes)`,
}

func (a *ClusterMetricsAPI) GetClusterMetrics(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, op cluster.ContainerOperation) (any, error) {
		meta := cluster.ResourceMetaFromRequest(r)
		// cluster metrics
		if meta.GroupVersionResource.Empty() {
			return GetClusterMetrics(r, op)
		}
		// resource metrics
		return GetResourceMetrics(r, op, meta)
	})
}

func GetClusterMetrics(r *http.Request, op cluster.ContainerOperation) (any, error) {
	mapper, ok := TypedClusterMetricsMapper[cluster.ClusterCategoryFrom(op.ClusterType)]
	if !ok {
		return UnknownMetrics, nil
	}
	return GetMetrics(r, op, mapper, "")
}

func (a *ClusterMetricsAPI) GetClusterDashboard(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, op cluster.ContainerOperation) (any, error) {
		fullname := "cluster-" + strings.ToLower(string(op.ClusterType)) + "-" + api.Path(r, "dashboard", "basic")
		builtin, ok := a.BuiltInDashboards[fullname]
		if !ok {
			return nil, errors.NewNotFound("dashboard", fullname)
		}
		options := dashboard.GetRenderDashboardOptions(r, map[string]string{
			"cluster": api.Path(r, "cluster", ""),
		})
		return dashboard.RenderDashboardSimple(r, op, builtin, options)
	})
}

func (a *ClusterMetricsAPI) GetVirtualMachineMetrics(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, op cluster.ContainerOperation) (any, error) {
		meta := cluster.RequestMetadata{
			GroupVersionResource: schema.GroupVersionResource{
				Group: "cloud", Version: "v1", Resource: "virtualmachines",
			},
			Name: api.Path(r, "id", ""),
		}
		return RenderClusterDashboard(a.BuiltInDashboards, r, op, meta, nil)
	})
}

func (a *ClusterMetricsAPI) GetHostMetrics(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, op cluster.ContainerOperation) (any, error) {
		meta := cluster.RequestMetadata{
			GroupVersionResource: schema.GroupVersionResource{
				Group: "cloud", Version: "v1", Resource: "hosts",
			},
			Name: api.Path(r, "id", ""),
		}
		return RenderClusterDashboard(a.BuiltInDashboards, r, op, meta, nil)
	})
}

func (a *ClusterMetricsAPI) GetDiskMetrics(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, op cluster.ContainerOperation) (any, error) {
		meta := cluster.RequestMetadata{
			GroupVersionResource: schema.GroupVersionResource{
				Group: "cloud", Version: "v1", Resource: "disks",
			},
			Name: api.Path(r, "id", ""),
		}
		return RenderClusterDashboard(a.BuiltInDashboards, r, op, meta, nil)
	})
}

func (a *ClusterMetricsAPI) QueryClusterDashboard(w http.ResponseWriter, r *http.Request) {
	a.onClusterPromethus(w, r, func(ctx context.Context, op cluster.ContainerOperation) (any, error) {
		return RenderClusterDashboard(a.BuiltInDashboards, r, op, cluster.ResourceMetaFromRequest(r), nil)
	})
}

func RenderClusterDashboard(builtins map[string]dashboard.DashboradConfiguration, r *http.Request, op cluster.ContainerOperation, meta cluster.RequestMetadata, otherparams map[string]string) (any, error) {
	fullname := dashboard.GetBuiltClusterDashboardName(op.ClusterType, meta.Resource, api.Path(r, "dashboard", "basic"))
	builtin, ok := builtins[fullname]
	if !ok {
		return nil, errors.NewNotFound("dashboard", fullname)
	}
	params := map[string]string{
		"namespace": meta.Namespace,
		// it sets the resource name as the key in the params
		// eg.  pods:abc 		-> pod=abc
		// eg.  services:abc 	-> service=abc
		simpleToSingular(meta.Resource): meta.Name,
	}
	switch meta.Resource {
	case "virtualmachines":
		params["vm"] = meta.Name
	}
	for k, v := range otherparams {
		params[k] = v
	}
	options := dashboard.GetRenderDashboardOptions(r, params)
	return dashboard.RenderDashboardSimple(r, op, builtin, options)
}

func simpleToSingular(resource string) string {
	if strings.HasSuffix(resource, "s") {
		return strings.TrimSuffix(resource, "s")
	}
	return resource
}

func (a *ClusterMetricsAPI) onClusterPromethus(w http.ResponseWriter, r *http.Request,
	fn func(ctx context.Context, op cluster.ContainerOperation) (any, error),
) {
	a.onCluster(w, r, func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error) {
		info, err := a.CloudInfo.Get(ctx, clusterref)
		if err != nil {
			return nil, err
		}
		op, err := cluster.NewContainerOperation(info)
		if err != nil {
			return nil, err
		}
		return fn(ctx, op)
	})
}

func (a *ClusterMetricsAPI) onCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, clusterref store.ObjectReference) (any, error)) {
	cluster.OnClusterOrTenantCluster(w, r, func(ctx context.Context, clusterref store.ObjectReference) (any, error) {
		store := a.Store.Scope(clusterref.Scopes...).Scope(base.ScopeCluster(clusterref.Name))
		return fn(ctx, store, clusterref)
	})
}

var ClusterMetricsParams = []api.Param{
	api.QueryParam("filter", "filter expression,example: foo=bar,pod=abc ").Optional(),
	api.QueryParam("by", "sum/avg by labels").In("node", "pod", "instance").Optional(),
	api.QueryParam("aggregate", "aggregate function").In("sum", "avg", "max", "min").Optional(),
	api.PathParam("metrics", "metrics name").
		In(getMetricskeys(ContainerClusterMetricsMapper)...).
		In(getMetricskeys(VirtualMachineClusterMetricsMapper)...),
}

func getMetricskeys(mapper map[string]string) []any {
	keys := make([]any, 0, len(mapper))
	for k := range mapper {
		keys = append(keys, k)
	}
	return keys
}

func (a *ClusterMetricsAPI) MetricsGroup() api.Group {
	return api.NewGroup("").
		Route(
			api.GET("/metrics/{metrics}").
				Doc("Get cluster metrics").
				Param(GetMetricsParams...).
				Param(ClusterMetricsParams...).
				To(a.GetClusterMetrics),

			api.GET("/{group}/{version}/{resource}/{name}/metrics/{metrics}").
				Doc("get cluster resource metrics").
				Param(GetMetricsParams...).
				Param(ResourceMetricsParams...).
				To(a.GetClusterMetrics),

			api.GET("/{group}/{version}/namespaces/{namespace}/{resource}/{name}/metrics/{metrics}").
				Doc("get cluster resource metrics").
				Param(GetMetricsParams...).
				Param(ResourceMetricsParams...).
				To(a.GetClusterMetrics),

			api.GET("/cloud/v1/virtualmachines/{id}/metrics-dashboards/basic/query").
				Doc("Get vitual machine metrics").
				To(a.GetVirtualMachineMetrics),

			api.GET("/cloud/v1/disks/{id}/metrics-dashboards/basic/query").
				Doc("Get disk metrics").
				To(a.GetDiskMetrics),

			api.GET("/cloud/v1/hosts/{id}/metrics-dashboards/basic/query").
				Doc("Get host metrics").
				To(a.GetHostMetrics),

			api.GET("/{group}/{version}/{resource}/{name}/metrics-dashboards/{dashboard}/query").
				Doc("Get cluster resource metrics").
				Param(MetricsDashboradParams...).
				Response([]any{}).
				To(a.QueryClusterDashboard),
		)
}
