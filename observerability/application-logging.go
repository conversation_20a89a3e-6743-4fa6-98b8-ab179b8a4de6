package observerability

import (
	"context"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"time"

	"github.com/gorilla/websocket"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/cluster"
)

func (a *ApplicationObservabilityAPI) ApplicationLogging(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		options := cluster.LokiQueryRangeOptions{
			Query:     getLokiQuery(r, app),
			Start:     parseNanoTime(api.Query(r, "start", "")),
			End:       parseNanoTime(api.Query(r, "end", "")),
			Since:     api.Query(r, "since", time.Duration(0)),
			Step:      api.Query(r, "step", time.Duration(0)),
			Limit:     api.Query(r, "limit", 0),
			Direction: api.Query(r, "direction", "backward"),
			Interval:  api.Query(r, "interval", time.Duration(0)),
		}
		log.FromContext(ctx).V(5).Info("loki query", "query", options.Query)
		data, err := op.LokiQueryRange(ctx, options)
		if err != nil {
			return nil, err
		}
		return ConvertLokiResponse(data, options.Direction), nil
	})
}

func parseNanoTime(s string) time.Time {
	if len(s) == 0 {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC3339Nano, s)
	if err != nil {
		// try rfc3339
		t, err = time.Parse(time.RFC3339, s)
		if err != nil {
			return time.Time{}
		}
	}
	return t
}

func appFilterParams(app *application.Application) (string, map[string]string) {
	rls := application.GetRelease(app)
	params := map[string]string{
		"namespace": rls.Namespace,
		"instance":  rls.Name,
	}
	cond := fmt.Sprintf(`namespace="%s",instance="%s"`, rls.Namespace, rls.Name)
	return cond, params
}

func appFilterWithMap(app *application.Application) map[string][]string {
	rls := application.GetRelease(app)
	return map[string][]string{
		"namespace": {rls.Namespace},
		"instance":  {rls.Name},
	}
}

func (a *ApplicationObservabilityAPI) ApplicationLoggingStrem(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		log := log.FromContext(ctx)

		options := cluster.LokiTailOptions{
			Query:    getLokiQuery(r, app),
			Start:    parseNanoTime(api.Query(r, "start", "")),
			DelayFor: api.Query(r, "delay_for", time.Duration(0)),
			Limit:    api.Query(r, "limit", 100),
		}
		log.V(5).Info("loki query", "query", options.Query)

		up := websocket.Upgrader{CheckOrigin: func(r *http.Request) bool { return true }}
		conn, err := up.Upgrade(w, r, nil)
		if err != nil {
			return nil, err
		}
		defer conn.Close()

		data, err := op.LokiTail(ctx, options, func(ctx context.Context, msg cluster.LokiQueryRangeResult) error {
			for _, m := range ConvertLokiValues(msg) {
				return conn.WriteJSON(m)
			}
			return nil
		})
		if err != nil {
			log.Error(err, "loki tail error")
			return nil, nil
		}
		_ = data
		return nil, nil
	})
}

type LogResponse []LogItem

type LogItem struct {
	Timestamp time.Time         `json:"timestamp"`
	Labels    map[string]string `json:"labels"`
	Log       string            `json:"log"`
}

func ConvertLokiResponse(data *cluster.LokiQueryRangeResponse, direction string) LogResponse {
	logs := []LogItem{}
	for i := range data.Data.Result {
		logs = append(logs, ConvertLokiValues(data.Data.Result[i])...)
	}
	switch direction {
	case "forward":
		slices.SortFunc(logs, func(i, j LogItem) int {
			return i.Timestamp.Compare(j.Timestamp)
		})
	case "backward":
		slices.SortFunc(logs, func(i, j LogItem) int {
			return j.Timestamp.Compare(i.Timestamp)
		})
	}
	return LogResponse(logs)
}

func ConvertLokiValues(data cluster.LokiQueryRangeResult) []LogItem {
	var logs []LogItem
	for _, values := range data.Values {
		if len(values) != 2 {
			continue
		}
		unixnano, _ := strconv.ParseInt(values[0], 10, 64)
		logs = append(logs, LogItem{Timestamp: time.Unix(0, unixnano), Labels: data.Stream, Log: values[1]})
	}
	return logs
}

func (a *ApplicationObservabilityAPI) ApplicationLoggingSeries(w http.ResponseWriter, r *http.Request) {
	a.OnApplicationContainerCluster(w, r, func(ctx context.Context, op cluster.ContainerOperation, app *application.Application) (any, error) {
		log := log.FromContext(ctx)
		options := cluster.LokiSeriesOptions{
			Match: []string{getLokiQuery(r, app)},
			Start: api.Query(r, "start", time.Time{}),
			End:   api.Query(r, "end", time.Time{}),
			Since: api.Query(r, "since", time.Duration(0)),
		}
		log.V(5).Info("loki series", "match", options.Match)
		data, err := op.LokiSeries(ctx, options)
		if err != nil {
			return nil, err
		}
		return data.Data, nil
	})
}

func getLokiQuery(r *http.Request, app *application.Application) string {
	cond, _ := appFilterParams(app)
	if filter := api.Query(r, "filter", ""); filter != "" {
		cond += ", " + filter
	}
	query := "{" + cond + "}"
	if customquery := api.Query(r, "query", ""); customquery != "" {
		query += customquery
	}
	return query
}

func (a *ApplicationObservabilityAPI) ApplicationsLoggingGroup() api.Group {
	return api.NewGroup("/log").
		Param(
			api.QueryParam("filter", "additional filter expression,exmaple: foo=\"bar\", level=\"info\"").Optional(),
		).
		Route(
			api.GET("").
				Doc("Tenant application logging").
				To(a.ApplicationLogging).
				Response(LogResponse{}).
				Param(
					api.QueryParam("start", "rfc3339nano timestamp,start time for the query").Optional(),
					api.QueryParam("end", "rfc3339nano timestamp,end time for the query").Optional(),
					api.QueryParam("since", "duration,query start relative to end").Optional(),
					api.QueryParam("step", "duration,query resolution step width").Optional(),
					api.QueryParam("interval", "duration,only return entries at (or greater than) the specified interval").Optional(),
					api.QueryParam("direction", "string,sort order of logs,forward or backward").Optional(),
					api.QueryParam("filter", "filter expression,exmaple: foo=bar, level=info").Optional(),
				),
			api.GET("/stream").
				Doc("Tenant application logging stream").
				To(a.ApplicationLoggingStrem).
				Param(
					api.QueryParam("start", "rfc3339nano timestamp,start time for the query").Optional(),
					api.QueryParam("delay_for", "duration,delay for the query").Optional(),
					api.QueryParam("limit", "int,limit the number of logs").Optional(),
				),
			api.GET("/series").
				Doc("Tenant application logging series").
				To(a.ApplicationLoggingSeries).
				Param(
					api.QueryParam("start", "rfc3339 timestamp,start time for the query").Optional(),
					api.QueryParam("end", "rfc3339 timestamp,end time for the query").Optional(),
					api.QueryParam("since", "duration,query start relative to end").Optional(),
				),
		)
}
