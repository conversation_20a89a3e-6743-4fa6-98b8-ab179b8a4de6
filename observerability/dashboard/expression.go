package dashboard

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"
	"unicode"

	"xiaoshiai.cn/core/observerability/prometheus"
)

type PrometheusExprNodeType string

const (
	PrometheusExprNodeTypeFunc     PrometheusExprNodeType = "func"
	PrometheusExprNodeTypeIdentity PrometheusExprNodeType = "identity"
)

type PrometheusExprNode struct {
	Type  PrometheusExprNodeType
	Value string
	Args  []PrometheusExprNode
}

type PrometheusExprParser struct {
	Expr string
	pos  int
}

func (p *PrometheusExprParser) Parse() (*PrometheusExprNode, error) {
	for {
		id, b, ok := p.ReadIdentity()
		if !ok {
			return &PrometheusExprNode{Type: PrometheusExprNodeTypeIdentity, Value: id}, nil
		}
		switch b {
		case '(':
			return p.ParseFunc(id)
		}
	}
}

func (p *PrometheusExprParser) ParseFunc(funname string) (*PrometheusExprNode, error) {
	node := &PrometheusExprNode{
		Type:  PrometheusExprNodeTypeFunc,
		Value: funname,
	}
	for {
		id, b, ok := p.ReadIdentity()
		if !ok {
			return nil, fmt.Errorf("unexpected end of expression")
		}
		switch b {
		case '(':
			funcnode, err := p.ParseFunc(id)
			if err != nil {
				return nil, err
			}
			node.Args = append(node.Args, *funcnode)
		case ')':
			node.Args = append(node.Args, PrometheusExprNode{Type: PrometheusExprNodeTypeIdentity, Value: id})
			return node, nil
		case ',':
			node.Args = append(node.Args, PrometheusExprNode{Type: PrometheusExprNodeTypeIdentity, Value: id})
		}
	}
}

func (p *PrometheusExprParser) ReadIdentity() (string, byte, bool) {
	prev := p.pos

	var startch byte
	started := false
	for {
		b, ok := p.read()
		if !ok {
			return "", 0, false
		}
		switch b {
		case '\'', '"':
			if !started {
				started = true
				startch = b
				prev = p.pos
			} else {
				if startch == b {
					return strings.TrimSpace(p.Expr[prev : p.pos-1]), b, true
				}
			}
		case '(', ',', ')':
			return strings.TrimSpace(p.Expr[prev : p.pos-1]), b, true
		case ' ':
			if started {
				return strings.TrimSpace(p.Expr[prev : p.pos-1]), b, true
			}
		default:
			if !started {
				started = true
				prev = p.pos
			}
		}
	}
}

func (p *PrometheusExprParser) read() (byte, bool) {
	if p.pos >= len(p.Expr) {
		return 0, false
	}
	b := p.Expr[p.pos]
	p.pos++
	return b, true
}

// expr: "label_values(node_uname_info, job)",
// expr: "query_result(topk(5, sum(rate(http_requests_total[$__range])) by (instance)))",

var (
	labelValuesRegex = regexp.MustCompile(`^label_values\((.+),\s*(.+)\)$`)
	queryResultRegex = regexp.MustCompile(`^query_result\((.+)\)$`)
)

func ExecutePrometheusExpr(ctx context.Context, expr string, ds PrometheusDatasource, params map[string]string) ([]string, error) {
	expr = templExpr(expr, params)
	expr = strings.TrimFunc(expr, unicode.IsSpace)

	if strings.HasPrefix(expr, "label_values") {
		matches := labelValuesRegex.FindStringSubmatch(expr)
		if len(matches) != 3 {
			return nil, fmt.Errorf("invalid label_values expression: %s", expr)
		}
		expr, label := matches[1], matches[2]
		// remove spaces or quotes
		label, _, _ = (&PrometheusExprParser{Expr: label}).ReadIdentity()
		vector, err := prometheus.QueryVector(ctx, ds, expr, time.Now())
		if err != nil {
			return nil, err
		}
		var values []string
		for _, vector := range vector.Result {
			if val, ok := vector.Metric[label]; ok {
				values = append(values, val)
			}
		}
		return values, nil
	}
	if strings.HasPrefix(expr, "query_result") {
		matches := queryResultRegex.FindStringSubmatch(expr)
		if len(matches) != 2 {
			return nil, fmt.Errorf("invalid query_result expression: %s", expr)
		}
		vector, err := prometheus.QueryVector(ctx, ds, expr, time.Now())
		if err != nil {
			return nil, err
		}
		var values []string
		for _, vector := range vector.Result {
			values = append(values, vector.Value.Value)
		}
		return values, nil
	}

	return nil, fmt.Errorf("unsupported expression: %s", expr)
}
