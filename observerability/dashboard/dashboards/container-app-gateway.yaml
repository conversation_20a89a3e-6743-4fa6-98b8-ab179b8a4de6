title: Basic Overview
templating:
  list:
    - name: namespace
    - name: instance
    - name: controller_namespace
    - name: controller_class
panels:
  - name: cpu_usage
    title: CPU Used Core
    fieldConfig:
      defaults:
        unit: cores
    targets:
      - expr: ismc_pod_cpu_used_cores{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        refId: A
        range: true

  - name: memory_usage
    title: Memory Used Bytes
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: ismc_pod_memory_used_bytes{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        refId: A
        range: true
      - expr: ismc_pod_memory_limit_bytes{namespace="$namespace", instance="$instance"}
        legendFormat: "{{ pod }}"
        refId: B
        range: true

  - name: connections
    title: Connections
    targets:
      - expr: nginx_ingress_controller_nginx_process_connections{namespace="$controller_namespace", controller_class="$controller_class"}
        legendFormat: "{{ state }} {{ pod }}"
        refId: A
        range: true

  - name: requests_per_second
    title: Requests
    fieldConfig:
      defaults:
        unit: qps
    targets:
      - expr: irate(nginx_ingress_controller_nginx_process_requests_total{namespace="$controller_namespace", controller_class="$controller_class"}[5m])
        legendFormat: "requests {{ pod }}"
        refId: A
        range: true

  - name: network_io
    title: Network IO
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: irate(nginx_ingress_controller_nginx_process_read_bytes_total{namespace="$controller_namespace", controller_class="$controller_class"}[5m])
        legendFormat: "read {{ pod }}"
        refId: A
        range: true
      - expr: irate(nginx_ingress_controller_nginx_process_write_bytes_total{namespace="$controller_namespace", controller_class="$controller_class"}[5m])
        legendFormat: "write {{ pod }}"
        refId: B
        range: true
time:
  from: now-1h
  to: now
  step: 1m
