title: Pod Overview
templating:
  list:
    - name: pod
    - name: namespace
panels:
  - name: cpu_usage
    title: CPU Usage
    fieldConfig:
      defaults:
        unit: cores
    targets:
      - expr: gems_container_cpu_usage_cores{pod="$pod", namespace="$namespace"}
        legendFormat: ""
        refId: A
        range: true

  - name: memory_usage
    title: Memory Usage
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: gems_container_memory_usage_bytes{pod="$pod", namespace="$namespace"}
        legendFormat: ""
        refId: A
        range: true

  - name: network
    title: Network I/O
    fieldConfig:
      defaults:
        unit: bps
    targets:
      - expr: gems_container_network_receive_bps{pod="$pod", namespace="$namespace"}
        legendFormat: "receive {{ pod }}"
        refId: A
        range: true
      - expr: gems_container_network_send_bps{pod="$pod", namespace="$namespace"}
        legendFormat: "send {{ pod }}"
        refId: B
        range: true

time:
  from: now-1h
  to: now
  step: 1m
