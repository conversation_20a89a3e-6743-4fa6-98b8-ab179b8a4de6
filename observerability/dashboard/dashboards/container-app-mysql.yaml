title: MySQL Exporter Overview
templating:
  list:
    - name: namespace
    - name: service
panels:
  - name: up
    title: Up
    targets:
      - expr: mysql_up{namespace="$namespace", service="$service"}
        legendFormat: "up"
        refId: A
        range: true

  - name: command_top10
    title: Command Top10
    fieldConfig:
      defaults:
        unit: qps
    targets:
      - expr: topk(10, rate(mysql_global_status_commands_total{namespace="$namespace", service="$service"}[5m])>0)
        legendFormat: "{{ command }}"
        refId: A
        range: true

  - name: max_connections
    title: Max Connections
    targets:
      - expr: mysql_global_status_max_used_connections{namespace="$namespace", service="$service"}
        legendFormat: "connections"
        refId: A
        range: true

  - name: error_connections_per_minute
    title: Error Connections(1m)
    fieldConfig:
      defaults:
        unit: opm
    targets:
      - expr: delta(mysql_global_status_connection_errors_total{namespace="$namespace", service="$service"}[1m])
        legendFormat: "{{ error }}"
        refId: A
        range: true

  - name: innodb_buffer_pool
    title: InnoDB Buffer Pool Size
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: mysql_global_variables_innodb_buffer_pool_size{namespace="$namespace", service="$service"}
        legendFormat: "innodb_buffer_pool_size"
        refId: A
        range: true

  - name: open_files
    title: Open Files
    targets:
      - expr: mysql_global_status_innodb_num_open_files{namespace="$namespace", service="$service"}
        legendFormat: "open_files"
        refId: A
        range: true

  - name: qps
    title: Query Rate
    fieldConfig:
      defaults:
        unit: qps
    targets:
      - expr: rate(mysql_global_status_queries{namespace="$namespace", service="$service"}[5m])
        legendFormat: "query"
        refId: A
        range: true
      - expr: rate(mysql_global_status_questions{namespace="$namespace", service="$service"}[5m])
        legendFormat: "question"
        refId: B
        range: true

  - name: network_io
    title: Network IO
    fieldConfig:
      defaults:
        unit: bps
    targets:
      - expr: irate(mysql_global_status_bytes_received{namespace="$namespace", service="$service"}[5m])
        legendFormat: "received"
        refId: A
        range: true
      - expr: irate(mysql_global_status_bytes_sent{namespace="$namespace", service="$service"}[5m])
        legendFormat: "sent"
        refId: B
        range: true

  - name: slow_queries_rate
    title: Slow Queries(1m)
    fieldConfig:
      defaults:
        unit: ops
    targets:
      - expr: idelta(mysql_global_status_slow_queries{namespace="$namespace", service="$service"}[1m])
        legendFormat: "slow queries"
        refId: A
        range: true

  - name: mysql_slow_query_enabled
    title: MySQL Slow Query Enabled
    targets:
      - expr: mysql_global_variables_slow_query_log{namespace="$namespace", service="$service"}
        legendFormat: "enabled"
        refId: A
        range: true

  - name: table_locks_waited_5m
    title: Table Locks Waited 5 Minutes
    fieldConfig:
      defaults:
        unit: ops
    targets:
      - expr: increase(mysql_global_status_table_locks_waited{namespace="$namespace", service="$service"}[5m])
        legendFormat: "table locks waited"
        refId: A
        range: true

  - name: table_open_cache_hit_ratio
    title: Table Open Cache Hit Ratio
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: rate(mysql_global_status_table_open_cache_hits{namespace="$namespace", service="$service"}[5m]) / ( rate(mysql_global_status_table_open_cache_hits{namespace="$namespace", service="$service"}[5m]) + rate(mysql_global_status_table_open_cache_misses{namespace="$namespace", service="$service"}[5m]))
        legendFormat: "open cache hits"
        refId: A
        range: true

  - name: tmp_table_created_rate
    title: Tmp Table Created Rate
    fieldConfig:
      defaults:
        unit: ops
    targets:
      - expr: rate(mysql_global_status_created_tmp_tables{namespace="$namespace", service="$service"}[5m])
        legendFormat: "created tmp tables"
        refId: A
        range: true

  - name: threads
    title: Threads Status
    targets:
      - expr: mysql_global_status_threads_running{namespace="$namespace", service="$service"}
        legendFormat: "running"
        refId: A
        range: true
      - expr: mysql_global_status_threads_connected{namespace="$namespace", service="$service"}
        legendFormat: "connected"
        refId: B
        range: true
      - expr: mysql_global_status_threads_cached{namespace="$namespace", service="$service"}
        legendFormat: "cached"
        refId: C
        range: true

  - name: innodb_log_waits
    title: InnoDB Log Waits
    targets:
      - expr: mysql_global_status_innodb_log_waits{namespace="$namespace", service="$service"}
        legendFormat: "innodb log waits"
        refId: A
        range: true

  - name: innodb_data_io
    title: InnoDB Data IO
    fieldConfig:
      defaults:
        unit: bps
    targets:
      - expr: rate(mysql_global_status_innodb_data_writes{namespace="$namespace", service="$service"}[5m])
        legendFormat: "write"
        refId: A
        range: true
      - expr: rate(mysql_global_status_innodb_data_read{namespace="$namespace", service="$service"}[5m])
        legendFormat: "read"
        refId: B
        range: true

  - name: handler_rate
    title: Handlers
    fieldConfig:
      defaults:
        unit: ops
    targets:
      - expr: rate(mysql_global_status_handlers_total{ handler=~"commit|rollback|savepoint.*|prepare",namespace="$namespace", service="$service"}[5m])
        legendFormat: "{{ handler }}"
        refId: A
        range: true

  - name: sort
    title: Sort Statistics
    fieldConfig:
      defaults:
        unit: rowsps
    targets:
      - expr: rate(mysql_global_status_sort_rows{namespace="$namespace", service="$service"}[5m])
        legendFormat: "sort rows"
        refId: A
        range: true
      - expr: rate(mysql_global_status_sort_range{namespace="$namespace", service="$service"}[5m])
        legendFormat: "sort range"
        refId: B
        range: true
      - expr: rate(mysql_global_status_sort_merge_passes{namespace="$namespace", service="$service"}[5m])
        legendFormat: "sort merge passes"
        refId: C
        range: true
      - expr: rate(mysql_global_status_sort_scan{namespace="$namespace", service="$service"}[5m])
        legendFormat: "sort scan"
        refId: D
        range: true

  - name: select
    title: Select Statistics
    fieldConfig:
      defaults:
        unit: ops
    targets:
      - expr: rate(mysql_global_status_select_full_join{namespace="$namespace", service="$service"}[5m])
        legendFormat: "select full join"
        refId: A
        range: true
      - expr: rate(mysql_global_status_select_full_range_join{namespace="$namespace", service="$service"}[5m])
        legendFormat: "select full range join"
        refId: B
        range: true
      - expr: rate(mysql_global_status_select_range{namespace="$namespace", service="$service"}[5m])
        legendFormat: "select range"
        refId: C
        range: true
      - expr: rate(mysql_global_status_select_scan{namespace="$namespace", service="$service"}[5m])
        legendFormat: "select scan"
        refId: D
        range: true
time:
  from: now-1h
  to: now
  step: 1m
