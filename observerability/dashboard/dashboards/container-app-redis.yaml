title: Exporter Overview
templating:
  list:
    - name: namespace
    - name: service
panels:
  - name: redis_cpu_average_ratio
    title: CPU Average Ratio
    fieldConfig:
      defaults:
        unit: percent
    targets:
      - expr: rate(redis_cpu_user_seconds_total{namespace=~"$namespace",service="$service"}[5m]) * 100
        refId: A
        range: true
        legendFormat: "{{ pod }} | user_sys"
      - expr: rate(redis_cpu_sys_seconds_total{namespace=~"$namespace",service="$service"}[5m]) * 100
        refId: B
        range: true
        legendFormat: "{{ pod }} | cpu_sys"

  - name: redis_memory_used_bytes
    title: Memory Used Bytes
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: redis_memory_used_bytes{namespace="$namespace", service="$service"}
        legendFormat: memory
        refId: A
        range: true

  - name: command_top5
    title: Command Top5
    fieldConfig:
      defaults:
        unit: qps
    targets:
      - expr: topk(5, irate(redis_commands_total{namespace="$namespace", service="$service"}[5m]))
        legendFormat: "{{ cmd }}"
        refId: A
        range: true

  - name: connected_clients
    title: Connected Clients
    targets:
      - expr: redis_connected_clients{namespace="$namespace", service="$service"}
        legendFormat: "clients"
        refId: A
        range: true

  - name: db_keys
    title: DB Total and Expiring Keys
    targets:
      # allow db0 to be included in the query in case there are no results returned
      - expr: redis_db_keys{namespace="$namespace", service="$service"} > 0 or redis_db_keys{namespace="$namespace", service="$service", db="db0"}
        legendFormat: "total {{ db }}"
        refId: A
        range: true
      - expr: redis_db_keys_expiring{namespace="$namespace", service="$service"} > 0 or redis_db_keys_expiring{namespace="$namespace", service="$service", db="db0"}
        legendFormat: "expiring {{ db }}"
        refId: B
        range: true

  - name: keys_evicted_expired
    title: Keys Evicted and Expired
    targets:
      - expr: irate(redis_evicted_keys_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "evicted"
        refId: A
        range: true
      - expr: irate(redis_expired_keys_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "expired"
        refId: B
        range: true

  - name: keyspace_hits_rate
    fieldConfig:
      defaults:
        unit: percent
    title: Keyspace Hits Rate
    targets:
      - expr: redis_keyspace_hits_total{namespace="$namespace", service="$service"} / (redis_keyspace_hits_total{namespace="$namespace", service="$service"} + redis_keyspace_misses_total{namespace="$namespace", service="$service"})
        legendFormat: "hits rate"
        refId: A
        range: true

  - name: keyspace_hits_misses
    title: Keyspace Hits and Misses
    fieldConfig:
      defaults:
        unit: iops
    targets:
      - expr: irate(redis_keyspace_misses_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "missed"
        refId: A
        range: true
      - expr: irate(redis_keyspace_hits_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "hits"
        refId: B
        range: true

  - name: commands_processed
    title: Commands Processed
    fieldConfig:
      defaults:
        unit: iops
    targets:
      - expr: irate(redis_commands_processed_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "processed"
        refId: A
        range: true

  - name: redis_net_io
    title: Network IO
    fieldConfig:
      defaults:
        unit: bps
    targets:
      - expr: irate(redis_net_input_bytes_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "input"
        refId: A
        range: true
      - expr: irate(redis_net_output_bytes_total{namespace="$namespace", service="$service"}[5m])
        legendFormat: "output"
        refId: B
        range: true

time:
  from: now-1h
  to: now
  step: 1m
