title: Persistent Volume Claim Overview
templating:
  list:
    - name: namespace
    - name: persistentvolumeclaim
panels:
  - name: used_bytes
    title: Used Bytes
    fieldConfig:
      defaults:
        unit: bytes
    targets:
      - expr: kubelet_volume_stats_used_bytes{namespace="$namespace",persistentvolumeclaim="$persistentvolumeclaim"}
        legendFormat: "used {{ persistentvolumeclaim }}"
        refId: A
        range: true
      - expr: kubelet_volume_stats_capacity_bytes{namespace="$namespace",persistentvolumeclaim="$persistentvolumeclaim"}
        legendFormat: "total {{ persistentvolumeclaim }}"
        refId: B
        range: true

  - name: inodes
    title: Inodes
    targets:
      - expr: kubelet_volume_stats_inodes_used{namespace="$namespace",persistentvolumeclaim="$persistentvolumeclaim"}
        legendFormat: "used {{ persistentvolumeclaim }}"
        refId: A
        range: true
      - expr: kubelet_volume_stats_inodes{namespace="$namespace",persistentvolumeclaim="$persistentvolumeclaim"}
        legendFormat: "total {{ persistentvolumeclaim }}"
        refId: B
        range: true

time:
  from: now-1h
  to: now
  step: 1m
