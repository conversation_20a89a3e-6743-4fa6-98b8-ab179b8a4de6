package observerability

import (
	"context"

	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/organization"
)

var _ controller.Reconciler[*AlertChannel] = &AlertChannelReconciler{}

type AlertChannelReconciler struct {
	Store  store.Store
	Clouds cluster.CloudInfoGetter
}

func NewAlertChannelReconciler(storage store.Store, cloudinfo cluster.CloudInfoGetter) (*controller.Controller, error) {
	rec := &AlertChannelReconciler{
		Store:  storage,
		Clouds: cloudinfo,
	}
	better := controller.NewBetterReconciler(rec, storage,
		controller.WithFinalizer("alertchannel-controller"),
	)
	c := controller.
		NewController("alertchannels", better).
		Watch(
			controller.NewStoreSource(storage, &AlertChannel{}),
		)
	return c, nil
}

// Remove implements controller.Reconciler.
// 当通知渠道被删除时，该渠道绑定的告警规则一起删除
func (a *AlertChannelReconciler) Remove(ctx context.Context, obj *AlertChannel) (controller.Result, error) {
	return base.DispatchOnScopes(ctx, obj, base.ScopeHandler[*AlertChannel]{
		On:                   a.removeSystemChannel,
		OnTenant:             a.removeTenantChannel,
		OnTenantOrganization: a.removeTenantOrganizationChannel,
	})
}

// 删除系统通知渠道
func (a *AlertChannelReconciler) removeSystemChannel(ctx context.Context, obj *AlertChannel) (controller.Result, error) {
	return base.UnwrapReQueueError(a.removeSystemChannelE(ctx, obj))
}

func (a *AlertChannelReconciler) removeSystemChannelE(ctx context.Context, obj *AlertChannel) error {
	// 获取所有集群下的告警规则
	list := store.List[cluster.Cluster]{}
	if err := a.Store.List(ctx, &list); err != nil {
		return err
	}
	for _, item := range list.Items {
		var clusterRules = store.List[AlertRule]{}
		cloud, err := a.Clouds.Get(ctx, store.ObjectReference{
			Name: item.Name,
		})
		if err != nil {
			return err
		}
		op, err := cluster.NewContainerOperation(cloud)
		if err != nil {
			return err
		}
		clusterStore := a.Store.Scope(base.ScopeCluster(item.Name))
		err = clusterStore.List(ctx, &clusterRules)
		if err != nil {
			return err
		}
		if err := removeAlertRuleBindByChannel(ctx, clusterStore, clusterRules.Items, obj.Name); err != nil {
			return err
		}
		if err := op.AlertManagerReload(ctx); err != nil {
			log.FromContext(ctx).Error(err, "failed to reload alertmanager")
		}
	}

	return nil
}

func (a *AlertChannelReconciler) removeTenantChannel(ctx context.Context, tenantname string, obj *AlertChannel) (controller.Result, error) {
	return base.UnwrapReQueueError(a.removeTenantChannelE(ctx, tenantname, obj))
}

func (a *AlertChannelReconciler) removeTenantChannelE(ctx context.Context, tenantname string, obj *AlertChannel) error {
	tenantStorage := a.Store.Scope(base.ScopeTenant(tenantname))
	orgs := &store.List[organization.Organization]{}
	if err := tenantStorage.List(ctx, orgs); err != nil {
		return err
	}

	for _, org := range orgs.Items {
		if err := a.removeTenantOrganizationChannelE(ctx, tenantname, org.Name, obj); err != nil {
			return err
		}
	}
	return nil
}

func (a *AlertChannelReconciler) removeTenantOrganizationChannel(ctx context.Context, tenantname, org string, obj *AlertChannel) (controller.Result, error) {
	return base.UnwrapReQueueError(a.removeTenantOrganizationChannelE(ctx, tenantname, org, obj))
}

func (a *AlertChannelReconciler) removeTenantOrganizationChannelE(ctx context.Context, tenantname, org string, obj *AlertChannel) error {
	orgStorage := a.Store.Scope(store.Scope{Resource: "tenants", Name: tenantname}).Scope(store.Scope{Resource: "organizations", Name: org})
	rules := &store.List[AlertRule]{}
	if err := orgStorage.List(ctx, rules); err != nil {
		return err
	}
	// 移除组织下的告警规则
	if err := removeAlertRuleBindByChannel(ctx, orgStorage, rules.Items, obj.Name); err != nil {
		return err
	}
	// 移除组织下应用的告警规则
	return a.removeApplicationAlertRule(ctx, tenantname, org, obj)
}

func (a *AlertChannelReconciler) removeApplicationAlertRule(ctx context.Context, tenantname, org string, obj *AlertChannel) error {
	// 获取组织下的应用
	orgStorage := a.Store.Scope(store.Scope{Resource: "tenants", Name: tenantname}).Scope(store.Scope{Resource: "organizations", Name: org})
	apps := &store.List[application.Application]{}
	if err := orgStorage.List(ctx, apps); err != nil {
		return err
	}
	// 获取应用下的规则
	for _, app := range apps.Items {
		appStorage := orgStorage.Scope(store.Scope{Resource: "applications", Name: app.Name})
		rules := &store.List[AlertRule]{}
		if err := appStorage.List(ctx, rules); err != nil {
			return err
		}
		if err := removeAlertRuleBindByChannel(ctx, appStorage, rules.Items, obj.Name); err != nil {
			return err
		}
	}
	return nil
}

func removeAlertRuleBindByChannel(ctx context.Context, store store.Store, rules []AlertRule, channelName string) error {
	for i := range rules {
		item := &rules[i]
		for i := 0; i < len(item.Receivers); i++ {
			if item.Receivers[i].AlertChannel != nil {
				if item.Receivers[i].AlertChannel.Name == channelName {
					item.Receivers = append(item.Receivers[:i], item.Receivers[i+1:]...)
					i--
				}
			}
		}
		if err := store.Update(ctx, item); err != nil {
			return err
		}
	}
	return nil
}

// Sync implements controller.Reconciler.
// 只处理更新告警渠道逻辑
func (a *AlertChannelReconciler) Sync(ctx context.Context, obj *AlertChannel) (controller.Result, error) {
	// tenantname, organizationname, _ := tenantOrganizationAppFromScopes(obj.GetScopes())
	// if tenantname == "" {
	// 	return nil
	// }
	// if organizationname == "" {
	// 	return a.syncTenantChannel(ctx, tenantname, obj)
	// }
	// return a.syncTenantOrganizationChannel(ctx, tenantname, organizationname, obj)
	return controller.Result{}, nil
}

// func (a *AlertChannelReconciler) syncTenantChannel(ctx context.Context, tenantname string, obj *AlertChannel) error {
// 	if obj.ChannelType != alertchannel.TypeEmail {
// 		return nil
// 	}
// 	clusterrefs, err := resourcequota.ListClustersUsedByTenant(ctx, a.Store, tenantname)
// 	if err != nil {
// 		return err
// 	}
// 	for _, ref := range clusterrefs {
// 		cloud, err := a.Clouds.Get(ctx, ref.Reference)
// 		if err != nil {
// 			return err
// 		}
// 		clients, err := cloud.KubernetesConfig()
// 		if err != nil {
// 			return err
// 		}
// 		if err := syncEmailSecret(ctx, clients.Client, obj); err != nil {
// 			return err
// 		}
// 	}
// 	return nil
// }

// func (a *AlertChannelReconciler) syncTenantOrganizationChannel(ctx context.Context, tenantname, org string, obj *AlertChannel) error {
// clusterrefs, err := resourcequota.ListClustersUsedByTenantOrganization(ctx, a.Store, tenantname, org)
// if err != nil {
// 	return err
// }
// 	for _, ref := range clusterrefs {
// 		cloud, err := a.Clouds.Get(ctx, ref.Reference)
// 		if err != nil {
// 			return err
// 		}
// 		clients, err := cloud.KubernetesConfig()
// 		if err != nil {
// 			return err
// 		}
// 		if err := syncEmailSecret(ctx, clients.Client, obj); err != nil {
// 			return err
// 		}
// 	}
// 	return nil
// }

// func syncEmailSecret(ctx context.Context, cli client.Client, obj *AlertChannel) error {
// 	return nil
// 	email := obj.ChannelEmail
// 	if email == nil {
// 		return fmt.Errorf("email config is nil pointer")
// 	}
// 	// 如果告警渠道类型时email的话,需要下发secret
// 	secretName := fmt.Sprintf("%s-%s", obj.ReceiverName(), alertchannel.EmailSecretName)
// 	secret := &v1.Secret{
// 		ObjectMeta: metav1.ObjectMeta{
// 			Name: secretName,
// 			//Namespace: "", 使用哪个namespace
// 			Labels: map[string]string{
// 				alertchannel.EmailSecretLabelKey: alertchannel.EmailSecretLabelValue,
// 			},
// 		},
// 		Type: v1.SecretTypeOpaque,
// 	}
// 	_, err := controllerutil.CreateOrUpdate(ctx, cli, secret, func() error {
// 		if secret.Data == nil {
// 			secret.Data = make(map[string][]byte)
// 		}
// 		secret.Data[alertchannel.EmailSecretKey(secretName, email.From)] = []byte(email.AuthPassword)
// 		return nil
// 	})
// 	return err
// }
