package observerability

import (
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/observerability/dashboard"
)

// 迁移
func init() {
	mongo.GlobalObjectsScheme.Register(&AlertRecord{},
		mongo.ObjectDefination{
			// Uniques: []mongo.UnionFields{
			// 	{"name"},
			// },
			Indexes: []mongo.UnionFields{
				{"tenant"},
				{"cluster"},
				{"organization"},
				{"application"},
			},
		})
}

func NewApplicationObservabilityAPI(base *application.ApplicationStatusAPI, dashboards map[string]dashboard.DashboradConfiguration) *ApplicationObservabilityAPI {
	return &ApplicationObservabilityAPI{
		ApplicationStatusAPI: base,
		BuiltInDashboards:    dashboards,
	}
}

type ApplicationObservabilityAPI struct {
	*application.ApplicationStatusAPI
	BuiltInDashboards map[string]dashboard.DashboradConfiguration
}

func (a *ApplicationObservabilityAPI) Group() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			// logging
			a.ApplicationsLoggingGroup(),
			// monitoring
			a.ApplicationsMonitoringGroup(),
			// metrics dashboard
			a.ApplicationsMetricsDashboardGroup(),
			// tracing
			a.ApplicationsTracingGroup(),
			// open telementry
			a.ApplicationsOpenTelementryGroup(),
			// alert rule
			a.ApplicationAlertRuleGroup(),
			// alert rule template
			a.ApplicationRuleTemplateGroup(),
			// alert record
			a.ApplicationAlertRecordGroup(),
		)
}
