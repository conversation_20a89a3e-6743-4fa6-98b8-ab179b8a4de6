#!/usr/bin/env bash

set -o errexit
set -o nounset
set -o pipefail

SCRIPT_ROOT=$(dirname "${BASH_SOURCE[0]}")
BUILD_ROOT=${BUILD_ROOT:-.}
THIS_PKG=${THIS_PKG:-$(go list)}
API_KNOWN_VIOLATIONS_FILE=${API_KNOWN_VIOLATIONS_FILE:-${SCRIPT_ROOT}/violation_exceptions.list}
BOILERPLATE=${BOILERPLATE:-${SCRIPT_ROOT}/boilerplate.txt}

GOBIN=$SCRIPT_ROOT/../bin
CODEGEN_ROOT=$(go list -f '{{.Dir}}' k8s.io/code-generator)
source "$(go list -f '{{.Dir}}' k8s.io/code-generator)/kube_codegen.sh"

# this env var is used in codegen scripts, must be set
KUBE_CODEGEN_ROOT=${BUILD_ROOT}

kube::codegen::gen_helpers \
    --boilerplate "${BOILERPLATE}" \
    "${BUILD_ROOT}/apis"

if [[ "${UPDATE_API_KNOWN_VIOLATIONS:-}" == "true" ]]; then
    update_report="--update-report"
fi

kube::codegen::gen_openapi \
    --output-dir "${BUILD_ROOT}/generated/openapi" \
    --output-pkg "${THIS_PKG}/generated/openapi" \
    --report-filename "${API_KNOWN_VIOLATIONS_FILE}" \
    ${update_report:+"${update_report}"} \
    --boilerplate "${BOILERPLATE}" \
    "${BUILD_ROOT}/apis"

kube::codegen::gen_client \
    --with-watch \
    --output-dir "${BUILD_ROOT}/generated" \
    --output-pkg "${THIS_PKG}/generated" \
    --boilerplate "${BOILERPLATE}" \
    "${BUILD_ROOT}/apis"
