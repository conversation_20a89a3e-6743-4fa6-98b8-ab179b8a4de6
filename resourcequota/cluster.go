package resourcequota

import (
	"context"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func (a *API) ListClusterResourcequotas(w http.ResponseWriter, r *http.Request) {
	a.onClusterOrTenantCluster(w, r, func(ctx context.Context, ref store.ObjectReference) (any, error) {
		options := []store.ListOption{
			store.WithSubScopes(),
			store.WithFieldRequirements(store.RequirementEqual("name", ClusterReferenceToQuotaName (ref))),
		}
		quotalist := &store.List[ResourceQuota]{}
		if err := a.Store.Scope(ref.Scopes...).List(ctx, quotalist, options...); err != nil {
			return nil, err
		}
		// filter
		filtered := make([]ResourceQuota, 0, len(quotalist.Items))
		for _, quota := range quotalist.Items {
			if len(quota.Scopes) != len(ref.Scopes)+1 {
				continue
			}
			filtered = append(filtered, quota)
		}
		quotalist.Items = filtered
		return quotalist, nil
	})
}

func (a *API) onClusterOrTenantCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, ref store.ObjectReference) (any, error)) {
	a.On(w, r, func(ctx context.Context, storage store.Store) (any, error) {
		tenant := api.Path(r, "tenant", "")
		cluster := api.Path(r, "cluster", "")
		if cluster == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		ref := store.ObjectReference{
			Name: cluster,
		}
		if tenant != "" {
			ref.Scopes = append(ref.Scopes, base.ScopeTenant(tenant))
		}
		return fn(ctx, ref)
	})
}

func (a *API) ClusterGroup() api.Group {
	return api.NewGroup("").
		SubGroup(
			base.NewClusterGroup("resourcequotas").
				Route(
					api.GET("").
						To(a.ListClusterResourcequotas).
						Response(store.List[ResourceQuota]{}),
				),
			base.NewTenantClusterGroup("resourcequotas").
				Route(
					api.GET("").
						To(a.ListClusterResourcequotas).
						Response(store.List[ResourceQuota]{}),
				),
		)
}
