package resourcequota

import (
	"context"
	"fmt"
	"net/http"
	"slices"

	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
)

// FeatureUseSyncModeInResourceQuota
// 在资源配额中使用同步模式进行操作
const FeatureUseSyncModeInResourceQuota bool = true

// FeatureSetToZeroOnRmoveResourceQuota
// 在删除资源配额时，将资源配额设置为0
const FeatureSetToZeroOnRmoveResourceQuota bool = true

// ResourceQuota is the resource quota of the tenant
// tenants/<tenant>
// tenants/<tenant>/organizations/<organization>
type ResourceQuota struct {
	store.ObjectMeta `json:",inline"`
	Cluster          store.ObjectReference `json:"cluster,omitempty"`
	Hard             base.ResourceList     `json:"hard,omitempty"`
	Status           ResourceQuotaStatus   `json:"status,omitempty"`
}

type ResourceQuotaStatus struct {
	Message string            `json:"message,omitempty"`
	Hard    base.ResourceList `json:"hard,omitempty"`
	Used    base.ResourceList `json:"used,omitempty"`
}

func NewAPI(base base.API, clusterInfo cluster.CloudInfoGetter) *API {
	return &API{API: base, ClusterInfo: clusterInfo}
}

type API struct {
	base.API
	ClusterInfo cluster.CloudInfoGetter
}

func (a *API) SetTenantResourceQuota(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		req := &SetResourceQuota{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		quotaname := ClusterReferenceToQuotaName(req.Cluster)
		cluster := &cluster.Cluster{}
		if err := a.Store.Scope(req.Cluster.Scopes...).Get(ctx, quotaname, cluster); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		exmaple := &ResourceQuota{
			ObjectMeta: store.ObjectMeta{Name: quotaname},
		}
		err := store.CreateOrUpdate(ctx, storage, exmaple, func() error {
			exmaple.Hard = req.Hard
			exmaple.Cluster = req.Cluster
			if FeatureUseSyncModeInResourceQuota {
				op := ResourceQuotaOperation{Clusters: a.ClusterInfo, Client: a.Store}
				return op.SetTenantResourceQuota(ctx, tenant, exmaple)
			}
			return nil
		})
		return exmaple, err
	})
}

func (a *API) UnSetTenantResourceQuota(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		req := &SetResourceQuota{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		quotaname := ClusterReferenceToQuotaName(req.Cluster)
		quota := &ResourceQuota{ObjectMeta: store.ObjectMeta{Name: quotaname}}
		if FeatureUseSyncModeInResourceQuota {
			if err := storage.Get(ctx, quota.Name, quota); err != nil {
				return nil, err
			}
			op := ResourceQuotaOperation{Clusters: a.ClusterInfo, Client: a.Store}
			if err := op.UnsetTenantResourceQuota(ctx, tenant, quota); err != nil {
				return nil, err
			}
			// remove finalizers if needed
			if controller.RemoveFinalizer(quota, ResourceQuotaFinalizer) {
				if err := storage.Update(ctx, quota); err != nil {
					return nil, err
				}
			}
		}
		if err := storage.Delete(ctx, quota); err != nil {
			return nil, err
		}
		return quota, nil
	})
}

func (a *API) SystemGroup() api.Group {
	return api.
		NewGroup("tenantresourcequotas").
		Route(
			api.PUT("/{tenant}").
				To(a.SetTenantResourceQuota).
				Operation("create tenant resourcequota").
				Param(
					api.BodyParam("quota", &SetResourceQuota{}),
				),
			api.DELETE("/{tenant}").
				To(a.UnSetTenantResourceQuota).
				Operation("delete tenant resourcequota").
				Param(
					api.BodyParam("ref", &SetResourceQuota{}),
				),
		)
}

type ResourceQuotaResponse struct {
	ResourceQuota `json:",inline"`
	ClusterInfo   cluster.Cluster `json:"clusterInfo,omitempty"`
}

func (a *API) ListTenantResourceQuotas(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenantname string) (any, error) {
		if !FeatureUseSyncModeInResourceQuota {
			return a.listTenantResourceQuotas(ctx, storage, tenantname)
		}
		return a.listTenantResourceQuotaSync(ctx, tenantname)
	})
}

func (a *API) listTenantResourceQuotas(ctx context.Context, storage store.Store, _ string) ([]ResourceQuotaResponse, error) {
	resourcequotalist := &store.List[ResourceQuota]{}
	if err := storage.List(ctx, resourcequotalist); err != nil {
		return nil, err
	}
	return a.convertResourcequotalistWithCluster(ctx, resourcequotalist.Items, "")
}

// listTenantResourceQuotaSync list tenant resource quota
// it calc the used resource from the organizations real time
func (a *API) listTenantResourceQuotaSync(ctx context.Context, tenant string) ([]ResourceQuotaResponse, error) {
	resourcequotalist := &store.List[ResourceQuota]{}
	if err := a.Store.Scope(base.ScopeTenant(tenant)).List(ctx, resourcequotalist, store.WithSubScopes()); err != nil {
		return nil, err
	}
	perclusterused := map[string]base.ResourceList{}

	ret := []ResourceQuota{}
	for _, rq := range resourcequotalist.Items {
		quotatenant, org := base.TenantOrganizationFromScopes(rq.Scopes...)
		if quotatenant != tenant {
			continue
		}
		if org == "" {
			ret = append(ret, rq)
			continue
		}
		if _, ok := perclusterused[rq.Name]; !ok {
			perclusterused[rq.Name] = base.ResourceList{}
		}
		base.AddResourceList(perclusterused[rq.Name], rq.Hard)
	}
	for i, rq := range ret {
		ret[i].Status.Used = perclusterused[rq.Name]
	}
	return a.convertResourcequotalistWithCluster(ctx, ret, "")
}

func (a *API) convertResourcequotalistWithCluster(ctx context.Context, list []ResourceQuota, fillusednamespace string) ([]ResourceQuotaResponse, error) {
	withclusters := []ResourceQuotaResponse{}
	for _, rq := range list {
		ref := rq.Cluster
		c := &cluster.Cluster{}
		if err := a.Store.Scope(ref.Scopes...).Get(ctx, ref.Name, c); err != nil {
			// ignore error
			log := log.FromContext(ctx)
			log.Error(err, "get quota cluster")
			if errors.IsNotFound(err) {
				log.Info("cluster not found but resource quota exists,remove quota", "quota", rq.Name)
				if err := a.Store.Scope(rq.Scopes...).Delete(ctx, &rq); err != nil {
					log.Error(err, "delete resource quota")
				}
				continue
			}
		}
		if fillusednamespace != "" {
			switch cluster.ClusterCategoryFrom(c.Type) {
			case cluster.ClusterCategoryContainer:
				info, err := a.ClusterInfo.Get(ctx, store.ObjectReferenceFrom(c))
				if err != nil {
					continue
				}
				kubeclients, err := info.KubernetesConfig()
				if err != nil {
					continue
				}
				k8srq := &corev1.ResourceQuota{}
				_ = kubeclients.Client.Get(ctx, client.ObjectKey{Namespace: fillusednamespace, Name: "default"}, k8srq)
				rq.Status.Used = k8srq.Status.Used
			}
		}
		withclusters = append(withclusters, ResourceQuotaResponse{
			ResourceQuota: rq,
			ClusterInfo:   cluster.OmmitClusterConfig(*c),
		})
	}
	return withclusters, nil
}

func (a *API) SetOrganizationResourceQuota(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, organization string) (any, error) {
		req := &SetResourceQuota{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		ref := req.Cluster
		// check cluster
		cluster := &cluster.Cluster{}
		if err := a.Store.Scope(ref.Scopes...).Get(ctx, ref.Name, cluster); err != nil {
			return nil, errors.NewBadRequest(err.Error())
		}
		// check tenant quota overlimited
		if len(ref.Scopes) == 0 {
			if err := CheckResourcequotaOverLimitOnTenant(ctx, a.Store, ref.Name, tenant, organization, req.Hard); err != nil {
				return nil, err
			}
		}
		exmaple := &ResourceQuota{
			ObjectMeta: store.ObjectMeta{Name: ClusterReferenceToQuotaName(ref)},
		}
		err := store.CreateOrUpdate(ctx, storage, exmaple, func() error {
			exmaple.Hard = req.Hard
			exmaple.Cluster = req.Cluster
			if FeatureUseSyncModeInResourceQuota {
				op := ResourceQuotaOperation{Clusters: a.ClusterInfo, Client: a.Store}
				return op.SetOrganizationResourceQuota(ctx, tenant, organization, exmaple)
			}
			return nil
		})
		return exmaple, err
	})
}

func CheckResourcequotaOverLimitOnTenant(ctx context.Context, storage store.Store, cluster, tenant, org string, hard base.ResourceList) error {
	tenantquota := &ResourceQuota{}
	if err := storage.Scope(base.ScopeTenant(tenant)).Get(ctx, cluster, tenantquota); err != nil {
		if errors.IsNotFound(err) {
			return errors.NewBadRequest("tenant resource quota not set on the cluster")
		}
		return err
	}
	tenantlimit := tenantquota.Hard

	organused, err := CalcAllOrganizationsUsed(ctx, storage, store.ObjectReference{Name: cluster}, tenant, org)
	if err != nil {
		return err
	}
	// other organization resource quota + current organization resource quota
	base.AddResourceList(organused, hard)
	if overlimit := CheckOverLimit(tenantlimit, organused); len(overlimit) > 0 {
		return liberrors.NewBadRequest(fmt.Sprintf("over the tenant resource quota limit: %s", overlimit.String()))
	}
	return nil
}

type ResourceQuotaWithTenantOrganization struct {
	ResourceQuota `json:",inline"`
	Tenant        string `json:"tenant,omitempty"`
	Organization  string `json:"organization,omitempty"`
}

func ListAllOrganizationsQuota(ctx context.Context, storage store.Store, cluster store.ObjectReference, tenant string) ([]ResourceQuotaWithTenantOrganization, error) {
	list := store.List[ResourceQuota]{}
	if err := storage.
		Scope(base.ScopeTenant(tenant)).
		List(ctx, &list,
			store.WithSubScopes(), // may include tenant resource quota self
			store.WithFieldRequirements(store.RequirementEqual("name", ClusterReferenceToQuotaName(cluster)))); err != nil {
		return nil, err
	}
	orglist := []ResourceQuotaWithTenantOrganization{}
	for _, rq := range list.Items {
		if rq.DeletionTimestamp != nil {
			continue
		}
		thistenant, thisorg := base.TenantOrganizationFromScopes(rq.Scopes...)
		if thistenant != tenant || thisorg == "" {
			continue
		}
		if !rq.Cluster.Equals(cluster) {
			continue
		}
		orglist = append(orglist, ResourceQuotaWithTenantOrganization{
			ResourceQuota: rq,
			Tenant:        thistenant,
			Organization:  thisorg,
		})
	}
	return orglist, nil
}

func CalcAllOrganizationsUsed(ctx context.Context, storage store.Store, cluster store.ObjectReference, tenant string, excludeorg ...string) (base.ResourceList, error) {
	orglist, err := ListAllOrganizationsQuota(ctx, storage, cluster, tenant)
	if err != nil {
		return nil, err
	}
	organused := base.ResourceList{}
	for _, rq := range orglist {
		if len(excludeorg) > 0 && slices.Contains(excludeorg, rq.Organization) {
			continue
		}
		base.AddResourceList(organused, rq.Hard)
	}
	return organused, nil
}

func (a *API) UnSetOrganizationResourceQuota(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, organization string) (any, error) {
		req := &SetResourceQuota{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		if req.Cluster.Name == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		ref := req.Cluster
		quota := &ResourceQuota{ObjectMeta: store.ObjectMeta{Name: ClusterReferenceToQuotaName(ref)}}
		if FeatureUseSyncModeInResourceQuota {
			if err := storage.Get(ctx, quota.Name, quota); err != nil {
				return nil, err
			}
			op := ResourceQuotaOperation{Clusters: a.ClusterInfo, Client: a.Store}
			if err := op.UnsetOrganizationResourceQuota(ctx, tenant, organization, quota); err != nil {
				return nil, err
			}
		}
		if err := storage.Delete(ctx, quota); err != nil {
			return nil, err
		}
		return quota, nil
	})
}

func ClusterReferenceToQuotaName(cluster store.ObjectReference) string {
	return base.ReferenceToName(cluster)
}

type SetResourceQuota struct {
	Cluster store.ObjectReference `json:"cluster,omitempty"`
	Hard    base.ResourceList     `json:"hard,omitempty"`
}

func (a *API) TenantGroup() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			base.
				NewTenantGroup("resourcequotas").
				Route(
					api.GET("").
						To(a.ListTenantResourceQuotas).
						Param(api.PageParams...).
						Operation("list resource quotas").
						Response(&store.List[ResourceQuotaResponse]{}),
				),
			base.
				NewTenantGroup("organizationresourcequotas").
				Route(
					api.GET("").
						To(a.ListTenantResourceQuotas).
						Param(api.PageParams...).
						Operation("list resource quotas").
						Response(&store.List[ResourceQuotaResponse]{}),

					api.PUT("/{organization}").
						To(a.SetOrganizationResourceQuota).
						Operation("set organization resourcequota").
						Param(
							api.BodyParam("quota", &SetResourceQuota{}),
						),

					api.DELETE("/{organization}").
						To(a.UnSetOrganizationResourceQuota).
						Operation("unset organization resourcequota").
						Param(
							api.BodyParam("quota", &SetResourceQuota{}),
						),
				),
		)
}

func (a *API) ListOrganizationResourceQuotas(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenantname, org string) (any, error) {
		resourcequotalist := &store.List[ResourceQuota]{}
		if err := storage.List(ctx, resourcequotalist); err != nil {
			return nil, err
		}
		return a.convertResourcequotalistWithCluster(ctx, resourcequotalist.Items,
			base.GetNamespaceFromTenantOrganization(tenantname, org))
	})
}

func (a *API) OrganizationGroup() api.Group {
	return base.
		NewTenantWorkspaceGroup("resourcequotas").
		Route(
			api.GET("").
				To(a.ListOrganizationResourceQuotas).
				Param(api.PageParams...).
				Operation("list resource quotas").
				Response(&store.List[ResourceQuota]{}),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("ResourceQuotas").
		SubGroup(
			a.SystemGroup(),
			a.TenantGroup(),
			a.OrganizationGroup(),
			a.ClusterGroup(),
		)
}
