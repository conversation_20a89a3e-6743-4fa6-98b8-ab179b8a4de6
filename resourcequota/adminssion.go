package resourcequota

import (
	"context"

	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"

	"xiaoshiai.cn/core/base"
)

var _ api.AdmissionPlugin = &ResourceQuotaLicenseAdminssion{}

func NewResourceQuotaLicenseAdminssion(storage store.Store) *ResourceQuotaLicenseAdminssion {
	return &ResourceQuotaLicenseAdminssion{
		Storage: storage,
	}
}

type ResourceQuotaLicenseAdminssion struct {
	Storage store.Store
}

// Match implements api.AdminssionPlugin.
func (a *ResourceQuotaLicenseAdminssion) Match(attr api.Attributes) bool {
	return attr.Action == "create" && base.IsResourcesMatch(
		attr.Resources,
		"tenants",
		"organizations",
		"applications",
	)
}

// Admit implements api.AdminssionPlugin.
func (a *ResourceQuotaLicenseAdminssion) Admit(ctx context.Context, attr api.Attributes, data api.AdmissionContent) (bool, error) {
	tenantName := attr.Resources[0].Name
	if tenantName == "" {
		return false, liberrors.NewBadRequest("tenant name is required")
	}
	return true, nil
}
