

import argparse
import re
import time
from typing import List, Optional
from datetime import datetime
import boto3
from botocore.config import Config
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from tabulate import tabulate
import humanize


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description='S3 Object Search Tool - List and search objects in S3 buckets',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all objects in a bucket
  %(prog)s --access-key ACCESS_KEY --secret-key SECRET_KEY --endpoint https://s3.example.com --bucket my-bucket

  # Search objects with pattern matching
  %(prog)s --access-key ACCESS_KEY --secret-key SECRET_KEY --endpoint https://s3.example.com --bucket my-bucket --pattern "*.jpg"

  # List first 100 objects only
  %(prog)s --access-key ACCESS_KEY --secret-key SECRET_KEY --endpoint https://s3.example.com --bucket my-bucket --max-keys 100

  # Skip SSL verification (useful for self-signed certificates)
  %(prog)s --access-key ACCESS_KEY --secret-key SECRET_KEY --endpoint https://s3.example.com --bucket my-bucket --no-verify
""")

    # Required arguments
    required = parser.add_argument_group('required arguments')
    required.add_argument(
        '--access-key',
        required=True,
        help='S3 access key for authentication'
    )
    required.add_argument(
        '--secret-key',
        required=True,
        help='S3 secret key for authentication'
    )
    required.add_argument(
        '--endpoint',
        required=True,
        help='S3 endpoint URL (e.g., https://s3.example.com)'
    )
    required.add_argument(
        '--bucket',
        required=True,
        help='Name of the S3 bucket to search in'
    )

    # Optional arguments
    optional = parser.add_argument_group('optional arguments')
    optional.add_argument(
        '--pattern',
        default='',
        help='Search pattern for object keys (supports * and ? wildcards)'
    )
    optional.add_argument(
        '--max-keys',
        type=int,
        default=1000,
        help='Maximum number of keys to return (default: 1000)'
    )
    optional.add_argument(
        '--no-verify',
        action='store_true',
        help='Disable SSL certificate verification'
    )

    return parser


def setup_s3_client(access_key: str, secret_key: str, endpoint: str, verify: bool = True) -> boto3.client:
    """设置 S3 客户端"""
    config = Config(
        connect_timeout=5,
        read_timeout=5,
        retries={'max_attempts': 3}
    )
    return boto3.client(
        's3',
        aws_access_key_id=access_key,
        aws_secret_access_key=secret_key,
        endpoint_url=endpoint,
        verify=verify,
        config=config
    )


def contains_wildcard(pattern: str) -> bool:
    """检查模式中是否包含通配符"""
    return '*' in pattern or '?' in pattern


def pattern_to_regex(pattern: str) -> re.Pattern:
    """将通配符模式转换为正则表达式"""
    regex = pattern.replace('.', r'\.').replace('*', '.*').replace('?', '.')
    return re.compile(f'^{regex}$')


def list_objects_by_prefix(
    s3_client: boto3.client,
    bucket: str,
    prefix: str,
    max_keys: int
) -> List[dict]:
    """按前缀列出对象"""
    objects = []
    paginator = s3_client.get_paginator('list_objects_v2')
    for page in paginator.paginate(
        Bucket=bucket,
        Prefix=prefix,
        PaginationConfig={'MaxItems': max_keys}
    ):
        if 'Contents' in page:
            objects.extend(page['Contents'])
    return objects


def list_objects_by_pattern(
    s3_client: boto3.client,
    bucket: str,
    pattern: str,
    max_keys: int
) -> List[dict]:
    """按模式列出对象"""
    # 获取模式的公共前缀
    prefix = pattern.split('*')[0].split('?')[0]
    regex = pattern_to_regex(pattern)
    
    objects = []
    for obj in list_objects_by_prefix(s3_client, bucket, prefix, max_keys):
        if regex.match(obj['Key']):
            objects.append(obj)
            if len(objects) >= max_keys:
                break
    return objects


def format_size(size_in_bytes: int) -> str:
    """格式化文件大小"""
    return humanize.naturalsize(size_in_bytes)


def format_time(timestamp) -> str:
    """格式化时间戳"""
    dt = datetime.fromtimestamp(timestamp)
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def main():
    parser = create_parser()
    args = parser.parse_args()

    # 设置 S3 客户端
    s3_client = setup_s3_client(
        args.access_key,
        args.secret_key,
        args.endpoint,
        not args.no_verify
    )

    print("\n🔍 Searching objects in S3...")
    print(f"📦 Bucket: {args.bucket}")
    if args.pattern:
        print(f"🎯 Pattern: {args.pattern}")
    print("=" * 80)

    start_time = time.time()
    try:
        if args.pattern:
            if contains_wildcard(args.pattern):
                objects = list_objects_by_pattern(
                    s3_client,
                    args.bucket,
                    args.pattern,
                    args.max_keys
                )
            else:
                objects = list_objects_by_prefix(
                    s3_client,
                    args.bucket,
                    args.pattern,
                    args.max_keys
                )
        else:
            objects = list_objects_by_prefix(
                s3_client,
                args.bucket,
                '',
                args.max_keys
            )

        # 准备表格数据
        table_data = []
        total_size = 0
        for obj in objects:
            table_data.append([
                obj['Key'],
                format_size(obj['Size']),
                format_time(obj['LastModified'].timestamp())
            ])
            total_size += obj['Size']

        # 输出结果表格
        if table_data:
            print(tabulate(
                table_data,
                headers=['Object Key', 'Size', 'Last Modified'],
                tablefmt='grid'
            ))
        
        # 输出统计信息
        print("\n📊 Summary:")
        print(f"   Found: {len(objects)} objects")
        print(f"   Total Size: {format_size(total_size)}")
        print(f"   Search Time: {time.time() - start_time:.2f} seconds")

    except Exception as e:
        print(f"\n❌ Error: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()
        exit(1)


if __name__ == '__main__':
    main()