package main

import (
	"context"
	"crypto/rand"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cobra"
	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/pprof"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/units"
	"xiaoshiai.cn/common/version"
	"xiaoshiai.cn/common/version/verflag"
	"xiaoshiai.cn/core/edge/tunnel"
	"xiaoshiai.cn/core/edge/tunnel/transport"
)

func main() {
	if err := NewCommand().Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "%v\n", err)
		os.Exit(1)
	}
}

const DeviceIDBenchmarkServer = "benchmark-server"

type GlobalOptions struct {
	EdgeServer string `json:"server,omitempty" description:"The edge server address"`
	Token      string `json:"token,omitempty" description:"The token for authentication"`
}

func NewCommand() *cobra.Command {
	globalOptions := &GlobalOptions{
		EdgeServer: "ws://bob-edgeserver:80/devices-connect",
	}
	cmd := &cobra.Command{
		RunE: func(cmd *cobra.Command, args []string) error {
			verflag.PrintAndExitIfRequested()
			return cmd.Help()
		},
	}
	cmd.AddCommand(
		NewServerCommand(globalOptions),
		NewClientCommand(globalOptions),
	)
	flags := cmd.PersistentFlags()
	flags.StringVar(&globalOptions.EdgeServer, "edge-server", globalOptions.EdgeServer, "The edge server address")
	flags.StringVar(&globalOptions.Token, "token", globalOptions.Token, "The token for authentication")
	verflag.AddFlags(flags)
	return cmd
}

type ServerOptions struct {
	DeviceID string `json:"deviceID,omitempty"`
	Listen   string `json:"listen,omitempty"`
	Http3    bool   `json:"http3,omitempty"`
}

func NewServerCommand(opt *GlobalOptions) *cobra.Command {
	options := &ServerOptions{
		DeviceID: DeviceIDBenchmarkServer,
	}
	cmd := &cobra.Command{
		Use:   "server",
		Short: "Start the benchmark server",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, os.Kill)
			defer cancel()

			return runServer(ctx, opt, options)
		},
	}
	flags := cmd.Flags()
	flags.StringVar(&options.DeviceID, "deviceID", options.DeviceID, "The device id")
	flags.StringVar(&options.Listen, "listen", "", "The listen address")
	flags.BoolVar(&options.Http3, "http3", options.Http3, "Use HTTP/3")
	return cmd
}

func runServer(ctx context.Context, opt *GlobalOptions, options *ServerOptions) error {
	go pprof.Run(ctx)
	tunneloptions := &tunnel.Options{
		Eventer: tunnel.LoggerEventer{},
	}
	log.Info("edge server start", "deviceID", options.DeviceID)
	manager, err := tunnel.NewManager(ctx, options.DeviceID, tunneloptions)
	if err != nil {
		return err
	}
	log.Info("connect to edge server", "server", opt.EdgeServer)
	if err := ConnectUpstream(ctx, manager, opt.EdgeServer, opt.Token); err != nil {
		return err
	}
	mux := http.NewServeMux()
	mux.HandleFunc("/version", func(w http.ResponseWriter, r *http.Request) {
		http.Error(w, version.Get().String(), http.StatusOK)
	})
	mux.HandleFunc("/benchmark", func(w http.ResponseWriter, r *http.Request) {
		log.Info("receive request", "url", r.URL, "method", r.Method, "remote", r.RemoteAddr)
		defer r.Body.Close()
		n, err := io.Copy(io.Discard, r.Body)
		if err != nil {
			log.Error(err, "failed to read request body")
			http.Error(w, "failed to read request body", http.StatusInternalServerError)
			return
		}

		queries := r.URL.Query()
		sleepBeforeResponse := api.ValueOrDefault(queries.Get("sleepBeforeResponse"), time.Duration(0))
		if sleepBeforeResponse > 0 {
			log.Info("sleep before response", "duration", sleepBeforeResponse)
			time.Sleep(sleepBeforeResponse)
		}

		response := &RandomBody{Size: n}
		w.Header().Set("Content-Type", "application/octet-stream")
		w.Header().Set("Content-Length", fmt.Sprintf("%d", n))
		w.WriteHeader(http.StatusOK)
		if _, err := io.Copy(w, response); err != nil {
			log.Error(err, "failed to write response body")
			return
		}
	})
	if options.Http3 {
		log.Info("start http3 server", "listen", options.Listen)
		return manager.ServeHttp3(ctx, "", nil, mux)
	}
	log.Info("start http server", "listen", options.Listen)
	return tunnel.ServeHTTP(ctx, manager, mux)
}

func ConnectUpstream(ctx context.Context, man tunnel.Manager, wsserver string, addroverride string) error {
	ready := make(chan error)
	onceready := sync.Once{}

	go tunnel.Retry(ctx, func(ctx context.Context) error {
		dev, err := transport.Open(ctx, wsserver, nil)
		if err != nil {
			log.Error(err, "failed to open tcp connection to edge server")
			return err
		}
		return man.Connect(ctx, dev, tunnel.ConnectOptions{
			IsUpstream:            true,
			AuthenticatorOverride: tunnel.AlwaysAllowAuthenticator{},
			// ismc server use a random address to connect to edge server
			// so the edge server can identify every ismc server
			LocalAddressOverride:   addroverride,
			RouteAdvertiseInterval: 1 * time.Minute,
			IgnoreRouteAdvertise:   true, // do not receive route advertise from upstream
			ConnectedCallback: func(i tunnel.Interface) {
				onceready.Do(func() { close(ready) })
			},
		})
	})
	select {
	case err := <-ready:
		return err
	case <-ctx.Done():
		return ctx.Err()
	}
}

type ClientOptions struct {
	DeviceID           string        `json:"deviceID,omitempty"`
	Server             string        `json:"server,omitempty"`
	Count              int           `json:"count,omitempty"`
	Concurrent         int           `json:"concurrent,omitempty"`
	Size               int64         `json:"size,omitempty"`
	TimeBeforeResponse time.Duration `json:"timeBeforeResponse,omitempty"`
	Http3              bool          `json:"http3,omitempty"`
}

const (
	KB = 1024
	MB = 1024 * KB
	GB = 1024 * MB
)

func NewClientCommand(opt *GlobalOptions) *cobra.Command {
	options := &ClientOptions{
		DeviceID:   uuid.New().String(),
		Server:     "http://" + DeviceIDBenchmarkServer,
		Count:      1,
		Concurrent: 1,
		Size:       MB,
	}
	cmd := &cobra.Command{
		Use:   "client",
		Short: "Start the benchmark client",
		RunE: func(cmd *cobra.Command, args []string) error {
			ctx, cancel := signal.NotifyContext(context.Background(), os.Interrupt, os.Kill)
			defer cancel()
			return runClient(ctx, opt, options)
		},
	}
	flags := cmd.Flags()
	flags.StringVar(&options.DeviceID, "deviceID", options.DeviceID, "The device id")
	flags.StringVar(&options.Server, "server", options.Server, "The server address")
	flags.IntVarP(&options.Count, "count", "n", options.Count, "The count of requests")
	flags.IntVarP(&options.Concurrent, "concurrent", "c", options.Concurrent, "The concurrent of requests")
	flags.Int64Var(&options.Size, "size", options.Size, "The size of requests")
	flags.DurationVar(&options.TimeBeforeResponse, "timeBeforeResponse", options.TimeBeforeResponse, "The time to sleep before response")
	flags.BoolVar(&options.Http3, "http3", options.Http3, "Use HTTP/3")
	return cmd
}

func runClient(ctx context.Context, opt *GlobalOptions, options *ClientOptions) error {
	tunneloptions := &tunnel.Options{
		Eventer: tunnel.LoggerEventer{},
	}
	manager, err := tunnel.NewManager(ctx, options.DeviceID, tunneloptions)
	if err != nil {
		return err
	}
	log.Info("connect to edge server", "server", opt.EdgeServer)
	if err := ConnectUpstream(ctx, manager, opt.EdgeServer, opt.Token); err != nil {
		return err
	}
	u, err := url.Parse(options.Server)
	if err != nil {
		return err
	}
	config := &httpclient.ClientConfig{
		Server:       u,
		RoundTripper: tunnel.HttpTransport(manager),
	}
	if options.Http3 {
		config.Server.Scheme = "https"
		config.RoundTripper = manager.Http3Transport()
	}
	httpcli := httpclient.NewClientFromClientConfig(config)

	resp, err := httpcli.Get("/version").Do(ctx)
	if err != nil {
		log.Error(err, "failed to send request")
		return err
	}
	defer resp.Body.Close()

	version, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Error(err, "failed to read response body")
		return err
	}
	log.Info("server version", "version", string(version))

	starttime := time.Now()
	sendbytes, readbytes := atomic.Int64{}, atomic.Int64{}
	sendrequests := atomic.Int32{}

	log.Info("start to send requests", "count", options.Count, "concurrent", options.Concurrent, "size", units.HumanSize(float64(options.Size)))
	eg := errgroup.Group{}
	errcnt := atomic.Int32{}
	for range options.Concurrent {
		eg.Go(func() error {
			for range options.Count {
				do := func() error {
					req := httpcli.
						Post("/benchmark").
						Body(&RandomBody{Size: int64(options.Size)}, "application/octet-stream")
					if options.TimeBeforeResponse > 0 {
						req.Query("sleepBeforeResponse", options.TimeBeforeResponse.String())
					}
					sendrequests.Add(1)
					resp, err := req.Do(ctx)
					if err != nil {
						log.Error(err, "failed to send request")
						return err
					}
					sendbytes.Add(options.Size)

					defer resp.Body.Close()
					n, err := io.Copy(io.Discard, resp.Body)
					if err != nil {
						if err != io.EOF {
							log.Error(err, "failed to read response body")
							return err
						}
					}
					readbytes.Add(n)
					return nil
				}()
				if do != nil {
					errcnt.Add(1)
				}
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return err
	}

	cost := time.Since(starttime)

	sendspeed := units.HumanSize(float64(sendbytes.Load()) / cost.Seconds())
	readspeed := units.HumanSize(float64(readbytes.Load()) / cost.Seconds())

	fmt.Printf("total %d requests, %d failed, failed rate %.2f%%\n", sendrequests.Load(), errcnt.Load(), float64(errcnt.Load())/float64(sendrequests.Load())*100)
	fmt.Printf("send %d bytes, read %d bytes, cost %s, send speed %s/s, read speed %s/s\n",
		sendbytes.Load(), readbytes.Load(), cost, sendspeed, readspeed)
	return nil
}

type RandomBody struct {
	Size int64
}

func (r *RandomBody) Read(p []byte) (n int, err error) {
	if r.Size <= 0 {
		return 0, io.EOF
	}
	if int64(len(p)) > r.Size {
		p = p[:r.Size]
	}
	io.ReadFull(rand.Reader, p)
	r.Size -= int64(len(p))
	return len(p), nil
}
