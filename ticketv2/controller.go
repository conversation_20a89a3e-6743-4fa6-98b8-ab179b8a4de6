package ticketv2

import (
	"context"
	"reflect"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/tenant"
)

// 工单分类
type BobTicketModule struct {
	store.ObjectMeta `json:",inline"`
	Module           string `json:"module"`       // 模块
	ApprovalUser     string `json:"approvalUser"` // 审批人
}

// 工单
type BobTicket struct {
	store.ObjectMeta `json:",inline"`
	Title            string             `json:"title"`       // 工单标题
	Comments         []BobTicketComment `json:"comments"`    // 评论列表
	Module           BobTicketModule    `json:"module"`      // 工单模块
	State            BobTicketState     `json:"state"`       // 工单状态
	Tenant           tenant.Tenant      `json:"tenant"`      // 工单所在的企业
	CreateUser       string             `json:"createUser"`  // 工单创建人
	LasetDealer      string             `json:"lasetDealer"` // 最后处理人
}

type BobTicketComment struct {
	Comment        string    `json:"comment"`
	OnlyManageSide bool      `json:"onlyManageSide"` //只有管理端可见
	Picture        string    `json:"pricture"`
	User           string    `json:"user"`
	CommentTime    time.Time `json:"contentTime"`
}

type BobTicketState string

const (
	BobTicketStatetoApproval BobTicketState = "toapproval" //待受理
	BobTicketStateDealing    BobTicketState = "dealing"    //处理中
	BobTicketStateReopen     BobTicketState = "reopen"     //重新打开
	BobTicketStateComplete   BobTicketState = "complete"   //已完成
)

// 工单评论
type BobTicketPicture struct {
	store.ObjectMeta `json:",inline"`
	Pictures         [][]byte `json:"prictures"`
}

type BobTicketService interface {
	List(ctx context.Context, list store.ObjectList, opts ...store.ListOption) error
	Create(ctx context.Context, obj store.Object, opts ...store.CreateOption) error
	Update(ctx context.Context, obj store.Object, opts ...store.UpdateOption) error
	Delete(ctx context.Context, obj store.Object, opts ...store.DeleteOption) error
	Get(ctx context.Context, name string, obj store.Object, opts ...store.GetOption) error
	GetTicketModuleByModule(ctx context.Context, module string) (*BobTicketModule, error)
	ExistTicketModule(ctx context.Context, module string) (bool, error)
	CreateWithComment(ctx context.Context, ticket *BobTicket, comment *BobTicketPicture) error
	DeleteWithComment(ctx context.Context, name string) error
}

var _ BobTicketService = &bobTicketService{}

type bobTicketService struct {
	storage store.TransactionStore // mongodb
	db      *mongo.Database
}

// DeleteWithComment implements BobTicketService.
func (b *bobTicketService) DeleteWithComment(ctx context.Context, name string) error {
	obj := &BobTicket{}
	if err := b.storage.Get(ctx, name, obj); err != nil {
		return err
	}
	return b.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		var pics []any
		for _, comment := range obj.Comments {
			if comment.Picture == "" {
				continue
			}
			pics = append(pics, comment.Picture)
		}
		if len(pics) > 0 {
			if err := storage.DeleteBatch(ctx, &store.List[BobTicketPicture]{}, store.WithDeleteBatchFieldRequirements(store.Requirement{
				Key:      "name",
				Operator: store.In,
				Values:   pics,
			})); err != nil {
				return err
			}
		}
		return storage.Delete(ctx, obj)
	})
}

// CreateWithComment implements BobTicketService.
func (b *bobTicketService) CreateWithComment(ctx context.Context, ticket *BobTicket, pic *BobTicketPicture) error {
	return b.storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		if err := storage.Create(ctx, ticket); err != nil {
			return err
		}
		if pic == nil {
			return nil
		}
		return storage.Create(ctx, pic)
	})
}

func (b *bobTicketService) ExistTicketModule(ctx context.Context, module string) (bool, error) {
	list := store.List[BobTicketModule]{}
	if err := b.List(ctx, &list, store.WithFieldRequirementsFromSet(map[string]string{
		"module": module,
	})); err != nil {
		return false, err
	}
	if len(list.Items) > 0 {
		return true, nil
	}
	return false, nil
}

func (b *bobTicketService) GetTicketModuleByModule(ctx context.Context, module string) (*BobTicketModule, error) {
	list := store.List[BobTicketModule]{}
	if err := b.List(ctx, &list, store.WithFieldRequirementsFromSet(map[string]string{
		"module": module,
	})); err != nil {
		return nil, err
	}
	if len(list.Items) > 0 {
		return &list.Items[0], nil
	}
	return nil, nil
}

// Create implements BobTicketService.
func (b *bobTicketService) Create(ctx context.Context, obj store.Object, opts ...store.CreateOption) error {
	return b.storage.Create(ctx, obj, opts...)
}

// Delete implements BobTicketService.
func (b *bobTicketService) Delete(ctx context.Context, obj store.Object, opts ...store.DeleteOption) error {
	return b.storage.Delete(ctx, obj, opts...)
}

// Get implements BobTicketService.
func (b *bobTicketService) Get(ctx context.Context, name string, obj store.Object, opts ...store.GetOption) error {
	return b.storage.Get(ctx, name, obj, opts...)
}

// List implements BobTicketService.
func (b *bobTicketService) List(ctx context.Context, list store.ObjectList, opts ...store.ListOption) error {
	return b.storage.List(ctx, list, opts...)
}

// Update implements BobTicketService.
func (b *bobTicketService) Update(ctx context.Context, obj store.Object, opts ...store.UpdateOption) error {
	return b.storage.Update(ctx, obj, opts...)
}

func NewBobTicketService(storage store.TransactionStore, db *mongo.Database) BobTicketService {
	bts := &bobTicketService{
		storage: storage,
		db:      db,
	}
	return bts
}

type multiFieldSorter[T any] struct {
	data        []T
	fieldOrders []fieldOrder
	compare     func(a, b reflect.Value) int8
}

type fieldOrder struct {
	field string
	order uint8 // 0: ascending, 1: descending
}

func newMultiFieldSorter[T any](data []T, fieldOrders []fieldOrder) *multiFieldSorter[T] {
	return &multiFieldSorter[T]{
		data:        data,
		fieldOrders: fieldOrders,
		compare: func(a, b reflect.Value) int8 {
			switch a.Kind() {
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				if a.Int() < b.Int() {
					return -1
				} else if a.Int() > b.Int() {
					return 1
				}
			case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
				if a.Uint() < b.Uint() {
					return -1
				} else if a.Uint() > b.Uint() {
					return 1
				}
			case reflect.Float32, reflect.Float64:
				if a.Float() < b.Float() {
					return -1
				} else if a.Float() > b.Float() {
					return 1
				}
			case reflect.String:
				if a.String() < b.String() {
					return -1
				} else if a.String() > b.String() {
					return 1
				}
			case reflect.Struct:
				if a.Type() == reflect.TypeOf(store.Time{}) {
					at := a.Interface().(store.Time)
					bt := b.Interface().(store.Time)
					if at.Before(&bt) {
						return -1
					} else if !at.Equal(&bt) {
						return 1
					}
				}
			}
			return 0
		},
	}
}
func (s *multiFieldSorter[T]) Len() int {
	return len(s.data)
}
func (s *multiFieldSorter[T]) Swap(i, j int) {
	s.data[i], s.data[j] = s.data[j], s.data[i]
}
func (s *multiFieldSorter[T]) Less(i, j int) bool {
	a := reflect.ValueOf(s.data[i])
	b := reflect.ValueOf(s.data[j])
	for _, field := range s.fieldOrders {
		order := field.order
		fieldName := field.field
		if comp := s.compare(a.FieldByName(fieldName), b.FieldByName(fieldName)); comp != 0 {
			if order == 1 {
				return comp > 0
			}
			return comp < 0
		}
	}
	return false
}
