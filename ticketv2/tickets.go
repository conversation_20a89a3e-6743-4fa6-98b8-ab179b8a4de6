package ticketv2

import (
	"context"
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/google/uuid"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/rbac"
	"xiaoshiai.cn/core/tenant"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&BobTicket{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"state"},
				{"createUser"},
			},
		})
	mongo.GlobalObjectsScheme.Register(&BobTicketModule{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"module"},
			},
		})
	mongo.GlobalObjectsScheme.Register(&BobTicketPicture{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
		})
}

type API struct {
	base.API
	Service BobTicketService
	message events.Recorder
}

func NewAPI(base base.API, service BobTicketService, message events.Recorder) *API {
	a := &API{
		API:     base,
		Service: service,
		message: message,
	}
	go a.initBobTicketModule(context.Background())
	return a
}

// 初始化工单分类
func (a *API) initBobTicketModule(ctx context.Context) {
	var modules = map[string]string{
		"Payment":        "Payment, Order, Invoice",
		"Alarm":          "Application alarms, cluster alarms, notification channels, alarm rules, alarm templates and other related issues",
		"Cloud Storage":  "Object storage bucket access, statistics, online operations and fragmentation management, credential management, and other issues",
		"System":         "Enterprise, organization, quota, order, bill, audit, announcement, message and other system related issues",
		"Infrastructure": "VMware, Huawei Cloud, Kubernetes, private clusters, private nodes, network management and other related issues",
		"Account Issue":  "User account management, login, authentication, organization users, role management, enterprise users, role management",
		"Application":    "Application center, application order, application market, application deployment, application instance, application license, application alarm and other related issues",
	}
	for module, description := range modules {
		mo, err := a.Service.GetTicketModuleByModule(ctx, module)
		if err != nil {
			log.FromContext(ctx).Error(err, "init ticket module")
			return
		}
		if mo == nil {
			if err := a.Service.Create(ctx, &BobTicketModule{
				ObjectMeta: store.ObjectMeta{
					Name:        uuid.NewString(),
					Description: description,
				},
				Module:       module,
				ApprovalUser: "admin", // 默认指派给admin账号
			}); err != nil {
				log.FromContext(ctx).Error(err, "init ticket module")
				return
			}
		} else {
			if mo.ApprovalUser == "" {
				mo.ApprovalUser = "admin"
				if err := a.Service.Update(ctx, mo); err != nil {
					log.FromContext(ctx).Error(err, "update ticket module approval user")
					return
				}
			}
		}
	}
}

// 获取工单分类
func (a *API) ListBobTicketModule(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		list := store.List[BobTicketModule]{}
		if err := a.Service.List(ctx, &list); err != nil {
			return nil, err
		}
		var newList []BobTicketModule
		newList = append(newList, list.Items...)
		lt := api.PageFromListOptions(newList, api.GetListOptions(r), func(item BobTicketModule) string {
			return item.Module
		}, nil)
		return store.List[BobTicketModule]{
			Items: lt.Items,
			Total: int(lt.Total),
			Page:  int(lt.Page),
			Size:  int(lt.Size),
		}, nil
	})
}

// 创建工单分类
func (a *API) CreateBobTicketModule(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		ticketmodule := &BobTicketModule{
			ObjectMeta: store.ObjectMeta{
				Name: uuid.NewString(),
			},
		}
		if err := api.Body(r, ticketmodule); err != nil {
			return nil, err
		}
		exist, err := a.Service.ExistTicketModule(ctx, ticketmodule.Module)
		if err != nil {
			return nil, err
		}
		if exist {
			return nil, fmt.Errorf("name %s already exists", ticketmodule.Module)
		}
		if err := a.Service.Create(ctx, ticketmodule); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

// 更新工单分类
func (a *API) UpdateBobTicketModule(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		originObj := &BobTicketModule{}
		if err := a.Service.Get(ctx, name, originObj); err != nil {
			return nil, err
		}
		obj := &BobTicketModule{}
		if err := api.Body(r, obj); err != nil {
			return nil, err
		}
		if originObj.Module != obj.Module {
			exist, err := a.Service.ExistTicketModule(ctx, obj.Module)
			if err != nil {
				return nil, err
			}
			if exist {
				return nil, fmt.Errorf("module name %s already exists", obj.Module)
			}
		}
		if objname := obj.GetName(); objname != "" && objname != name {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, name)
		}
		obj.SetName(name)
		obj.SetResourceVersion(0)
		return nil, a.Service.Update(ctx, obj)
	})
}

// 删除工单分类
func (a *API) DeleteBobTicketModule(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		obj := &BobTicketModule{}
		if objname := obj.GetName(); objname != "" && objname != name {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, name)
		}
		obj.SetName(name)
		if err := a.Service.Delete(ctx, obj); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) ListAdminBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		user := api.AuthenticateFromContext(ctx).User.Name
		isAdmin, err := a.isAdministrator(ctx, user)
		if err != nil {
			return nil, err
		}
		if !isAdmin {
			// 后台普通成员只能看到指派给自己的工单
			newlist, finished, err := a.listBackendUserTickets(ctx, r, user)
			if err != nil {
				return nil, err
			}
			return a.paginateAndSortTickets(r, newlist, finished)
		}
		newlist, finished, err := a.listAdminTickets(ctx, r, user)
		if err != nil {
			return nil, err
		}
		return a.paginateAndSortTickets(r, newlist, finished)
	})
}

func (a *API) ListTenantBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenantName := api.Path(r, "tenant", "")
		if tenantName == "" {
			return nil, liberrors.NewBadRequest("tenant name is required")
		}
		user := api.AuthenticateFromContext(ctx).User.Name
		newlist, err := a.listUserTickets(ctx, r, user)
		if err != nil {
			return nil, err
		}
		return a.paginateAndSortTickets(r, newlist, nil)
	})
}

// 创建工单
func (a *API) CreateTenantBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenantName := api.Path(r, "tenant", "")
		if tenantName == "" {
			return nil, liberrors.NewBadRequest("tenant name is required")
		}
		tenantInfo := &tenant.Tenant{}
		if err := a.Store.Get(ctx, tenantName, tenantInfo); err != nil {
			return nil, err
		}
		cb := &CreateBobTicket{}
		if err := api.Body(r, cb); err != nil {
			return nil, err
		}
		user := api.AuthenticateFromContext(ctx).User.Name
		if cb.Module.ApprovalUser == "" {
			return nil, fmt.Errorf("approval user is empty")
		}
		var (
			picname string
			btp     *BobTicketPicture
		)
		if len(cb.Pictures) > 0 {
			picname = uuid.NewString()
			btp = &BobTicketPicture{
				ObjectMeta: store.ObjectMeta{
					Name: picname,
				},
				Pictures: cb.Pictures,
			}
		}
		ticket := &BobTicket{
			ObjectMeta: store.ObjectMeta{
				Name:     uuid.NewString(),
				Resource: "tickets",
			},
			Title: cb.Title,
			Comments: []BobTicketComment{
				{
					Comment:        cb.Comment,
					OnlyManageSide: false,
					Picture:        picname,
					User:           user,
					CommentTime:    time.Now(),
				},
			},
			Module:      cb.Module,
			State:       BobTicketStatetoApproval,
			Tenant:      *tenantInfo,
			CreateUser:  user,
			LasetDealer: user,
		}
		err := a.Service.CreateWithComment(ctx, ticket, btp)
		if err != nil {
			return nil, err
		}
		a.message.EventNoAggregate(ctx, ticket, events.ReasonTicketOpened, "Ticket Created")
		return nil, nil
	})
}

func (a *API) paginateAndSortTickets(r *http.Request, newlist, finished []BobTicket) (any, error) {
	fieldOrders := []fieldOrder{
		{field: "CreationTimestamp", order: 1},
	}
	sorter := newMultiFieldSorter(newlist, fieldOrders)
	sort.Sort(sorter)
	newlist = append(newlist, finished...)
	lt := api.PageFromListOptions(newlist, api.GetListOptions(r), func(item BobTicket) string {
		return item.Title
	}, nil)
	return store.List[BobTicket]{
		Items: lt.Items,
		Total: int(lt.Total),
		Page:  int(lt.Page),
		Size:  int(lt.Size),
	}, nil
}

func (a *API) listUserTickets(ctx context.Context, r *http.Request, user string) ([]BobTicket, error) {
	var (
		list   = &store.List[BobTicket]{}
		fields = make(map[string]string)
	)
	state := api.Query(r, "state", "")
	fields["createUser"] = user
	if state != "" {
		fields["state"] = state
	}
	if err := a.Service.List(ctx, list, store.WithFieldRequirementsFromSet(fields)); err != nil {
		return nil, err
	}
	return list.Items, nil
}

func (a *API) listAdminTickets(ctx context.Context, r *http.Request, user string) ([]BobTicket, []BobTicket, error) {
	incharge := api.Query(r, "inCharge", false)
	state := api.Query(r, "state", "")
	var (
		list     = store.List[BobTicket]{}
		finished []BobTicket
		newlist  []BobTicket
		fields   = make(map[string]string)
	)
	if state != "" {
		fields["state"] = state
	}
	if incharge {
		fields["module.approvalUser"] = user
	}
	if err := a.Service.List(ctx, &list,
		store.WithFieldRequirementsFromSet(fields)); err != nil {
		return nil, nil, err
	}
	for _, item := range list.Items {
		if item.State == BobTicketStateComplete {
			finished = append(finished, item)
			continue
		}
		newlist = append(newlist, item)
	}
	return newlist, finished, nil
}

func (a *API) listBackendUserTickets(ctx context.Context, r *http.Request, user string) ([]BobTicket, []BobTicket, error) {
	var (
		list     = store.List[BobTicket]{}
		finished []BobTicket
		newlist  []BobTicket
		fields   = make(map[string]string)
	)
	state := api.Query(r, "state", "")
	if state != "" {
		fields["state"] = state
	}
	fields["module.approvalUser"] = user
	if err := a.Service.List(ctx, &list,
		store.WithFieldRequirementsFromSet(fields)); err != nil {
		return nil, nil, err
	}
	for _, item := range list.Items {
		if item.State == BobTicketStateComplete {
			finished = append(finished, item)
			continue
		}
		newlist = append(newlist, item)
	}
	return newlist, finished, nil

}

type CreateBobTicket struct {
	Title    string          `json:"title"`     // 工单标题
	Module   BobTicketModule `json:"module"`    // 工单分类
	Comment  string          `json:"comment"`   // 评论
	Pictures [][]byte        `json:"prictures"` // 截图
}

func (a *API) GetAdminBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("ticket name is required")
		}
		bt := &BobTicket{}
		if err := a.Service.Get(ctx, name, bt); err != nil {
			return nil, err
		}
		return bt, nil
	})
}

func (a *API) GetTenantBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenantName := api.Path(r, "tenant", "")
		if tenantName == "" {
			return nil, liberrors.NewBadRequest("tenant name is required")
		}
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("ticket name is required")
		}
		bt := &BobTicket{}
		if err := a.Service.Get(ctx, name, bt); err != nil {
			return nil, err
		}
		for i := 0; i < len(bt.Comments); i++ {
			comment := bt.Comments[i]
			if comment.OnlyManageSide {
				bt.Comments = append(bt.Comments[:i], bt.Comments[i+1:]...)
				i--
			}
		}
		return bt, nil
	})
}

func (a *API) isAdministrator(ctx context.Context, user string) (bool, error) {
	ur := &rbac.UserRole{}
	if err := a.Store.Get(ctx, user, ur); err != nil {
		if liberrors.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}
	if len(ur.Roles) == 0 {
		return false, nil
	}
	return ur.Roles[0] == "admin", nil
}

type Assign struct {
	AssignUser string `json:"assignUser"`
}

func (a *API) OperatTenantBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenantName := api.Path(r, "tenant", "")
		if tenantName == "" {
			return nil, liberrors.NewBadRequest("tenant name is required")
		}
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("ticket name is required")
		}
		ticket := &BobTicket{}
		if err := a.Service.Get(ctx, name, ticket); err != nil {
			return nil, err
		}
		user := api.AuthenticateFromContext(ctx).User.Name
		operation := api.Path(r, "operation", "")
		if operation == "" {
			return nil, fmt.Errorf("operation is required")
		}
		ticket.LasetDealer = user
		switch operation {
		case "close":
			if ticket.State == BobTicketStateComplete {
				return nil, fmt.Errorf("ticket is already complete")
			}
			ticket.Comments = append(ticket.Comments, BobTicketComment{
				Comment:        "close ticket",
				OnlyManageSide: false,
				User:           user,
				CommentTime:    time.Now(),
			})
			ticket.State = BobTicketStateComplete
			a.message.EventNoAggregateAnnotations(ctx, ticket, events.ReasonTickerClosed, "Ticket Completed", map[string]string{
				"operator": user,
			})
		case "reopen":
			if ticket.State != BobTicketStateComplete {
				return nil, fmt.Errorf("ticket is not complete")
			}
			if ticket.CreateUser != user {
				return nil, fmt.Errorf("only ticket creator can reopen ticket")
			}
			// 重新打开，变成待受理
			ticket.State = BobTicketStateReopen
			ticket.Comments = append(ticket.Comments, BobTicketComment{
				Comment:        "reopen ticket",
				OnlyManageSide: false,
				User:           user,
				CommentTime:    time.Now(),
			})
			a.message.EventNoAggregateAnnotations(ctx, ticket, events.ReasonTicketReOpened, "Ticket Reopened", map[string]string{
				"operator": user,
			})
		default:
			return nil, fmt.Errorf("operation %s is not support", operation)
		}
		return nil, a.Service.Update(ctx, ticket)
	})
}

func (a *API) OperatAdminBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("ticket name is required")
		}
		user := api.AuthenticateFromContext(ctx).User.Name
		operation := api.Path(r, "operation", "")
		if operation == "" {
			return nil, fmt.Errorf("operation is required")
		}
		assign := &Assign{}
		if err := api.Body(r, assign); err != nil {
			return nil, err
		}
		ticket := &BobTicket{}
		if err := a.Service.Get(ctx, name, ticket); err != nil {
			return nil, err
		}
		ticket.LasetDealer = user
		switch operation {
		case "close":
			if ticket.State == BobTicketStateComplete {
				return nil, fmt.Errorf("ticket is already complete")
			}
			ticket.Comments = append(ticket.Comments, BobTicketComment{
				Comment:        "close ticket",
				OnlyManageSide: false,
				User:           user,
				CommentTime:    time.Now(),
			})
			ticket.State = BobTicketStateComplete
			a.message.EventNoAggregateAnnotations(ctx, ticket, events.ReasonTickerClosed, "Ticket Completed", map[string]string{
				"operator": user,
			})
		case "assign":
			if ticket.State == BobTicketStateComplete {
				return nil, fmt.Errorf("ticket is already complete")
			}
			if assign.AssignUser != "" {
				ticket.Module.ApprovalUser = assign.AssignUser
				ticket.Comments = append(ticket.Comments, BobTicketComment{
					Comment:        fmt.Sprintf("assign ticket to %s", assign.AssignUser),
					OnlyManageSide: true,
					User:           user,
					CommentTime:    time.Now(),
				})
			}
			a.message.EventNoAggregateAnnotations(ctx, ticket, events.ReasonTicketAssigned, "Ticket Assigned", map[string]string{
				"operator": user,
			})
		default:
			return nil, fmt.Errorf("operation %s is not support", operation)
		}
		return nil, a.Service.Update(ctx, ticket)
	})
}

func (a *API) DeleteAdminBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		if err := a.Service.DeleteWithComment(ctx, name); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

func (a *API) DeleteTenantBobTicket(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenantName := api.Path(r, "tenant", "")
		if tenantName == "" {
			return nil, liberrors.NewBadRequest("tenant name is required")
		}
		if err := a.Store.Get(ctx, tenantName, &tenant.Tenant{}); err != nil {
			return nil, err
		}
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		if err := a.Service.DeleteWithComment(ctx, name); err != nil {
			return nil, err
		}
		return nil, nil
	})
}

type CreateBobTicketComment struct {
	Comment  string   `json:"comment"`
	Pictures [][]byte `json:"prictures"`
}

func (a *API) CreateAdminBobTicketComment(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		user := api.AuthenticateFromContext(ctx).User.Name
		ticketcomment := &CreateBobTicketComment{}
		if err := api.Body(r, ticketcomment); err != nil {
			return nil, err
		}
		var (
			tpname string
			tc     *BobTicketPicture
		)
		if len(ticketcomment.Pictures) > 0 {
			tpname = uuid.NewString()
			tc = &BobTicketPicture{
				ObjectMeta: store.ObjectMeta{
					Name: tpname,
				},
				Pictures: ticketcomment.Pictures,
			}
		}
		if tc != nil {
			if err := a.Service.Create(ctx, tc); err != nil {
				return nil, err
			}
		}
		newcomment := BobTicketComment{
			Comment:     ticketcomment.Comment,
			Picture:     tpname,
			User:        user,
			CommentTime: time.Now(),
		}
		obj := &BobTicket{}
		if err := a.Service.Get(ctx, name, obj); err != nil {
			return nil, err
		}
		obj.Comments = append(obj.Comments, newcomment)
		obj.LasetDealer = user
		// 用户自己就是单纯的追加评论,不改变状态
		if obj.State == BobTicketStatetoApproval || obj.State == BobTicketStateReopen {
			if obj.CreateUser != newcomment.User {
				obj.State = BobTicketStateDealing
				a.message.EventNoAggregateAnnotations(ctx, obj, events.ReasonTicketReplied, "Ticket Processing", map[string]string{
					"comment":  newcomment.Comment,
					"operator": newcomment.User,
				})
			}
		}
		return nil, a.Service.Update(ctx, obj)
	})
}

// 添加工单评论,如果是待受理状态，添加评论后就是处理中
func (a *API) CreateTenantBobTicketComment(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		tenantName := api.Path(r, "tenant", "")
		if tenantName == "" {
			return nil, liberrors.NewBadRequest("tenant name is required")
		}
		name := api.Path(r, "name", "")
		if name == "" {
			return nil, fmt.Errorf("name is required")
		}
		user := api.AuthenticateFromContext(ctx).User.Name
		ticketcomment := &CreateBobTicketComment{}
		if err := api.Body(r, ticketcomment); err != nil {
			return nil, err
		}
		var (
			tpname string
			tc     *BobTicketPicture
		)
		if len(ticketcomment.Pictures) > 0 {
			tpname = uuid.NewString()
			tc = &BobTicketPicture{
				ObjectMeta: store.ObjectMeta{
					Name: tpname,
				},
				Pictures: ticketcomment.Pictures,
			}
		}
		if tc != nil {
			if err := a.Service.Create(ctx, tc); err != nil {
				return nil, err
			}
		}
		newcomment := BobTicketComment{
			Comment:     ticketcomment.Comment,
			Picture:     tpname,
			User:        user,
			CommentTime: time.Now(),
		}
		obj := &BobTicket{}
		if err := a.Service.Get(ctx, name, obj); err != nil {
			return nil, err
		}
		obj.Comments = append(obj.Comments, newcomment)
		obj.LasetDealer = user
		// 用户自己就是单纯的追加评论,不改变状态
		if obj.State == BobTicketStatetoApproval || obj.State == BobTicketStateReopen {
			if obj.CreateUser != newcomment.User {
				obj.State = BobTicketStateDealing
				a.message.EventNoAggregateAnnotations(ctx, obj, events.ReasonTicketReplied, "Ticket Processing", map[string]string{
					"comment":  newcomment.Comment,
					"operator": newcomment.User,
				})
			}
		}
		return nil, a.Service.Update(ctx, obj)
	})
}

// 获取工单图片
func (a *API) GetBobTicketPicture(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		ticketName := api.Path(r, "name", "")
		if ticketName == "" {
			return nil, fmt.Errorf("ticket name is required")
		}
		bt := &BobTicket{}
		if err := a.Service.Get(ctx, ticketName, bt); err != nil {
			return nil, err
		}
		picName := api.Path(r, "pic", "")
		if picName == "" {
			return nil, fmt.Errorf("picture name is required")
		}
		pic := &BobTicketPicture{}
		if err := a.Service.Get(ctx, picName, pic); err != nil {
			return nil, err
		}
		return pic, nil
	})
}

func (a *API) bobTenantTicketGroup() api.Group {
	return base.NewTenantGroup("tickets").
		Route(
			api.GET("").
				To(a.ListTenantBobTicket).
				Operation("list tickets").
				Param(api.PageParams...).
				Param(api.QueryParam("state", "ticket state").In("toapproval", "dealing", "reopen", "complete").Optional()).
				Response(store.List[BobTicket]{}),

			api.POST("").
				To(a.CreateTenantBobTicket).
				Operation("create ticket").
				Param(api.BodyParam("ticket", CreateBobTicket{})),

			api.GET("/{name}").
				To(a.GetTenantBobTicket).
				Operation("get ticket details").
				Response(BobTicket{}),

			api.PUT("/{name}:{operation}").
				To(a.OperatTenantBobTicket).
				Operation("operate tenant ticket").
				Param(
					api.PathParam("operation", "close,reopen").In("close", "reopen"),
				),
			api.DELETE("/{name}").
				To(a.DeleteTenantBobTicket).
				Operation("delete ticket"),

			api.PUT("/{name}").
				To(a.CreateTenantBobTicketComment).
				Operation("create ticket comment").
				Param(api.BodyParam("comment", CreateBobTicketComment{})),
		)
}

func (a *API) bobAdminTicketGroup() api.Group {
	return api.NewGroup("/tickets").
		Route(
			api.GET("").
				To(a.ListAdminBobTicket).
				Operation("list tickets").
				Param(api.PageParams...).
				Param(
					api.QueryParam("state", "ticket state").In("toapproval", "dealing", "reopen", "complete").Optional(),
					api.QueryParam("inCharge", "current user in charge tickets").In(true, false).Optional(),
				).
				Response(store.List[BobTicket]{}),

			api.GET("/{name}").
				To(a.GetAdminBobTicket).
				Operation("get ticket details").
				Response(BobTicket{}),

			api.DELETE("/{name}").
				To(a.DeleteAdminBobTicket).
				Operation("delete ticket"),

			api.PUT("/{name}:{operation}").
				To(a.OperatAdminBobTicket).
				Operation("operate ticket").
				Param(
					api.PathParam("operation", "close,assign").In("close", "assign"),
					api.BodyParam("assign", Assign{}),
				),

			api.GET("/{name}/pictures/{pic}").
				To(a.GetBobTicketPicture).
				Operation("get ticket comment pictures").
				Response(BobTicketPicture{}),

			api.PUT("/{name}").
				To(a.CreateAdminBobTicketComment).
				Operation("create ticket comment").
				Param(api.BodyParam("comment", CreateBobTicketComment{})),
		)
}

func (a *API) bobTicketModuleGroup() api.Group {
	return api.NewGroup("/ticket-modules").
		Route(
			api.GET("").
				To(a.ListBobTicketModule).
				Operation("list ticket module").
				Param(api.PageParams...).
				Response(store.List[BobTicketModule]{}),

			api.POST("").
				To(a.CreateBobTicketModule).
				Operation("create ticket module").
				Param(api.BodyParam("module", BobTicketModule{})),

			api.PUT("/{name}").
				To(a.UpdateBobTicketModule).
				Operation("update ticket module").
				Param(api.BodyParam("module", BobTicketModule{})),

			api.DELETE("/{name}").
				To(a.DeleteBobTicketModule).
				Operation("delete ticket module"),
		)
}

func (a *API) Group() api.Group {
	return api.NewGroup("").Tag("Ticket").
		SubGroup(
			//a.bobTicketGroup(),
			a.bobTicketModuleGroup(),
			a.bobAdminTicketGroup(),
			a.bobTenantTicketGroup(),
		)
}
