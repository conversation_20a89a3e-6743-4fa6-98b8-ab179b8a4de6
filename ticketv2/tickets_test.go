package ticketv2

import (
	"context"
	"fmt"
	"testing"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
)

func setupMongoStore(ctx context.Context) (*mongo.MongoStorage, error) {
	mongooptions := mongo.NewDefaultMongoOptions("bob")
	mongooptions.Address = "bob-mongodb-headless.bob:27017"
	mongooptions.Database = "bob"
	mongooptions.Username = "root"
	mongooptions.Password = ""
	mongostore, err := mongo.NewMongoStorage(ctx, mongo.GlobalObjectsScheme, mongooptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create mongo store: %w", err)
	}
	return mongostore, nil
}

func TestListAfterDelete(t *testing.T) {
	var (
		ticketname = "cc105c2b-b13e-4e97-8f16-b7b52694ecbf"
		ctx        = context.Background()
	)
	storage, err := setupMongoStore(ctx)
	if err != nil {
		t.Fatal(err)
	}
	obj := &BobTicket{}
	if err := storage.Get(ctx, ticketname, obj); err != nil {
		t.Fatal(err)
	}

	if err := storage.Transaction(ctx, func(ctx context.Context, storage store.Store) error {
		var pics []any
		for _, comment := range obj.Comments {
			if comment.Picture == "" {
				continue
			}
			pics = append(pics, comment.Picture)
		}
		if len(pics) > 0 {
			if err := storage.DeleteBatch(ctx, &store.List[BobTicketPicture]{}, store.WithDeleteBatchFieldRequirements(store.Requirement{
				Key:      "name",
				Operator: store.In,
				Values:   pics,
			})); err != nil {
				return err
			}
		}
		return storage.Delete(ctx, obj)
	}); err != nil {
		t.Fatal(err)
	}
	t.Log("delete success")
	t.Log("start get this ticket")
	newObj := &BobTicket{}
	if err := storage.Get(ctx, ticketname, obj); err != nil {
		t.Fatal(err)
	}
	t.Log("find this ticket,delete failed")
	t.Log(newObj)
}
