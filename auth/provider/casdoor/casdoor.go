package casdoor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/auth"
)

const CasdoorSessionID = "casdoor_session_id"

type Options struct {
	Address             string `json:"address,omitempty"`
	DefaultOrganization string `json:"defaultOrganization,omitempty"`
	DefaultApplication  string `json:"defaultApplication,omitempty"`
	AdminUsername       string `json:"adminUsername,omitempty"`
	ClientID            string `json:"clientID,omitempty"`
	ClientSecret        string `json:"clientSecret,omitempty"`
}

func NewDefaultOptions() *Options {
	return &Options{
		Address:             "http://127.0.0.1:8000",
		DefaultOrganization: "built-in",
		DefaultApplication:  "app-built-in",
		AdminUsername:       "admin",
		ClientID:            "",
		ClientSecret:        "",
	}
}

func NewProvider(ctx context.Context, options *Options) (*Provider, error) {
	client, err := NewUserClient(options)
	if err != nil {
		return nil, err
	}
	adminclient := &AdminClient{options: options}
	user, err := NewUserProvider(ctx, adminclient)
	if err != nil {
		return nil, fmt.Errorf("failed to create casdoor user provider: %w", err)
	}
	auth := &AuthProvider{Client: client, Admin: adminclient}
	return &Provider{AuthProvider: *auth, UserProvider: *user}, nil
}

type Provider struct {
	AuthProvider
	UserProvider
}

var _ auth.AuthProvider = &AuthProvider{}

type AuthProvider struct {
	Client *SessionClient
	Admin  *AdminClient
}

// CheckAPIKey implements auth.AuthProvider.
func (p *AuthProvider) CheckAPIKey(ctx context.Context, key auth.APIKey) (*auth.User, error) {
	user, err := p.Client.getAccount(ctx, basicAuth(key.AccessKey, key.SecretKey))
	if err != nil {
		return nil, err
	}
	authuser := ConvertToUser(user)
	return &authuser.User, nil
}

func basicAuth(username, password string) string {
	return "Basic " + base64.StdEncoding.EncodeToString([]byte(username+":"+password))
}

// GenerateAPIKey implements auth.AuthProvider.
func (p *AuthProvider) GenerateAPIKey(ctx context.Context, session string, options auth.GenerateAPIKeyOptions) (*auth.APIKey, error) {
	if err := p.Client.addUserKeys(ctx, session); err != nil {
		return nil, err
	}
	// get user account
	user, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return nil, err
	}
	ak, sk := user.AccessKey, user.AccessSecret
	if ak == "" || sk == "" {
		return nil, errors.NewUnauthorized("failed to generate api key")
	}
	return &auth.APIKey{Name: "default", AccessKey: ak, SecretKey: sk}, nil
}

// DeleteAPIKey implements auth.AuthProvider.
func (p *AuthProvider) DeleteAPIKey(ctx context.Context, session string, accessKey string) error {
	// not supported
	return nil
}

// ListAPIKeys implements auth.AuthProvider.
func (p *AuthProvider) ListAPIKeys(ctx context.Context, session string) ([]auth.APIKey, error) {
	user, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return nil, err
	}
	apikeys := []auth.APIKey{}
	ak, sk := user.AccessKey, user.AccessSecret
	if ak != "" && sk != "" {
		apikeys = append(apikeys, auth.APIKey{
			Name:      "default",
			AccessKey: ak,
			SecretKey: "******",
		})
	}
	return apikeys, nil
}

// LoginAsUser implements auth.AuthProvider.
func (p *AuthProvider) LoginAsUser(ctx context.Context, username string) (*auth.Session, error) {
	// get user account
	u, err := p.Admin.getUser(ctx, username)
	if err != nil {
		return nil, err
	}
	// login with aksk
	ak, sk := u.AccessKey, u.AccessSecret

	if ak == "" || sk == "" {
		// init user aksk
		if err := p.Admin.addUserKeys(ctx, u); err != nil {
			return nil, err
		}
		// get user account
		u, err := p.Admin.getUser(ctx, username)
		if err != nil {
			return nil, err
		}
		// login with aksk
		ak, sk = u.AccessKey, u.AccessSecret
	}
	req := httpclient.Get("/api/get-user").Query("id", p.Client.options.DefaultOrganization+"/"+username)
	resp, respdata, err := p.Client.do(ctx, basicAuth(ak, sk), req)
	if err != nil {
		return nil, err
	}
	if err := respdata.Error(); err != nil {
		return nil, err
	}
	for _, cookie := range resp.Cookies() {
		if cookie.Name == CasdoorSessionID {
			return &auth.Session{Value: cookie.Value, Expires: cookie.Expires}, nil
		}
	}
	return nil, errors.NewUnauthorized("login failed")
}

// InitiateMFA implements auth.AuthProvider.
func (p *AuthProvider) InitMFA(ctx context.Context, session string, options auth.InitMFAOptions) (*auth.MFAInitConfig, error) {
	acc, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return nil, err
	}
	// get init data
	data, err := p.Client.mfaSetupInitiate(ctx, session, acc.Name, strings.ToLower(string(options.Provider)))
	if err != nil {
		return nil, err
	}
	username := acc.Name
	if acc.Email != "" {
		username = acc.Email
	}
	ret := &auth.MFAInitConfig{
		Provider:      auth.MFAProvider(data.MFAType),
		Secret:        data.Secret,
		Username:      username, // for otp app
		RecoveryCodes: data.RecoveryCodes,
	}
	return ret, nil
}

// VerifyMFA implements auth.Provider.
func (p *AuthProvider) VerifyMFA(ctx context.Context, session string, data auth.VerrifyMFAData) error {
	if strings.ToLower(data.Action) == "bind" {
		acc, err := p.Client.getAccount(ctx, session)
		if err != nil {
			return err
		}
		if err := p.Client.mfaSetupVerify(ctx, session, acc.Name, data.Provider, data.Code); err != nil {
			return err
		}
		return p.Client.mfaSetupFinalize(ctx, session, acc.Name, data.Provider)
	} else {
		if data.RecoveryCode != "" {
			return p.Client.loginVerifyMFARecoveryCode(ctx, session, data.RecoveryCode)
		}
		return p.Client.loginVerifyMFA(ctx, session, data.Provider, data.Code)
	}
}

// RemoveMFA implements auth.AuthProvider.
func (p *AuthProvider) RemoveMFA(ctx context.Context, session string, config auth.RemoveMFAOptions) error {
	acc, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return err
	}
	return p.Client.deleteMFA(ctx, session, acc.Name, "")
}

// CheckSession implements auth.Provider.
func (p *AuthProvider) CheckSession(ctx context.Context, session string) (*auth.Session, error) {
	cookies, err := p.Client.getHealth(ctx, session)
	if err != nil {
		return nil, err
	}
	for _, cookie := range cookies {
		if cookie.Name == CasdoorSessionID {
			return &auth.Session{Value: cookie.Value, Expires: cookie.Expires}, nil
		}
	}
	return nil, nil
}

// UpdateProfile implements auth.Provider.
func (p *AuthProvider) UpdateCurrentProfile(ctx context.Context, session string, data auth.UserProfile) error {
	exists, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return err
	}
	UpdateFromUser(exists, data, p.Client.options.DefaultOrganization)
	return p.Client.updateUser(ctx, session, *exists)
}

// ResetPassword implements auth.Provider.
func (p *AuthProvider) ResetPassword(ctx context.Context, session string, data auth.ResetPasswordData) error {
	acc, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return err
	}
	return p.Client.setPassword(ctx, session, acc.Name, data.Password, data.NewPassword)
}

// ResetEmail implements auth.Provider.
func (p *AuthProvider) ResetEmail(ctx context.Context, session string, data auth.ResetEmailData) error {
	return p.Client.resetEmailOrPhone(ctx, session, "email", data.NewEmail, data.Code)
}

// ResetPhone implements auth.Provider.
func (p *AuthProvider) ResetPhone(ctx context.Context, session string, data auth.ResetPhoneData) error {
	return p.Client.resetEmailOrPhone(ctx, session, "phone", data.NewPhone, data.Code)
}

// GetProfile implements auth.OIDCProvider.
func (p *AuthProvider) GetCurrentProfile(ctx context.Context, session string) (*auth.UserProfile, error) {
	acc, err := p.Client.getAccount(ctx, session)
	if err != nil {
		return nil, err
	}
	return ConvertToUser(acc), nil
}

// GetCaptcha implements auth.OIDCProvider.
func (p *AuthProvider) GetCaptcha(ctx context.Context, option auth.GetCaptchaConfigOption) (*auth.CaptchaConfig, error) {
	data, err := p.Client.getCaptcha(ctx, option.Username)
	if err != nil {
		return nil, err
	}
	config := &auth.CaptchaConfig{
		Provider: ConvertCaptchaTypeToAuth(data.Type),
	}
	switch config.Provider {
	case auth.CaptchaTypeGraphic:
		config.Key = data.CaptchaID
		config.Params = map[string]string{"image": data.CaptchaImg}
	default:
		return nil, errors.NewBadRequest("unsupported captcha type: " + data.Type)
	}
	return config, nil
}

// Signin implements auth.OIDCProvider.
func (p *AuthProvider) Signin(ctx context.Context, session string, config auth.LoginData) (*auth.LoginResponse, error) {
	logindata := logindata{AutoSignin: true}
	switch config.Type {
	case auth.LoginMethodTypePassword:
		logindata.SigninMethod = "Password"
		logindata.Username = config.Username
		logindata.Password = config.Password.Value
	case auth.LoginMethodTypeOTP:
		logindata.SigninMethod = "Verification code"
		logindata.Username = config.Username
		logindata.Code = config.OTP.Code
	case auth.LoginMethodTypeOauth2:
		logindata.Provider = config.Oauth2.Provider
		if logindata.Provider == "" {
			// use the first(default) provider
			// It's a special case for login directly from the Auth0's launchpad
			config, err := p.GetConfiguration(ctx, auth.GetConfigurationOptions{})
			if err != nil {
				return nil, err
			}
			for _, method := range config.Methods {
				if method.Type == auth.LoginMethodTypeOauth2 {
					logindata.Provider = method.Oauth2.Provider
					break
				}
			}
		}
		logindata.Code = config.Oauth2.Code
		logindata.RedirectUri = config.Oauth2.RedirectURI
		logindata.Method = "signup"
	default:
		return nil, errors.NewBadRequest("unsupported login method: " + string(config.Type))
	}
	if config.Captcha.Code != "" {
		logindata.CaptchaToken = config.Captcha.Code
		logindata.CaptchaType = ConvertCaptchaTypeFromAuth(config.Captcha.Provider)
		logindata.ClientSecret = config.Captcha.Key
	}

	respdata, err := p.Client.login(ctx, session, logindata)
	if err != nil {
		return nil, err
	}
	if err := respdata.ErrorSignin(); err != nil {
		return nil, err
	}
	next := ""
	if err := respdata.Decode(&next); err != nil {
		return nil, err
	}
	switch next {
	case "NextMfa":
		config := &MultiFactorAuth{}
		if err := json.Unmarshal(respdata.Data2, config); err != nil {
			return nil, err
		}
		mfa := &auth.MFAConfig{
			Enabled:  config.Enabled,
			Provider: auth.MFAProvider(config.MFAType),
		}
		return &auth.LoginResponse{Next: auth.NextLoginTypeMFA, MFA: mfa}, nil
	case "RequiredMfa":
		// MFA is required but user has not set up
		// it allow user login without MFA but force user to set up MFA after login
		return &auth.LoginResponse{}, nil
	default:
		// if auth success, casdoor will return the user org/name id.
		if i := strings.Index(next, "/"); i > 0 {
			return &auth.LoginResponse{}, nil
		}
		return nil, errors.NewBadRequest("unsupported next step: " + next)
	}
}

// Signup implements auth.Provider.
func (p *AuthProvider) Signup(ctx context.Context, session string, data auth.SignUpData) error {
	return p.Client.signup(ctx, session, data)
}

// Signout implements auth.OIDCProvider.
func (p *AuthProvider) Signout(ctx context.Context, session string) error {
	return p.Client.signout(ctx, session)
}

// SendOTPCode implements auth.OIDCProvider.
func (p *AuthProvider) SendOTPCode(ctx context.Context, config auth.SendOTPCode) error {
	return p.Client.sendOTPCode(ctx, config)
}

// GetConfiguration implements auth.OIDCProvider.
func (p *AuthProvider) GetConfiguration(ctx context.Context, options auth.GetConfigurationOptions) (*auth.LoginConfiguration, error) {
	app, err := p.Client.getapplication(ctx, "admin/app-built-in")
	if err != nil {
		return nil, err
	}
	methods := []auth.LoginMethod{}
	for _, signinmethod := range app.SigninMethods {
		method := auth.LoginMethod{}
		switch signinmethod.Name {
		case "Password":
			method.Type = auth.LoginMethodTypePassword
			method.Password = &auth.PasswordLoginConfig{
				Algorithm: auth.PasswordAlgorithmPlainText,
			}
		case "Verification code":
			method.Type = auth.LoginMethodTypeOTP
			method.OTP = &auth.OTPLoginConfig{
				Provider: "auto",
			}
		case "WebAuthn":
			method.Type = auth.LoginMethodTypeWebAuthn
		default:
			log.FromContext(ctx).Info("unsupported casdoor signin method", "method", signinmethod)
			continue
		}
		methods = append(methods, method)
	}
	for _, provider := range app.Providers {
		// filter prividers by options.Platform
		if options.Platform != "" && !strings.HasPrefix(provider.Name, options.Platform) {
			continue
		}
		switch provider.Provider.Type {
		case "GitHub":
			method := auth.LoginMethod{
				Type: auth.LoginMethodTypeOauth2,
				Oauth2: &auth.Oauth2LoginConfig{
					Name:         provider.Provider.DisplayName,
					Provider:     provider.Provider.Name,
					AuthorizeURL: "https://github.com/login/oauth/authorize",
					ClientID:     provider.Provider.ClientID,
					Scope:        "user:email read:user",
					ResponseType: "code",
				},
			}
			methods = append(methods, method)
		case "Default": // captcha
			continue
		case "Custom":
			switch provider.Provider.Category {
			case "OAuth":
				method := auth.LoginMethod{
					Type: auth.LoginMethodTypeOauth2,
					Oauth2: &auth.Oauth2LoginConfig{
						Name:         provider.Provider.DisplayName,
						Provider:     provider.Provider.Name,
						AuthorizeURL: provider.Provider.CustomAuthUrl,
						TokenURL:     provider.Provider.CustomTokenUrl,
						UserInfoURL:  provider.Provider.CustomUserUrl,
						ClientID:     provider.Provider.ClientID,
						Scope:        provider.Provider.Scopes,
						ResponseType: "code",
					},
				}
				methods = append(methods, method)
			}
		default:
			log.FromContext(ctx).Info("unsupported casdoor sigin provider", "provider", provider)
			continue
		}
	}
	return &auth.LoginConfiguration{Methods: methods}, nil
}

type Application struct {
	SigninMethods []SigninMethod        `json:"signinMethods"`
	Providers     []ApplicationProvider `json:"providers"`
}

type ApplicationProvider struct {
	Name     string         `json:"name"`
	Rule     string         `json:"rule"`
	Provider ProviderConfig `json:"provider"`
}

type ProviderConfig struct {
	Method       string `json:"method"`
	Type         string `json:"type"`
	DisplayName  string `json:"displayName"`
	Name         string `json:"name"`
	Scopes       string `json:"scopes"`
	Category     string `json:"category"`
	ClientID     string `json:"clientId"`
	ClientSecret string `json:"clientSecret"`

	CustomAuthUrl  string                    `json:"customAuthUrl"`
	CustomTokenUrl string                    `json:"customTokenUrl"`
	CustomUserUrl  string                    `json:"customUserUrl"`
	UserMapping    ProviderConfigUserMapping `json:"userMapping"`
}

type ProviderConfigUserMapping struct {
	AvatarUrl   string `json:"avatarUrl"`
	DisplayName string `json:"displayName"`
	Email       string `json:"email"`
	ID          string `json:"id"`
	Username    string `json:"username"`
}

type SigninMethod struct {
	Name        string `json:"name"`
	DisplayName string `json:"displayName"`
	Rule        string `json:"rule"`
}
