package casdoor

import (
	"context"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/core/auth"
)

var _ auth.UserProvider = &UserProvider{}

func NewUserProvider(ctx context.Context, cli *AdminClient) (*UserProvider, error) {
	return &UserProvider{Client: cli}, nil
}

type UserProvider struct {
	Client *AdminClient
}

// CountUsers implements auth.UserProvider.
func (u *UserProvider) CountUsers(ctx context.Context) (int, error) {
	count, err := u.Client.getUserCount(ctx, nil)
	if err != nil {
		return 0, err
	}
	return int(count), nil
}

// GetUserProfile implements auth.UserProvider.
func (u *UserProvider) GetUserProfile(ctx context.Context, username string) (*auth.UserProfile, error) {
	return u.GetUser(ctx, username)
}

// SetUserPassword implements auth.UserProvider.
func (u *UserProvider) SetUserPassword(ctx context.Context, username string, password string) error {
	return u.Client.setPassword(ctx, username, password)
}

// UpdateUserProfile implements auth.UserProvider.
func (u *UserProvider) UpdateUserProfile(ctx context.Context, profile *auth.UserProfile) error {
	return u.UpdateUser(ctx, profile)
}

// ListUsers implements auth.UserProvider.
func (u *UserProvider) ListUsers(ctx context.Context, options auth.ListUserOptions) (api.Page[auth.UserProfile], error) {
	list, err := u.Client.listUsers(ctx, u.Client.options.DefaultOrganization, options.ListOptions)
	if err != nil {
		return api.Page[auth.UserProfile]{}, err
	}
	users := make([]auth.UserProfile, 0, len(list.Items))
	for _, item := range list.Items {
		users = append(users, *ConvertToUser(&item))
	}
	return api.Page[auth.UserProfile]{Items: users, Total: list.Total}, nil
}

// CreateUser implements auth.UserProvider.
func (u *UserProvider) CreateUser(ctx context.Context, user *auth.UserProfile, password string) error {
	// check user email and phone already exists
	// casdoor only check username on user creation
	allusers, err := u.Client.listUsers(ctx, u.Client.options.DefaultOrganization, api.ListOptions{})
	if err != nil {
		return err
	}
	for _, u := range allusers.Items {
		if u.Name == user.Name {
			return errors.NewAlreadyExists("users", user.Name)
		}
		if user.Email != "" && u.Email == user.Email {
			return errors.NewAlreadyExists("email", user.Email)
		}
		if user.Phone != "" && u.Phone == user.Phone {
			return errors.NewAlreadyExists("phone", user.Phone)
		}
	}
	newuser := User{}
	UpdateFromUser(&newuser, *user, u.Client.options.DefaultOrganization)
	newuser.Password = password
	return u.Client.addUser(ctx, newuser)
}

// DeleteUser implements auth.UserProvider.
func (u *UserProvider) DeleteUser(ctx context.Context, username string) (*auth.UserProfile, error) {
	userinfo, err := u.Client.getUser(ctx, username)
	if err != nil {
		return nil, err
	}
	if err := u.Client.deleteUser(ctx, *userinfo); err != nil {
		return nil, err
	}
	return ConvertToUser(userinfo), nil
}

// SetUserDisabled implements auth.UserProvider.
func (u *UserProvider) SetUserDisabled(ctx context.Context, username string, disabled bool) error {
	user, err := u.Client.getUser(ctx, username)
	if err != nil {
		return err
	}
	user.IsForbidden = disabled
	return u.Client.updateUser(ctx, *user)
}

// GetUser implements auth.UserProvider.
func (u *UserProvider) GetUser(ctx context.Context, username string) (*auth.UserProfile, error) {
	user, err := u.Client.getUser(ctx, username)
	if err != nil {
		return nil, err
	}
	return ConvertToUser(user), nil
}

// UpdateUser implements auth.UserProvider.
func (u *UserProvider) UpdateUser(ctx context.Context, data *auth.UserProfile) error {
	exists, err := u.Client.getUser(ctx, data.Name)
	if err != nil {
		return err
	}
	UpdateFromUser(exists, *data, u.Client.options.DefaultOrganization)
	if err := u.Client.updateUser(ctx, *exists); err != nil {
		return err
	}
	*data = *ConvertToUser(exists)
	return nil
}
