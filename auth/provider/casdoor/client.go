package casdoor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/core/auth"
)

// password or code is incorrect, you have 3 remaining chances
var passwordOrCodeIncorrectRegxp = regexp.MustCompile(`password or code is incorrect, you have (\d+) remaining chances`)

// "You have entered the wrong password or code too many times, please wait for 15 minutes and try again"
var tooManyAttemptsRegexp = regexp.MustCompile(`You have entered the wrong password or code too many times, please wait for (\d+) minutes and try again`)

func NewUserClient(options *Options) (*SessionClient, error) {
	return &SessionClient{options: options}, nil
}

type SessionClient struct {
	options *Options
}

type CaptchaData struct {
	Owner         string `json:"owner"`
	Name          string `json:"name"`
	Type          string `json:"type"`
	AppKey        string `json:"appKey"`
	Scene         string `json:"scene"`
	CaptchaID     string `json:"captchaId"`
	CaptchaImg    string `json:"captchaImage"`
	ClientID      string `json:"clientId"`
	ClientSecret  string `json:"clientSecret"`
	ClientID2     string `json:"clientId2"`
	ClientSecret2 string `json:"clientSecret2"`
	SubType       string `json:"subType"`
}

// keepAlive
func (c *SessionClient) KeepAlive(ctx context.Context, session string) error {
	_, err := c.getAccount(ctx, session)
	return err
}

func (c *SessionClient) getCaptcha(ctx context.Context, _ string) (*CaptchaData, error) {
	data := &CaptchaData{}
	req := httpclient.
		Get("/api/get-captcha").
		Query("applicationId", c.options.AdminUsername+"/"+c.options.DefaultApplication).
		// Query("isCurrentProvider", "true").
		Return(data)
	return data, c.send(ctx, "", req)
}

func (c *SessionClient) signout(ctx context.Context, session string) error {
	req := httpclient.Post("/api/logout")
	return c.send(ctx, session, req)
}

func (c *SessionClient) signup(ctx context.Context, session string, data auth.SignUpData) error {
	reqbody := map[string]any{
		"application":  c.options.DefaultApplication,
		"organization": c.options.DefaultOrganization,
		"username":     data.Username,
		"name":         data.DisplayName,
		"password":     data.Password.Value,
		"confirm":      data.Password.Value,
		"email":        data.Email.Value,
		"emailCode":    data.Email.Code,
		"agreement":    data.Agreement,
	}
	req := httpclient.Post("/api/signup").JSON(reqbody)
	return c.send(ctx, session, req)
}

func (c *SessionClient) sendOTPCode(ctx context.Context, config auth.SendOTPCode) error {
	if config.Action == "" {
		config.Action = auth.OPTActionLogin
	}
	formdata := map[string]string{
		"applicationId": c.options.AdminUsername + "/" + c.options.DefaultApplication,
		"method":        strings.ToLower(string(config.Action)),
		"type":          strings.ToLower(string(config.Type)),
		"dest":          config.Target,
		"captchaType":   ConvertCaptchaTypeFromAuth(config.Captcha.Provider),
		"clientSecret":  config.Captcha.Key,
		"captchaToken":  config.Captcha.Code,
	}
	req := httpclient.Post("/api/send-verification-code").MultiFormData(formdata)
	return c.send(ctx, "", req)
}

func ConvertCaptchaTypeFromAuth(t auth.CaptchaProvider) string {
	switch t {
	case auth.CaptchaTypeGraphic:
		return "Default"
	case auth.CaptchaTypeRecaptcha:
		return "Recaptcha"
	default:
		return ""
	}
}

func ConvertCaptchaTypeToAuth(t string) auth.CaptchaProvider {
	switch t {
	case "Default":
		return auth.CaptchaTypeGraphic
	case "Recaptcha":
		return auth.CaptchaTypeRecaptcha
	default:
		return auth.CaptchaTypeNone
	}
}

type logindata struct {
	Application  string `json:"application"`
	Organization string `json:"organization"`
	AutoSignin   bool   `json:"autoSignin"`
	Username     string `json:"username"`
	Type         string `json:"type"`
	Password     string `json:"password"`
	SigninMethod string `json:"signinMethod"`
	Code         string `json:"code"`
	RedirectUri  string `json:"redirectUri"`
	// oauth2
	Provider string `json:"provider"`
	State    string `json:"state"`
	// if method set to signup, then user will be created
	Method string `json:"method"`
	// Captcha
	CaptchaType  string `json:"captchaType"`
	CaptchaToken string `json:"captchaToken"`
	ClientSecret string `json:"clientSecret"`
}

func (c *SessionClient) login(ctx context.Context, data logindata) (*ResponseWrapper, string, error) {
	if data.Application == "" {
		data.Application = c.options.DefaultApplication
	}
	if data.Organization == "" {
		data.Organization = c.options.DefaultOrganization
	}
	if data.Type == "" {
		data.Type = "login"
	}
	if data.Provider != "" {
		// oauth2 should set state to application
		data.State = data.Application
	}
	next := ""
	req := httpclient.Post("/api/login").JSON(data).Return(&next)
	resp, respdata, err := c.do(ctx, "", req)
	if err != nil {
		return nil, "", err
	}
	var session string
	for _, cookie := range resp.Cookies() {
		if cookie.Name == CasdoorSessionID {
			session = cookie.Value
			break
		}
	}
	respdata.Name = session
	return respdata, session, nil
}

func (c *SessionClient) loginVerifyMFA(ctx context.Context, session string, mfatype, passcode string) error {
	if mfatype == "" {
		mfatype = "app"
	}
	logindata := map[string]any{
		"application":  c.options.DefaultApplication,
		"organization": c.options.DefaultOrganization,
		"type":         "login",
		"signinMethod": "Password",
		"autoSignin":   true,
		"mfaType":      mfatype,
		"passcode":     passcode,
	}
	req := httpclient.Post("/api/login").JSON(logindata)
	return c.send(ctx, session, req)
}

func (c *SessionClient) loginVerifyMFARecoveryCode(ctx context.Context, session string, recoverycode string) error {
	logindata := map[string]any{
		"type":         "login",
		"application":  c.options.DefaultApplication,
		"organization": c.options.DefaultOrganization,
		"autoSignin":   true,
		"recoveryCode": recoverycode,
	}
	req := httpclient.Post("/api/login").JSON(logindata)
	return c.send(ctx, session, req)
}

func (c *SessionClient) updateUser(ctx context.Context, session string, user User) error {
	id := c.options.DefaultOrganization + "/" + user.Name
	req := httpclient.Post("/api/update-user").Query("id", id).JSON(user)
	return c.send(ctx, session, req)
}

func (c *SessionClient) addUserKeys(ctx context.Context, session string) error {
	user, err := c.getAccount(ctx, session)
	if err != nil {
		return err
	}
	req := httpclient.Post("/api/add-user-keys").JSON(user)
	return c.send(ctx, session, req)
}

func (c *SessionClient) getAccount(ctx context.Context, session string) (*User, error) {
	user := &User{}
	req := httpclient.NewRequest(http.MethodGet, "/api/get-account").Return(user)
	if err := c.send(ctx, session, req); err != nil {
		return nil, err
	}
	return user, nil
}

type MFASetupInitiate struct {
	Enabled       bool     `json:"enabled"`
	IsPreferred   bool     `json:"isPreferred"`
	MFAType       string   `json:"mfaType"`
	Secret        string   `json:"secret"`
	URL           string   `json:"url"`
	RecoveryCodes []string `json:"recoveryCodes"`
}

func (c *SessionClient) mfaSetupInitiate(ctx context.Context, session, username, mfatyp string) (*MFASetupInitiate, error) {
	if mfatyp == "" {
		mfatyp = "app"
	}
	reqdata := map[string]string{
		"owner":   c.options.DefaultOrganization,
		"name":    username,
		"mfaType": mfatyp,
	}
	data := &MFASetupInitiate{}
	req := httpclient.Post("/api/mfa/setup/initiate").MultiFormData(reqdata).Return(data)
	if err := c.send(ctx, session, req); err != nil {
		return nil, err
	}
	return data, nil
}

func (c *SessionClient) mfaSetupVerify(ctx context.Context, session, username, mfatyp, code string) error {
	if mfatyp == "" {
		mfatyp = "app"
	}
	reqdata := map[string]string{
		"owner":    c.options.DefaultOrganization,
		"name":     username,
		"mfaType":  mfatyp,
		"passcode": code,
	}
	req := httpclient.Post("/api/mfa/setup/verify").MultiFormData(reqdata)
	return c.send(ctx, session, req)
}

func (c *SessionClient) mfaSetupFinalize(ctx context.Context, session, username, mfatyp string) error {
	if mfatyp == "" {
		mfatyp = "app"
	}
	reqdata := map[string]string{
		"owner":   c.options.DefaultOrganization,
		"name":    username,
		"mfaType": mfatyp,
	}
	req := httpclient.Post("/api/mfa/setup/enable").MultiFormData(reqdata)
	return c.send(ctx, session, req)
}

func (c *SessionClient) deleteMFA(ctx context.Context, session, username, _ string) error {
	reqdata := map[string]string{
		"owner": c.options.DefaultOrganization,
		"name":  username,
	}
	req := httpclient.Post("/api/delete-mfa").MultiFormData(reqdata)
	return c.send(ctx, session, req)
}

func (c *SessionClient) CheckUserPassword(ctx context.Context, session, username, password string) error {
	reqdata := map[string]string{
		"owner":    c.options.DefaultOrganization,
		"name":     username,
		"password": password,
	}
	req := httpclient.Post("/api/check-user-password").JSON(reqdata)
	return c.send(ctx, session, req)
}

func (c *SessionClient) getapplication(ctx context.Context, name string) (*Application, error) {
	app := &Application{}
	req := httpclient.NewRequest(http.MethodGet, "/api/get-application").Query("id", name).Return(app)
	return app, c.send(ctx, "", req)
}

func (c *SessionClient) getHealth(ctx context.Context, cookie string) ([]*http.Cookie, error) {
	resp, _, err := c.do(ctx, cookie, httpclient.Get("/api/health"))
	if err != nil {
		return nil, err
	}
	return resp.Cookies(), nil
}

func (c *SessionClient) setPassword(ctx context.Context, session, username, password, newPassword string) error {
	reqdata := map[string]string{
		"userOwner":   c.options.DefaultOrganization,
		"userName":    username,
		"oldPassword": password,
		"newPassword": newPassword,
	}
	req := httpclient.Post("/api/set-password").FormURLEncoded(reqdata)
	_, respdata, err := c.do(ctx, session, req)
	if err != nil {
		return err
	}
	if respdata.Success() {
		return nil
	}
	// password or code is incorrect, you have 3 remaining chances
	if matches := passwordOrCodeIncorrectRegxp.FindStringSubmatch(respdata.Msg); len(matches) > 1 {
		return errors.NewBadRequest(fmt.Sprintf("Password incorrect (%s chances left)", matches[1]))
	}
	// "You have entered the wrong password or code too many times, please wait for 15 minutes and try again"
	if matches := tooManyAttemptsRegexp.FindStringSubmatch(respdata.Msg); len(matches) > 1 {
		return errors.NewBadRequest(fmt.Sprintf("Too many attempts, please retry after %s minutes", matches[1]))
	}
	return respdata.Error()
}

func (c *SessionClient) resetEmailOrPhone(ctx context.Context, session, typ, dest, verifyCode string) error {
	reqdata := map[string]string{"type": typ, "dest": dest, "code": verifyCode}
	req := httpclient.Post("/api/reset-email-or-phone").FormURLEncoded(reqdata)
	return c.send(ctx, session, req)
}

func (r SessionClient) send(ctx context.Context, sessionOrapikey string, req *httpclient.Builder) error {
	into := req.R.DecodeInto
	_, respdata, err := r.do(ctx, sessionOrapikey, req)
	if err != nil {
		return err
	}
	if err := respdata.Error(); err != nil {
		return err
	}
	if into != nil {
		return respdata.Decode(into)
	}
	return nil
}

func (r SessionClient) do(ctx context.Context, sessionOrapikey string, req *httpclient.Builder) (*http.Response, *ResponseWrapper, error) {
	if sessionOrapikey != "" {
		if strings.HasPrefix(sessionOrapikey, "Basic ") {
			username, password, ok := parseBasicAuth(sessionOrapikey)
			if !ok {
				return nil, nil, errors.NewBadRequest("Invalid session key format")
			}
			// https://casdoor.org/docs/basic/public-api/#3-by-access-key-and-access-secret
			req = req.Query("accessKey", username).Query("accessSecret", password)
		} else {
			req.Cookie(CasdoorSessionID, sessionOrapikey)
		}
	}
	respdata := &ResponseWrapper{}

	u, err := url.Parse(r.options.Address)
	if err != nil {
		return nil, nil, errors.NewInternalError(fmt.Errorf("invalid casdoor address: %s, error: %v", r.options.Address, err))
	}

	resp, err := req.BaseAddr(u).Return(respdata).Do(ctx)
	if err != nil {
		return nil, nil, err
	}
	return resp, respdata, nil
}

func parseBasicAuth(auth string) (username, password string, ok bool) {
	const prefix = "Basic "
	// Case insensitive prefix match. See Issue 22736.
	if len(auth) < len(prefix) || !strings.EqualFold(auth[:len(prefix)], prefix) {
		return "", "", false
	}
	c, err := base64.StdEncoding.DecodeString(auth[len(prefix):])
	if err != nil {
		return "", "", false
	}
	cs := string(c)
	username, password, ok = strings.Cut(cs, ":")
	if !ok {
		return "", "", false
	}
	return username, password, true
}

type ResponseWrapper struct {
	Data   json.RawMessage `json:"data"`
	Data2  json.RawMessage `json:"data2"`
	Msg    string          `json:"msg"`
	Name   string          `json:"name"`
	Status string          `json:"status"`
	Sub    string          `json:"sub"`
}

type BaseClient struct {
	BaseAddr string
	httpcli  *http.Client
}

func (r ResponseWrapper) Success() bool {
	return r.Status == "ok"
}

// "email - password or code is incorrect, you have 1 remaining chances"
var incorrectPassOrCodeRgexp = regexp.MustCompile(`(.*) - password or code is incorrect, you have (\d+) remaining chances`)

// ErrorSignin return error in sign
func (r ResponseWrapper) ErrorSignin() *errors.Status {
	if r.Status == "ok" {
		return nil
	}
	if r.Msg == "Please login first" {
		return auth.ErrorUnauthorized
	}
	// "Please sign out first" is not an error
	if strings.Contains(r.Msg, "Please sign out first") {
		return auth.ErrorAlreadyLoggedIn
	}
	// "The user: built-in/admin1 doesn't exist"
	if strings.Contains(r.Msg, "doesn't exist") {
		return auth.ErrorInvalidUsernameOrPassword
	}
	// "password or code is incorrect, you have 4 remaining chances"
	if strings.Contains(r.Msg, "password or code is incorrect") {
		if result := incorrectPassOrCodeRgexp.FindStringSubmatch(r.Msg); len(result) > 2 {
			switch result[1] {
			case "email", "phone":
				return auth.ErrorInvalidVerificationCode
			}
		}
		return auth.ErrorInvalidUsernameOrPassword
	}
	// when empty password
	// "Phone number is invalid in your region US"
	if strings.Contains(r.Msg, "Phone number is invalid") {
		return auth.ErrorInvalidUsernameOrPassword
	}
	return r.Error()
}

func (r ResponseWrapper) Error() *errors.Status {
	if r.Status == "ok" {
		return nil
	}
	// need captcha
	if strings.Contains(r.Msg, "invalid captcha provider") {
		return auth.ErrorNeedCaptcha
	}
	// captcha failed
	if strings.Contains(r.Msg, "Turing test failed") {
		return auth.ErrorInvalidCaptcha
	}
	// send-verification-code need captcha
	if strings.Contains(r.Msg, "Missing parameter: checkType.") {
		return auth.ErrorNeedCaptcha
	}
	// The verification code has not been sent yet!
	if strings.Contains(r.Msg, "The verification code has not been sent yet!") {
		return errors.NewBadRequest("Verification code not sent")
	}
	// The verification code has already been used!
	if strings.Contains(r.Msg, "The verification code has already been used!") {
		return errors.NewBadRequest("Invalid verification code")
	}
	// Wrong verification code!
	if strings.Contains(r.Msg, "Wrong verification code!") {
		return errors.NewBadRequest("Invalid verification code")
	}
	// You should verify your code in %d min!
	// email - You should verify your code in 10 min!
	if strings.Contains(r.Msg, "You should verify your code in") {
		return errors.NewBadRequest("Invalid verification code")
	}
	return errors.NewBadRequest(r.Msg)
}

func (r ResponseWrapper) IsNullData() bool {
	return r.Data == nil || string(r.Data) == "null"
}

func (r ResponseWrapper) Decode(into any) error {
	if r.Data == nil || into == nil {
		return nil
	}
	return json.Unmarshal(r.Data, into)
}
