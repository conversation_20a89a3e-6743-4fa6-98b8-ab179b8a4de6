package auth

import (
	"context"
	"net/http"
	"strings"

	"xiaoshiai.cn/common/rest/api"
)

const SessionCookieKey = "user_session"

func (a *API) OnSession(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, session string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		if authn := r.Header.Get("Authorization"); authn != "" {
			if after, ok := strings.CutPrefix(authn, "Bearer "); ok {
				return fn(ctx, after)
			}
			return fn(ctx, authn)
		}
		if key := api.GetCookie(r, SessionCookieKey); key != "" {
			return fn(ctx, key)
		}
		if token := r.URL.Query().Get("token"); token != "" {
			return fn(ctx, token)
		}
		return fn(ctx, "")
	})
}
