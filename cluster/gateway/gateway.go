package gateway

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/helm"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability"
	"xiaoshiai.cn/core/observerability/dashboard"
)

func NewAPI(base base.API, mongoStore store.Store, cloudinfo cluster.CloudInfoGetter, dashboards map[string]dashboard.DashboradConfiguration) *API {
	api := &API{API: base}
	api.Status = application.NewApplicationStatusAPI(base, mongoStore, cloudinfo, api.onGatewayApplication)
	api.Observerability = observerability.NewApplicationObservabilityAPI(api.Status, dashboards)
	return api
}

type API struct {
	base.API
	Status          *application.ApplicationStatusAPI
	Observerability *observerability.ApplicationObservabilityAPI
}

// Gateway is a tenant scopes resource
type Gateway struct {
	store.ObjectMeta `json:",inline"`
	Product          application.ProductReference   `json:"product,omitempty"`
	Cluster          application.ApplicationCluster `json:"cluster,omitempty"`
	Values           helm.HelmValues                `json:"values,omitempty"`
	Status           application.ApplicationStatus  `json:"status,omitempty"`
	Paused           bool                           `json:"paused,omitempty"`
}

type GatewayType string

func (a *API) onGatewayApplication(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, appref store.ObjectReference, appscopestorage store.Store) (any, error)) {
	a.onGateway(w, r, func(ctx context.Context, tenant string, gateway string) (any, error) {
		// check if the organization is allowed to access the gateway
		if organization := api.Path(r, "organization", ""); organization != "" {
			gateway, err := GetOrganizationGateway(ctx, a.Store, tenant, organization, gateway)
			if err != nil {
				return nil, err
			}
			_ = gateway
		}
		appref := store.ObjectReference{
			Name:   gateway,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		}
		appscopestorage := a.Store.Scope(appref.Scopes...).Scope(base.ScopeApplication(gateway))
		return fn(ctx, appref, appscopestorage)
	})
}

func (a *API) ListTenantGateway(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, _ store.Store, tenant string) (any, error) {
		return ListTenantGateways(r, a.Store, tenant)
	})
}

func ListTenantGateways(r *http.Request, root store.Store, tenant string, opts ...store.ListOption) (store.List[Gateway], error) {
	ret := store.List[Gateway]{}
	root = root.Scope(base.ScopeTenant(tenant))

	opts = append(opts, store.WithLabelRequirements(store.RequirementEqual(application.LabelApplicationSubCategory, base.ProductSubCategoryGateway)))
	applist, err := base.GenericList(r, root, &store.List[application.Application]{}, opts...)
	if err != nil {
		return ret, err
	}
	for _, app := range applist.Items {
		ret.Items = append(ret.Items, GatewayFromApplication(app))
	}
	return ret, nil
}

func (a *API) CreateTenantGateway(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, _ store.Store, tenant string) (any, error) {
		gateway := &Gateway{}
		if err := api.Body(r, gateway); err != nil {
			return nil, err
		}
		if err := a.checkBeforeSetGateway(ctx, gateway); err != nil {
			return nil, err
		}
		app := GatewayToApplication(tenant, gateway)
		app, err := application.CreateApplication(ctx, a.Store, []store.Scope{base.ScopeTenant(tenant)}, app)
		if err != nil {
			return nil, err
		}
		return GatewayFromApplication(*app), nil
	})
}

func (a *API) GetTenantGateway(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant string, gateway string) (any, error) {
		appref := store.ObjectReference{
			Name:   gateway,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		}
		app, err := application.GetApplication(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		return GatewayFromApplication(*app), nil
	})
}

func (a *API) PauseApplication(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant string, gateway string) (any, error) {
		appref := store.ObjectReference{
			Name:   gateway,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		}
		app, err := application.PauseApplication(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		return GatewayFromApplication(*app), nil
	})
}

func (a *API) ResumeApplication(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant string, gateway string) (any, error) {
		appref := store.ObjectReference{
			Name:   gateway,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		}
		app, err := application.ResumeApplication(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		return GatewayFromApplication(*app), nil
	})
}

func (a *API) UpdateTenantGateway(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant string, gateway string) (any, error) {
		gatewareq := &Gateway{}
		if err := api.Body(r, gatewareq); err != nil {
			return nil, err
		}
		if err := a.checkBeforeSetGateway(ctx, gatewareq); err != nil {
			return nil, err
		}
		app := GatewayToApplication(tenant, gatewareq)
		app.Name = gateway
		app, err := application.UpdateApplication(ctx, a.Store, []store.Scope{base.ScopeTenant(tenant)}, app)
		if err != nil {
			return nil, err
		}
		return GatewayFromApplication(*app), nil
	})
}

func (a *API) DeleteTenantGateway(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant string, gateway string) (any, error) {
		appref := store.ObjectReference{
			Name:   gateway,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		}
		app, err := application.DeleteApplication(ctx, a.Store, appref)
		if err != nil {
			return nil, err
		}
		return GatewayFromApplication(*app), nil
	})
}

func (a *API) onGateway(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant string, gateway string) (any, error)) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		gateway := api.Path(r, "gateway", "")
		if gateway == "" {
			return nil, errors.NewBadRequest("gateway name is required")
		}
		return fn(ctx, tenant, gateway)
	})
}

func (a *API) checkBeforeSetGateway(_ context.Context, gateway *Gateway) error {
	if len(gateway.Cluster.Scopes) == 0 && gateway.Cluster.ResourcePool == "" {
		return errors.NewBadRequest("gateway must deploy in a resource pool")
	}
	return nil
}

func GatewayToApplication(tenant string, gateway *Gateway) *application.Application {
	return &application.Application{
		ObjectMeta: gateway.ObjectMeta,
		Cluster:    gateway.Cluster,
		Values:     gateway.Values,
		// tenant gatway should be named as "tenant-" as prefix,it created the ingressClass.
		ReleaseName:              fmt.Sprintf("%s-%s", tenant, gateway.Name),
		Product:                  gateway.Product,
		AllowClusterSopeResource: true,
		Paused:                   gateway.Paused,
		Status:                   gateway.Status,
	}
}

func GatewayFromApplication(app application.Application) Gateway {
	return Gateway{
		ObjectMeta: app.ObjectMeta,
		Cluster:    app.Cluster,
		Values:     app.Values,
		Product:    app.Product,
		Status:     app.Status,
		Paused:     app.Paused,
	}
}

func (a *API) gatewaySubresourcesGroup() api.Group {
	return api.
		NewGroup("/{gateway}").
		Route(
			// overrried the default
			api.GET("/resources/core/v1/pods").
				Doc("Get application resource").
				To(a.ListGatewayPods),
		).
		SubGroup(
			a.Status.Group(),
			a.Observerability.Group(),
		)
}

func (a *API) tenantGroup() api.Group {
	return base.
		NewTenantGroup("gateways").
		SubGroup(
			a.gatewaySubresourcesGroup(),
		).
		Route(
			api.GET("").
				Operation("list tenant gateways").
				To(a.ListTenantGateway).
				Param(api.PageParams...).
				Response(store.List[Gateway]{}),

			api.POST("").
				Operation("create tenant gateway").
				To(a.CreateTenantGateway).
				Param(api.BodyParam("gateway", Gateway{})).
				Response(Gateway{}),

			api.GET("/{gateway}").
				Operation("get tenant gateway").
				To(a.GetTenantGateway).
				Response(Gateway{}),

			api.PUT("/{gateway}").
				Operation("update tenant gateway").
				To(a.UpdateTenantGateway).
				Param(api.BodyParam("gateway", Gateway{})).
				Response(Gateway{}),

			api.DELETE("/{gateway}").
				Operation("delete tenant gateway").
				To(a.DeleteTenantGateway),

			api.POST("/{gateway}:pause").
				Doc("Pause application").
				To(a.PauseApplication),

			api.POST("/{gateway}:resume").
				Doc("Resume application").
				To(a.ResumeApplication),

			api.GET("/{gateway}/organizations").
				Operation("get shared organizations").
				To(a.GetGatewayOrganizations).
				Response([]string{}),

			api.PUT("/{gateway}/organizations").
				Operation("share gateway to organizations").
				To(a.SetGatewayOrganizations).
				Param(api.BodyParam("organizations", []string{})).
				Response([]string{}),
		)
}

func (a *API) Group() api.Group {
	return api.NewGroup("").
		Tag("Gateway").
		SubGroup(
			a.tenantGroup(),
			a.organizationGroup(),
		)
}
