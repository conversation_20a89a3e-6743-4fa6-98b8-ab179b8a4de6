package gateway

import (
	"context"
	"encoding/json"
	"net/http"

	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/application"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/cluster/privatenode"
)

func (a *API) SetGatewayOrganizations(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant, gatewayname string) (any, error) {
		orgnames := []string{}
		if err := api.Body(r, &orgnames); err != nil {
			return nil, err
		}
		tenantscoped := a.Store.Scope(base.ScopeTenant(tenant))
		if err := base.SetShareddOrganizationToObject(ctx, tenantscoped, &application.Application{}, gatewayname, orgnames); err != nil {
			return nil, err
		}
		return orgnames, nil
	})
}

func (a *API) GetGatewayOrganizations(w http.ResponseWriter, r *http.Request) {
	a.onGateway(w, r, func(ctx context.Context, tenant, gatewayname string) (any, error) {
		gateway := &application.Application{}
		tenantscoped := a.Store.Scope(base.ScopeTenant(tenant))
		if err := tenantscoped.Get(ctx, gatewayname, gateway); err != nil {
			return nil, err
		}
		return base.ExtractSharedOrganizationFromLabels(gateway.Labels), nil
	})
}

func (a *API) ListOrganizationGateway(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		return ListTenantGateways(r, a.Store, tenant, base.WithSharedToOrganizationOption(org))
	})
}

func (a *API) GetOrganizationGateway(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		gateway := api.Path(r, "gateway", "")
		if gateway == "" {
			return nil, errors.NewBadRequest("gateway name is required")
		}
		return GetOrganizationGateway(ctx, a.Store, tenant, org, gateway)
	})
}

func GetOrganizationGateway(ctx context.Context, storage store.Store, tenant, org, gateway string) (*Gateway, error) {
	tenantscopestorage := storage.Scope(base.ScopeTenant(tenant))
	app, err := base.GetSharedToOrganizationObject(ctx, tenantscopestorage, &application.Application{}, org, gateway)
	if err != nil {
		return nil, err
	}
	return ptr.To(GatewayFromApplication(*app)), nil
}

type UnstructedWithAddtional struct {
	unstructured.Unstructured `json:",inline"`
	PrivateNodeName           string `json:"privateNodeName"`
}

func (a *API) ListGatewayPods(w http.ResponseWriter, r *http.Request) {
	a.Status.OnApplicationResourceOperation(w, r, func(ctx context.Context, op application.ResourceOperationInterface, app *application.Application, meta cluster.RequestMetadata) (any, error) {
		meta.GroupVersionResource = schema.GroupVersionResource{Group: "", Version: "v1", Resource: "pods"}
		list, err := op.List(r, app, meta)
		if err != nil {
			return nil, err
		}
		// to unstructured list
		jsondata, err := json.Marshal(list)
		if err != nil {
			return nil, err
		}
		unslist := []unstructured.Unstructured{}
		if err := json.Unmarshal(jsondata, &unslist); err != nil {
			log.FromContext(ctx).Error(err, "failed to unmarshal to unstructured list, use original list")
			// when error, return original list
			return list, nil
		}
		// inject privatenode info
		privatenodecliamslist := &store.List[privatenode.PrivateNodeClaim]{}
		if err := a.Store.Scope(app.Scopes...).List(ctx, privatenodecliamslist); err != nil {
			log.FromContext(ctx).Error(err, "failed to list privatenodeclaims")
		}
		nodenameToPrivateNodeCliamMap := map[string]privatenode.PrivateNodeClaim{}
		for _, privatenodeclaim := range privatenodecliamslist.Items {
			nodenameToPrivateNodeCliamMap[privatenodeclaim.Status.NodeName] = privatenodeclaim
		}
		for _, item := range unslist {
			nodename, _, _ := unstructured.NestedString(item.Object, "spec", "nodeName")
			if nodename == "" {
				continue
			}
			privatenodename := nodenameToPrivateNodeCliamMap[nodename].Name
			if privatenodename == "" {
				continue
			}
			item.Object["privateNodeName"] = privatenodename
		}
		return unslist, nil
	})
}

func (a *API) organizationGroup() api.Group {
	return base.
		NewTenantOrganizationGroup("gateways").
		Route(
			api.GET("").
				Operation("list organization gateways").
				To(a.ListOrganizationGateway).
				Param(base.PageParams...).
				Response(store.List[Gateway]{}),

			api.GET("/{gateway}").
				Operation("get organization gateway").
				To(a.GetOrganizationGateway).
				Response(Gateway{}),
		).
		SubGroup(
			a.gatewaySubresourcesGroup(),
		)
}
