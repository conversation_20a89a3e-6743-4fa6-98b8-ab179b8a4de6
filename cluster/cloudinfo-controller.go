package cluster

import (
	"context"
	"net/http"
	"net/url"

	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"sigs.k8s.io/controller-runtime/pkg/client"
	ctrlcluster "sigs.k8s.io/controller-runtime/pkg/cluster"
	"xiaoshiai.cn/common/controller"
	liberrors "xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type ClusterInfo struct {
	ConnectionInfo
	Type       ClusterType
	cancel     context.CancelFunc
	Raw        []byte
	RestConfig *rest.Config
	Client     client.Client
	Cluster    ctrlcluster.Cluster
	Dynamic    *dynamic.DynamicClient
	Kubernetes kubernetes.Interface
}

type ConnectionInfo struct {
	URL       url.URL
	Transport http.RoundTripper
}

type ClusterInfoContext interface {
	Get(ctx context.Context, key string) (*ClusterInfo, error)
	Sync(ctx context.Context, key string, cluster *Cluster) error
}

type ClusterInfoReconciler struct {
	Client store.Store
	Holder CloudInfoHolder
}

func NewClusterInfoController(ctx context.Context, storage store.Store, holder CloudInfoHolder) (*controller.Controller, error) {
	rec := &ClusterInfoReconciler{
		Client: storage,
		Holder: holder,
	}
	c := controller.
		NewController("cluster-info", rec).
		Watch(controller.NewStoreSource(storage, &Cluster{}))
	return c, nil
}

// InitSync sync all clusters info before other controllers start
// so that other controllers can get the cluster info from the cache
func InitCloudInfoHolder(ctx context.Context, storage store.Store, holder CloudInfoHolder) error {
	allclusters := &store.List[Cluster]{}
	if err := storage.List(ctx, allclusters, store.WithSubScopes()); err != nil {
		return err
	}
	for _, cluster := range allclusters.Items {
		if _, err := holder.Sync(ctx, &cluster); err != nil {
			log.FromContext(ctx).Error(err, "failed to sync cluster info", "cluster", cluster.Name)
		}
	}
	return nil
}

func (c *ClusterInfoReconciler) Reconcile(ctx context.Context, key controller.ScopedKey) (controller.Result, error) {
	return base.UnwrapReQueueError(c.reconcile(ctx, key))
}

func (c *ClusterInfoReconciler) reconcile(ctx context.Context, key controller.ScopedKey) error {
	cluster := &Cluster{}
	if err := c.Client.Scope(key.Scopes()...).Get(ctx, key.Name, cluster); err != nil {
		if liberrors.IsNotFound(err) {
			if err := c.remove(ctx, cluster); err != nil {
				return nil
			}
			return nil
		}
		return err
	}
	if cluster.DeletionTimestamp != nil {
		if err := c.remove(ctx, cluster); err != nil {
			return nil
		}
		return nil
	}
	if err := c.sync(ctx, cluster); err != nil {
		// cluster info reconcile just refresh the cluster info context when cluster config changed
		// it does not change the cluster status, so we not retry on error
		log.FromContext(ctx).Error(err, "failed to get cluster info")
	}
	return nil
}

func (c *ClusterInfoReconciler) remove(ctx context.Context, cluster *Cluster) error {
	return c.Holder.Remove(ctx, store.ObjectReferenceFrom(cluster))
}

func (c *ClusterInfoReconciler) sync(ctx context.Context, cluster *Cluster) error {
	_, err := c.Holder.Sync(ctx, cluster)
	return err
}
