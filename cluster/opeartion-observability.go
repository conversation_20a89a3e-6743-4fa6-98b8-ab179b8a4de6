package cluster

import (
	"net/http"
)

func (s ContainerOperation) JaegerProxy(w http.ResponseWriter, r *http.Request, path string) (any, error) {
	return s.ServiceProxy(w, r, "observability", "jaeger-query", 16686, path)
}

func (s ContainerOperation) OpenTelementryProxy(w http.ResponseWriter, r *http.Request, path string) (any, error) {
	return s.ServiceProxy(w, r, "observabilityg", "kube-prometheus-stack-alertmanager", 9093, path)
}
