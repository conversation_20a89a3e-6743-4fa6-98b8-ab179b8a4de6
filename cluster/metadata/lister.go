package metadata

import (
	"context"
	"net/http"
	"slices"

	networkingv1 "k8s.io/api/networking/v1"
	storagev1 "k8s.io/api/storage/v1"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/ismc/common"
	cloud "xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/rest"
	"xiaoshiai.cn/core/resourcequota"
)

// MetadtaLister
// list metadata for cluster according to permission of organization or tenant
type MetadtaLister struct {
	CloudInfo cluster.CloudInfoGetter
	Store     store.Store
}

func (m MetadtaLister) ListSystemClusters(r *http.Request, tenant, org string) ([]cluster.ResourceMetadata, error) {
	metadatas, err := cluster.ListClusterMetadata(r, m.Store, false)
	if err != nil {
		return nil, err
	}
	// check by tenant and organization
	if tenant != "" {
		// make sure the cluster is used by the tenant
		refclusters, err := resourcequota.ListClustersUsedByTenant(r.Context(), m.Store, tenant)
		if err != nil {
			return nil, err
		}
		metadatas = slices.DeleteFunc(metadatas, func(i cluster.ResourceMetadata) bool {
			return !slices.ContainsFunc(refclusters, func(ref store.ObjectReference) bool {
				return ref.Equals(store.ObjectReference{Name: i.Name, Scopes: i.Scopes})
			})
		})
		// make sure the cluster is used by the tenant organization
		if org != "" {
			refclusters, err := resourcequota.ListClustersUsedByTenantOrganization(r.Context(), m.Store, tenant, org)
			if err != nil {
				return nil, err
			}
			metadatas = slices.DeleteFunc(metadatas, func(i cluster.ResourceMetadata) bool {
				return !slices.ContainsFunc(refclusters, func(ref store.ObjectReference) bool {
					return ref.Equals(store.ObjectReference{Name: i.Name, Scopes: i.Scopes})
				})
			})
		}
	}
	return metadatas, nil
}

func (m MetadtaLister) ListTenantClusters(r *http.Request, tenant, org string) ([]cluster.ResourceMetadata, error) {
	list, err := cluster.ListClusterMetadata(r, m.Store.Scope(base.ScopeTenant(tenant)), false)
	if err != nil {
		return nil, err
	}
	// check by tenant and organization
	if tenant != "" && org != "" {
		// make sure the cluster is used by the tenant organization
		refclusters, err := resourcequota.ListClustersUsedByTenantOrganization(r.Context(), m.Store, tenant, org)
		if err != nil {
			return nil, err
		}
		// only show the cluster used by the tenant organization
		list = slices.DeleteFunc(list, func(i cluster.ResourceMetadata) bool {
			return !slices.ContainsFunc(refclusters, func(ref store.ObjectReference) bool {
				return ref.Equals(store.ObjectReference{Name: i.Name, Scopes: i.Scopes})
			})
		})
	}
	return list, nil
}

func (m MetadtaLister) ListIngressClass(ctx context.Context, tenant, org string, clusterref store.ObjectReference) ([]cluster.ResourceMetadata, error) {
	cli, err := cluster.GetClusterKubeClient(ctx, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	ingressclasslist := &networkingv1.IngressClassList{}
	if err := cli.List(ctx, ingressclasslist); err != nil {
		return nil, err
	}
	useable := []cluster.ResourceMetadata{}
	for _, ingressclass := range ingressclasslist.Items {
		item := cluster.ResourceMetadataFrom(&ingressclass)
		// check permission
		ok, err := permitIngressClass(ctx, m.Store, clusterref, item, tenant, org)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		useable = append(useable, item)
	}
	return useable, nil
}

func (m MetadtaLister) ListStorageClass(ctx context.Context, tenant, org string, clusterref store.ObjectReference) ([]cluster.ResourceMetadata, error) {
	cli, err := cluster.GetClusterKubeClient(ctx, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	storageclasslist := &storagev1.StorageClassList{}
	if err := cli.List(ctx, storageclasslist); err != nil {
		return nil, err
	}
	useable := []cluster.ResourceMetadata{}
	for _, storageclass := range storageclasslist.Items {
		item := cluster.ResourceMetadataFrom(&storageclass)
		// check permission
		ok, err := permitStorageClass(ctx, clusterref, item, tenant, org)
		if err != nil {
			return nil, err
		}
		if !ok {
			continue
		}
		useable = append(useable, item)
	}
	return useable, nil
}

func (m MetadtaLister) ListDiskClass(r *http.Request, tenant, org string, clusterref store.ObjectReference) ([]cloud.DiskClass, error) {
	cli, err := cluster.GetClusterCloudProvider(r.Context(), m.Store, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	list, err := rest.ListDiskClass(r, cli)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}

func (m MetadtaLister) ListNetworks(r *http.Request, tenant, org string, clusterref store.ObjectReference) ([]cloud.VirtualNetwork, error) {
	ctx := r.Context()
	cli, err := cluster.GetClusterCloudProvider(ctx, m.Store, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	list, err := rest.ListVirtualNetworks(r, cli)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}

func (m MetadtaLister) ListSubNetworks(r *http.Request, tenant, org string, clusterref store.ObjectReference, network string) ([]cloud.VirtualSubnetwork, error) {
	ctx := r.Context()
	cli, err := cluster.GetClusterCloudProvider(ctx, m.Store, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	list, err := rest.ListVirtualSubnetworks(r, cli, network)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}

func (m MetadtaLister) ListInstanceTypes(r *http.Request, tenant, org string, clusterref store.ObjectReference) ([]cloud.InstanceType, error) {
	ctx := r.Context()
	cli, err := cluster.GetClusterCloudProvider(ctx, m.Store, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	list, err := rest.ListInstanceTypes(r, cli)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}

func (m MetadtaLister) ListZones(r *http.Request, tenant, org string, clusterref store.ObjectReference) ([]cloud.Zone, error) {
	ctx := r.Context()
	cli, err := cluster.GetClusterCloudProvider(ctx, m.Store, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	list, err := rest.ListZones(r, cli)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}

func (m MetadtaLister) ListImages(r *http.Request, tenant, org string, clusterref store.ObjectReference) ([]cloud.Image, error) {
	ctx := r.Context()
	cli, err := cluster.GetClusterCloudProvider(ctx, m.Store, m.CloudInfo, clusterref)
	if err != nil {
		return nil, err
	}
	list, err := rest.ListImages(r, cli, common.ImagePhaseReady)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}
