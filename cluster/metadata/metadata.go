package metadata

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	cloud "xiaoshiai.cn/core/ismc/common"
)

func NewAPI(base base.API, cloudinfo cluster.CloudInfoGetter) *API {
	return &API{
		API:       base,
		CloudInfo: cloudinfo,
	}
}

type API struct {
	base.API
	CloudInfo cluster.CloudInfoGetter
}

type ClusterMetaOnly struct {
	Name          string                 `json:"name,omitempty"`
	Scopes        []store.Scope          `json:"scopes,omitempty"`
	Annotations   map[string]string      `json:"annotations,omitempty"`
	Labels        map[string]string      `json:"labels,omitempty"`
	Description   string                 `json:"description,omitempty"`
	Type          cluster.ClusterType    `json:"type,omitempty"`
	ResourcePools []ResourcePoolMetadata `json:"resourcePools,omitempty"`
}

type ResourcePoolMetadata struct {
	Name        string `json:"name,omitempty"`
	Description string `json:"description,omitempty"`
}

func (a *API) Group() api.Group {
	return api.NewGroup("").
		Tag("Clusters").
		SubGroup(
			a.tenantOrOrganizationClustersMetadataGroup(),
		)
}

func (a *API) tenantOrOrganizationClustersMetadataGroup() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			base.NewTenantGroup("").
				SubGroup(
					a.systemclustersGroup(),
					a.localClustersGroup(),
				),
			base.NewTenantOrganizationGroup("").
				SubGroup(
					a.systemclustersGroup(),
					a.localClustersGroup(),
					a.clustersMetadataGroup(),
				),
		)
}

func (a *API) clustersMetadataGroup() api.Group {
	return api.
		NewGroup("/clustermetadata").
		Route(
			api.GET("").
				To(a.ListOrganizationClusterMetadata).
				Operation("list available tenant cluster metadata").
				Param(api.QueryParam("marketapplication-type", "filter clusters by type, Container Virtualmachine").Optional()).
				Response([]ClusterMetaOnly{}),
		)
}

func (a *API) ListOrganizationClusterMetadata(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		clusterlist := &store.List[cluster.Cluster]{}
		platforms := []string{}
		if cate := api.Query(r, "marketapplication-type", ""); cate != "" {
			switch cate {
			case string("VirtualMachine"):
				list := []string{}
				for _, t := range cluster.VirtualMachineClusterTypes {
					list = append(list, string(t))
				}
				platforms = append(platforms, list...)
			case string("Container"):
				list := []string{}
				for _, t := range cluster.ContainerClusterTypes {
					list = append(list, string(t))
				}
				platforms = append(platforms, list...)
			}
		}
		options := []store.ListOption{
			store.WithSubScopes(),
		}
		if len(platforms) != 0 {
			req, err := labels.NewRequirement(cluster.LabelClusterType, selection.In, platforms)
			if err != nil {
				return nil, err
			}
			options = append(options, store.WithLabelRequirementsFromSelector(labels.NewSelector().Add(*req)))
		}
		fields := store.Requirements{
			store.RequirementEqual("published", true),
		}
		if len(fields) != 0 {
			options = append(options, store.WithFieldRequirements(fields...))
		}
		if err := a.Store.List(ctx, clusterlist, options...); err != nil {
			return nil, err
		}
		ret := make([]ClusterMetaOnly, 0, len(clusterlist.Items))
		for _, c := range clusterlist.Items {
			// tenant cluster but not current tenant
			if len(c.Scopes) != 0 && c.Scopes[len(c.Scopes)-1].Name != tenant {
				continue
			}
			ret = append(ret, ClusterMetaOnly{
				Name:        c.Name,
				Scopes:      c.Scopes,
				Type:        c.Type,
				Description: c.Description,
				Annotations: c.Annotations,
				Labels:      c.Labels,
			})
		}
		return ret, nil
	})
}

func (a *API) ListSystemClusterMetadata(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		return MetadtaLister{CloudInfo: a.CloudInfo, Store: a.Store}.ListSystemClusters(r, tenant, org)
	})
}

func (a *API) ListSystemClusterIngressClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListIngressClass(ctx, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterStorageClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListStorageClass(ctx, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterDiskClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListDiskClass(r, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterNetworks(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListNetworks(r, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterSubNetworks(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		network := api.Path(r, "network", "")
		return lister.ListSubNetworks(r, tenant, org, store.ObjectReference{Name: clustername}, network)
	})
}

func (a *API) ListSystemClusterInstanceTypes(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListInstanceTypes(r, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterZones(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListZones(r, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) ListSystemClusterImages(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListImages(r, tenant, org, store.ObjectReference{Name: clustername})
	})
}

func (a *API) systemclustersGroup() api.Group {
	return api.
		NewGroup("/system-clustermetadata").
		Route(
			api.GET("").
				To(a.ListSystemClusterMetadata).
				Operation("list available cluster metadata").
				Param(api.QueryParam("marketapplication-type", "filter clusters by type, Container Virtualmachine").Optional()).
				Response([]ClusterMetaOnly{}),

			// container cluster resources
			api.GET("/{cluster}/ingressclasses").
				To(a.ListSystemClusterIngressClass).
				Operation("list available ingress class").
				Response([]cluster.ResourceMetadata{}),

			api.GET("/{cluster}/storageclasses").
				To(a.ListSystemClusterStorageClass).
				Operation("list available storage class").
				Response([]cluster.ResourceMetadata{}),

			// virtualmachine cluster resources
			api.GET("/{cluster}/diskclasses").
				To(a.ListSystemClusterDiskClass).
				Param(api.QueryParam("zone", "filter disk class by zone").Optional()).
				Operation("list available disk class(virtualmachine").
				Response([]cloud.DiskClass{}),

			api.GET("/{cluster}/networks").
				To(a.ListSystemClusterNetworks).
				Operation("list available networks").
				Response([]cloud.VirtualNetwork{}),

			api.GET("/{cluster}/networks/{network}/subnetworks").
				To(a.ListSystemClusterSubNetworks).
				Operation("list available sub networks").
				Response([]cloud.VirtualSubnetwork{}),

			api.GET("/{cluster}/instancetypes").
				To(a.ListSystemClusterInstanceTypes).
				Operation("list available instance types").
				Response([]cloud.InstanceType{}),

			api.GET("/{cluster}/zones").
				To(a.ListSystemClusterZones).
				Operation("list available zones").
				Response([]cloud.Zone{}),

			api.GET("/{cluster}/images").
				To(a.ListSystemClusterImages).
				Operation("list available images").
				Response([]cloud.Image{}),
		)
}

func (a *API) ListLocalClusterMetadata(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrTenantOrganization(w, r, func(ctx context.Context, _ store.Store, tenant, org string) (any, error) {
		return MetadtaLister{CloudInfo: a.CloudInfo, Store: a.Store}.ListTenantClusters(r, tenant, org)
	})
}

func (a *API) ListLocalClusterIngressClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListIngressClass(ctx, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) ListLocalClusterStorageClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListStorageClass(ctx, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) ListLocalClusterDiskClass(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListDiskClass(r, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) ListLocalClusterNetworks(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListNetworks(r, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) ListLocalClusterSubNetworks(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		network := api.Path(r, "network", "")
		return lister.ListSubNetworks(r, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		}, network)
	})
}

func (a *API) ListLocalClusterInstanceTypes(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListInstanceTypes(r, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) ListLocalClusterZones(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListZones(r, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) ListLocalClusterImages(w http.ResponseWriter, r *http.Request) {
	a.onMetadataLister(w, r, func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error) {
		return lister.ListImages(r, tenant, org, store.ObjectReference{
			Name:   clustername,
			Scopes: []store.Scope{base.ScopeTenant(tenant)},
		})
	})
}

func (a *API) onMetadataLister(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, lister MetadtaLister, tenant, org, clustername string) (any, error)) {
	a.onTenantOrganizationCluster(w, r, func(ctx context.Context, tenant, org, clustername string) (any, error) {
		lister := MetadtaLister{
			CloudInfo: a.CloudInfo, Store: a.Store,
		}
		return fn(ctx, lister, tenant, org, clustername)
	})
}

func (a *API) onTenantOrganizationCluster(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, tenant, org, clustername string) (any, error)) {
	a.OnTenantOrTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		clustername := api.Path(r, "cluster", "")
		if clustername == "" {
			return nil, errors.NewBadRequest("cluster name is required")
		}
		return fn(ctx, tenant, org, clustername)
	})
}

func (a *API) localClustersGroup() api.Group {
	return api.
		NewGroup("/local-clustermetadata").
		Route(
			api.GET("").
				To(a.ListLocalClusterMetadata).
				Operation("list available tenant cluster metadata").
				Param(api.QueryParam("marketapplication-type", "filter clusters by type, Container Virtualmachine").Optional()).
				Response([]ClusterMetaOnly{}),

			api.GET("/{cluster}/ingressclasses").
				To(a.ListLocalClusterIngressClass).
				Operation("list available tenant ingress class").
				Response([]cluster.ResourceMetadata{}),

			api.GET("/{cluster}/storageclasses").
				To(a.ListLocalClusterStorageClass).
				Operation("list available storage class").
				Response([]cluster.ResourceMetadata{}),

			// virtualmachine cluster resources
			api.GET("/{cluster}/diskclasses").
				To(a.ListLocalClusterDiskClass).
				Param(api.QueryParam("zone", "filter disk class by zone").Optional()).
				Operation("list available disk class(virtualmachine").
				Response([]cloud.DiskClass{}),

			api.GET("/{cluster}/networks").
				To(a.ListLocalClusterNetworks).
				Operation("list available networks").
				Response([]cloud.VirtualNetwork{}),

			api.GET("/{cluster}/networks/{network}/subnetworks").
				To(a.ListLocalClusterSubNetworks).
				Operation("list available sub networks").
				Response([]cloud.VirtualSubnetwork{}),

			api.GET("/{cluster}/instancetypes").
				To(a.ListLocalClusterInstanceTypes).
				Operation("list available instance types").
				Response([]cloud.InstanceType{}),

			api.GET("/{cluster}/zones").
				To(a.ListLocalClusterZones).
				Operation("list available zones").
				Response([]cloud.Zone{}),

			api.GET("/{cluster}/images").
				To(a.ListLocalClusterImages).
				Operation("list available images").
				Response([]cloud.Image{}),
		)
}
