package metadata

import (
	"context"

	networkingv1 "k8s.io/api/networking/v1"
	storagev1 "k8s.io/api/storage/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/resourcequota"
)

func PermitCluster(ctx context.Context, store store.Store, clusterref store.ObjectReference, tenant, org string) (bool, error) {
	// check by tenant and organization
	if tenant != "" {
		quota, err := resourcequota.GetResourceQuota(ctx, store, tenant, org, clusterref)
		if err != nil {
			if errors.IsNotFound(err) {
				return false, nil
			}
			return false, err
		}
		_ = quota
	}
	return true, nil
}

func PermitIngressClass(ctx context.Context, store store.Store, cli client.Client, clusterref store.ObjectReference, ingressClassName string, tenant, org string) (bool, error) {
	ingressclass := &networkingv1.IngressClass{}
	if err := cli.Get(ctx, client.ObjectKey{Name: ingressClassName}, ingressclass); err != nil {
		return false, err
	}
	metadata := cluster.ResourceMetadataFrom(ingressclass)
	return permitIngressClass(ctx, store, clusterref, metadata, tenant, org)
}

func permitIngressClass(ctx context.Context, storage store.Store, clusterref store.ObjectReference, metadata cluster.ResourceMetadata, tenant, org string) (bool, error) {
	// system cluster
	if len(clusterref.Scopes) == 0 {
		// public
		if metadata.Public {
			return true, nil
		}
		// this tenant's gateway
		if metadata.Labels != nil && metadata.Labels[base.LabelTenant] == tenant {
			// only show shared with the organization
			if org != "" {
				// check if the application is shared with the organization
				appname := metadata.Labels[base.LabelApplication]
				if appname == "" {
					return false, nil
				}
				// gateway application
				app := &store.Unstructured{}
				app.SetResource("applications")
				if err := storage.Scope(base.ScopeTenant(tenant)).Get(ctx, appname, app); err != nil {
					return false, nil
				}
				if !base.IsSharedToOrganization(app, org) {
					return false, nil
				}
			}
			return true, nil
		}
		return false, nil
	} else {
		// not public
		if metadata.Public {
			return true, nil
		}
		// not this tenant's cluster
		if tenant != "" {
			if clustertenant, _ := base.TenantOrganizationFromScopes(clusterref.Scopes...); clustertenant != tenant {
				return false, nil
			}
		}
		// shared with the organization
		// only show shared with the organization
		if org != "" {
			// check if the application is shared with the organization
			appname := metadata.Labels[base.LabelApplication]
			if appname == "" {
				return false, nil
			}
			// gateway application
			app := &store.Unstructured{}
			app.SetResource("applications")
			if err := storage.Scope(base.ScopeTenant(tenant)).Get(ctx, appname, app); err != nil {
				return false, nil
			}
			if !base.IsSharedToOrganization(app, org) {
				return false, nil
			}
		}
		return true, nil
	}
}

func PermitStorageClass(ctx context.Context, store store.Store, cli client.Client, clusterref store.ObjectReference, storageClassName string, tenant, org string) (bool, error) {
	storageclass := &storagev1.StorageClass{}
	if err := cli.Get(ctx, client.ObjectKey{Name: storageClassName}, storageclass); err != nil {
		return false, err
	}
	metadata := cluster.ResourceMetadataFrom(storageclass)
	return permitStorageClass(ctx, clusterref, metadata, tenant, org)
}

func permitStorageClass(_ context.Context, clusterref store.ObjectReference, metadata cluster.ResourceMetadata, tenant, _ string) (bool, error) {
	if !metadata.Public {
		return false, nil
	}
	// if is system cluster
	// allow tenant self storage class
	if len(clusterref.Scopes) == 0 {
		// public storage class
		if metadata.Public {
			return true, nil
		}
		// or tenant storage class
		if metadata.Labels != nil && metadata.Labels[base.LabelTenant] == tenant {
			return true, nil
		}
		return false, nil
	} else {
		// not public
		if !metadata.Public {
			return false, nil
		}
		// not this tenant's cluster
		if tenant != "" {
			// not this tenant's cluster
			if clustertenant, _ := base.TenantOrganizationFromScopes(clusterref.Scopes...); clustertenant != tenant {
				return false, nil
			}
		}
		return true, nil
	}
}
