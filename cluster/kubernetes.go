package cluster

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
	ctrlcluster "sigs.k8s.io/controller-runtime/pkg/cluster"
	"xiaoshiai.cn/common/httpclient"
	apiserverscheme "xiaoshiai.cn/core/scheme"
)

// UseCachedClient is a flag to enable the cached client
// currently, the cached client is not stable, so we disable it by default
const UseCachedClient = false

type KubernetesClients struct {
	RestConfig *rest.Config
	cancel     context.CancelFunc
	Client     client.Client
	Cached     client.Client
	Kubernetes kubernetes.Interface
	Dynamic    dynamic.Interface
	// baseURL is the url to kubernetes api
	baseURL   url.URL
	Transport http.RoundTripper
	// isEdge is the special cluster is connected via edge
	isEdge bool

	KubeCluster KubeCluster
}

func (c *KubernetesClients) Close() {
	if c.cancel != nil {
		c.cancel()
	}
}

func (c *KubernetesClients) GetServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig {
	target := c.baseURL
	if c.isEdge {
		// override the default path
		target.Path = fmt.Sprintf("/proxy/%s.%s:%d", name, namespace, port)
	} else {
		target.Path += fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/", namespace, name, port)
	}
	return &httpclient.ClientConfig{Server: &target, RoundTripper: c.Transport, DialContext: c.RestConfig.Dial}
}

func (c *KubernetesClients) GetWebsocketServiceProxyAddr(namespace, name string, port int) *httpclient.ClientConfig {
	if kube := c.KubeCluster; kube.Config != "" {
		target := c.baseURL
		target.Path = fmt.Sprintf("/api/v1/namespaces/%s/services/%s:%d/proxy/proxy/%s.%s:%d",
			def(kube.Namespace, "ismc"), def(kube.Service, "ismc-agent"), def(kube.Port, 80),
			name, namespace, port)
		return &httpclient.ClientConfig{Server:& target, RoundTripper: c.Transport, DialContext: c.RestConfig.Dial}
	}
	return c.GetServiceProxyAddr(namespace, name, port)
}

func (c *KubernetesClients) GetKubernetesAddr() *httpclient.ClientConfig {
	return &httpclient.ClientConfig{
		Server:      & c.baseURL,
		RoundTripper: c.Transport,
		DialContext:  c.RestConfig.Dial,
	}
}

func NewKubernetesClients(ctx context.Context, restconfig *rest.Config, isedge bool) (*KubernetesClients, error) {
	restconfig = rest.CopyConfig(restconfig)
	// set default QPS
	restconfig.QPS = 1024
	restconfig.Burst = 4096

	tranport := restconfig.Transport
	if tranport == nil {
		newtranport, err := rest.TransportFor(restconfig)
		if err != nil {
			return nil, err
		}
		tranport = newtranport
	}
	httpcli := &http.Client{
		Transport: tranport,
		Timeout:   restconfig.Timeout,
	}
	baseURL, _, err := rest.DefaultServerUrlFor(restconfig)
	if err != nil {
		return nil, err
	}
	dynamiccli, err := dynamic.NewForConfigAndClient(restconfig, httpcli)
	if err != nil {
		return nil, err
	}
	kubernetescs, err := kubernetes.NewForConfigAndClient(restconfig, httpcli)
	if err != nil {
		return nil, err
	}
	ctrlc, err := ctrlcluster.New(restconfig, func(o *ctrlcluster.Options) {
		o.Scheme = apiserverscheme.Scheme
		o.HTTPClient = httpcli
		o.Client.Cache = &client.CacheOptions{Unstructured: true}
	})
	if err != nil {
		return nil, err
	}

	ctrlclient, err := client.New(restconfig, client.Options{
		HTTPClient: httpcli, Scheme: apiserverscheme.Scheme,
	})
	if err != nil {
		return nil, err
	}
	cli := VersionedClient{Client: ctrlclient}

	ctx, cancel := context.WithCancel(ctx)
	go ctrlc.Start(ctx)

	newclients := &KubernetesClients{
		cancel:     cancel,
		Client:     cli,
		Cached:     ctrlc.GetClient(),
		Kubernetes: kubernetescs,
		Dynamic:    dynamiccli,
		RestConfig: restconfig,
		baseURL:    *baseURL,
		isEdge:     isedge,
		Transport:  tranport,
	}
	return newclients, nil
}

var _ client.Client = &VersionedClient{}

// VersionedClient is a client.Client that sets the GroupVersionKind on objects it reads.
// When use an no cache client, th returned object will not have the GroupVersionKind set.
// it cause by the client.Client use [serializer.WithoutConversionCodecFactory] as default codec factory.
type VersionedClient struct {
	client.Client
}

// Get implements client.Client.
func (v VersionedClient) Get(ctx context.Context, key types.NamespacedName, obj client.Object, opts ...client.GetOption) error {
	defer v.setGroupVersionKind(obj)
	return v.Client.Get(ctx, key, obj, opts...)
}

// List implements client.Client.
func (v VersionedClient) List(ctx context.Context, list client.ObjectList, opts ...client.ListOption) error {
	defer v.setListGroupVersionKind(list)
	return v.Client.List(ctx, list, opts...)
}

func (v VersionedClient) setGroupVersionKind(obj client.Object) {
	gvk, err := apiutil.GVKForObject(obj, v.Client.Scheme())
	if err != nil || gvk.Kind == "" {
		return
	}
	obj.GetObjectKind().SetGroupVersionKind(gvk)
}

func (v VersionedClient) setListGroupVersionKind(list client.ObjectList) {
	listgvk, err := apiutil.GVKForObject(list, v.Client.Scheme())
	if err != nil || listgvk.Kind == "" {
		return
	}
	list.GetObjectKind().SetGroupVersionKind(listgvk)

	gvk := listgvk
	gvk.Kind = strings.TrimSuffix(gvk.Kind, "List")
	meta.EachListItem(list, func(obj runtime.Object) error {
		obj.GetObjectKind().SetGroupVersionKind(gvk)
		return nil
	})
}
