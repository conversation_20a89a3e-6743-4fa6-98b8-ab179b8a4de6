package resourcepool

import (
	"reflect"
	"testing"

	"xiaoshiai.cn/common/store"
)

func TestListResourcePoolsFromLabels(t *testing.T) {
	tests := []struct {
		labels map[string]string
		name   string
		want   []store.ObjectReference
	}{
		{
			labels: map[string]string{
				"cluster.xiaoshiai.cn/resourcepool-gcgc-testk8s-pool": "true",
			},
			name: "TestListResourcePoolsFromLabels",
			want: []store.ObjectReference{
				{
					Name: "testk8s-pool",
					Scopes: []store.Scope{
						{
							Name:     "gcgc",
							Resource: "tenants",
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ListResourcePoolsFromLabels(tt.labels); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rrorf("ListResourcePoolsFromLabels() = %v, want %v", got, tt.want)
			}
		})
	}
}
