package privatenode

import (
	"context"
	"fmt"
	"net/http"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability/dashboard"
	"xiaoshiai.cn/core/pay/order"
	"xiaoshiai.cn/core/pay/product"
)

type API struct {
	base.API
	Clusters     cluster.CloudInfoGetter
	SKUs         product.SKUService
	Status       *PrivateNodeClaimStatusAPI
	OrderService order.OrderService
}

func NewAPI(base base.API,
	clusterinfos cluster.CloudInfoGetter,
	sku product.SKUService,
	ordersvc order.OrderService,
	dashboards map[string]dashboard.DashboradConfiguration,
) *API {
	return &API{
		API:          base,
		Clusters:     clusterinfos,
		SKUs:         sku,
		OrderService: ordersvc,
		Status:       NewPrivateNodeClaimStatusAPI(base, clusterinfos, dashboards),
	}
}

type SetPrivateNodeRequest struct {
	// Name is the name of the node in kubernetes
	// Name is the zone name in cloud
	Name string `json:"name" validate:"required"`

	Model string `json:"model" validate:"required"`

	// Region is the region of the node
	// it is required for virtual machine
	// +optional
	Region string `json:"region"`
}

func (a *API) CreatePrivateNode(w http.ResponseWriter, r *http.Request) {
	a.OnCluster(w, r, func(ctx context.Context, storage store.Store, clustername string) (any, error) {
		req := &SetPrivateNodeRequest{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		return a.createPrivateNode(ctx, clustername, req.Name, req.Model)
	})
}

func (a *API) createPrivateNode(ctx context.Context, clustername, nodename, model string) (*PrivateNode, error) {
	// check node exists in cluster
	info, err := a.Clusters.Get(ctx, store.ObjectReference{Name: clustername})
	if err != nil {
		return nil, err
	}
	switch cluster.ClusterCategoryFrom(info.Type()) {
	case cluster.ClusterCategoryContainer:
		kubeclients, err := info.KubernetesConfig()
		if err != nil {
			return nil, err
		}
		// check node exists in k8s cluster
		node := &corev1.Node{}
		if err := kubeclients.Client.Get(ctx, client.ObjectKey{Name: nodename}, node); err != nil {
			return nil, err
		}
		// check node ready condition
		if !isNodeReady(node) {
			return nil, fmt.Errorf("node %s is not ready", nodename)
		}
	case cluster.ClusterCategoryVirtualMachine:
		return nil, fmt.Errorf("not support virtual machine cluster")
	default:
		return nil, fmt.Errorf("unsupported cluster type %s", info.Type())
	}
	pnode := &PrivateNode{
		ObjectMeta: store.ObjectMeta{Name: nodename},
		Model:      model,
	}
	if err := a.Store.Scope(base.ScopeCluster(clustername)).Create(ctx, pnode); err != nil {
		return nil, err
	}
	// sku ++
	if err := a.SKUs.DeltaStock(ctx, product.CategoryPrivateNode, clustername, model, 1); err != nil {
		return nil, err
	}
	return pnode, nil
}

func isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}

type PrivateNodeWithAdditional struct {
	PrivateNode `json:",inline"`
	Expire      *time.Time `json:"expire,omitempty"`
}

func (a *API) ListPrivateNode(w http.ResponseWriter, r *http.Request) {
	a.OnCluster(w, r, func(ctx context.Context, storage store.Store, cluster string) (any, error) {
		options := []store.ListOption{}
		if matchsku := api.Query(r, "sku", ""); matchsku != "" {
			options = append(options, store.WithFieldRequirements(store.RequirementEqual("model", matchsku)))
		}
		list, err := base.GenericList(r, storage, &store.List[PrivateNode]{}, options...)
		if err != nil {
			return nil, err
		}
		retlist := make([]PrivateNodeWithAdditional, 0, len(list.Items))
		for _, item := range list.Items {
			// check sku is used by other objects
			retitem := PrivateNodeWithAdditional{
				PrivateNode: item,
			}
			for _, usedby := range item.Status.UsedBy {
				switch usedby.Resource {
				case "privatenodeclaims":
					claim := &PrivateNodeClaim{}
					_ = a.Store.Scope(usedby.Scopes...).Get(ctx, usedby.Name, claim)
					if !claim.Expire.Time.IsZero() {
						retitem.Expire = &claim.Expire.Time
					}
				}
			}
			retlist = append(retlist, retitem)
		}
		return &store.List[PrivateNodeWithAdditional]{
			Resource: list.Resource,
			Total:    list.Total,
			Page:     list.Page,
			Size:     list.Size,
			Items:    retlist,
		}, nil
	})
}

func (a *API) GetPrivateNode(w http.ResponseWriter, r *http.Request) {
	a.onClusterNode(w, r, func(ctx context.Context, storage store.Store, cluster store.ObjectReference, nodename string) (any, error) {
		return base.GenericGet(r, storage, &PrivateNode{}, nodename)
	})
}

func (a *API) UpdatePrivateNode(w http.ResponseWriter, r *http.Request) {
	a.onClusterNode(w, r, func(ctx context.Context, storage store.Store, cluster store.ObjectReference, nodename string) (any, error) {
		req := &PrivateNode{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}

		if objname := req.GetName(); objname != "" && objname != nodename {
			return nil, fmt.Errorf("name in body %s is not equal to name in path %s", objname, nodename)
		}
		exists := &PrivateNode{}
		if err := storage.Get(ctx, nodename, exists); err != nil {
			return nil, err
		}
		// avoid change deleting node
		if exists.DeletionTimestamp != nil {
			return exists, errors.NewBadRequest("node is deleting")
		}
		if exists.Model != req.Model {
			// sku --
			if err := a.SKUs.DeltaStock(ctx, product.CategoryPrivateNode, cluster.Name, exists.Model, -1); err != nil {
				return nil, err
			}
			// sku ++
			if err := a.SKUs.DeltaStock(ctx, product.CategoryPrivateNode, cluster.Name, req.Model, 1); err != nil {
				return nil, err
			}
		}
		req.SetName(nodename)
		req.SetResourceVersion(0)
		if err := storage.Update(r.Context(), req); err != nil {
			return nil, err
		}
		return req, nil
	})
}

func (a *API) DeletePrivateNode(w http.ResponseWriter, r *http.Request) {
	a.onClusterNode(w, r, func(ctx context.Context, storage store.Store, cluster store.ObjectReference, nodename string) (any, error) {
		node := &PrivateNode{}
		if err := storage.Get(ctx, nodename, node); err != nil {
			return nil, err
		}
		// avoid delete node twice, it not idempotent
		if node.DeletionTimestamp != nil {
			return node, nil
		}
		// check node is used by other objects
		if len(node.Status.UsedBy) > 0 {
			return nil, errors.NewBadRequest(fmt.Sprintf("node is used by: %v", node.Status.UsedBy))
		}
		// sku --
		if err := a.SKUs.DeltaStock(ctx, product.CategoryPrivateNode, cluster.Name, node.Model, -1); err != nil {
			// allow delete node if sku not found
			if !errors.IsNotFound(err) {
				return nil, err
			}
		}
		return node, storage.Delete(ctx, node)
	})
}

func (a *API) onClusterNode(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, cluster store.ObjectReference, nodename string) (any, error)) {
	a.OnCluster(w, r, func(ctx context.Context, storage store.Store, cluster string) (any, error) {
		nodename := api.Path(r, "node", "")
		if nodename == "" {
			return nil, errors.NewBadRequest("node name is required")
		}
		return fn(ctx, storage, store.ObjectReference{Name: cluster}, nodename)
	})
}

func (a *API) clusterGroup() api.Group {
	return base.
		NewClusterGroup("adminprivatenodes").
		Route(
			api.GET("").
				To(a.ListPrivateNode).
				Operation("list private nodes").
				Param(
					api.QueryParam("sku", "").Optional(),
				).
				Response(store.List[PrivateNode]{}),

			api.POST("").
				To(a.CreatePrivateNode).
				Operation("create private node").
				Param(
					api.BodyParam("private node", SetPrivateNodeRequest{}),
				).
				Response(PrivateNode{}),

			api.GET("/{node}").
				To(a.GetPrivateNode).
				Operation("get private node").
				Response(PrivateNode{}),

			api.PUT("/{node}").
				To(a.UpdatePrivateNode).
				Param(
					api.BodyParam("private node", PrivateNode{}),
				).
				Operation("update private node").
				Response(PrivateNode{}),

			api.DELETE("/{node}").
				To(a.DeletePrivateNode).
				Operation("delete private node"),
		)
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Private Nodes").
		SubGroup(
			a.clusterGroup(),
			a.tenantGroup(),
			a.skuGroup(),
		)
}
