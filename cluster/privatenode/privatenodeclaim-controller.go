package privatenode

import (
	"context"
	"fmt"
	"maps"
	"reflect"
	"slices"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/pay/product"
)

type PrivateNodeClaim struct {
	store.ObjectMeta `json:",inline"`
	Cluster          store.ObjectReference  `json:"cluster"`
	Model            string                 `json:"model"` // the model of the private node to create
	Expire           store.Time             `json:"expire,omitempty"`
	Status           PrivateNodeCliamStatus `json:"status"`
}

type PrivateNodeClaimPhase string

const (
	PrivateNodeClaimPhaseReady   PrivateNodeClaimPhase = "Ready"
	PrivateNodeClaimPhaseExpired PrivateNodeClaimPhase = "Expired"
	PrivateNodeClaimPhaseFailed  PrivateNodeClaimPhase = "Failed"
)

type PrivateNodeCliamStatus struct {
	Phase            PrivateNodeClaimPhase  `json:"phase"`
	Ready            bool                   `json:"ready"`
	ReleaseTimestamp *store.Time            `json:"releaseTimestamp,omitempty"`
	Message          string                 `json:"message"`
	Conditions       []controller.Condition `json:"conditions"`
	// NodeName is the name of the node in kubernetes
	NodeName string                `json:"nodename"`
	NodeInfo corev1.NodeSystemInfo `json:"nodeInfo,omitempty"`
	Capacity corev1.ResourceList   `json:"capacity,omitempty"`
}

// PrivateNodeCliamStatusNode is node under this cluster
type PrivateNodeCliamStatusNode struct {
	Name string `json:"name"`
}

func NewTenantPrivateNodeController(storage store.Store, recorder events.Recorder, cloudinfo cluster.CloudInfoHolder, sku product.SKUService) (*controller.Controller, error) {
	rec := &TenantPrivateNodeReconciler{
		Store:    storage,
		Holder:   cloudinfo,
		recorder: recorder,
		sku:      sku,
	}
	better := controller.NewBetterReconciler(
		rec, storage,
		controller.WithFinalizer("tenantprivatenode-controller"),
		controller.WithAutosetStatus(),
	)
	c := controller.
		NewController("privatenodeclaims", better).
		Watch(
			controller.NewStoreSource(storage, &PrivateNodeClaim{}),
			controller.NewCustomStoreSource(storage, "privatenodes", rec.OnPrivateNodeChange),
		)
	return c, nil
}

func (c *TenantPrivateNodeReconciler) OnPrivateNodeChange(ctx context.Context, kind store.WatchEventType, obj store.Object) ([]controller.ScopedKey, error) {
	clusterref := clusterRefFromNode(obj)
	if clusterref.Name == "" {
		return nil, nil
	}
	pnodename := obj.GetName()

	cliamslist := store.List[PrivateNodeClaim]{}
	if err := c.Store.List(ctx, &cliamslist,
		store.WithSubScopes(),
		store.WithFieldRequirements(
			store.RequirementEqual("cluster.name", clusterref.Name),
			store.RequirementEqual("status.nodename", pnodename),
		)); err != nil {
		return nil, err
	}
	var keys []controller.ScopedKey
	for _, claim := range cliamslist.Items {
		if claim.Cluster.Name == clusterref.Name && claim.Status.NodeName == pnodename {
			keys = append(keys, controller.ScopedKeyFromObject(&claim))
		}
	}
	return keys, nil
}

type TenantPrivateNodeReconciler struct {
	Store    store.Store
	Holder   cluster.CloudInfoHolder
	recorder events.Recorder
	sku      product.SKUService
}

func (c *TenantPrivateNodeReconciler) Sync(ctx context.Context, node *PrivateNodeClaim) (controller.Result, error) {
	expired, err := c.syncExpire(ctx, node)
	if err != nil {
		return controller.Result{}, err
	}
	if expired {
		node.Status.Ready = false
		node.Status.Phase = PrivateNodeClaimPhaseExpired
		node.Status.Message = fmt.Sprintf("expired at %s", node.Expire.Time)
		node.Status.ReleaseTimestamp = ptr.To(store.Time{Time: node.Expire.Add(DurationAfterExpire)})

		// mark expire if we can
		if err := c.markKubeNodeOnExpires(ctx, node, true); err != nil {
			log.FromContext(ctx).Error(err, "mark kube node expired")
		}
		return events.RecordRequeueBeforeRelease(ctx, c.Store, c.recorder, node, node.Expire.Time, node.Status.ReleaseTimestamp.Time)
	} else {
		err := c.sync(ctx, node)
		if err != nil {
			if requeue, ok := err.(base.ReQueueError); ok {
				return controller.Result{Requeue: true, RequeueAfter: requeue.After}, nil
			}
			node.Status.Ready = false
			node.Status.Phase = PrivateNodeClaimPhaseFailed
		}
		// when requeue on error, requeue wait must not longer than expire time
		// otherwise, we will miss the expire time
		left := node.Expire.Time.Sub(time.Now())
		// requeue as needed
		return events.RecordRequeueOnExpires(ctx, c.recorder, node, left), err
	}
}

const DurationAfterExpire = events.Week

func (c *TenantPrivateNodeReconciler) sync(ctx context.Context, node *PrivateNodeClaim) error {
	if err := c.markKubeNodeOnExpires(ctx, node, false); err != nil {
		return err
	}
	if err := c.syncBind(ctx, node); err != nil {
		return err
	}
	if err := c.syncKubernetesNodeLabels(ctx, node); err != nil {
		return err
	}
	if node.Status.Ready {
		node.Status.Phase = PrivateNodeClaimPhaseReady
	} else {
		node.Status.Phase = ""
	}
	return nil
}

func (c *TenantPrivateNodeReconciler) markKubeNodeOnExpires(ctx context.Context, node *PrivateNodeClaim, expired bool) error {
	kubenodename := node.Status.NodeName
	if kubenodename == "" {
		return nil
	}
	info, err := c.Holder.Get(ctx, node.Cluster)
	if err != nil {
		return err
	}
	kubeclients, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	cli := kubeclients.Client

	k8snode := &corev1.Node{}
	if err := cli.Get(ctx, client.ObjectKey{Name: kubenodename}, k8snode); err != nil {
		return fmt.Errorf("get node %s: %w", kubenodename, err)
	}
	orginal := k8snode.DeepCopy()

	taint := corev1.Taint{
		Key: base.TaintKeyNodeExpired, Value: "true",
		Effect:    corev1.TaintEffectNoSchedule,
		TimeAdded: ptr.To(metav1.Now()),
	}
	if expired {
		k8snode.Spec.Taints = SetTaints(k8snode.Spec.Taints, taint)
	} else {
		k8snode.Spec.Taints = UnsetTaints(k8snode.Spec.Taints, taint.Key)
	}
	if reflect.DeepEqual(k8snode.Spec.Taints, orginal.Spec.Taints) {
		return nil
	}
	return cli.Update(ctx, k8snode)
}

func (c *TenantPrivateNodeReconciler) syncBind(ctx context.Context, claim *PrivateNodeClaim) error {
	if claim.Status.NodeName != "" {
		// check node exist after bind
		pnode := &PrivateNode{}
		if err := c.Store.
			Scope(claim.Cluster.Scopes...).
			Scope(base.ScopeCluster(claim.Cluster.Name)).
			Get(ctx, claim.Status.NodeName, pnode); err != nil {
			claim.Status.Ready = false
			return err
		}
		claim.Status.Ready = pnode.Status.Ready
		return nil
	}
	// reset ready status
	claim.Status.Ready = false

	// check cluster
	cluster := &cluster.Cluster{}
	if err := c.Store.Scope(claim.Cluster.Scopes...).Get(ctx, claim.Cluster.Name, cluster); err != nil {
		return err
	}
	// find a private node
	pnodelist := store.List[PrivateNode]{}
	if err := c.Store.
		Scope(cluster.Scopes...).
		Scope(base.ScopeCluster(cluster.Name)).
		List(ctx, &pnodelist); err != nil {
		return err
	}
	// if node useby this, it a workaround pnode usedby updated but cliam status not.
	cliamref := store.ResourcedObjectReferenceFrom(claim)
	for _, pnode := range pnodelist.Items {
		for _, u := range pnode.Status.UsedBy {
			if u.Equals(cliamref) {
				claim.Status.NodeName = pnode.Name
				claim.Status.Ready = pnode.Status.Ready
				return nil
			}
		}
	}
	for _, pnode := range pnodelist.Items {
		if !pnode.Status.Ready {
			continue
		}
		if pnode.Model != claim.Model {
			continue
		}
		if len(pnode.Status.UsedBy) > 0 {
			continue
		}
		// found a private node and bind it
		pnode.Status.UsedBy = append(pnode.Status.UsedBy, cliamref)
		if err := c.Store.Scope(pnode.Scopes...).Status().Update(ctx, &pnode); err != nil {
			return err
		}
		claim.Status.NodeName = pnode.Name
		claim.Status.Ready = pnode.Status.Ready
		return nil
	}
	return fmt.Errorf("no private node available for model %s", claim.Model)
}

func (c *TenantPrivateNodeReconciler) syncKubernetesNodeLabels(ctx context.Context, nodeclaim *PrivateNodeClaim) error {
	kubenodename, cluster := nodeclaim.Status.NodeName, nodeclaim.Cluster
	if kubenodename == "" {
		return nil
	}
	info, err := c.Holder.Get(ctx, cluster)
	if err != nil {
		return err
	}
	kubeclients, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	cli := kubeclients.Client
	labels := nodeclaim.Labels

	k8snode := &corev1.Node{}
	if err := cli.Get(ctx, client.ObjectKey{Name: kubenodename}, k8snode); err != nil {
		return fmt.Errorf("get node %s: %w", kubenodename, err)
	}
	if err := c.setNodeInfo(k8snode, nodeclaim); err != nil {
		log.Error(err, "set node info")
	}
	newlabels := map[string]string{}
	for k, v := range k8snode.Labels {
		// remove all our managed labels
		if strings.HasPrefix(k, base.LabelClusterPrefix) {
			continue
		}
		newlabels[k] = v
	}
	maps.Copy(newlabels, labels)

	// update node labels
	if !reflect.DeepEqual(k8snode.Labels, newlabels) {
		k8snode.Labels = newlabels
		if err := cli.Update(ctx, k8snode); err != nil {
			return fmt.Errorf("update node %s labels: %w", kubenodename, err)
		}
	}
	return nil
}

func (c *TenantPrivateNodeReconciler) setNodeInfo(node *corev1.Node, nodeclaim *PrivateNodeClaim) error {
	nodeclaim.Status.NodeInfo = node.Status.NodeInfo
	nodeclaim.Status.Capacity = node.Status.Capacity
	return nil
}

func (c *TenantPrivateNodeReconciler) syncExpire(_ context.Context, node *PrivateNodeClaim) (bool, error) {
	if node.Expire.IsZero() {
		controller.RemoveStatusCondition(&node.Status.Conditions, base.ConditionTypeExpired)
		return false, nil
	}
	left := node.Expire.Time.Sub(time.Now())
	if left < 0 {
		node.Status.Ready = false
		controller.SetStatusCondition(&node.Status.Conditions, controller.Condition{
			Type:    base.ConditionTypeExpired,
			Status:  controller.ConditionTrue,
			Reason:  "Expired",
			Message: fmt.Sprintf("expired at %s", node.Expire.Time),
		})
		return true, nil
	} else {
		controller.SetStatusCondition(&node.Status.Conditions, controller.Condition{
			Type:    base.ConditionTypeExpired,
			Status:  controller.ConditionFalse,
			Reason:  "NotExpired",
			Message: fmt.Sprintf("expire at %s", node.Expire.Time),
		})
		return false, nil
	}
}

func SetTaints(taints []corev1.Taint, taint corev1.Taint) []corev1.Taint {
	for i, t := range taints {
		if t.Key == taint.Key {
			taints[i] = taint
			return taints
		}
	}
	return append(taints, taint)
}

func HasTaint(taints []corev1.Taint, key string) bool {
	for _, t := range taints {
		if t.Key == key {
			return true
		}
	}
	return false
}

func UnsetTaints(taints []corev1.Taint, key string) []corev1.Taint {
	for i, t := range taints {
		if t.Key == key {
			return slices.Delete(taints, i, i+1)
		}
	}
	return taints
}

func (c *TenantPrivateNodeReconciler) Remove(ctx context.Context, node *PrivateNodeClaim) (controller.Result, error) {
	return base.UnwrapReQueueError(c.remove(ctx, node))
}

func (c *TenantPrivateNodeReconciler) remove(ctx context.Context, node *PrivateNodeClaim) error {
	if err := c.removeIncSKUStock(ctx, node); err != nil {
		return err
	}
	if err := c.removeOnKubernetsNode(ctx, node); err != nil {
		return err
	}
	if err := c.unbindPrivateNode(ctx, node); err != nil {
		return err
	}
	return nil
}

func (c *TenantPrivateNodeReconciler) removeOnKubernetsNode(ctx context.Context, nodeclaim *PrivateNodeClaim) error {
	kubenodename, cluster := nodeclaim.Status.NodeName, nodeclaim.Cluster
	if kubenodename == "" {
		return nil
	}
	info, err := c.Holder.Get(ctx, cluster)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	kubeclients, err := info.KubernetesConfig()
	if err != nil {
		return err
	}
	cli := kubeclients.Client

	k8snode := &corev1.Node{}
	if err := cli.Get(ctx, client.ObjectKey{Name: kubenodename}, k8snode); err != nil {
		if apierrors.IsNotFound(err) {
			return nil
		}
		return fmt.Errorf("get node %s: %w", kubenodename, err)
	}
	if err := c.setNodeInfo(k8snode, nodeclaim); err != nil {
		log.Error(err, "set node info")
	}
	orignal := k8snode.DeepCopy()
	for k := range k8snode.Labels {
		// remove all our managed labels
		if strings.HasPrefix(k, base.LabelClusterPrefix) {
			delete(k8snode.Labels, k)
		}
	}
	// remove node taint
	k8snode.Spec.Taints = slices.DeleteFunc(k8snode.Spec.Taints, func(t corev1.Taint) bool {
		return t.Key == base.TaintKeyNodeExpired
	})
	// update
	return cli.Patch(ctx, k8snode, client.MergeFrom(orignal))
}

func (c *TenantPrivateNodeReconciler) unbindPrivateNode(ctx context.Context, cliam *PrivateNodeClaim) error {
	if cliam.Status.NodeName == "" {
		return nil
	}
	kubenodename := cliam.Status.NodeName
	pnode := &PrivateNode{}
	if err := c.Store.
		Scope(cliam.Cluster.Scopes...).
		Scope(base.ScopeCluster(cliam.Cluster.Name)).
		Get(ctx, kubenodename, pnode); err != nil {
		if errors.IsNotFound(err) {
			cliam.Status.NodeName = ""
			return nil
		}
		return err
	}
	cliamref := store.ResourcedObjectReferenceFrom(cliam)
	usedby := pnode.Status.UsedBy
	for i, u := range usedby {
		if u.Equals(cliamref) {
			usedby = slices.Delete(usedby, i, i+1)
			break
		}
	}
	pnode.Status.UsedBy = usedby
	if err := c.Store.Scope(pnode.Scopes...).Status().Update(ctx, pnode); err != nil {
		return err
	}
	cliam.Status.NodeName = ""
	return nil
}

func (c *TenantPrivateNodeReconciler) removeIncSKUStock(ctx context.Context, cliam *PrivateNodeClaim) error {
	if val, ok := cliam.Annotations[base.AnnotationSKUReleased]; ok && val == base.ValueTrue {
		return nil
	}
	patch := store.MapMergePatch{
		"annotations": map[string]string{
			base.AnnotationSKUReleased: base.ValueTrue,
		},
	}
	if err := c.Store.Scope(cliam.Scopes...).Patch(ctx, cliam, patch); err != nil {
		return err
	}
	if err := c.sku.DeltaStock(ctx, product.CategoryPrivateNode, cliam.Cluster.Name, cliam.Model, 1); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
		return nil
	}
	return nil
}
