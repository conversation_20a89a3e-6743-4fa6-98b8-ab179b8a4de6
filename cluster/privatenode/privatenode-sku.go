package privatenode

import (
	"context"
	"fmt"
	"net/http"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/product"
)

type PrivateNodeSKU struct {
	store.ObjectMeta `json:",inline"`
	Price            base.Price `json:"price"`
	Enabled          bool       `json:"enabled"`
	Stock            int        `json:"stock"`
}

type SKUWithPrivateNodesCount struct {
	product.SKUData   `json:",inline"`
	PrivateNodesCount int `json:"privateNodesCount"`
}

func (a *API) ListPrivateNodeSKU(w http.ResponseWriter, r *http.Request) {
	a.OnCluster(w, r, func(ctx context.Context, storage store.Store, clustername string) (any, error) {
		list, err := a.SKUs.ListSKU(ctx, product.CategoryPrivateNode, clustername, &product.ListSKUOptions{
			ListOptions:  api.GetListOptions(r),
			WithDisabled: true,
		})
		if err != nil {
			return nil, err
		}
		pnodelist := &store.List[PrivateNode]{}
		if err := storage.List(ctx, pnodelist); err != nil {
			return nil, err
		}
		exists := map[string]int{}
		for _, pnode := range pnodelist.Items {
			exists[pnode.Model]++
		}
		result := &store.List[SKUWithPrivateNodesCount]{
			Items: make([]SKUWithPrivateNodesCount, 0, len(list.Items)),
			Total: list.Total,
			Page:  list.Page,
			Size:  list.Size,
		}
		for _, sku := range list.Items {
			result.Items = append(result.Items, SKUWithPrivateNodesCount{
				SKUData:           sku,
				PrivateNodesCount: exists[sku.Name],
			})
		}
		return result, nil
	})
}

type CreateSKU struct {
	Name        string     `json:"name" validate:"name"`
	Alias       string     `json:"alias"`
	Description string     `json:"description"`
	Price       base.Price `json:"price"`
	Enabled     bool       `json:"enabled"`
	Stock       int        `json:"stock"`
}

func (a *API) CreatePrivateNodeSKU(w http.ResponseWriter, r *http.Request) {
	a.OnCluster(w, r, func(ctx context.Context, _ store.Store, clustername string) (any, error) {
		req := &CreateSKU{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		if req.Alias == "" {
			req.Alias = req.Name
		}
		skudata := &product.SKUData{
			Name:         req.Name,
			Alias:        req.Alias,
			Description:  req.Description,
			Price:        req.Price,
			Enabled:      req.Enabled,
			Stock:        req.Stock,
			StockPolicy:  product.StockPolicyPerBatch,
			RefundPolicy: product.RefundPolicyProRated,
		}
		return a.SKUs.CreateSKU(ctx, product.CategoryPrivateNode, clustername, skudata)
	})
}

func (a *API) UpdatePrivateNodeSKU(w http.ResponseWriter, r *http.Request) {
	a.onClusterSku(w, r, func(ctx context.Context, _ store.Store, clustername, skuname string) (any, error) {
		req := &PrivateNodeSKU{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		if req.Alias == "" {
			req.Alias = req.Name
		}
		skudata := privatenodeskuToSKU(req)
		if err := a.SKUs.UpdateSKU(ctx, product.CategoryPrivateNode, clustername, req.Name, skudata); err != nil {
			return nil, err
		}
		return skudata, nil
	})
}

func privatenodeskuToSKU(req *PrivateNodeSKU) *product.SKUData {
	return &product.SKUData{
		Name:         req.Name,
		Alias:        req.Alias,
		Description:  req.Description,
		Price:        req.Price,
		Enabled:      req.Enabled,
		Stock:        req.Stock,
		StockPolicy:  product.StockPolicyPerBatch,
		RefundPolicy: product.RefundPolicyProRated,
	}
}

func (a *API) DeletePrivateNodeSKU(w http.ResponseWriter, r *http.Request) {
	a.onClusterSku(w, r, func(ctx context.Context, _ store.Store, clustername, skuname string) (any, error) {
		if err := a.SKUs.DeleteSKU(ctx, product.CategoryPrivateNode, clustername, skuname); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

type SetSKUPrivateNodes struct {
	Nodes []NodeReference `json:"nodes"`
}

type NodeReference struct {
	Name string `json:"name"`
}

func (a *API) SetSKUPrivateNodes(w http.ResponseWriter, r *http.Request) {
	a.onClusterSku(w, r, func(ctx context.Context, storage store.Store, clustername, skuname string) (any, error) {
		req := &SetSKUPrivateNodes{}
		if err := api.Body(r, req); err != nil {
			return nil, err
		}
		pnodelist := &store.List[PrivateNode]{}
		if err := storage.List(ctx, pnodelist); err != nil {
			return nil, err
		}
		exists := map[string]string{}
		for _, pnode := range pnodelist.Items {
			exists[pnode.Name] = pnode.Model
		}
		for _, node := range req.Nodes {
			if model, ok := exists[node.Name]; ok {
				if model == skuname {
					continue
				}
				return nil, errors.NewBadRequest(fmt.Sprintf("node %s already use sku %s", node.Name, model))
			} else {
				pnode, err := a.createPrivateNode(ctx, clustername, node.Name, skuname)
				if err != nil {
					return nil, err
				}
				_ = pnode
			}
		}
		return req, nil
	})
}

func (a *API) onClusterSku(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, storage store.Store, clustername, skuname string) (any, error)) {
	a.OnCluster(w, r, func(ctx context.Context, storage store.Store, clustername string) (any, error) {
		skuname := api.Path(r, "name", "")
		if skuname == "" {
			return nil, errors.NewBadRequest("sku name is required")
		}
		return fn(ctx, storage, clustername, skuname)
	})
}

func (a *API) adminSKUGroup() api.Group {
	return base.
		NewClusterGroup("/adminprivatenode-skus").
		Route(
			api.GET("").
				Operation("list private node sku").
				To(a.ListPrivateNodeSKU).
				Response(store.List[SKUWithPrivateNodesCount]{}),

			api.POST("").
				Operation("create private node sku").
				To(a.CreatePrivateNodeSKU).
				Param(
					api.BodyParam("sku", CreateSKU{}),
				),

			api.PUT("/{name}").
				Operation("update private node sku").
				To(a.UpdatePrivateNodeSKU).
				Param(
					api.PathParam("name", ""),
					api.BodyParam("sku", PrivateNodeSKU{}),
				),

			api.DELETE("/{name}").
				Operation("delete private node sku").
				To(a.DeletePrivateNodeSKU),

			api.POST("/{name}/privatenodes").
				Operation("add sku private nodes").
				To(a.SetSKUPrivateNodes).
				Param(
					api.BodyParam("sku", SetSKUPrivateNodes{}),
				),
		)
}

func (a *API) ListUserPrivateNodeSKU(w http.ResponseWriter, r *http.Request) {
	a.OnCluster(w, r, func(ctx context.Context, _ store.Store, clustername string) (any, error) {
		return a.SKUs.ListSKU(ctx, product.CategoryPrivateNode, clustername, &product.ListSKUOptions{
			ListOptions: api.GetListOptions(r),
		})
	})
}

func (a *API) userSKUGroup() api.Group {
	return base.
		NewClusterGroup("/privatenode-skus").
		Route(
			api.GET("").
				Operation("list private node sku").
				To(a.ListUserPrivateNodeSKU).
				Response(store.List[PrivateNodeSKU]{}),
		)
}

func (a *API) skuGroup() api.Group {
	return api.NewGroup("").
		SubGroup(
			a.adminSKUGroup(),
			a.userSKUGroup(),
		)
}
