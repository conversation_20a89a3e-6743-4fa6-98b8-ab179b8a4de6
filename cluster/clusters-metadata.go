package cluster

import (
	"context"
	"net/http"

	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	cloud "xiaoshiai.cn/core/ismc/common"
)

func ListClusterMetadata(r *http.Request, storage store.Store, withNotPublished bool) ([]ResourceMetadata, error) {
	ctx := r.Context()
	platforms := []string{}
	if cate := api.Query(r, "marketapplication-type", ""); cate != "" {
		switch cate {
		case string("VirtualMachine"):
			list := []string{}
			for _, t := range VirtualMachineClusterTypes {
				list = append(list, string(t))
			}
			platforms = append(platforms, list...)
		case string("Container"):
			list := []string{}
			for _, t := range ContainerClusterTypes {
				list = append(list, string(t))
			}
			platforms = append(platforms, list...)
		}
	}
	options := []store.ListOption{}
	if len(platforms) != 0 {
		req, err := labels.NewRequirement(LabelClusterType, selection.In, platforms)
		if err != nil {
			return nil, err
		}
		options = append(options, store.WithLabelRequirementsFromSelector(labels.NewSelector().Add(*req)))
	}
	fields := store.Requirements{}
	if !withNotPublished {
		fields = append(fields, store.RequirementEqual("published", true))
	}
	if len(fields) != 0 {
		options = append(options, store.WithFieldRequirements(fields...))
	}
	clusterlist := &store.List[Cluster]{}
	if err := storage.List(ctx, clusterlist, options...); err != nil {
		return nil, err
	}
	ret := make([]ResourceMetadata, 0, len(clusterlist.Items))
	for _, c := range clusterlist.Items {
		ret = append(ret, ResourceMetadata{
			Name:        c.Name,
			Type:        string(c.Type),
			Description: c.Description,
			Annotations: c.Annotations,
			Scopes:      c.Scopes,
			Labels:      c.Labels,
			Public:      c.Published,
		})
	}
	return ret, nil
}

func LabelRequirementClusterCategory(cate ClusterCategory) store.Requirement {
	platforms := []any{}
	switch cate {
	case ClusterCategoryVirtualMachine:
		for _, t := range VirtualMachineClusterTypes {
			platforms = append(platforms, string(t))
		}
	case ClusterCategoryContainer:
		for _, t := range ContainerClusterTypes {
			platforms = append(platforms, string(t))
		}
	}
	return store.Requirement{Key: LabelClusterType, Operator: store.In, Values: platforms}
}

func GetClusterKubeClient(ctx context.Context, cloudinfo CloudInfoGetter, clusterref store.ObjectReference) (client.Client, error) {
	info, err := cloudinfo.Get(ctx, clusterref)
	if err != nil {
		return nil, err
	}
	cli, err := info.KubernetesConfig()
	if err != nil {
		return nil, err
	}
	return cli.Client, nil
}

func GetClusterCloudProvider(ctx context.Context, storage store.Store, cloudinfo CloudInfoGetter, clusterref store.ObjectReference) (cloud.Provider, error) {
	info, err := cloudinfo.Get(ctx, clusterref)
	if err != nil {
		return nil, err
	}
	return info.CloudProvider()
}
