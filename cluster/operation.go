package cluster

import (
	"net/http"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"xiaoshiai.cn/common/rest/api"
)

type Resources struct {
	Capacity   corev1.ResourceList
	Alocatable corev1.ResourceList
}

func ResourceMetaFromRequest(r *http.Request) RequestMetadata {
	vars := api.PathVars(r).Map()
	return RequestMetadata{
		GroupVersionResource: schema.GroupVersionResource{
			Group: func() string {
				if group := vars["group"]; group != "core" {
					return group
				}
				return ""
			}(),
			Version:  vars["version"],
			Resource: vars["resource"],
		},
		Namespace:   vars["namespace"],
		Kind:        vars["kind"],
		Name:        vars["name"],
		Request:     r,
		Subresource: vars["subresource"],
	}
}

type RequestMetadata struct {
	schema.GroupVersionResource
	Namespace   string
	Kind        string
	Name        string
	Request     *http.Request
	Subresource string
}

func (r RequestMetadata) ResourcePath() string {
	result := "/" + r.GroupVersion().String()
	if r.Namespace != "" {
		result += "/namespaces/" + r.Namespace
	}
	result += "/" + r.Resource
	if r.Name != "" {
		result += "/" + r.Name
	}
	return result
}

func (r RequestMetadata) RequestPath() string {
	result := r.ResourcePath()
	if r.Subresource != "" {
		result += "/" + r.Subresource
	}
	return result
}
