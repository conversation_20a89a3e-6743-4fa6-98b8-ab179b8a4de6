package cluster

import (
	"context"
	"fmt"
	"hash/fnv"
	"sync"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/agent/proxy"
	cloud "xiaoshiai.cn/core/ismc/common"
	"xiaoshiai.cn/core/ismc/providers/remote"
)

type EdgeClientConfigGetter interface {
	GetConfig(addr string) *httpclient.ClientConfig
}

type CloudInfo interface {
	Reference() store.ObjectReference
	Type() ClusterType
	KubernetesConfig() (*KubernetesClients, error)
	CloudProvider() (cloud.Provider, error)
}

type CloudClient struct {
	ClientConfig *httpclient.ClientConfig
}

type CloudInfoGetter interface {
	Get(ctx context.Context, ref store.ObjectReference) (CloudInfo, error)
}

type CloudInfoHolder interface {
	CloudInfoGetter
	Sync(ctx context.Context, cluster *Cluster) (CloudInfo, error)
	Remove(ctx context.Context, ref store.ObjectReference) error
}

var _ CloudInfoHolder = &defaultCloudInfoHolder{}

func NewDefaultCloudInfoHolder(ctx context.Context, edgetp EdgeClientConfigGetter) *defaultCloudInfoHolder {
	return &defaultCloudInfoHolder{
		basectx: ctx,
		edgetp:  edgetp,
		infos:   make(map[string]*defaultCloudInfo),
	}
}

type defaultCloudInfoHolder struct {
	mu      sync.RWMutex
	basectx context.Context
	edgetp  EdgeClientConfigGetter
	infos   map[string]*defaultCloudInfo
}

// Get implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Get(ctx context.Context, ref store.ObjectReference) (CloudInfo, error) {
	if ref.Name == "" {
		return nil, errors.NewInvalid("cluster reference", ref.String(), fmt.Errorf(".name is required"))
	}
	d.mu.RLock()
	defer d.mu.RUnlock()
	info, ok := d.infos[ref.String()]
	if !ok {
		return nil, errors.NewNotFound("cluster info", ref.String())
	}
	return info, nil
}

// Sync implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Sync(ctx context.Context, cluster *Cluster) (CloudInfo, error) {
	hash := computeHash(cluster.Kube, cluster.Params, cluster.Edge)
	ref := store.ObjectReferenceFrom(cluster)

	d.mu.RLock()
	existsinfo, ok := d.infos[ref.String()]
	d.mu.RUnlock()
	if ok && existsinfo.confighash == hash {
		return existsinfo, nil
	}
	// cancel the old info
	if existsinfo != nil {
		existsinfo.Close()
	}
	d.mu.Lock()
	defer d.mu.Unlock()
	// update the info
	infoctx, cancel := context.WithCancel(ctx)

	newinfo := &defaultCloudInfo{
		ref:         ref,
		clustertype: cluster.Type,
		confighash:  hash,
		cancel:      cancel,
		ready:       false,
	}
	d.infos[ref.String()] = newinfo

	switch ClusterCategoryFrom(cluster.Type) {
	case ClusterCategoryContainer:
		restconfig, isedge, err := d.GetRestConfig(cluster)
		if err != nil {
			return nil, err
		}
		newkubeclients, err := NewKubernetesClients(infoctx, restconfig, isedge)
		if err != nil {
			return nil, err
		}
		newkubeclients.KubeCluster = cluster.Kube
		newinfo.kubernetes = newkubeclients

		newinfo.ready = true
	case ClusterCategoryVirtualMachine:
		restconfig, isedge, err := d.GetRestConfig(cluster)
		if err != nil {
			return nil, err
		}
		newkubeclients, err := NewKubernetesClients(infoctx, restconfig, isedge)
		if err != nil {
			return nil, err
		}
		newkubeclients.KubeCluster = cluster.Kube
		newinfo.kubernetes = newkubeclients

		cloudclientConfig, err := d.GetCloudConfig(cluster, newkubeclients)
		if err != nil {
			return nil, err
		}
		cloud, err := remote.NewRemote(&remote.Options{
			Server:    cloudclientConfig.Server.String(),
			Transport: cloudclientConfig.RoundTripper,
		})
		if err != nil {
			return nil, err
		}
		newinfo.provider = cloud
		newinfo.ready = true
	}
	return newinfo, nil
}

func (d *defaultCloudInfoHolder) GetCloudConfig(cluster *Cluster, kubeclients *KubernetesClients) (*httpclient.ClientConfig, error) {
	if kube := cluster.Kube; kube.Config != "" {
		clicoonfig := kubeclients.GetServiceProxyAddr(def(kube.Namespace, "ismc"), def(kube.Service, "ismc-agent"), def(kube.Port, 80))
		return clicoonfig, nil
	}
	if cluster.Edge.DeviceID != "" {
		return d.edgetp.GetConfig(cluster.Edge.DeviceID), nil
	}
	return nil, errors.NewInvalid("cluster", cluster.Name, fmt.Errorf("cloud config is not supported"))
}

func def[T comparable](v, def T) T {
	var zero T
	if v == zero {
		return def
	}
	return v
}

func (d *defaultCloudInfoHolder) GetRestConfig(cluster *Cluster) (*rest.Config, bool, error) {
	if cluster.Kube.Config != "" {
		kubeconfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(cluster.Kube.Config))
		if err != nil {
			return nil, false, err
		}
		return kubeconfig, false, nil
	}
	if cluster.Edge.DeviceID != "" {
		cfg := d.edgetp.GetConfig(cluster.Edge.DeviceID)
		host := cfg.Server
		host.Path += proxy.KubernetesProxyPath
		return &rest.Config{Host: host.String(), Transport: cfg.RoundTripper, Dial: cfg.DialContext}, true, nil
	}
	return nil, false, errors.NewInvalid("cluster", cluster.Name, fmt.Errorf("rest config is not supported"))
}

// Remove implements CloudInfoHolder.
func (d *defaultCloudInfoHolder) Remove(ctx context.Context, ref store.ObjectReference) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	key := ref.String()
	info, ok := d.infos[key]
	if !ok {
		return nil
	}
	info.Close()
	delete(d.infos, key)
	return nil
}

var _ CloudInfo = &defaultCloudInfo{}

type defaultCloudInfo struct {
	ref         store.ObjectReference
	clustertype ClusterType

	provider   cloud.Provider
	kubernetes *KubernetesClients

	// ready is true if the info is ready
	ready bool
	// confighash is the hash of the config
	// it is used to check if the config is changed
	confighash uint32
	cancel     context.CancelFunc
}

func (d *defaultCloudInfo) Close() {
	d.cancel()
}

// CloudProvider implements CloudInfo.
func (d *defaultCloudInfo) CloudProvider() (cloud.Provider, error) {
	if !d.ready {
		return nil, errors.NewInvalid("cloud info", d.ref.Name, fmt.Errorf("cloud info is not ready"))
	}
	if d.provider == nil {
		return nil, errors.NewUnsupported(fmt.Sprintf("cluster type %s not an ismc provider", d.clustertype))
	}
	return d.provider, nil
}

// KubernetesConfig implements CloudInfo.
func (d *defaultCloudInfo) KubernetesConfig() (*KubernetesClients, error) {
	if !d.ready {
		return nil, errors.NewInvalid("cloud info", d.ref.Name, fmt.Errorf("cloud info is not ready"))
	}
	if d.kubernetes == nil {
		return nil, errors.NewUnsupported(fmt.Sprintf("cluster type %s not a kubernetes cluster", d.clustertype))
	}
	return d.kubernetes, nil
}

// Name implements CloudInfo.
func (d *defaultCloudInfo) Reference() store.ObjectReference {
	return d.ref
}

// Type implements CloudInfo.
func (d *defaultCloudInfo) Type() ClusterType {
	return d.clustertype
}

func computeHash(values ...any) uint32 {
	hasher := fnv.New32a()
	hasher.Reset()
	fmt.Fprintf(hasher, "%#v", values...)
	return hasher.Sum32()
}
