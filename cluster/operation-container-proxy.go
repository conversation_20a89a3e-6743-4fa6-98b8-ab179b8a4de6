package cluster

import (
	"net/http"

	"xiaoshiai.cn/common/httpclient"
	"xiaoshiai.cn/common/rest/proxy"
)

func (s ContainerOperation) ServiceProxy(w http.ResponseWriter, r *http.Request, namespace, name string, port int, path string) (any, error) {
	cliconfig := s.Info.GetServiceProxyAddr(namespace, name, port)
	return clusterPoxyHandle(w, r, cliconfig, cliconfig.Server.Path, "/"+path)
}

func clusterPoxyHandle(w http.ResponseWriter, r *http.Request, clienconfig *httpclient.ClientConfig, removeprefix, reqpath string) (any, error) {
	p := proxy.Proxy{ClientConfig: clienconfig, RemovePrefix: removeprefix, RequestPath: reqpath, ProxyPrefix: "/proxy"}
	p.ServeHTTP(w, r)
	return nil, nil
}
