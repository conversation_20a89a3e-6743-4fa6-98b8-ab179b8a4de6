package cluster

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime"
	"net/http"
	"path"
	"sort"

	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/selection"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/utils/ptr"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
)

func NewContainerOperation(info CloudInfo) (ContainerOperation, error) {
	kubes, err := info.KubernetesConfig()
	if err != nil {
		return ContainerOperation{}, err
	}
	return ContainerOperation{
		ClusterReference: info.Reference(),
		ClusterType:      info.Type(),
		Info:             *kubes,
	}, nil
}

type ContainerOperation struct {
	ClusterReference store.ObjectReference
	ClusterType      ClusterType
	Info             KubernetesClients
}

func (s ContainerOperation) Get(r *http.Request, meta RequestMetadata) (any, error) {
	options := metav1.GetOptions{}
	if err := metav1.Convert_url_Values_To_v1_GetOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
		return nil, err
	}
	return s.Info.Dynamic.
		Resource(meta.GroupVersionResource).
		Namespace(meta.Namespace).Get(r.Context(), meta.Name, options)
}

func (s ContainerOperation) Create(r *http.Request, meta RequestMetadata) (any, error) {
	options := metav1.CreateOptions{}
	if err := metav1.Convert_url_Values_To_v1_CreateOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
		return nil, err
	}
	obj, err := s.readObject(meta, r.Body)
	if err != nil {
		return nil, err
	}
	return s.Info.Dynamic.Resource(meta.GroupVersionResource).
		Namespace(meta.Namespace).Create(r.Context(), obj, options)
}

func (s ContainerOperation) List(r *http.Request, meta RequestMetadata) (any, error) {
	listOptions := metav1.ListOptions{}
	if err := metav1.Convert_url_Values_To_v1_ListOptions(ptr.To(r.URL.Query()), &listOptions, nil); err != nil {
		return nil, err
	}
	list, err := s.listWithAddional(r, meta, listOptions)
	if err != nil {
		return nil, err
	}
	// page the list
	pageed := api.PageObjectFromRequest(r, list.Items)
	return pageed, nil
}

func (s ContainerOperation) listWithAddional(r *http.Request, meta RequestMetadata,
	listOptions metav1.ListOptions,
) (*unstructured.UnstructuredList, error) {
	switch meta.GroupVersionResource {
	case corev1.SchemeGroupVersion.WithResource("persistentvolumeclaims"):
		ctx := r.Context()
		allpvc := &unstructured.UnstructuredList{}
		allpvc.SetGroupVersionKind(corev1.SchemeGroupVersion.WithKind("PersistentVolumeClaimList"))

		cli := s.Info.Client
		if err := cli.List(ctx, allpvc, &client.ListOptions{Namespace: meta.Namespace, Raw: &listOptions}); err != nil {
			return nil, err
		}
		allpods := &corev1.PodList{}
		if err := cli.List(ctx, allpods, client.InNamespace(meta.Namespace)); err != nil {
			return nil, err
		}
		pvcPods := make(map[string][]string)
		for _, pod := range allpods.Items {
			for _, vol := range pod.Spec.Volumes {
				if vol.PersistentVolumeClaim != nil {
					pvcPods[vol.PersistentVolumeClaim.ClaimName] = append(pvcPods[vol.PersistentVolumeClaim.ClaimName], pod.Name)
				}
			}
		}
		for i := range allpvc.Items {
			if pods, ok := pvcPods[allpvc.Items[i].GetName()]; ok {
				unstructured.SetNestedStringSlice(allpvc.Items[i].Object, pods, "status", "mountedByPods")
			}
		}
		return allpvc, nil
	default:
		return s.Info.Dynamic.Resource(meta.GroupVersionResource).
			Namespace(meta.Namespace).List(r.Context(), listOptions)
	}
}

func (s ContainerOperation) Patch(r *http.Request, meta RequestMetadata) (any, error) {
	options := metav1.PatchOptions{}
	if err := metav1.Convert_url_Values_To_v1_PatchOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
		return nil, err
	}
	patchType, _, err := mime.ParseMediaType(r.Header.Get("Content-Type"))
	if err != nil {
		return nil, err
	}
	if patchType == "application/json" {
		// default to merge patch
		patchType = string(types.MergePatchType)
	}
	patchData, err := io.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}
	return s.Info.Dynamic.Resource(meta.GroupVersionResource).Namespace(meta.Namespace).
		Patch(r.Context(), meta.Name, types.PatchType(patchType), patchData, options)
}

func (s ContainerOperation) Update(r *http.Request, meta RequestMetadata) (any, error) {
	options := metav1.UpdateOptions{}
	if err := metav1.Convert_url_Values_To_v1_UpdateOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
		return nil, err
	}
	obj, err := s.readObject(meta, r.Body)
	if err != nil {
		return nil, err
	}
	// remove resourceVersion to avoid conflict
	obj.SetResourceVersion("")
	return s.Info.Dynamic.Resource(meta.GroupVersionResource).
		Namespace(meta.Namespace).Update(r.Context(), obj, options)
}

func (s ContainerOperation) Delete(r *http.Request, meta RequestMetadata) (any, error) {
	options := metav1.DeleteOptions{}
	if err := metav1.Convert_url_Values_To_v1_DeleteOptions(ptr.To(r.URL.Query()), &options, nil); err != nil {
		return nil, err
	}
	return nil, s.Info.Dynamic.
		Resource(meta.GroupVersionResource).
		Namespace(meta.Namespace).Delete(r.Context(), meta.Name, options)
}

func (s ContainerOperation) SubResource(w http.ResponseWriter, r *http.Request, meta RequestMetadata) (any, error) {
	prefix := "/" + path.Join("apis", meta.ResourcePath())
	return clusterPoxyHandle(w, r, s.Info.GetKubernetesAddr(), prefix, prefix+"/"+meta.Subresource)
}

func (s ContainerOperation) readObject(meta RequestMetadata, reader io.Reader) (*unstructured.Unstructured, error) {
	obj := &unstructured.Unstructured{}
	if err := json.NewDecoder(reader).Decode(obj); err != nil {
		return nil, err
	}
	// override name/namespace in body if set in url
	obj.SetNamespace(meta.Namespace)
	if meta.Name != "" {
		obj.SetName(meta.Name)
	}
	return obj, nil
}

func (s ContainerOperation) ListResourceChildren(w http.ResponseWriter, r *http.Request, meta RequestMetadata) (any, error) {
	ctx := r.Context()
	info := s.Info
	resourceType := meta.Resource
	resourceName := meta.Name

	cli := info.Client

	listPods := func(ctx context.Context, cli client.Client, ns string, selector map[string]string) ([]corev1.Pod, error) {
		podlist := &corev1.PodList{}
		if err := cli.List(ctx, podlist, client.InNamespace(ns), client.MatchingLabels(selector)); err != nil {
			return nil, err
		}
		return podlist.Items, nil
	}

	switch resourceType {
	case "deployments":
		deployment := &appsv1.Deployment{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: resourceName}, deployment); err != nil {
			return nil, err
		}
		// replica set
		rsList := &appsv1.ReplicaSetList{}
		if err := cli.List(ctx, rsList,
			client.InNamespace(meta.Namespace),
			client.MatchingLabels(deployment.Spec.Selector.MatchLabels),
		); err != nil {
			return nil, err
		}
		return rsList.Items, nil
	case "replicasets":
		replicaSet := &appsv1.ReplicaSet{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: resourceName}, replicaSet); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, meta.Namespace, replicaSet.Spec.Selector.MatchLabels)
	case "statefulsets":
		statefulSet := &appsv1.StatefulSet{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: resourceName}, statefulSet); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, meta.Namespace, statefulSet.Spec.Selector.MatchLabels)
	case "daemonsets":
		daemonSet := &appsv1.DaemonSet{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: resourceName}, daemonSet); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, meta.Namespace, daemonSet.Spec.Selector.MatchLabels)
	case "jobs":
		job := &batchv1.Job{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: resourceName}, job); err != nil {
			return nil, err
		}
		return listPods(ctx, cli, meta.Namespace, job.Spec.Selector.MatchLabels)
	case "cronjobs":
		cronJob := &batchv1.CronJob{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: resourceName}, cronJob); err != nil {
			return nil, err
		}
		// job
		joblist := &batchv1.JobList{}
		if err := cli.List(ctx, joblist,
			client.InNamespace(meta.Namespace),
			client.MatchingLabels(cronJob.Spec.JobTemplate.Spec.Selector.MatchLabels),
		); err != nil {
			return nil, err
		}
		return joblist.Items, nil
	default:
		return nil, fmt.Errorf("unsupported resource type %s", resourceType)
	}
}

func (s ContainerOperation) ListResourcePods(w http.ResponseWriter, r *http.Request, meta RequestMetadata) (*corev1.PodList, error) {
	ctx := r.Context()
	info := s.Info
	resourceType := meta.Resource
	resourceName := meta.Name
	var matchLabels map[string]string
	switch resourceType {
	case "deployments":
		deployment, err := info.Kubernetes.AppsV1().Deployments(meta.Namespace).Get(ctx, resourceName, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		matchLabels = deployment.Spec.Selector.MatchLabels
	case "statefulsets":
		statefulSet, err := info.Kubernetes.AppsV1().StatefulSets(meta.Namespace).Get(ctx, resourceName, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		matchLabels = statefulSet.Spec.Selector.MatchLabels
	case "daemonsets":
		daemonSet, err := info.Kubernetes.AppsV1().DaemonSets(meta.Namespace).Get(ctx, resourceName, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		matchLabels = daemonSet.Spec.Selector.MatchLabels
	case "jobs":
		job, err := info.Kubernetes.BatchV1().Jobs(meta.Namespace).Get(ctx, resourceName, metav1.GetOptions{})
		if err != nil {
			// job may not exist long time after it's done
			// so we return empty list here instead of error on not found
			if apierrors.IsNotFound(err) {
				return &corev1.PodList{}, nil
			}
			return nil, err
		}
		matchLabels = job.Spec.Selector.MatchLabels
	case "cronjobs":
		// jobs
		jobs, err := info.Kubernetes.BatchV1().Jobs(meta.Namespace).List(ctx, metav1.ListOptions{LabelSelector: labels.FormatLabels(map[string]string{"cronjob-name": resourceName})})
		if err != nil {
			return nil, err
		}
		if len(jobs.Items) == 0 {
			return &corev1.PodList{}, nil
		}
		uidlist := make([]string, 0, len(jobs.Items))
		for _, job := range jobs.Items {
			uidlist = append(uidlist, string(job.UID))
		}
		req, err := labels.NewRequirement("controller-uid", selection.In, uidlist)
		if err != nil {
			return nil, err
		}
		return info.Kubernetes.CoreV1().Pods(meta.Namespace).List(ctx, metav1.ListOptions{LabelSelector: labels.NewSelector().Add(*req).String()})
	default:
		return nil, fmt.Errorf("unsupported resource type %s", resourceType)
	}

	opts := metav1.ListOptions{
		LabelSelector: labels.FormatLabels(matchLabels),
	}
	return info.Kubernetes.CoreV1().Pods(meta.Namespace).List(ctx, opts)
}

func (s ContainerOperation) ListEvents(r *http.Request, meta RequestMetadata) (any, error) {
	listOptions := metav1.ListOptions{}
	if err := metav1.Convert_url_Values_To_v1_ListOptions(ptr.To(r.URL.Query()), &listOptions, nil); err != nil {
		return nil, err
	}
	if meta.Name != "" {
		if meta.Kind == "" && meta.Resource != "" {
			gvk, err := s.Info.Client.RESTMapper().KindFor(meta.GroupVersionResource)
			if err != nil {
				return nil, err
			}
			meta.Kind = gvk.Kind
		}
		fielsSelectors := []fields.Selector{
			fields.OneTermEqualSelector("involvedObject.kind", meta.Kind),
			fields.OneTermEqualSelector("involvedObject.apiVersion", meta.GroupVersion().String()),
			fields.OneTermEqualSelector("involvedObject.name", meta.Name),
		}
		if meta.Namespace != "" {
			fielsSelectors = append(fielsSelectors, fields.OneTermEqualSelector("involvedObject.namespace", meta.Namespace))
		}
		listOptions.FieldSelector = fields.AndSelectors(fielsSelectors...).String()
	}
	list, err := s.Info.Kubernetes.CoreV1().Events(meta.Namespace).List(r.Context(), listOptions)
	if err != nil {
		return nil, err
	}
	events := list.Items

	// combine related events
	relatedEvents, err := s.listRelatedEvents(r.Context(), meta)
	if err != nil {
		log := log.FromContext(r.Context())
		log.Error(err, "failed to list related events")
	}
	events = append(events, relatedEvents...)

	// page the list
	pageoptions := api.GetListOptions(r)
	pageoptions.Sort = ""
	// events should sort by it .LastTimestamp (in kubectl) but reversed
	sort.Sort((SortableEvents(events)))
	pageed := api.PageFromListOptions(events, pageoptions,
		func(e corev1.Event) string { return e.InvolvedObject.Name }, nil)
	return pageed, nil
}

func (s ContainerOperation) listRelatedEvents(ctx context.Context, meta RequestMetadata) ([]corev1.Event, error) {
	cli := s.Info.Client

	var events []corev1.Event
	switch meta.GroupVersionResource {
	case appsv1.SchemeGroupVersion.WithResource("deployments"):
		deployment := &appsv1.Deployment{}
		if err := cli.Get(ctx, types.NamespacedName{Namespace: meta.Namespace, Name: meta.Name}, deployment); err != nil {
			return nil, err
		}
		// replica set
		rsList := &appsv1.ReplicaSetList{}
		if err := cli.List(ctx, rsList, client.InNamespace(meta.Namespace), client.MatchingLabels(deployment.Spec.Selector.MatchLabels)); err != nil {
			return nil, err
		}
		for _, rs := range rsList.Items {
			rsEvents, err := listEvents(ctx, s.Info.Kubernetes, rs.GetObjectKind().GroupVersionKind(), meta.Namespace, rs.Name)
			if err != nil {
				return nil, err
			}
			events = append(events, rsEvents...)
		}
	}
	return events, nil
}

func listEvents(ctx context.Context, cli kubernetes.Interface, gvk schema.GroupVersionKind, namespace, name string) ([]corev1.Event, error) {
	fielsSelectors := []fields.Selector{
		fields.OneTermEqualSelector("involvedObject.kind", gvk.Kind),
		fields.OneTermEqualSelector("involvedObject.apiVersion", gvk.GroupVersion().String()),
	}
	if name != "" {
		fielsSelectors = append(fielsSelectors, fields.OneTermEqualSelector("involvedObject.name", name))
	}
	if namespace != "" {
		fielsSelectors = append(fielsSelectors, fields.OneTermEqualSelector("involvedObject.namespace", namespace))
	}
	listOptions := metav1.ListOptions{
		FieldSelector: fields.AndSelectors(fielsSelectors...).String(),
	}
	list, err := cli.CoreV1().Events(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, err
	}
	return list.Items, nil
}

// SortableEvents implements sort.Interface for []api.Event based on the Timestamp field
type SortableEvents []corev1.Event

func (list SortableEvents) Len() int {
	return len(list)
}

func (list SortableEvents) Swap(i, j int) {
	list[i], list[j] = list[j], list[i]
}

func (list SortableEvents) Less(i, j int) bool {
	return !list[i].LastTimestamp.Time.Before(list[j].LastTimestamp.Time)
}
