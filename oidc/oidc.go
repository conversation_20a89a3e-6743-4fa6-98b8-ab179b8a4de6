package oidc

import (
	"context"

	"xiaoshiai.cn/common/rest/api"
)

type Provider interface {
	GetOpenIDConfiguration(ctx context.Context) (*ProviderMetadata, error)
	GetJWKS(ctx context.Context) (*JWKS, error)
}

type API struct {
	Provider Provider
}

func NewAPI(provider Provider) *API {
	return &API{Provider: provider}
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		SubGroup(
			a.wellKnownGroup(),
		)
}
