package oidc

// https://datatracker.ietf.org/doc/html/rfc6749#section-4.1.1
type AuthorizationRequest struct {
	ResponseType string `json:"response_type"`
	ClientID     string `json:"client_id"`
	RedirectURI  string `json:"redirect_uri"`
	Scope        string `json:"scope"`
	State        string `json:"state"`
}

// https://datatracker.ietf.org/doc/html/rfc6749#section-4.1.2
type AuthorizationResponse struct {
	Code  string `json:"code"`
	State string `json:"state"`
}

// https://openid.net/specs/openid-connect-core-1_0.html#AuthError
type ErrorResponse struct {
	Error            ErrorType `json:"error"`
	ErrorDescription string    `json:"error_description"`
	ErrorURI         string    `json:"error_uri"`
	State            string    `json:"state"`
}

type ErrorType string

const (
	// https://datatracker.ietf.org/doc/html/rfc6749#section-*******
	ErrorInvalidRequest          ErrorType = "invalid_request"
	ErrorUnauthorizedClient      ErrorType = "unauthorized_client"
	ErrorAccessDenied            ErrorType = "access_denied"
	ErrorUnsupportedResponseType ErrorType = "unsupported_response_type"
	ErrorInvalidScope            ErrorType = "invalid_scope"
	ErrorServerError             ErrorType = "server_error"
	ErrorTemporarilyUnavailable  ErrorType = "temporarily_unavailable"

	// https://openid.net/specs/openid-connect-core-1_0.html#AuthError
	ErrorInteractionRequired      ErrorType = "interaction_required"
	ErrorLoginRequired            ErrorType = "login_required"
	ErrorAccountSelection         ErrorType = "account_selection_required"
	ErrorConsentRequired          ErrorType = "consent_required"
	ErrorInvalidRequestURI        ErrorType = "invalid_request_uri"
	ErrorInvalidRequestObject     ErrorType = "invalid_request_object"
	ErrorRequestNotSupported      ErrorType = "request_not_supported"
	ErrorRequestURINotSupported   ErrorType = "request_uri_not_supported"
	ErrorRegistrationNotSupported ErrorType = "registration_not_supported"

	// custom
	ErrorInvalidUserOrPassword ErrorType = "invalid_user_or_password"
)

// https://datatracker.ietf.org/doc/html/rfc6749#section-4.1.3
type TokenRequest struct {
	GrantType   string `json:"grant_type"`
	Code        string `json:"code"`
	RedirectURI string `json:"redirect_uri"`
	ClientID    string `json:"client_id"`
}

// https://datatracker.ietf.org/doc/html/rfc6749#section-4.1.4
// https://datatracker.ietf.org/doc/html/rfc6749#section-5.1
// https://openid.net/specs/openid-connect-core-1_0.html#TokenResponse
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
	IDToken      string `json:"id_token"`
}

// https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest
type AuthorizationCodeRequest struct {
	Scope        string `json:"scope,omitempty"`
	ResponseType string `json:"response_type,omitempty"`
	ClientID     string `json:"client_id,omitempty"`
	RedirectURI  string `json:"redirect_uri,omitempty"`
	State        string `json:"state,omitempty"`
	ResponseMode string `json:"response_mode,omitempty"`
	Nonce        string `json:"nonce,omitempty"`
	Display      string `json:"display,omitempty"`
	Prompt       string `json:"prompt,omitempty"`
	MaxAge       int    `json:"max_age,omitempty"`
	UILocales    string `json:"ui_locales,omitempty"`
	IDTokenHint  string `json:"id_token_hint,omitempty"`
	LoginHint    string `json:"login_hint,omitempty"`
	ACRValue     string `json:"acr_value,omitempty"`
}

type ImplictRequest struct {
	ResponseType string `json:"response_type"`
	RedirectURI  string `json:"redirect_uri"`
	Nonce        string `json:"nonce"`
}

const (
	PromptLogin         = "login"
	PromptConsent       = "consent"
	PromptNone          = "none"
	PromptSelectAccount = "select_account"
)

// https://openid.net/specs/openid-connect-core-1_0.html#IDToken
type IDToken struct {
	Issuer                          string        `json:"iss,omitempty"`
	Subject                         StringOrArray `json:"sub,omitempty"`
	Audience                        []string      `json:"aud,omitempty"`
	Expiration                      int64         `json:"exp,omitempty"`
	IssueAt                         int64         `json:"iat,omitempty"`
	AuthTiem                        int64         `json:"auth_time,omitempty"`
	Nonce                           string        `json:"nonce,omitempty"`
	AuthenticationContextClass      string        `json:"acr,omitempty"`
	AuthenticationMethodsReferences []string      `json:"amr,omitempty"`
	AuthorizedParty                 string        `json:"azp,omitempty"`
}

const (
	AuthorizationCodeFlow = "authorization_code"
	ImplictFlow           = "implicit"
	HybridFlow            = "hybrid"
)

// https://datatracker.ietf.org/doc/html/rfc6749#section-3.1.1
type ResponseType string

const (
	ResponseTypeCode    ResponseType = "code"
	ResponseTypeToken   ResponseType = "token"
	ResponseTypeIDToken ResponseType = "id_token"
)

// https://openid.net/specs/openid-connect-core-1_0.html#Authentication
func DetectFlow(types string) string {
	switch types {
	case "code":
		return AuthorizationCodeFlow
	case "id_token", "id_token token":
		return ImplictFlow
	case "code id_token", "code token", "code id_token token":
		return HybridFlow
	default:
		return ""
	}
}

// https://openid.net/specs/openid-connect-core-1_0.html#StandardClaims
type StandardClaims struct {
	Subject           string `json:"sub,omitempty"`
	Name              string `json:"name,omitempty"`
	GivenName         string `json:"given_name,omitempty"`
	FamilyName        string `json:"family_name,omitempty"`
	MiddleName        string `json:"middle_name,omitempty"`
	Nickname          string `json:"nickname,omitempty"`
	PreferredUsername string `json:"preferred_username,omitempty"`
	Profile           string `json:"profile,omitempty"`
	Picture           string `json:"picture,omitempty"`
	Website           string `json:"website,omitempty"`
	Email             string `json:"email,omitempty"`
	EmailVerified     bool   `json:"email_verified,omitempty"`
	Gender            string `json:"gender,omitempty"`
	// https://openid.net/specs/openid-connect-core-1_0.html#ClaimsLanguagesAndScripts
	ClaimsLocales string `json:"locale,omitempty"`
}

// https://openid.net/specs/openid-connect-core-1_0.html#AddressClaim
type AddressClaims struct {
	Formatted     string `json:"formatted,omitempty"`
	StreetAddress string `json:"street_address,omitempty"`
	Locality      string `json:"locality,omitempty"`
	Region        string `json:"region,omitempty"`
	PostalCode    string `json:"postal_code,omitempty"`
	Country       string `json:"country,omitempty"`
}

const (
	ScopeProfile string = "profile"
	ScopeEmail   string = "email"
	ScopeAddress string = "address"
	ScopePhone   string = "phone"
)

// https://openid.net/specs/openid-connect-core-1_0.html#ClaimsParameter
type ClaimsRequest struct {
	UserInfo map[string]*ClaimParameter `json:"userinfo,omitempty"`
	IDToken  map[string]*ClaimParameter `json:"id_token,omitempty"`
}

type ClaimParameter struct {
	// essential
	Essential bool     `json:"essential,omitempty"`
	Value     string   `json:"value,omitempty"`
	Values    []string `json:"values,omitempty"`
}

type ClaimType string

const (
	ClaimTypeNormal      ClaimType = "normal"
	ClaimTypeAggregated  ClaimType = "aggregated"
	ClaimTypeDistributed ClaimType = "distributed"
)
