package oidc

// https://datatracker.ietf.org/doc/html/rfc6749#section-2
type Client struct {
	ClientType    ClientType
	ClientID      string
	ClientSecret  string
	RedirectURIs  []string
	AllowedScopes []string
	DefaultScopes []string
}

type ClientType string

const (
	ClientTypeConfidential ClientType = "confidential"
	ClientTypePublic       ClientType = "public"
)

type ClientMetadata struct {
	// https://openid.net/specs/openid-connect-registration-1_0.html#ClientMetadata
	RedirectURIs                 []string        `json:"redirect_uris,omitempty"`
	ResponseTypes                []string        `json:"response_types,omitempty"`
	GrantTypes                   []GrantType     `json:"grant_types,omitempty"`
	ApplicationType              ApplicationType `json:"application_type,omitempty"`
	Contacts                     []string        `json:"contacts,omitempty"`
	ClientName                   string          `json:"client_name,omitempty"`
	LogoURI                      string          `json:"logo_uri,omitempty"`
	ClientURI                    string          `json:"client_uri,omitempty"`
	PolicyURI                    string          `json:"policy_uri,omitempty"`
	TosURI                       string          `json:"tos_uri,omitempty"`
	JWKSURI                      string          `json:"jwks_uri,omitempty"`
	JWKS                         JWKS            `json:"jwks,omitempty"`
	SelectorIdentifierURI        string          `json:"selector_identifier_uri,omitempty"`
	SubjectType                  string          `json:"subject_type,omitempty"`
	IDTokenSignedResponseAlg     string          `json:"id_token_signed_response_alg,omitempty"`
	IDTokenEncryptedResponseAlg  string          `json:"id_token_encrypted_response_alg,omitempty"`
	IDTokenEncryptedResponseEnc  string          `json:"id_token_encrypted_response_enc,omitempty"`
	UserInfoSignedResponseAlg    string          `json:"userinfo_signed_response_alg,omitempty"`
	UserInfoEncryptedResponseAlg string          `json:"userinfo_encrypted_response_alg,omitempty"`
	UserInfoEncryptedResponseEnc string          `json:"userinfo_encrypted_response_enc,omitempty"`
	RequestObjectSigningAlg      string          `json:"request_object_signing_alg,omitempty"`
	RequestObjectEncryptionAlg   string          `json:"request_object_encryption_alg,omitempty"`
	RequestObjectEncryptionEnc   string          `json:"request_object_encryption_enc,omitempty"`
	TokenEndpointAuthMethod      string          `json:"token_endpoint_auth_method,omitempty"`
	TokenEndpointAuthSigningAlg  string          `json:"token_endpoint_auth_signing_alg,omitempty"`
	DefaultMaxAge                int             `json:"default_max_age,omitempty"`
	RequireAuthTime              bool            `json:"require_auth_time,omitempty"`
	DefaultACRValues             []string        `json:"default_acr_values,omitempty"`
	InitiateLoginURI             string          `json:"initiate_login_uri,omitempty"`
	RequestURIs                  []string        `json:"request_uris,omitempty"`

	// https://openid.net/specs/openid-connect-rpinitiated-1_0.html#ClientMetadata
	PostLogoutRedirectURIs []string `json:"post_logout_redirect_uris,omitempty"`

	// https://openid.net/specs/openid-connect-prompt-create-1_0.html#name-discovery-metadata
	PromptValuesSupported []string `json:"prompt_values_supported,omitempty"`
}

type GrantType string

const (
	GrantTypeAuthorizationCode string    = "authorization_code"
	GrantTypeImplicit          string    = "implicit"
	GrantTypePassword          string    = "password"
	GrantTypeRefreshToken      string    = "refresh_token"
	GrantTypeClientCredentials GrantType = "client_credentials"
)

type ApplicationType string

const (
	ApplicationTypeWeb    string = "web"
	ApplicationTypeNative string = "native"
)
