package avatar

import (
	"context"
	"io"
	"net/http"
	"strconv"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

type SetAvatarOptions struct {
	Location      string
	ContentType   string
	ContentLength int64
	Modified      time.Time
	Expires       time.Time
	ETag          string
	Content       io.ReadCloser
}

type GetAvatarOptions struct {
	IfNoneMatch     string
	IfModifiedSince time.Time
	IfMatch         string
	CacheControl    string
}

type GetAvatarResponse struct {
	HttpCode      int
	Location      string
	LastModified  time.Time
	Expires       time.Time
	ETag          string
	ContentType   string
	ContentLength int64
	CacheControl  string
	Body          io.ReadCloser
}

type AvatarService interface {
	SetAvatar(ctx context.Context, kind string, name string, data SetAvatarOptions) error
	GetAvatar(ctx context.Context, kind string, name string, options GetAvatarOptions) (*GetAvatarResponse, error)
}

func NewAPI(base base.API, avatarService AvatarService) *API {
	return &API{
		API: base, AvatarService: avatarService,
		DefaultAvatarSVG: map[string][]byte{
			"tenant":  DefaultTenantAvatarSVG,
			"user":    DefaultAvatarSVG,
			"product": DefaultProductSVG,
		},
	}
}

type API struct {
	base.API
	AvatarService    AvatarService
	DefaultAvatarSVG map[string][]byte
}

func (a *API) SetTenantAvatar(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenantname string) (any, error) {
		if err := a.SetAvatarFromRequest(w, r, "tenant", tenantname); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) SetProductAvatar(w http.ResponseWriter, r *http.Request) {
	a.OnProduct(w, r, func(ctx context.Context, storage store.Store, productname string) (any, error) {
		if err := a.SetAvatarFromRequest(w, r, "product", productname); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) CurrentUserSetAvatar(w http.ResponseWriter, r *http.Request) {
	a.OnCurrentUser(w, r, func(ctx context.Context, storage store.Store, username string) (any, error) {
		if err := a.SetAvatarFromRequest(w, r, "user", username); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Avatar").
		SubGroup(
			api.NewGroup("/current/avatar").Route(
				api.POST("").To(a.CurrentUserSetAvatar).
					Doc("Set avatar").
					Param(
						api.BodyParam("avatar", []byte{}).Desc("image content"),
					),
			),
			base.NewTenantGroup("avatar").Route(
				api.POST("").
					To(a.SetTenantAvatar).
					Doc("Set avatar").
					Param(
						api.BodyParam("avatar", []byte{}).Desc("image content"),
					),
			),
			base.NewProductGroup("avatar").Route(
				api.POST("").
					To(a.SetProductAvatar).
					Doc("Set avatar").
					Param(
						api.BodyParam("avatar", []byte{}).Desc("image content"),
					),
			),
		)
}

var (
	DefaultAvatarSVG       = []byte(`<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 24 24"><path fill="#7F7F7F" d="M6.25 7a5.75 5.75 0 1 1 11.5 0a5.75 5.75 0 0 1-11.5 0m5.548 7.261a1 1 0 0 1 .13-.011h.144q.066 0 .13.011l7.295 1.283l.038.008c1.344.31 2.788 1.163 3.069 2.82l.004.029l.114.877v.002c.264 2.009-1.329 3.47-3.21 3.47a1 1 0 0 1-.124-.01h-14.9c-1.881 0-3.475-1.462-3.21-3.472l.114-.869l.005-.03c.28-1.627 1.736-2.528 3.077-2.819l.029-.006z"/></svg>`)
	DefaultTenantAvatarSVG = []byte(`<svg xmlns="http://www.w3.org/2000/svg" width="128" height="128" viewBox="0 0 256 256"><path fill="#7F7F7F" d="M239.73 208H224V96a16 16 0 0 0-16-16h-44a4 4 0 0 0-4 4v124h-16V32.41a16.43 16.43 0 0 0-6.16-13a16 16 0 0 0-18.72-.69L39.12 72A16 16 0 0 0 32 85.34V208H16.27A8.18 8.18 0 0 0 8 215.47a8 8 0 0 0 8 8.53h224a8 8 0 0 0 8-8.53a8.18 8.18 0 0 0-8.27-7.47M76 184a8 8 0 0 1-8.53 8a8.18 8.18 0 0 1-7.47-8.28v-15.45a8.19 8.19 0 0 1 7.47-8.27a8 8 0 0 1 8.53 8Zm0-56a8 8 0 0 1-8.53 8a8.19 8.19 0 0 1-7.47-8.28v-15.45a8.19 8.19 0 0 1 7.47-8.27a8 8 0 0 1 8.53 8Zm40 56a8 8 0 0 1-8.53 8a8.18 8.18 0 0 1-7.47-8.26v-15.47a8.19 8.19 0 0 1 7.47-8.26a8 8 0 0 1 8.53 8Zm0-56a8 8 0 0 1-8.53 8a8.19 8.19 0 0 1-7.47-8.26v-15.47a8.19 8.19 0 0 1 7.47-8.26a8 8 0 0 1 8.53 8Z"/></svg>`)
	DefaultProductSVG      = []byte(`
<svg xmlns="http://www.w3.org/2000/svg" width="256" height="297" viewBox="0 0 256 297">
  <path fill="#017943"
    d="m60.349 70.537l-.811-.767l-.278-.266c-7.589-7.29-13.462-15.721-17.01-25.685c-.951-2.673-1.68-5.397-1.621-8.262l.012-.375c.012-.285.011-.571.031-.856c.268-3.877 2.863-5.836 6.652-4.918c1.195.31 2.351.753 3.447 1.321c4.007 2.01 7.297 4.948 10.317 8.185l.312.336a67.7 67.7 0 0 1 13.066 20.108q.102.266.234.518a.3.3 0 0 0 .061.074l.212.181l.064.057a106.1 106.1 0 0 1 48.733-14.954l-.043-.218l-.112-.585a12 12 0 0 0-.073-.35l-.041-.17a67.7 67.7 0 0 1-1.596-22.23a50.6 50.6 0 0 1 2.674-13.09a18.8 18.8 0 0 1 3.294-6.118a9.4 9.4 0 0 1 1.902-1.697a4.23 4.23 0 0 1 4.832-.034a10.4 10.4 0 0 1 3.513 4.049a32 32 0 0 1 3.165 8.828a67.7 67.7 0 0 1 1.277 19.852a57.5 57.5 0 0 1-2.1 11.976q1.072.197 2.14.382l3.2.552c2.481.43 4.946.88 7.38 1.458a112 112 0 0 1 12.296 3.611c4.059 1.49 8.03 3.211 11.891 5.155c2.227 1.11 4.39 2.346 6.564 3.612l1.866 1.09q1.403.824 2.829 1.637l.08-.163l.178-.354q.092-.187.167-.387a65.36 65.36 0 0 1 19.79-28.128a22.7 22.7 0 0 1 7.11-4.14c.717-.25 1.46-.42 2.213-.508c3.768-.431 5.35 1.94 5.628 4.771a18 18 0 0 1-.465 6.228a52.9 52.9 0 0 1-6.147 14.357l-.308.493c-3.982 6.435-8.69 12.247-14.633 16.98l-.46.362a3 3 0 0 0-.12.1l-.192.177l-.24.228l-.098.09a107 107 0 0 1 15.996 18.576a6.6 6.6 0 0 1-1.016.179c-6.373.01-12.746-.011-19.12.028a2.4 2.4 0 0 1-1.908-1.017a88.95 88.95 0 0 0-53.039-27.999a86.2 86.2 0 0 0-17.453-.75l-.76.047a88.06 88.06 0 0 0-49.207 18.785l-.44.349a84 84 0 0 0-10.21 9.529a2.84 2.84 0 0 1-2.324 1.057a1483 1483 0 0 0-10.958-.026l-8.6.005q.044-.168.144-.398l.09-.193q.049-.101.107-.211l.123-.23l.14-.245l.157-.262l.171-.277l.186-.292l.306-.464l.337-.493l.241-.343l.384-.535l.134-.183l.419-.564l.292-.386l.302-.394l.472-.604l.491-.616l.338-.418l.346-.42l.352-.425l.36-.426l.365-.428l.558-.644l.378-.43l.382-.428l.386-.426l.39-.425c1.896-2.049 3.859-4.036 5.86-6.045l2.013-2.019q1.013-1.015 2.033-2.052m155.187 136.407a106.3 106.3 0 0 1-14.42 16.339l1.12.933a65.15 65.15 0 0 1 20.26 28.96a20.8 20.8 0 0 1 1.325 8.673a9 9 0 0 1-.363 1.95l-.087.271a4.335 4.335 0 0 1-4.907 3.021a13.4 13.4 0 0 1-4.067-1.206a30.8 30.8 0 0 1-5.523-3.498a64.7 64.7 0 0 1-19.627-27.589l-.262-.713l-.357-.922a117 117 0 0 1-15.045 8.426a109 109 0 0 1-16.052 5.85a113 113 0 0 1-17.025 3.24l.078.41l.07.373q.034.178.073.348l.042.168a65.7 65.7 0 0 1 1.787 21.92a48.6 48.6 0 0 1-2.66 13.52a47 47 0 0 1-2.505 5.26a8 8 0 0 1-1.407 1.788c-2.394 2.471-5.253 2.492-7.586-.045a16.4 16.4 0 0 1-2.35-3.379c-1.85-3.474-2.803-7.252-3.483-11.104a70.3 70.3 0 0 1-.818-15.918a57 57 0 0 1 1.568-10.873l.167-.667q.13-.48.224-.969a.3.3 0 0 0-.015-.132l-.067-.23l-.021-.082A105.94 105.94 0 0 1 74.9 236.033l-.56 1.24l-.102.231a66.45 66.45 0 0 1-18.164 24.56l-.423.355a23 23 0 0 1-7.247 4.186a7.27 7.27 0 0 1-3.919.42a4.28 4.28 0 0 1-3.19-2.55l-.06-.15c-.783-1.894-.718-3.859-.478-5.815l.067-.51a33.4 33.4 0 0 1 2.59-8.572a67.7 67.7 0 0 1 15.947-22.714l.556-.52q.139-.129.275-.262q.13-.186.22-.394A107.6 107.6 0 0 1 43.287 206.7l.19-.017l.65-.062c.193-.018.369-.03.545-.03c5.06-.005 10.12.009 15.179-.008l3.794-.018a2.83 2.83 0 0 1 2.228.978a88.4 88.4 0 0 0 23.1 17.07l.6.304a84.2 84.2 0 0 0 29.898 8.887q42.514 4.132 73.142-25.777a4.6 4.6 0 0 1 3.605-1.47c2.947.036 5.896.044 8.844.042l5.896-.01l2.949-.002h1.523zm-26.342-90.316q.559.001 1.109.013l1.093.022c.724.013 1.439.015 2.148-.026l.531-.039c1.802-.163 3.091.481 4.405 1.692c6.323 5.826 12.7 11.592 19.071 17.364l3.966 3.595l.45.4l.675.595l.799-.68q.257-.22.503-.443q11.813-10.753 23.608-21.525a3.28 3.28 0 0 1 2.362-.995l.167.005c1.21.049 2.422.045 3.66.036l1.498-.011q.378-.003.761-.003v63.38a1 1 0 0 1-.097.021l-.14.02l-.18.02l-.22.02l-.255.017l-.447.026l-.897.037l-1.06.032l-1.188.025l-1.817.025l-1.639.01l-1.653.002l-1.082-.005l-1.3-.013l-.978-.015l-.686-.015l-.637-.017l-.762-.028l-.496-.024l-.424-.027l-.239-.02l-.2-.02l-.087-.012l-.141-.022v-32.098l-.323-.153l-16.383 14.942l-16.516-14.879l-.316.116l-.007 2.679v29.509h-16.497c-.016-.053-.031-.251-.046-.58l-.017-.455l-.017-.575l-.024-1.076l-.023-1.314l-.03-2.093l-.033-3.101l-.047-5.884l-.032-5.84l-.027-7.156l-.017-8.245l-.002-5.379l.009-6.732l.013-3.813l.023-4.033l.025-2.81l.025-1.825l.022-1.095l.015-.588l.017-.47l.018-.345q.004-.07.01-.124m-152.24.073h16.398c.013.042.026.18.039.406l.018.404l.018.53l.016.652l.025 1.19l.036 2.508l.027 2.424l.028 3.479l.037 6.391l.017 4.372l.018 8.267l.003 5.583l-.006 6.347l-.01 4.294l-.02 4.749l-.022 3.515l-.035 3.555l-.02 1.461l-.014.844l-.016.732l-.017.616l-.017.494l-.018.365q-.015.226-.03.295H36.996a277 277 0 0 1-.048-6.9l.009-3.445l-.001-1.722l-.004-1.707l-.002-3.422l.003-6.924H16.63v23.904a1 1 0 0 1-.116.027l-.162.026l-.205.024l-.383.035l-.303.021l-.706.04l-.61.025l-.904.03l-.737.02l-1.043.02l-1.66.022l-1.716.01H6.66l-1.384-.009l-1.551-.022l-.939-.02l-.84-.027l-.553-.024l-.329-.017l-.293-.019l-.367-.03l-.103-.011l-.172-.023a2 2 0 0 1-.128-.024v-63.324h16.564v22.37a1 1 0 0 0 .096.02l.138.021l.177.02l.336.028l.415.026l.665.033l.576.022l.635.02l1.423.036l1.864.032l1.135.014l1.761.014l1.789.006l2.343-.005l1.395-.01l1.308-.017l1.193-.022l1.05-.028l.88-.035l.433-.023l.357-.026l.192-.02q.042-.003.08-.009l.133-.02l.012-4.084l.003-1.375l.002-2.853zm36.054 63.45v-63.262q.045-.015.18-.028l.22-.018l.14-.01l.525-.025l.682-.025l.826-.024l1.308-.029l1.52-.027l1.704-.025l4.374-.046l2.123-.016l5.002-.026l3.952-.01l4.384.002l3.563.012l2.737.019l1.99.022l1.67.028l.567.013l.732.021l.58.024l.296.017l.22.018l.08.01v13.482c-.528.038-1.073.11-1.619.111l-9.63.007l-11.413-.002v10.487h20.058v13.95H89.9a3.4 3.4 0 0 0-.078.448l-.03.255l-.026.294l-.037.505l-.023.375l-.03.615l-.026.666l-.027.951l-.016.747l-.01.764l-.008 1.028l.003 1.264l.008.73l.009.467l.01.45l.021.632l.026.573l.02.345l.022.311l.025.274l.014.122c.508.035 1.052.102 1.597.103l9.63.007l11.415-.002v13.426zm58.17.024a5 5 0 0 1-.035-.479l-.016-.437l-.015-.562l-.014-.68l-.02-1.231l-.018-1.466l-.03-3.56l-.02-3.516l-.019-5.581l-.01-7.949l.006-10.192l.022-8.11l.03-6.587l.03-4.327l.03-3.08l.026-2.056l.014-.877l.022-1.109l.016-.594l.016-.472l.017-.343q.013-.208.026-.26h16.338v46.775l.503.03l.46.028l.431.023q.21.008.417.009l9.701.005l11.45-.001v16.6z" />
</svg>`)
)

func (h *API) GetAvatar(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		h.GetAvatarFromRequest(w, r, api.Path(r, "kind", ""), api.Path(r, "name", ""))
		return nil, nil
	})
}

func (h *API) GetAvatarFromRequest(w http.ResponseWriter, r *http.Request, kind, name string) {
	ctx := r.Context()
	getopt := GetAvatarOptions{
		IfNoneMatch:  r.Header.Get("If-None-Match"),
		IfMatch:      r.Header.Get("If-Match"),
		CacheControl: r.Header.Get("Cache-Control"),
	}
	if ifmodsince := r.Header.Get("If-Modified-Since"); ifmodsince != "" {
		ifmodsince, err := http.ParseTime(ifmodsince)
		if err != nil {
			http.Error(w, "Invalid If-Modified-Since header", http.StatusBadRequest)
			return
		}
		getopt.IfModifiedSince = ifmodsince
	}
	resp, err := h.AvatarService.GetAvatar(ctx, kind, name, getopt)
	if err != nil {
		if errors.IsNotFound(err) {
			if def, ok := h.DefaultAvatarSVG[kind]; ok {
				w.Header().Set("Content-Type", "image/svg+xml")
				w.Header().Set("Content-Length", strconv.Itoa(len(def)))
				w.Write(def)
				return
			}
			w.WriteHeader(http.StatusNotFound)
			return
		}
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	if resp.ContentType != "" {
		w.Header().Set("Content-Type", resp.ContentType)
	}
	if resp.ContentLength > 0 {
		w.Header().Set("Content-Length", strconv.FormatInt(resp.ContentLength, 10))
	}
	if resp.ETag != "" {
		w.Header().Set("ETag", resp.ETag)
	}
	if !resp.Expires.IsZero() {
		w.Header().Set("Expires", resp.Expires.Format(http.TimeFormat))
	}
	if !resp.LastModified.IsZero() {
		w.Header().Set("Last-Modified", resp.LastModified.Format(http.TimeFormat))
	}
	if resp.CacheControl != "" {
		w.Header().Set("Cache-Control", resp.CacheControl)
	}
	if resp.Location != "" {
		w.Header().Set("Location", resp.Location)
		if resp.HttpCode == 0 {
			resp.HttpCode = http.StatusFound
		}
	}
	if resp.HttpCode != 0 {
		w.WriteHeader(resp.HttpCode)
	}
	if resp.Body != nil {
		defer resp.Body.Close()
		io.Copy(w, resp.Body)
	}
}

func (h *API) SetAvatar(w http.ResponseWriter, r *http.Request) {
	h.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenantname string) (any, error) {
		h.SetAvatarFromRequest(w, r, api.Path(r, "kind", ""), api.Path(r, "name", ""))
		return nil, nil
	})
}

func (h *API) SetAvatarFromRequest(w http.ResponseWriter, r *http.Request, kind, name string) error {
	mf, mfheader, err := r.FormFile("avatar")
	if err != nil {
		return err
	}
	setopts := SetAvatarOptions{
		ContentType:   mfheader.Header.Get("Content-Type"),
		ContentLength: int64(mfheader.Size),
		Modified:      time.Now(),
		Expires:       time.Now().Add(24 * time.Hour),
		Content:       mf,
	}
	return h.AvatarService.SetAvatar(r.Context(), kind, name, setopts)
}

func (l *API) PublicGroup() api.Group {
	return api.
		NewGroup("/avatars").
		Tag("Avatar").
		Route(
			api.GET("/{kind}/{name}").To(l.GetAvatar).Doc("Get avatar"),
		)
}
