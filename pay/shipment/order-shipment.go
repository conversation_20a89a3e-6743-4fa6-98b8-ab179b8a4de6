package shipment

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster/privatenode"
	"xiaoshiai.cn/core/license"
	"xiaoshiai.cn/core/market"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/pay/order"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/tenant"
)

const ShipmentLicenseDirectly = true

func NewOrderShipment(store store.Store, _ store.Store, orderEventQueue queue.Queue, orderService order.OrderService, skusvc product.SKUService, artifact *artifact.ArtifactService) *OrderEventConsumer {
	licenseShipment := &LicenseShipment{
		Store:       store,
		TokenIssuer: license.ArtifactServiceAsRegistryTokenIssuer{ArtifactService: artifact},
	}
	return &OrderEventConsumer{
		orderEventQueue: orderEventQueue,
		orderService:    orderService,
		skus:            skusvc,
		shipments: map[string]OrderItemShipment{
			product.CategoryApplication: licenseShipment,
			product.CategoryPrivateNode: &PrivateNodeClaimShipment{Store: store},
		},
	}
}

type OrderEventConsumer struct {
	orderEventQueue queue.Queue
	orderService    order.OrderService
	skus            product.SKUService
	// category -> shipment
	shipments map[string]OrderItemShipment
}

func (o *OrderEventConsumer) Run(ctx context.Context) error {
	return wait.PollUntilContextCancel(ctx, 10*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log.Info("order event consumer running")
		return false, o.run(ctx)
	})
}

func (o *OrderEventConsumer) Name() string {
	return "OrderEventConsumer"
}

func (o *OrderEventConsumer) run(ctx context.Context) error {
	return o.orderEventQueue.Consume(ctx, func(ctx context.Context, _ string, data []byte) error {
		event := &order.OrderEvent{}
		if err := json.Unmarshal(data, event); err != nil {
			return err
		}
		return o.consume(ctx, event.ID)
	}, queue.ConsumeOptions{})
}

func (o *OrderEventConsumer) consume(ctx context.Context, id string) error {
	od, err := o.orderService.GetOrder(ctx, id, order.GetOrderOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if od.State != order.OrderStatusPhasePaid && od.State != order.OrderStatusPhaseRefunded {
		return nil
	}

	var errs []error
	for _, item := range od.Items {
		skuref := item.SKU
		if skuref.Empty() || skuref.Name == "" {
			continue
		}
		// find shipment
		shipment, ok := o.shipments[skuref.Category]
		if !ok {
			continue
		}
		switch od.Kind {
		case order.OrderKindNew:
			result, err := shipment.OnNew(ctx, od, item)
			if err != nil {
				errs = append(errs, err)
				continue
			}
			if result != nil {
				opt := order.ShipmentOptions{
					Reference: result.Reference,
					From:      result.Start,
					To:        result.End,
				}
				if err := o.orderService.ShipmentOrderItem(ctx, od.Name, item.ID, opt); err != nil {
					errs = append(errs, err)
					continue
				}
			}
		case order.OrderKindRenew:
			result, err := shipment.OnRenew(ctx, od, item)
			if err != nil {
				errs = append(errs, err)
				continue
			}
			if result != nil {
				opt := order.ShipmentOptions{
					Reference: result.Reference,
					From:      result.Start,
					To:        result.End,
				}
				if err := o.orderService.ShipmentOrderItem(ctx, od.Name, item.ID, opt); err != nil {
					errs = append(errs, err)
					continue
				}
			}
		case order.OrderKindRefund:
			if err := shipment.OnRefund(ctx, od, item); err != nil {
				errs = append(errs, err)
				continue
			}
		}
	}
	if len(errs) != 0 {
		return errors.NewAggregate(errs)
	}
	return nil
}

func OrderIDToAlphaNumeric(id string, len int) string {
	h := sha256.New()
	h.Write([]byte(id))
	hash := h.Sum(nil)
	const alphaNumeric = "abcdefghijklmnopqrstuvwxyz0123456789"
	result := make([]byte, len)
	for i := range result {
		result[i] = alphaNumeric[hash[i%sha256.Size]%byte(36)]
	}
	return string(result)
}

const Month = time.Hour * 24 * 30

func CalcStartEndFromNow(months int) (time.Time, time.Time) {
	now := time.Now()
	// start time is next hour
	nexthour := now.Truncate(time.Hour).Add(time.Hour)
	return CalcStartEndFrom(nexthour, months)
}

func CalcStartEndFrom(from time.Time, months int) (time.Time, time.Time) {
	year := from.Year()
	newmonth := from.Month() + time.Month(months)
	for newmonth > 12 {
		newmonth -= 12
		year++
	}
	to := time.Date(year, newmonth, from.Day(), from.Hour(), from.Minute(), from.Second(), from.Nanosecond(), from.Location())
	return from, to
}

type ShipmentResult struct {
	Reference store.ResourcedObjectReference
	Start     time.Time
	End       time.Time
}

type OrderItemShipment interface {
	OnNew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error)
	OnRenew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error)
	OnRefund(ctx context.Context, od *order.Order, item order.OrderItem) error
}

type LicenseShipment struct {
	Store       store.Store
	TokenIssuer license.RegistryTokenIssuer
}

func (l *LicenseShipment) OnNew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error) {
	// 1 quantity = 1 month
	from, to := CalcStartEndFromNow(int(item.Quantity))
	from = from.Add(-time.Hour) // make sure license can be used immediately

	licensereq := license.LicenseRequest{
		ObjectMeta: store.ObjectMeta{
			Name:        od.Name, // use order name as license request name
			Description: "License request auto created from order " + od.Name,
			Annotations: map[string]string{
				events.AnnotationKeyUsers: strings.Join([]string{od.Creator}, ","),
				base.AnnotationCreator:    od.Creator,
				base.AnnotationOrderID:    od.Name,
				base.AnnotationSKUID:      item.SKU.Name,
			},
		},
		Request: license.LicenseRequestData{
			NotBefore: from,
			NotAfter:  to,
			Content:   market.AddtionalToContent(item.SKUAdditional),
		},
		Application: item.SKU.Product,
	}
	licensestorescopes := []store.Scope{}
	if tenantname := od.Tenant; tenantname != "" {
		tenant := &tenant.Tenant{}
		if err := l.Store.Get(ctx, od.Tenant, tenant); err != nil {
			return nil, err
		}
		licensereq.Request.Email = tenant.Email
		licensereq.Request.Subject = tenant.Name
		licensestorescopes = append(licensestorescopes, base.ScopeTenant(od.Tenant))
		if orgname := od.Organization; orgname != "" {
			licensereq.Request.Subject = orgname
			licensestorescopes = append(licensestorescopes, base.ScopeOrganization(orgname))
		}
	}
	if ShipmentLicenseDirectly {
		licensereq.Scopes = licensestorescopes
		if err := license.ApproveLicenseRequest(ctx, l.Store, &licensereq, l.TokenIssuer, true); err != nil {
			return nil, err
		}
		result := &ShipmentResult{
			Reference: licensereq.Status.LicenseReference,
			Start:     licensereq.Request.NotBefore,
			End:       licensereq.Request.NotAfter,
		}
		return result, nil
	}

	licensestore := l.Store.Scope(licensestorescopes...)
	if err := licensestore.Create(ctx, &licensereq); err != nil {
		if !errors.IsAlreadyExists(err) {
			return nil, err
		}
		exists := &license.LicenseRequest{}
		if err := licensestore.Get(ctx, licensereq.Name, exists); err != nil {
			return nil, err
		}
		licensereq = *exists
	}
	result := &ShipmentResult{
		Reference: store.ResourcedObjectReferenceFrom(&licensereq),
		Start:     licensereq.Request.NotBefore,
		End:       licensereq.Request.NotAfter,
	}
	return result, nil
}

func (l *LicenseShipment) OnRefund(ctx context.Context, od *order.Order, item order.OrderItem) error {
	return nil
}

func (l *LicenseShipment) OnRenew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error) {
	return nil, nil
}

type PrivateNodeClaimShipment struct {
	Store store.Store
}

func (p *PrivateNodeClaimShipment) OnNew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error) {
	tenant := &tenant.Tenant{}
	if err := p.Store.Get(ctx, od.Tenant, tenant); err != nil {
		return nil, err
	}
	// use the product name as the cluster name in this case
	clustername, model := item.SKU.Product, item.SKU.Name

	claisstore := p.Store
	if od.Tenant != "" {
		claisstore = claisstore.Scope(base.ScopeTenant(od.Tenant))
	}

	from, to := CalcStartEndFromNow(int(item.Quantity))

	// create a private node claim for the tenant
	cliam := &privatenode.PrivateNodeClaim{
		ObjectMeta: store.ObjectMeta{
			Name: "node-" + OrderIDToAlphaNumeric(item.ID, 8),
			Annotations: map[string]string{
				base.AnnotationCreator:     od.Creator,
				base.AnnotationOrderItemID: item.ID,
				base.AnnotationOrderID:     od.Name,
				base.AnnotationSKUID:       item.SKU.Name,
			},
			Labels: map[string]string{},
		},
		Cluster: store.ObjectReference{Name: clustername},
		Model:   model,
		Expire:  store.Time{Time: to},
	}
	if renewalpolicy := mapvalue(item.Params, order.ParamRenewalPolicy); renewalpolicy != "" {
		cliam.Labels[base.LabelRenewalPolicy] = renewalpolicy
	}
	if err := claisstore.Create(ctx, cliam); err != nil {
		if !errors.IsAlreadyExists(err) {
			return nil, err
		}
		exists := &privatenode.PrivateNodeClaim{}
		if err := claisstore.Get(ctx, cliam.Name, exists); err != nil {
			return nil, err
		}
		cliam = exists
	}
	result := &ShipmentResult{
		Reference: store.ResourcedObjectReferenceFrom(cliam),
		Start:     cliam.Expire.Time.Add(from.Sub(to)),
		End:       cliam.Expire.Time,
	}
	return result, nil
}

func mapvalue(m map[string]string, key string) string {
	if m == nil {
		return ""
	}
	return m[key]
}

func (p *PrivateNodeClaimShipment) OnRenew(ctx context.Context, od *order.Order, item order.OrderItem) (*ShipmentResult, error) {
	exists := &privatenode.PrivateNodeClaim{}
	if err := p.Store.Scope(item.Reference.Scopes...).Get(ctx, item.Reference.Name, exists); err != nil {
		if errors.IsNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	var from, to time.Time
	// if the node expired, we need to set the start time to now
	// so that the new expiration time is from now
	if now := time.Now(); now.After(exists.Expire.Time) {
		from, to = CalcStartEndFromNow(int(item.Quantity))
	} else {
		from, to = CalcStartEndFrom(exists.Expire.Time, int(item.Quantity))
	}
	exists.Expire = store.Time{Time: to}
	if err := p.Store.Scope(exists.Scopes...).Update(ctx, exists); err != nil {
		return nil, err
	}
	result := &ShipmentResult{
		Reference: store.ResourcedObjectReferenceFrom(exists),
		Start:     from,
		End:       to,
	}
	return result, nil
}

func (p *PrivateNodeClaimShipment) OnRefund(ctx context.Context, od *order.Order, item order.OrderItem) error {
	if item.Reference.Name == "" {
		return nil
	}
	cliam := &privatenode.PrivateNodeClaim{
		ObjectMeta: store.ObjectMeta{
			Name: item.Reference.Name,
		},
	}
	if err := p.Store.Scope(item.Reference.Scopes...).Delete(ctx, cliam); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	return nil
}
