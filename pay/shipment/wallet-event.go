package shipment

import (
	"context"
	"encoding/json"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/wallet"
)

func NewWalletEventConsumer(store store.Store, walletService wallet.WalletService, events queue.Queue) *WalletEventConsumer {
	return &WalletEventConsumer{
		events: events,
	}
}

type WalletEventConsumer struct {
	events queue.Queue
}

func (w *WalletEventConsumer) Name() string {
	return "wallet-event-consumer"
}

func (w *WalletEventConsumer) Run(ctx context.Context) error {
	return wait.PollUntilContextCancel(ctx, 10*time.Second, true, func(ctx context.Context) (done bool, err error) {
		log.Info("order event consumer running")
		return false, w.run(ctx)
	})
}

func (w *WalletEventConsumer) run(ctx context.Context) error {
	return w.events.Consume(ctx, func(ctx context.Context, _ string, data []byte) error {
		event := &wallet.WalletEvent{}
		if err := json.Unmarshal(data, event); err != nil {
			return err
		}
		return w.consume(ctx, event)
	}, queue.ConsumeOptions{})
}

func (w *WalletEventConsumer) consume(ctx context.Context, event *wallet.WalletEvent) error {
	if event.Overdue {
		return w.onTenantWalletOverDue(ctx, event.Wallet)
	} else {
		return w.onTenantWalletRestore(ctx, event.Wallet)
	}
}

func (w *WalletEventConsumer) onTenantWalletOverDue(ctx context.Context, tenant string) error {
	//
	return nil
}

func (w *WalletEventConsumer) onTenantWalletRestore(ctx context.Context, tenant string) error {
	return nil
}
