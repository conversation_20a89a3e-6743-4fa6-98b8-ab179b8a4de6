package wallet

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/payment"
	"xiaoshiai.cn/core/wallet"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&WalletPayment{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"account"},
				{"orderID"},
			},
		})
	mongo.GlobalObjectsScheme.Register(&WalletRefund{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"account"},
				{"orderID"},
				{"paymentID"},
			},
		})
}

type QueueCallback struct {
	Queue queue.Queue
}

type QueuePaymentEvent struct {
	Payment *payment.PaymentEvent `json:"payment,omitempty"`
	Refund  *payment.RefundEvent  `json:"refund,omitempty"`
}

func ProcessQueueEvent(ctx context.Context, data []byte, callback payment.PaymentServiceCallback) error {
	event := &QueuePaymentEvent{}
	if err := json.Unmarshal(data, event); err != nil {
		return err
	}
	if event.Payment != nil {
		return callback.OnPaymentEvent(ctx, *event.Payment)
	} else if event.Refund != nil {
		return callback.OnRefundEvent(ctx, *event.Refund)
	} else {
		return nil
	}
}

func (q *QueueCallback) OnPaymentEvent(ctx context.Context, callback payment.PaymentEvent) error {
	data, err := json.Marshal(QueuePaymentEvent{Payment: &callback})
	if err != nil {
		return err
	}
	return q.Queue.Enqueue(ctx, "", data, queue.EnqueueOptions{})
}

func (q *QueueCallback) OnRefundEvent(ctx context.Context, callback payment.RefundEvent) error {
	data, err := json.Marshal(QueuePaymentEvent{Refund: &callback})
	if err != nil {
		return err
	}
	return q.Queue.Enqueue(ctx, "", data, queue.EnqueueOptions{})
}

type WalletPayment struct {
	store.ObjectMeta     `json:",inline"`
	Account              string               `json:"account,omitempty"`
	OrderID              string               `json:"orderID,omitempty"`
	AutoDebitAgreementID string               `json:"autoDebitAgreementID,omitempty"`
	GoodName             string               `json:"goodName,omitempty"`
	Amount               base.Price           `json:"amount,omitempty"`
	Currency             string               `json:"currency,omitempty"`
	ExpireAt             *time.Time           `json:"expireAt,omitempty"`
	State                payment.PaymentState `json:"state,omitempty"`
	Payer                string               `json:"payer,omitempty"`
	PaiedTime            *time.Time           `json:"paiedTime,omitempty"`
	Message              string               `json:"message,omitempty"`
	Organization         string               `json:"organization,omitempty"`
}

type WalletRefund struct {
	store.ObjectMeta `json:",inline"`
	Account          string              `json:"account,omitempty"`
	OrderID          string              `json:"orderID,omitempty"`
	GoodName         string              `json:"goodName,omitempty"`
	Operator         string              `json:"operator,omitempty"`
	PaymentID        string              `json:"paymentID,omitempty"`
	Amount           base.Price          `json:"amount,omitempty"`
	Reason           string              `json:"reason,omitempty"`
	Currency         string              `json:"currency,omitempty"`
	State            payment.RefundState `json:"state,omitempty"`
	RefundTime       time.Time           `json:"refundTime,omitempty"`
	Message          string              `json:"message,omitempty"`
	Organization     string              `json:"organization,omitempty"`
}

func NewWalletPaymentServiceQueueCallback(wallet wallet.WalletService, mongo store.TransactionStore, eventsqueue queue.Queue) *WalletPaymentService {
	return NewWalletPaymentService(wallet, mongo, &QueueCallback{Queue: eventsqueue})
}

func NewWalletPaymentService(wallet wallet.WalletService, mongo store.TransactionStore, callback payment.PaymentServiceCallback) *WalletPaymentService {
	return &WalletPaymentService{
		Wallet:   wallet,
		Mongo:    mongo,
		Callback: callback,
	}
}

type WalletPaymentService struct {
	Wallet   wallet.WalletService
	Mongo    store.TransactionStore
	Callback payment.PaymentServiceCallback
}

func NewPaymentID() string {
	return time.Now().Format("**************") + rand.RandomNumeric(12)
}

// CreatePayment implements payment.PaymentService.
func (w *WalletPaymentService) CreatePayment(ctx context.Context, account string, orderid string, amount payment.Amount, options payment.CreatePaymentOptions) (*payment.Payment, error) {
	// check account exists
	if _, err := w.Wallet.Get(ctx, account); err != nil {
		return nil, err
	}
	if options.Timeout == 0 {
		options.Timeout = 10 * time.Minute
	}
	walletpayment := &WalletPayment{
		ObjectMeta: store.ObjectMeta{
			Name:        NewPaymentID(),
			Description: options.Description,
		},
		Payer:                options.Creator,
		GoodName:             options.GoodName,
		Account:              account,
		Organization:         options.Organization,
		OrderID:              orderid,
		AutoDebitAgreementID: options.AutoDebitAgreementID,
		Amount:               amount.Total,
		Currency:             amount.Currency,
		ExpireAt:             ptr.To(time.Now().Add(options.Timeout)),
		State:                payment.PaymentStateNotPaid,
	}
	if err := w.Mongo.Create(ctx, walletpayment); err != nil {
		return nil, err
	}
	if w.Callback != nil {
		if err := w.Callback.OnPaymentEvent(ctx, walletPaymentToPaymentStatus(walletpayment)); err != nil {
			log.FromContext(ctx).Error(err, "callback payment status")
		}
	}
	return walletPaymentToPayment(walletpayment), nil
}

func walletPaymentToPayment(walletpayment *WalletPayment) *payment.Payment {
	return &payment.Payment{
		ID:                walletpayment.Name,
		Description:       walletpayment.Description,
		CreationTimestamp: walletpayment.CreationTimestamp.Time,
		GoodName:          walletpayment.GoodName,
		PaiedTime:         walletpayment.PaiedTime,
		Payer:             walletpayment.Payer,
		OrderID:           walletpayment.OrderID,
		Amount:            walletpayment.Amount,
		Currency:          walletpayment.Currency,
		State:             walletpayment.State,
	}
}

func walletPaymentToPaymentStatus(walletpayment *WalletPayment) payment.PaymentEvent {
	return payment.PaymentEvent{
		PaymentID: walletpayment.Name,
		State:     walletpayment.State,
		OrderID:   walletpayment.OrderID,
		PaiedTime: walletpayment.PaiedTime,
		Payer:     walletpayment.Payer,
		Message:   walletpayment.Message,
		Success:   walletpayment.State == payment.PaymentStatePaid,
	}
}

// ClosePayment implements payment.PaymentService.
func (w *WalletPaymentService) ClosePayment(ctx context.Context, paymentID string) error {
	patch := store.MapMergePatch{
		"state": payment.PaymentStateClosed,
	}
	wp := &WalletPayment{ObjectMeta: store.ObjectMeta{Name: paymentID}}
	if err := w.Mongo.Patch(ctx, wp, patch); err != nil {
		return err
	}
	w.sendPaymentEvent(ctx, walletPaymentToPaymentStatus(wp))
	return nil
}

// GetPayment implements payment.PaymentService.
func (w *WalletPaymentService) GetPayment(ctx context.Context, paymentID string) (*payment.Payment, error) {
	walletpayment := &WalletPayment{}
	if err := w.Mongo.Get(ctx, paymentID, walletpayment); err != nil {
		return nil, err
	}
	return walletPaymentToPayment(walletpayment), nil
}

// GetPaymentByOrder implements payment.PaymentService.
func (w *WalletPaymentService) GetPaymentByOrder(ctx context.Context, orderid string) (*payment.Payment, error) {
	list := store.List[WalletPayment]{}
	if err := w.Mongo.List(ctx, &list, store.WithFieldRequirements(store.RequirementEqual("orderID", orderid))); err != nil {
		return nil, err
	}
	if len(list.Items) == 0 {
		return nil, nil
	}
	return walletPaymentToPayment(&list.Items[0]), nil
}

// PaymentStatus implements payment.PaymentService.
func (w *WalletPaymentService) PaymentStatus(ctx context.Context, paymentID string) (*payment.PaymentEvent, error) {
	walletpayment := &WalletPayment{}
	if err := w.Mongo.Get(ctx, paymentID, walletpayment); err != nil {
		return nil, err
	}
	return &payment.PaymentEvent{
		State:     walletpayment.State,
		OrderID:   walletpayment.OrderID,
		PaiedTime: walletpayment.PaiedTime,
		Payer:     walletpayment.Payer,
		Message:   walletpayment.Message,
		Success:   walletpayment.State == payment.PaymentStatePaid,
	}, nil
}

// RevokeAutoDebitAgreement implements payment.PaymentService.
func (w *WalletPaymentService) RevokeAutoDebitAgreement(ctx context.Context, aggreementID string) error {
	return errors.NewUnsupported("wallet payment does not support auto debit")
}

// SignAutoDebitAgreement implements payment.PaymentService.
func (w *WalletPaymentService) SignAutoDebitAgreement(ctx context.Context, options payment.SignAutoDebitOptions) (*payment.AutoDebitAgreement, error) {
	return nil, errors.NewUnsupported("wallet payment does not support auto debit")
}

// SignAutoDebitAgreement implements payment.PaymentService.
func (w *WalletPaymentService) GetAutoDebitAgreement(ctx context.Context, aggreementID string) (*payment.AutoDebitAgreement, error) {
	return nil, errors.NewUnsupported("wallet payment does not support auto debit")
}

func (w *WalletPaymentService) Pay(ctx context.Context, account string, paymentID string, options payment.PayPaymentOptions) error {
	walletpayment := &WalletPayment{}
	if err := w.Mongo.Get(ctx, paymentID, walletpayment, store.WithGetFieldRequirements(store.RequirementEqual("account", account))); err != nil {
		return err
	}

	txnctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	purchaseOptions := wallet.PurchaseDetails{
		PaymentID:     walletpayment.Name,
		Amount:        walletpayment.Amount,
		OrderID:       walletpayment.OrderID,
		Summary:       walletpayment.GoodName,
		Operator:      options.Operator,
		Organization:  walletpayment.Organization,
		AllowNegative: options.AllowNegative,
	}
	transaction, err := w.Wallet.Purchase(ctx, account, purchaseOptions)
	if err != nil {
		if err == wallet.ErrPaymentIDAlreadyExists {
			existstransaction, err := w.Wallet.GetTransactionByPaymentID(ctx, account, paymentID)
			if err != nil {
				return err
			}
			transaction = existstransaction
		} else {
			return err
		}
	}
	patch := store.MapMergePatch{
		"state":     payment.PaymentStatePaid,
		"paiedTime": transaction.CreationTimestamp.Time,
		"payer":     transaction.Operator,
	}
	if err := w.Mongo.Patch(ctx, walletpayment, patch); err != nil {
		return err
	}
	w.sendPaymentEvent(txnctx, walletPaymentToPaymentStatus(walletpayment))
	return nil
}

func (w *WalletPaymentService) CreateRefund(ctx context.Context, paymentID string, orderid string, amount payment.Amount, options payment.RefundOptions) (*payment.Refund, error) {
	walletpayment := &WalletPayment{}
	if err := w.Mongo.Get(ctx, paymentID, walletpayment); err != nil {
		return nil, err
	}
	account := walletpayment.Account

	if amount.Total.IsNegative() {
		return nil, errors.NewInvalid("payment", paymentID, fmt.Errorf("refund amount is negative"))
	}
	if amount.Total.GreaterThan(walletpayment.Amount) {
		return nil, errors.NewInvalid("payment", paymentID, fmt.Errorf("refund amount is greater than payment amount"))
	}

	walletrefund := &WalletRefund{
		ObjectMeta: store.ObjectMeta{
			Name:        NewPaymentID(),
			Description: options.Reason,
		},
		GoodName:     walletpayment.GoodName,
		PaymentID:    paymentID,
		Account:      account,
		OrderID:      orderid,
		Operator:     options.Operator,
		Amount:       amount.Total,
		Currency:     amount.Currency,
		Organization: options.Organization,
		Reason:       options.Reason,
		State:        payment.RefundStatePending,
	}
	if err := w.Mongo.Create(ctx, walletrefund); err != nil {
		return nil, err
	}
	return ptr.To(walletRefundToRefund(walletrefund)), nil
}

func walletRefundToRefund(wf *WalletRefund) payment.Refund {
	return payment.Refund{
		ID:                wf.Name,
		PaymentID:         wf.PaymentID,
		OrderID:           wf.OrderID,
		Amount:            wf.Amount,
		CreationTimestamp: wf.CreationTimestamp.Time,
		Reason:            wf.Description,
		RefundTime:        wf.RefundTime,
	}
}

func walletRefundToRefundEvent(wf *WalletRefund) payment.RefundEvent {
	return payment.RefundEvent{
		ID:         wf.Name,
		PaymentID:  wf.PaymentID,
		OrderID:    wf.OrderID,
		State:      wf.State,
		Message:    wf.Message,
		RefundTime: wf.RefundTime,
	}
}

// ListRefundByOrder implements payment.PaymentService.
func (w *WalletPaymentService) ListRefundByOrder(ctx context.Context, orderid string) ([]payment.Refund, error) {
	list := store.List[WalletRefund]{}
	if err := w.Mongo.List(ctx, &list, store.WithFieldRequirements(store.RequirementEqual("orderID", orderid))); err != nil {
		return nil, err
	}
	refunds := make([]payment.Refund, len(list.Items))
	for i, item := range list.Items {
		refunds[i] = walletRefundToRefund(&item)
	}
	return refunds, nil
}

// GetRefund implements payment.PaymentService.
func (w *WalletPaymentService) GetRefund(ctx context.Context, refundID string) (*payment.Refund, error) {
	walletrefund := &WalletRefund{}
	if err := w.Mongo.Get(ctx, refundID, walletrefund); err != nil {
		return nil, err
	}
	return ptr.To(walletRefundToRefund(walletrefund)), nil
}

// RefundStatus implements payment.PaymentService.
func (w *WalletPaymentService) RefundStatus(ctx context.Context, refundID string) (*payment.RefundEvent, error) {
	walletrefund := &WalletRefund{}

	if err := w.Mongo.Get(ctx, refundID, walletrefund); err != nil {
		return nil, err
	}
	return ptr.To(walletRefundToRefundEvent(walletrefund)), nil
}

func (w *WalletPaymentService) sendPaymentEvent(ctx context.Context, event payment.PaymentEvent) {
	if w.Callback == nil {
		return
	}
	if err := w.Callback.OnPaymentEvent(ctx, event); err != nil {
		log.FromContext(ctx).Error(err, "callback payment event")
	}
}

func (w *WalletPaymentService) sendRefundEvent(ctx context.Context, event payment.RefundEvent) {
	if w.Callback == nil {
		return
	}
	if err := w.Callback.OnRefundEvent(ctx, event); err != nil {
		log.FromContext(ctx).Error(err, "callback refund event")
	}
}

var _ payment.PaymentService = &WalletPaymentService{}
