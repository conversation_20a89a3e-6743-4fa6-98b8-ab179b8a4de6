package payment

import (
	"context"
	"time"

	"xiaoshiai.cn/core/base"
)

type Price = base.Price

type Amount struct {
	Total    Price  `json:"total,omitempty"`
	Currency string `json:"currency,omitempty"`
}

const DefaultAutoDebitAgreementID = "builtin-auto-debit-agreement"

type CreatePaymentOptions struct {
	// AutoDebitAgreementID is the id of the auto debit agreement.
	// If set, the payment will be paid by the auto debit agreement.
	AutoDebitAgreementID string         `json:"autoDebitAgreementID,omitempty"`
	GoodName             string         `json:"goodName,omitempty"`
	Description          string         `json:"description,omitempty"`
	Timeout              time.Duration  `json:"timeout,omitempty"`
	Goods                []PaymentGoods `json:"goods,omitempty"`
	Creator              string         `json:"creator,omitempty"`
	Organization         string         `json:"organization,omitempty"`
}

type PaymentGoods struct {
	ID        string  `json:"id,omitempty"`
	Name      string  `json:"name,omitempty"`
	Quantity  float64 `json:"quantity,omitempty"`
	UnitPrice Price   `json:"unitPrice,omitempty"`
}

type Payment struct {
	ID                string       `json:"id,omitempty"`
	CreationTimestamp time.Time    `json:"creationTimestamp,omitempty"`
	GoodName          string       `json:"goodName,omitempty"`
	Description       string       `json:"description,omitempty"`
	OrderID           string       `json:"orderID,omitempty"`
	Amount            Price        `json:"amount,omitempty"`
	Currency          string       `json:"currency,omitempty"`
	PaiedTime         *time.Time   `json:"paiedTime,omitempty"`
	Payer             string       `json:"payer,omitempty"`
	State             PaymentState `json:"state,omitempty"`
}

type PaymentState string

const (
	PaymentStateNotPaid PaymentState = "NotPaid"
	PaymentStatePaid    PaymentState = "Paid"
	PaymentStateClosed  PaymentState = "Closed"
	PaymentStateRefund  PaymentState = "Refund"
	PaymentStateError   PaymentState = "Error"
)

type PaymentEvent struct {
	PaymentID string       `json:"paymentID,omitempty"`
	State     PaymentState `json:"state,omitempty"`
	OrderID   string       `json:"orderID,omitempty"`
	PaiedTime *time.Time   `json:"paiedTime,omitempty"`
	Payer     string       `json:"payer,omitempty"`
	Message   string       `json:"message,omitempty"`
	Success   bool         `json:"success,omitempty"`
}

type RefundOptions struct {
	Reason       string `json:"reason,omitempty"`
	Operator     string `json:"operator,omitempty"`
	Organization string `json:"organization,omitempty"`
}

type PaymentServiceCallback interface {
	OnPaymentEvent(ctx context.Context, status PaymentEvent) error
	OnRefundEvent(ctx context.Context, status RefundEvent) error
}

type SignAutoDebitOptions struct {
	Description string        `json:"description,omitempty"`
	Period      time.Duration `json:"period,omitempty"`
}

type AutoDebitAgreement struct {
	ID                string         `json:"id,omitempty"`
	Channel           Channel        `json:"channel,omitempty"`
	Description       string         `json:"description,omitempty"`
	CreationTimestamp time.Time      `json:"creationTimestamp,omitempty"`
	Period            time.Duration  `json:"period,omitempty"`
	State             AutoDebitState `json:"state,omitempty"`
	Enabled           bool           `json:"enabled,omitempty"`
}

type AutoDebitState string

const (
	AutoDebitStateUnknown AutoDebitState = "Unknown"
	AutoDebitStateSigned  AutoDebitState = "Signed"
	AutoDebitStateRevoked AutoDebitState = "Revoked"
)

type Refund struct {
	ID                string      `json:"id,omitempty"`
	PaymentID         string      `json:"paymentID,omitempty"`
	OrderID           string      `json:"orderID,omitempty"`
	Amount            Price       `json:"amount,omitempty"`
	CreationTimestamp time.Time   `json:"creationTimestamp,omitempty"`
	Reason            string      `json:"reason,omitempty"`
	RefundTime        time.Time   `json:"refundTime,omitempty"`
	Currency          string      `json:"currency,omitempty"`
	State             RefundState `json:"state,omitempty"`
}

type RefundEvent struct {
	ID         string      `json:"id,omitempty"`
	PaymentID  string      `json:"paymentID,omitempty"`
	OrderID    string      `json:"orderID,omitempty"`
	RefundTime time.Time   `json:"refundTime,omitempty"`
	State      RefundState `json:"state,omitempty"`
	Message    string      `json:"message,omitempty"`
}

type RefundState string

const (
	RefundStatePending RefundState = "Pending"
	RefundStateSuccess RefundState = "Success"
	RefundStateFailed  RefundState = "Failed"
)

type PaymentService interface {
	// CreatePayment creates and pay a payment by channel.
	CreatePayment(ctx context.Context, account string, orderid string, amount Amount, options CreatePaymentOptions) (*Payment, error)

	// GetPayment gets a payment.
	GetPayment(ctx context.Context, paymentID string) (*Payment, error)
	GetPaymentByOrder(ctx context.Context, orderid string) (*Payment, error)
	PaymentStatus(ctx context.Context, paymentID string) (*PaymentEvent, error)
	ClosePayment(ctx context.Context, paymentID string) error

	// Refund refunds a payment.
	CreateRefund(ctx context.Context, paymentID string, orderid string, amount Amount, options RefundOptions) (*Refund, error)
	GetRefund(ctx context.Context, refundID string) (*Refund, error)
	ListRefundByOrder(ctx context.Context, orderid string) ([]Refund, error)
	RefundStatus(ctx context.Context, refundID string) (*RefundEvent, error)

	// Auto debit Sign
	SignAutoDebitAgreement(ctx context.Context, options SignAutoDebitOptions) (*AutoDebitAgreement, error)
	GetAutoDebitAgreement(ctx context.Context, aggreementID string) (*AutoDebitAgreement, error)
	RevokeAutoDebitAgreement(ctx context.Context, paymentID string) error
}

type PayPaymentOptions struct {
	Operator      string
	AllowNegative bool
}

type PayablePaymentService interface {
	PaymentService
	Pay(ctx context.Context, account string, paymentID string, options PayPaymentOptions) error
}
