package order

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"
	"unicode"

	"k8s.io/utils/ptr"
	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/common/txn"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/payment"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/pay/promotion"
	"xiaoshiai.cn/core/pay/resourcemetadata"
	"xiaoshiai.cn/core/wallet"
)

const (
	OrderRound      = 2
	OrderTotalRound = 2
)

const (
	ParamRenewalPolicy = "renewalPolicy"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&Order{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"creationTimestamp"},
				{"deleted"},
				{"tenant"},
				{"creator"},
				{"organization"},
			},
		})
}

var _ OrderService = &LocalOrderService{}

type DiscountRequest struct {
	Tenant       string      `json:"tenant"`
	Organization string      `json:"organization"`
	Kind         OrderKind   `json:"kind"`
	Items        []OrderItem `json:"items"`
}

type StoreOrderOptions struct {
	OrderTimeout time.Duration
}

func NewLocalOrderOptions() *StoreOrderOptions {
	return &StoreOrderOptions{
		OrderTimeout: 10 * time.Minute,
	}
}

type QueueAsOderServiceCallback struct {
	Queue queue.Queue
}

func (q *QueueAsOderServiceCallback) Callback(ctx context.Context, event OrderEvent) error {
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}
	return q.Queue.Enqueue(ctx, "", data, queue.EnqueueOptions{})
}

func NewLocalOrderService(store store.Store, mongo store.TransactionStore, paymentman *payment.PaymentChannelManager, discount promotion.DiscountService, wallet wallet.WalletService, skus product.SKUService, orderEventQueue queue.Queue, productmeta resourcemetadata.ResourceMetadataService, opts *StoreOrderOptions) *LocalOrderService {
	return &LocalOrderService{
		Store:                 store,
		Mongo:                 mongo,
		Options:               opts,
		SKU:                   skus,
		Wallet:                wallet,
		Callback:              &QueueAsOderServiceCallback{Queue: orderEventQueue},
		DiscountService:       discount,
		PaymentChannelManager: paymentman,
		ResourceMetadata:      productmeta,
	}
}

type LocalOrderService struct {
	Store                 store.Store
	Options               *StoreOrderOptions
	Mongo                 store.TransactionStore
	SKU                   product.SKUService
	Wallet                wallet.WalletService
	Callback              OrderServiceCallback
	DiscountService       promotion.DiscountService
	PaymentChannelManager *payment.PaymentChannelManager
	ResourceMetadata      resourcemetadata.ResourceMetadataService
}

// OnRefundEvent implements OrderService.
func (s *LocalOrderService) OnRefundEvent(ctx context.Context, event payment.RefundEvent) error {
	order := &Order{}
	if err := s.Mongo.Get(ctx, event.OrderID, order); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if event.PaymentID != "" && order.Payment.ID != event.ID {
		return nil
	}
	switch event.State {
	case payment.RefundStateFailed:
		patch := store.MapMergePatch{
			"state":   OrderStatusPhaseError,
			"message": event.Message,
		}
		if err := s.Mongo.Patch(ctx, order, patch); err != nil {
			return err
		}
		if s.Callback != nil {
			if err := s.Callback.Callback(ctx, orderToOrderEvent(order)); err != nil {
				log.FromContext(ctx).Error(err, "callback order event")
			}
		}
	case payment.RefundStateSuccess:
		if order.State == OrderStatusPhaseRefunded {
			return nil
		}
		patch := store.MapMergePatch{
			"state":    OrderStatusPhaseRefunded,
			"paidTime": event.RefundTime,
			"payment": map[string]any{
				"paid":     true,
				"paidTime": event.RefundTime,
			},
		}
		if err := s.Mongo.Patch(ctx, order, patch); err != nil {
			return err
		}
		if s.Callback != nil {
			if err := s.Callback.Callback(ctx, orderToOrderEvent(order)); err != nil {
				log.FromContext(ctx).Error(err, "callback order event")
			}
		}
	}
	return nil
}

func orderToOrderEvent(order *Order) OrderEvent {
	return OrderEvent{
		ID:    order.Name,
		Kind:  order.Kind,
		Phase: order.State,
	}
}

// OnPaymentEvent implements OrderService.
func (s *LocalOrderService) OnPaymentEvent(ctx context.Context, event payment.PaymentEvent) error {
	order := &Order{}
	if err := s.Mongo.Get(ctx, event.OrderID, order); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	switch event.State {
	case payment.PaymentStateClosed:
		// when a payment is closed, order can reopen another payment
		if order.Payment.ID == event.PaymentID {
			// clean the payment info
			patch := store.MapMergePatch{
				"payment": map[string]any{
					"channel": "",
					"id":      "",
				},
			}
			if err := s.Mongo.Patch(ctx, order, patch); err != nil {
				return err
			}
		}
	case payment.PaymentStatePaid:
		if order.State == OrderStatusPhasePaid {
			return nil
		}
		patch := store.MapMergePatch{
			"state":    OrderStatusPhasePaid,
			"paidTime": event.PaiedTime,
			"payment": map[string]any{
				"paid":     true,
				"paidTime": event.PaiedTime,
			},
		}
		if err := s.Mongo.Patch(ctx, order, patch); err != nil {
			return err
		}
		if s.Callback != nil {
			if err := s.Callback.Callback(ctx, OrderEvent{ID: order.Name, Phase: OrderStatusPhasePaid}); err != nil {
				log.FromContext(ctx).Error(err, "callback order event")
			}
		}
	}
	return nil
}

// CloseOrder implements OrderService.
func (s *LocalOrderService) CloseOrder(ctx context.Context, id string, options CloseOrderOptions) error {
	order, err := s.GetOrder(ctx, id, GetOrderOptions{Tenant: options.Tenant, Organization: options.Organization})
	if err != nil {
		return err
	}
	if order.State != OrderStatusPhaseNotPaid {
		return errors.NewBadRequest(fmt.Sprintf("can't close %s order", order.State))
	}
	if err := s.closePaymentIfExists(ctx, order); err != nil {
		return err
	}
	patch := store.MapMergePatch{
		"state":      OrderStatusPhaseClosed,
		"closedTime": time.Now(),
	}
	if err := s.Mongo.Patch(ctx, order, patch); err != nil {
		return err
	}
	if s.Callback != nil {
		if err := s.Callback.Callback(ctx, OrderEvent{ID: order.Name, Phase: OrderStatusPhaseClosed}); err != nil {
			log.FromContext(ctx).Error(err, "callback order event")
		}
	}
	return nil
}

func (s *LocalOrderService) closePaymentIfExists(ctx context.Context, order *Order) error {
	channel, paymentid := order.Payment.Channel, order.Payment.ID
	if paymentid == "" {
		return nil
	}
	svc, err := s.PaymentChannelManager.GetPaymentService(ctx, payment.Channel(channel))
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	paymentstatus, err := svc.PaymentStatus(ctx, paymentid)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	if paymentstatus.State == payment.PaymentStatePaid {
		return errors.NewBadRequest("can't close paid order")
	}
	if paymentstatus.State == payment.PaymentStateClosed {
		return nil
	}
	log := log.FromContext(ctx)
	log.Info("close payment", "payment", paymentid)
	if err := svc.ClosePayment(ctx, paymentid); err != nil {
		log.Error(err, "failed to close payment", "payment", paymentid)
		return err
	}
	return nil
}

type OrderEvent struct {
	ID    string     `json:"id"`
	Kind  OrderKind  `json:"kind"`
	Phase OrderState `json:"phase"`
}

func NewOrderID() string {
	return "order-" + time.Now().Format("20060102150405") + rand.RandomNumeric(6)
}

func NewOrderItemID() string {
	return rand.RandomNumeric(12)
}

func (s *LocalOrderService) DeleteOrder(ctx context.Context, id string, options DeleteOrderOptions) error {
	order, err := s.GetOrder(ctx, id, GetOrderOptions{})
	if err != nil {
		return err
	}
	if order.State == OrderStatusPhaseNotPaid {
		if err := s.CloseOrder(ctx, id, CloseOrderOptions{}); err != nil {
			return err
		}
	}
	reqs := store.Requirements{}
	if options.Tenant != "" {
		reqs = append(reqs, store.RequirementEqual("tenant", options.Tenant))
	}
	if options.Organization != "" {
		reqs = append(reqs, store.RequirementEqual("organization", options.Organization))
	}

	patch := store.RawPatch(store.PatchTypeMergePatch, []byte(`{"deleted":true}`))

	return s.Mongo.Patch(ctx, order, patch, func(po *store.PatchOptions) {
		po.FieldRequirements = reqs
	})
}

func (s *LocalOrderService) GetOrder(ctx context.Context, id string, options GetOrderOptions) (*Order, error) {
	reqs := store.Requirements{}
	if options.Tenant != "" {
		reqs = append(reqs, store.RequirementEqual("tenant", options.Tenant))
	}
	if options.Organization != "" {
		reqs = append(reqs, store.RequirementEqual("organization", options.Organization))
	}
	order := &Order{}
	if err := s.Mongo.Get(ctx, id, order, store.WithGetFieldRequirements(reqs...)); err != nil {
		return nil, fmt.Errorf("failed to get order %s: %w", id, err)
	}
	return order, nil
}

func (s *LocalOrderService) ListOrders(ctx context.Context, options ListOrderOptions) (store.List[Order], error) {
	fields := store.Requirements{}
	if options.Tenant != "" {
		fields = append(fields, store.RequirementEqual("tenant", options.Tenant))
	}
	if options.Organization != "" {
		fields = append(fields, store.RequirementEqual("organization", options.Organization))
	}
	if options.Creator != "" {
		fields = append(fields, store.RequirementEqual("creator", options.Creator))
	}
	if options.WithDeleted {
		fields = append(fields, store.RequirementEqual("deleted", true))
	} else {
		fields = append(fields, store.NewRequirement("deleted", store.NotEquals, true))
	}
	if len(options.Kinds) != 0 {
		kinds := make([]any, len(options.Kinds))
		for i, k := range options.Kinds {
			kinds[i] = string(k)
		}
		fields = append(fields, store.Requirement{Key: "kind", Operator: store.In, Values: kinds})
	}
	if options.State != "" {
		if options.State == OrderStatusPhaseNotPaid {
			fields = append(fields, store.NewRequirement("state", store.In, nil, "", string(options.State)))
		} else {
			fields = append(fields, store.RequirementEqual("state", options.State))
		}
	}
	fields = append(fields, store.NewCreationRangeRequirement(options.From, options.To)...)

	if options.Reference.Name != "" {
		matchval := map[string]any{
			"reference.name":     options.Reference.Name,
			"reference.resource": options.Reference.Resource,
			// "reference.scopes":   options.Reference.Scopes,
		}
		if options.Reference.UID != "" {
			matchval["reference.uid"] = options.Reference.UID
		}
		fields = append(fields, store.Requirement{
			Key: "items", Operator: "$elemMatch", Values: []any{matchval},
		})
	}

	if options.SKU.Name != "" {
		matchval := map[string]any{
			"sku.name": options.SKU.Name,
		}
		if options.SKU.Product != "" {
			matchval["sku.product"] = options.SKU.Product
		}
		if options.SKU.Category != "" {
			matchval["sku.category"] = options.SKU.Category
		}
		fields = append(fields, store.Requirement{
			Key: "items", Operator: "$elemMatch", Values: []any{matchval},
		})
	}

	listopts := base.ListOptionsToStoreListOptions(options.ListOptions)
	listopts = append(listopts, store.WithFieldRequirements(fields...))

	list := store.List[Order]{}
	if err := s.Mongo.List(ctx, &list, listopts...); err != nil {
		return list, fmt.Errorf("failed to list orders: %w", err)
	}
	return list, nil
}

func (s *LocalOrderService) CreateOrder(ctx context.Context, options CreateOrderOptions) (*Order, error) {
	return s.calcNewOrderAmount(ctx, false, options)
}

// EstimateOrder implements OrderService.
func (s *LocalOrderService) EstimateOrder(ctx context.Context, options CreateOrderOptions) (*Order, error) {
	return s.calcNewOrderAmount(ctx, true, options)
}

type orderItemsAmount struct {
	Tenant         string      `json:"tenant"`
	Organization   string      `json:"organization"`
	Kind           OrderKind   `json:"kind"`
	Description    string      `json:"description"`
	OriginalAmount base.Price  `json:"originalAmount"`
	PayableAmount  base.Price  `json:"payableAmount"`
	Items          []OrderItem `json:"items"`
	DiscountID     string      `json:"discountID,omitempty"`
}

func (s *LocalOrderService) calcNewOrderAmount(ctx context.Context, isEstimate bool, options CreateOrderOptions) (*Order, error) {
	if options.Kind == "" {
		options.Kind = OrderKindNew
	}
	if len(options.Items) == 0 {
		return nil, errors.NewBadRequest("order items is required")
	}
	account := options.Tenant
	if account == "" {
		return nil, errors.NewBadRequest("tenant is required")
	}
	if options.Kind == OrderKindRefund {
		return s.processRefundResourceOrders(ctx, account, isEstimate, options)
	}
	return s.processNewOrder(ctx, account, isEstimate, options)
}

func (s *LocalOrderService) processNewOrder(ctx context.Context, account string, isEstimate bool, options CreateOrderOptions) (*Order, error) {
	calcitems := make([]CalcOrderAmountItem, 0, len(options.Items))
	switch options.Kind {
	case OrderKindNew:
		for _, item := range options.Items {
			if item.SKU.Empty() {
				return nil, errors.NewBadRequest("order item sku is required")
			}
			if item.Quantity <= 0 {
				return nil, errors.NewBadRequest("order item quantity is required")
			}
			if item.Reference.Name != "" {
				return nil, errors.NewBadRequest("order item reference is not allowed in new order")
			}
			calcitems = append(calcitems, CalcOrderAmountItem{
				SKU:      item.SKU,
				Quantity: item.Quantity,
				Summary:  item.Summary,
				Params:   item.Params,
			})
		}
	case OrderKindRenew:
		for _, item := range options.Items {
			metadata, err := s.ResourceMetadata.GetMetadata(ctx, item.Reference)
			if err != nil {
				return nil, err
			}
			calcitems = append(calcitems, CalcOrderAmountItem{
				SKU:       metadata.SKU,
				Summary:   fmt.Sprintf("Renew %s %s", item.Reference.Resource, item.Reference.Name),
				Quantity:  item.Quantity,
				Reference: item.Reference,
				Params:    item.Params,
			})
		}
	default:
		return nil, errors.NewBadRequest(fmt.Sprintf("order kind %s is not supported", options.Kind))
	}
	calcopt := CalcOrderAmountOptions{
		Tenant:       options.Tenant,
		Organization: options.Organization,
		Kind:         options.Kind,
		Description:  options.Description,
		Operator:     options.Creator,
		Items:        calcitems,
		Timeout:      options.Timeout,
	}
	order, err := s.calcOrderAmount(ctx, account, isEstimate, calcopt)
	if err != nil {
		return nil, err
	}
	if isEstimate {
		return order, nil
	}
	if err := s.Mongo.Create(ctx, order); err != nil {
		return nil, fmt.Errorf("failed to create order: %w", err)
	}
	if s.Callback != nil {
		if err := s.Callback.Callback(ctx, OrderEvent{ID: order.Name, Kind: order.Kind, Phase: OrderStatusPhaseNotPaid}); err != nil {
			log.FromContext(ctx).Error(err, "callback order event")
		}
	}
	return order, nil
}

type CalcOrderAmountOptions struct {
	Tenant       string                `json:"tenant"`
	Organization string                `json:"organization"`
	Kind         OrderKind             `json:"kind"`
	Description  string                `json:"description"`
	Operator     string                `json:"operator"`
	Items        []CalcOrderAmountItem `json:"items"`
	Timeout      time.Duration         `json:"timeout"`
}

type CalcOrderAmountItem struct {
	Summary  string               `json:"summary,omitempty"`
	SKU      product.SKUReference `json:"sku,omitempty"`
	Quantity float64              `json:"quantity"`
	// Reference is optional, it point to the object this item related to
	// it used in order renewal/refund, it is the reference of the object
	// which will be used to calc the duration of the order item
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	// Params pass to shipment
	Params map[string]string `json:"params,omitempty"`
}

func (s *LocalOrderService) calcOrderAmount(ctx context.Context, account string, isEstimate bool, options CalcOrderAmountOptions) (*Order, error) {
	orderitems := make([]OrderItem, 0, len(options.Items))
	for _, item := range options.Items {
		sku, err := s.SKU.GetSKUPrice(ctx, item.SKU.Category, item.SKU.Product, item.SKU.Name, time.Time{})
		if err != nil {
			return nil, fmt.Errorf("get sku: %s", err.Error())
		}
		orderitem := OrderItem{
			ID:            NewOrderItemID(), // use an rand id to identify the order item in order
			Summary:       item.Summary,
			Kind:          options.Kind,
			Description:   sku.Description,
			SKU:           item.SKU,
			UnitPrice:     sku.Price,
			Quantity:      item.Quantity,
			Unit:          sku.Unit,
			Reference:     item.Reference,
			RefundPolicy:  sku.RefundPolicy,
			SKUAdditional: sku.Additional,
			Params:        item.Params,
		}
		orderitems = append(orderitems, orderitem)
	}
	desc := options.Description
	if desc == "" {
		desc = firstItemDesc(options.Items)
	}
	order := &Order{
		ObjectMeta: store.ObjectMeta{
			Name:        NewOrderID(),
			Description: desc,
		},
		Kind:         base.Def(options.Kind, OrderKindNew),
		Tenant:       options.Tenant,
		Organization: options.Organization,
		Creator:      options.Operator,
		Items:        orderitems,
		State:        OrderStatusPhaseNotPaid,
		ExpireAt:     ptr.To(time.Now().Add(base.Def(options.Timeout, s.Options.OrderTimeout).Truncate(time.Second))),
	}
	if s.DiscountService != nil {
		opts := promotion.CalculateDiscountOptions{
			Tenant:       order.Tenant,
			Organization: order.Organization,
			Kind:         string(order.Kind),
			Items:        make([]promotion.CalculateDiscountItem, 0, len(order.Items)),
			Round:        OrderRound,
		}
		for _, item := range order.Items {
			opts.Items = append(opts.Items, promotion.CalculateDiscountItem{
				SKU:       item.SKU,
				Quantity:  item.Quantity,
				UnitPrice: item.UnitPrice,
			})
		}
		if isEstimate {
			discountresult, err := s.DiscountService.EstimateDiscount(ctx, account, opts)
			if err != nil {
				return nil, err
			}
			for i := range discountresult.Items {
				order.Items[i].PayableAmount = discountresult.Items[i].PayableAmount
				order.Items[i].OriginalAmount = discountresult.Items[i].OriginalAmount
				order.Items[i].Discount = discountresult.Items[i].Discount
			}
			order.PayableAmount = discountresult.PayableAmount
			order.OriginalAmount = discountresult.OriginalAmount
		} else {
			discountresult, err := s.DiscountService.CalculateDiscount(ctx, account, opts)
			if err != nil {
				return nil, err
			}
			for i := range discountresult.Items {
				order.Items[i].PayableAmount = discountresult.Items[i].PayableAmount
				order.Items[i].OriginalAmount = discountresult.Items[i].OriginalAmount
				order.Items[i].Discount = discountresult.Items[i].Discount
			}
			order.PayableAmount = discountresult.PayableAmount
			order.OriginalAmount = discountresult.OriginalAmount
			order.Discount.ID = discountresult.ID
		}
	} else {
		for i := range order.Items {
			order.Items[i].PayableAmount = order.Items[i].UnitPrice.Mul(base.NewPrice(order.Items[i].Quantity)).Round(OrderRound)
			order.Items[i].OriginalAmount = order.Items[i].PayableAmount
			order.Items[i].Discount = 1.0

			order.PayableAmount = order.PayableAmount.Add(order.Items[i].PayableAmount)
			order.OriginalAmount = order.OriginalAmount.Add(order.Items[i].OriginalAmount)
		}
	}
	return order, nil
}

func firstItemDesc(items []CalcOrderAmountItem) string {
	for _, item := range items {
		if !item.SKU.Empty() {
			return fmt.Sprintf("%s %s(%s)", CapitalizeFirst(item.SKU.Category), item.SKU.Product, item.SKU.Name)
		}
	}
	return ""
}

func CapitalizeFirst(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(s)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// PayOrder implements OrderService.
func (s *LocalOrderService) PayOrder(ctx context.Context, id string, options PayOrderOptions) error {
	channel := base.Def(options.Channel, string(payment.ChannelWallet))
	operator := base.Def(options.Operator, api.AuthenticateFromContext(ctx).User.Name)

	order, err := s.GetOrder(ctx, id, GetOrderOptions{})
	if err != nil {
		return err
	}
	switch order.Kind {
	case OrderKindNew, OrderKindRenew, OrderKindUpgrade, OrderKindDegrade:
	default:
		return errors.NewBadRequest(fmt.Sprintf("can't pay %s order", order.Kind))
	}
	if order.State != OrderStatusPhaseNotPaid {
		return errors.NewBadRequest("order is not in a pay state")
	}
	// check the order payment
	if order.Payment.ID != "" {
		return errors.NewBadRequest("order already in a payment")
	}
	// check the stock
	if !options.IgnoreStock && order.Kind == OrderKindNew {
		for _, item := range order.Items {
			// skip the empty sku
			if item.SKU.Empty() {
				continue
			}
			if err := s.SKU.CheckStock(ctx, item.SKU.Category, item.SKU.Product, item.SKU.Name, uint(item.Quantity)); err != nil {
				return fmt.Errorf("check stock failed: %w", err)
			}
		}
	}
	svc, err := s.PaymentChannelManager.GetPaymentService(ctx, payment.Channel(channel))
	if err != nil {
		return err
	}

	account := order.Tenant

	type SkuQuanlity struct {
		SKU      product.SKUReference
		Quantity uint
	}

	type PayOrderContext struct {
		context.Context
		Payment           *payment.Payment
		SuccessReducedSKU []SkuQuanlity
		Error             error
	}

	txns := []txn.Transaction[PayOrderContext]{
		// reduce stock
		txn.CallbackTransaction[PayOrderContext]{
			CommitFunc: func(poc PayOrderContext) error {
				if !options.IgnoreStock && order.Kind == OrderKindNew {
					for _, item := range order.Items {
						// skip the empty sku
						if item.SKU.Empty() {
							continue
						}
						if err := s.SKU.ReduceStock(poc, item.SKU.Category, item.SKU.Product, item.SKU.Name, uint(item.Quantity)); err != nil {
							log.Error(err, "failed to reduce stock", "sku", item.SKU)
							return err
						}
						poc.SuccessReducedSKU = append(poc.SuccessReducedSKU, SkuQuanlity{
							SKU:      item.SKU,
							Quantity: uint(item.Quantity),
						})
					}
				}
				return nil
			},
			RevertFunc: func(poc PayOrderContext) error {
				// rollback reduced stock
				for _, sku := range poc.SuccessReducedSKU {
					if err := s.SKU.IncreaseStock(poc, sku.SKU.Category, sku.SKU.Product, sku.SKU.Name, sku.Quantity); err != nil {
						log.Error(err, "failed to increase stock", "sku", sku.SKU)
					}
				}
				return nil
			},
		},

		// create payment
		txn.CallbackTransaction[PayOrderContext]{
			CommitFunc: func(poc PayOrderContext) error {
				createpaymentopts := payment.CreatePaymentOptions{
					Description:  order.Description,
					Creator:      operator,
					GoodName:     getOrderSummary(order),
					Organization: order.Organization,
				}
				if order.ExpireAt != nil {
					createpaymentopts.Timeout = time.Until(*order.ExpireAt).Truncate(time.Second)
				}
				newpayment, err := svc.CreatePayment(poc, account, order.Name, payment.Amount{Total: order.PayableAmount}, createpaymentopts)
				if err != nil {
					return err
				}
				poc.Payment = newpayment

				patch := store.MapMergePatch{
					"payment": map[string]any{
						"id":      newpayment.ID,
						"channel": channel,
					},
				}
				if err := s.Mongo.Patch(poc, order, patch); err != nil {
					return err
				}

				// if can pay this payment directly
				if payable, ok := svc.(payment.PayablePaymentService); ok {
					// trigger the service to pay, it only works for wallet payment
					if err := payable.Pay(poc, account, newpayment.ID, payment.PayPaymentOptions{Operator: operator}); err != nil {
						return err
					}
				}
				return nil
			},
			RevertFunc: func(poc PayOrderContext) error {
				// when revert, close the payment if created
				if poc.Payment != nil {
					return svc.ClosePayment(poc, poc.Payment.ID)
				}
				return nil
			},
		},
	}
	txncontext, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	return txn.Execute(PayOrderContext{Context: txncontext}, txns...)
}

func getOrderSummary(order *Order) string {
	items := make([]string, len(order.Items))
	for i, item := range order.Items {
		if item.SKU.Empty() {
			items[i] = item.Description
		} else {
			items[i] = item.SKU.Summary()
		}
	}
	return strings.Join(items, ", ")
}

// ShipmentOrderItem implements OrderService.
func (s *LocalOrderService) ShipmentOrderItem(ctx context.Context, id string, itemid string, options ShipmentOptions) error {
	order, err := s.GetOrder(ctx, id, GetOrderOptions{})
	if err != nil {
		return err
	}
	for i, item := range order.Items {
		if item.ID == itemid {
			patch := store.MapMergePatch{
				"items": map[string]any{
					strconv.Itoa(i): map[string]any{
						"reference": options.Reference,
						"startTime": options.From,
						"endTime":   options.To,
					},
				},
			}
			return s.Mongo.Patch(ctx, order, patch)
		}
	}
	return nil
}

func (s *LocalOrderService) processRefundResourceOrders(ctx context.Context, account string, isEstimate bool, options CreateOrderOptions) (*Order, error) {
	refundorderitems, err := s.listResourceOrders(ctx, options)
	if err != nil {
		return nil, err
	}
	virtualorder, err := s.calculateRefundVirtualOrder(ctx, account, refundorderitems, options)
	if err != nil {
		return nil, err
	}
	if isEstimate {
		return virtualorder, err
	}
	if len(refundorderitems) == 0 {
		return nil, errors.NewBadRequest("no orders to refund")
	}
	refundordes := make([]Order, 0, len(refundorderitems))
	err = s.Mongo.Transaction(ctx, func(ctx context.Context, txn store.Store) error {
		for _, item := range refundorderitems {
			refundorder, err := s.refundOrder(ctx, txn, item, options)
			if err != nil {
				return err
			}
			refundordes = append(refundordes, *refundorder)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	if s.Callback != nil {
		for _, order := range refundordes {
			if err := s.Callback.Callback(ctx, OrderEvent{ID: order.Name, Kind: order.Kind, Phase: OrderStatusPhaseNotPaid}); err != nil {
				log.FromContext(ctx).Error(err, "callback order event")
			}
		}
	}
	return virtualorder, nil
}

type OrderAndItems struct {
	Order Order
	Items []OrderItem
}

func (s *LocalOrderService) listResourceOrders(ctx context.Context, options CreateOrderOptions) ([]OrderAndItems, error) {
	if len(options.Items) == 0 {
		return nil, errors.NewBadRequest("order items is required")
	}
	for _, item := range options.Items {
		if item.Reference.Name == "" {
			return nil, errors.NewBadRequest("order item reference.Name is required")
		}
		if item.Reference.Resource == "" {
			return nil, errors.NewBadRequest("order item reference.Resource is required")
		}
	}
	refundorderitems := []OrderAndItems{}
	now := time.Now()
	for _, item := range options.Items {
		orderlist, err := s.ListOrders(ctx, ListOrderOptions{
			Kinds:     []OrderKind{OrderKindNew, OrderKindRenew, OrderKindUpgrade, OrderKindDegrade},
			Reference: item.Reference,
		})
		if err != nil {
			return nil, err
		}
		for _, order := range orderlist.Items {
			if order.State != OrderStatusPhasePaid {
				continue
			}
			if order.Payment.Channel == "" || order.Payment.ID == "" {
				continue
			}
			if order.Refund.ID != "" {
				continue
			}
			orderitems := make([]OrderItem, 0, len(order.Items))
			for _, item := range order.Items {
				if !item.Reference.Equals(item.Reference) {
					continue
				}
				// check this order is in service time range
				if item.StartTime.IsZero() || item.EndTime.IsZero() {
					continue
				}
				if now.After(item.EndTime) {
					continue
				}
				orderitems = append(orderitems, item)
			}
			if len(orderitems) == 0 {
				continue
			}
			refundorderitems = append(refundorderitems, OrderAndItems{Order: order, Items: orderitems})
		}
	}
	return refundorderitems, nil
}

// RefundOrder implements OrderService.
func (s *LocalOrderService) refundOrder(ctx context.Context, txn store.Store, orderwithitem OrderAndItems, options CreateOrderOptions) (*Order, error) {
	// allow zero amount refund
	refunditems := make([]OrderItem, 0, len(orderwithitem.Items))
	totaloriginal, totalpayable := base.NewPrice(0), base.NewPrice(0)
	for _, item := range orderwithitem.Items {
		refunditem, err := s.calculateRefundOrderItem(ctx, orderwithitem.Order, item)
		if err != nil {
			return nil, err
		}
		totalpayable = totalpayable.Add(refunditem.PayableAmount)
		totaloriginal = totaloriginal.Add(refunditem.OriginalAmount)
		refunditems = append(refunditems, *refunditem)
	}
	refundorder := &Order{
		ObjectMeta: store.ObjectMeta{
			Name:        NewOrderID(),
			Description: options.Description,
		},
		Kind:           OrderKindRefund,
		Tenant:         options.Tenant,
		Organization:   options.Organization,
		Creator:        api.AuthenticateFromContext(ctx).User.Name,
		Items:          refunditems,
		OriginalAmount: totaloriginal,
		PayableAmount:  totalpayable,
		State:          OrderStatusPhaseNotPaid,
		OriginalOrder:  orderwithitem.Order.Name,
	}
	if refundorder.Description == "" {
		refundorder.Description = getOrderSummary(refundorder)
	}
	ctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
	defer cancel()

	if err := txn.Create(ctx, refundorder); err != nil {
		return nil, err
	}
	patch := store.MapMergePatch{
		"refund": map[string]any{"id": refundorder.Name},
	}
	if err := txn.Patch(ctx, &orderwithitem.Order, patch); err != nil {
		return nil, err
	}
	return refundorder, nil
}

func (s *LocalOrderService) calculateRefundVirtualOrder(ctx context.Context, account string, orders []OrderAndItems, options CreateOrderOptions) (*Order, error) {
	totalorignal, totalpayable := base.NewPrice(0), base.NewPrice(0)
	refunditems := make([]OrderItem, 0, len(orders))
	for _, item := range orders {
		for _, orderitem := range item.Items {
			refunditem, err := s.calculateRefundOrderItem(ctx, item.Order, orderitem)
			if err != nil {
				return nil, err
			}
			totalpayable = totalpayable.Add(refunditem.PayableAmount)
			totalorignal = totalorignal.Add(refunditem.OriginalAmount)
			refunditems = append(refunditems, *refunditem)
		}
	}
	ret := &Order{
		ObjectMeta: store.ObjectMeta{
			Name:        NewOrderID(),
			Description: options.Description,
		},
		Kind:           OrderKindRefund,
		Tenant:         options.Tenant,
		Organization:   options.Organization,
		OriginalAmount: totalorignal,
		PayableAmount:  totalpayable,
		Items:          refunditems,
		AutoPay:        true,
		State:          OrderStatusPhaseNotPaid,
	}
	_ = account
	return ret, nil
}

/*
退款金额通常依据以下公式计算：

退款金额 = 订单实付金额 × (剩余有效天数 / 总购买天数)

其中：

剩余有效天数 = 订单结束时间 - 退款申请时间
总购买天数 = 订单结束时间 - 订单开始时间

注意事项：
无理由全额退款：对于包年包月的资源，若在购买后的5天内申请退订，且符合相关条件，可享受无理由全额退款。
非全额退款：超过5天的订单，退款金额将根据实际使用天数进行折算，已使用部分的费用不予退还。
优惠券与代金券：退款时仅退还实际支付的现金部分，已使用的优惠券和代金券部分不予退还。
特殊资源：部分资源（如云市场镜像、带宽等）可能有特殊的退订规则，具体请参阅阿里云的退订规则说明 。
*/
func (s *LocalOrderService) calculateRefundOrderItem(_ context.Context, order Order, item OrderItem) (*OrderItem, error) {
	refunditem := OrderItem{
		ID:          NewOrderItemID(),
		Summary:     item.Summary,
		Kind:        OrderKindRefund,
		Description: item.Description,
		SKU:         item.SKU,
		Quantity:    item.Quantity,
		UnitPrice:   item.UnitPrice,
		Unit:        item.Unit,
		Reference:   item.Reference,

		StartTime: item.StartTime,
		EndTime:   item.EndTime,

		OriginalOrder: order.Name,
		OriginalItem:  item.ID,
		OriginalKind:  item.Kind,
	}
	switch item.RefundPolicy {
	case product.RefundPolicyNotSupport:
		return nil, errors.NewBadRequest("refund is not supported")
	case product.RefundPolicyProRated:
		leftamount, err := calcLeftRefund(item.StartTime, item.EndTime, item.PayableAmount)
		if err != nil {
			return nil, err
		}
		refunditem.PayableAmount = leftamount.Neg()
	case product.RefundPolicyWithPenalty:
		refunditem.PayableAmount = item.PayableAmount.Neg()
	case product.RefundPolicyNoReason:
		refunditem.PayableAmount = item.PayableAmount.Neg()
	default:
		return nil, errors.NewBadRequest("refund is not supported")
	}
	// 退款单的原始金额是原订单的已付金额
	refunditem.OriginalAmount = item.PayableAmount.Neg()
	return &refunditem, nil
}

func calcLeftRefund(from, end time.Time, amount base.Price) (base.Price, error) {
	if from.IsZero() || end.IsZero() {
		// no time range, refund all
		return amount, nil
	}
	now := time.Now()
	if now.After(end) {
		return base.NewPrice(0),
			errors.NewBadRequest(fmt.Sprintf("refund is not supported, service has been expired at %s", end.Format(time.RFC3339)))
	}
	if now.Before(from) {
		// not start yet, refund all
		return amount, nil
	}
	// 向上取整，不足一天按照一天计算
	used := math.Ceil((float64(now.Sub(from).Hours())) / 24)
	total := math.Ceil((float64(end.Sub(from).Hours())) / 24)
	leftpercentage := 1 - (used / total)
	leftpercentage = max(min(1, leftpercentage), 0)
	refundamount := amount.Mul(base.NewPrice(leftpercentage))
	refundamount = refundamount.Round(2)
	return refundamount, nil
}
