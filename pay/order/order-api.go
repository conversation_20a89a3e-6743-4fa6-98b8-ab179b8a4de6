package order

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/pay/resourcemetadata"
)

func NewAPI(base base.API, mongo store.Store, ordersvc OrderService, skus product.SKUService, meta resourcemetadata.ResourceMetadataService) *API {
	return &API{
		API:                     base,
		Orders:                  ordersvc,
		SKUs:                    skus,
		Mongo:                   mongo,
		ResourceMetadataService: meta,
	}
}

type API struct {
	base.API
	Mongo                   store.Store
	Orders                  OrderService
	SKUs                    product.SKUService
	ResourceMetadataService resourcemetadata.ResourceMetadataService
}

const Week = time.Hour * 24 * 7

func (s *API) ListOrders(w http.ResponseWriter, r *http.Request) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		return s.Orders.ListOrders(ctx, ListOrderOptions{
			ListOptions:  api.GetListOptions(r),
			State:        orderStateQuery(r),
			Creator:      api.Query(r, "creator", ""),
			Kinds:        tokinds(api.Query(r, "kind", []string{})),
			Tenant:       api.Query(r, "tenant", ""),
			Organization: api.Query(r, "organization", ""),
			WithDeleted:  api.Query(r, "withDeleted", false),
			From:         api.Query(r, "from", time.Time{}),
			To:           api.Query(r, "to", time.Time{}),
			SKU: product.SKUReference{
				Name:     api.Query(r, "skuName", ""),
				Product:  api.Query(r, "skuProduct", ""),
				Category: api.Query(r, "skuCategory", ""),
			},
		})
	})
}

func tokinds(kinds []string) []OrderKind {
	var ret []OrderKind
	for _, k := range kinds {
		ret = append(ret, OrderKind(k))
	}
	return ret
}

func (s *API) GetOrder(w http.ResponseWriter, r *http.Request) {
	s.onOrder(w, r, func(ctx context.Context, id string) (any, error) {
		return s.Orders.GetOrder(ctx, id, GetOrderOptions{})
	})
}

func (s *API) onOrder(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, id string) (any, error)) {
	api.On(w, r, func(ctx context.Context) (any, error) {
		id := api.Path(r, "id", "")
		if id == "" {
			return nil, errors.NewBadRequest("order id is required")
		}
		return fn(ctx, id)
	})
}

func (s *API) orderGroup() api.Group {
	return api.
		NewGroup("/orders").
		Route(
			api.GET("").
				To(s.ListOrders).
				Param(
					api.QueryParam("state", "").In(OrderStatusPhaseNotPaid, OrderStatusPhasePaid, OrderStatusPhaseFinished).Optional(),
					api.QueryParam("creator", "").Optional(),
					api.QueryParam("name", "").Optional(),
					api.QueryParam("tenant", "").Optional(),
					api.QueryParam("kind", "").
						Multiple().
						Desc("multiple kind example: New,Renew").
						In(OrderKindNew, OrderKindRenew, OrderKindUpgrade, OrderKindDegrade, OrderKindRefund).Optional(),
					api.QueryParam("organization", "").Optional(),
					api.QueryParam("withDeleted", "show deleted orders").Optional(),
					api.QueryParam("from", "").Format("date-time").Optional(),
					api.QueryParam("to", "").Format("date-time").Optional(),

					api.QueryParam("skuName", "").Optional(),
					api.QueryParam("skuProduct", "").Optional(),
					api.QueryParam("skuCategory", "").Optional(),
				).
				Response(store.List[Order]{}),
			api.GET("/{id}").
				To(s.GetOrder),
		)
}

func (s *API) ListTenantOrders(w http.ResponseWriter, r *http.Request) {
	s.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		return s.Orders.ListOrders(ctx, ListOrderOptions{
			ListOptions:  api.GetListOptions(r),
			Tenant:       tenant,
			Kinds:        tokinds(api.Query(r, "kind", []string{})),
			Organization: api.Query(r, "organization", ""),
			State:        orderStateQuery(r),
			Creator:      api.Query(r, "creator", ""),
			From:         api.Query(r, "from", time.Time{}),
			To:           api.Query(r, "to", time.Time{}),
			SKU: product.SKUReference{
				Name:     api.Query(r, "skuName", ""),
				Product:  api.Query(r, "skuProduct", ""),
				Category: api.Query(r, "skuCategory", ""),
			},
		})
	})
}

func (s *API) GetTenantOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		id := api.Path(r, "id", "")
		return s.Orders.GetOrder(ctx, id, GetOrderOptions{Tenant: tenant})
	})
}

func (s *API) CreateTenantOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		var req CreateOrderRequest
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		options := CreateOrderRequest{
			Kind:        req.Kind,
			Tenant:      tenant,
			Description: req.Description,
			Items:       req.Items,
		}
		order, err := CreateOrderFromSkuOptions(ctx, s.Orders, s.SKUs, options)
		if err != nil {
			return nil, err
		}
		// for audit
		base.InjectAttrName(ctx, order.Name)
		return order, nil
	})
}

func (s *API) EstimateTenantOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		var req CreateOrderRequest
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		options := CreateOrderRequest{
			Kind:        req.Kind,
			Tenant:      tenant,
			Description: req.Description,
			Items:       req.Items,
		}
		order, err := EstimateOrderFromSkuOptions(ctx, s.Orders, s.SKUs, options)
		if err != nil {
			return nil, err
		}
		return order, nil
	})
}

func (s *API) DeleteTenantOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		id := api.Path(r, "id", "")
		if err := s.Orders.DeleteOrder(ctx, id, DeleteOrderOptions{Tenant: tenant}); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

type ToPayResponse struct {
	PayID  string `json:"payID"`
	PayURL string `json:"payURL"`
}

func (s *API) PayOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		id := api.Path(r, "id", "")
		paymethod := PayOrderOptions{
			Operator: api.AuthenticateFromContext(ctx).User.Name,
		}
		if err := api.Body(r, &paymethod); err != nil {
			return nil, err
		}
		if err := s.Orders.PayOrder(ctx, id, paymethod); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (s *API) tenantOrderGroup() api.Group {
	return api.NewGroup("").
		SubGroup(
			base.NewTenantGroup("").
				SubGroup(s.MetadataGroup()),
			base.NewTenantGroup("orders").
				Route(
					api.GET("").
						To(s.ListTenantOrders).
						Param(
							api.QueryParam("state", "").In(OrderStatusPhaseNotPaid, OrderStatusPhasePaid, OrderStatusPhaseFinished).Optional(),
							api.QueryParam("creator", "").Optional(),
							api.QueryParam("organization", "filter by organization").Optional(),
							api.QueryParam("from", "").Format("date-time").Optional(),
							api.QueryParam("to", "").Format("date-time").Optional(),
							api.QueryParam("skuName", "").Optional(),
							api.QueryParam("skuProduct", "").Optional(),
							api.QueryParam("skuCategory", "").Optional(),
						).
						Param(api.PageParams...).
						Response(store.List[Order]{}),

					api.POST("").
						To(s.CreateTenantOrder).
						Param(api.BodyParam("order", CreateOrderRequest{})),

					api.POST(":estimate").
						To(s.EstimateTenantOrder).
						Param(api.BodyParam("order", CreateOrderRequest{})).
						Response(EstimateOrder{}),

					api.GET("/{id}").
						To(s.GetTenantOrder).
						Response(Order{}),

					api.DELETE("/{id}").
						To(s.DeleteTenantOrder),

					api.POST("/{id}:pay").
						To(s.PayOrder).
						Param(api.BodyParam("pay", PayOrderOptions{})),
				),
		)
}

func (s *API) ListTenantOrganizationOrders(w http.ResponseWriter, r *http.Request) {
	s.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		return s.Orders.ListOrders(ctx, ListOrderOptions{
			ListOptions:  api.GetListOptions(r),
			Tenant:       tenant,
			Organization: org,
			Kinds:        tokinds(api.Query(r, "kind", []string{})),
			State:        orderStateQuery(r),
			Creator:      api.Query(r, "creator", ""),
			From:         api.Query(r, "from", time.Time{}),
			To:           api.Query(r, "to", time.Time{}),
			SKU: product.SKUReference{
				Name:     api.Query(r, "skuName", ""),
				Product:  api.Query(r, "skuProduct", ""),
				Category: api.Query(r, "skuCategory", ""),
			},
		})
	})
}

func orderStateQuery(r *http.Request) OrderState {
	state := api.Query(r, "state", "")
	if state == "" {
		// backward compatibility
		state = api.Query(r, "phase", "")
	}
	return OrderState(state)
}

func (s *API) CreateTenantOrganizationOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		var req CreateOrderRequest
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		options := CreateOrderRequest{
			Tenant:       tenant,
			Kind:         req.Kind,
			Organization: org,
			Description:  req.Description,
			Items:        req.Items,
		}
		return CreateOrderFromSkuOptions(ctx, s.Orders, s.SKUs, options)
	})
}

func (s *API) EstimateTenantOrganizationOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		var req CreateOrderRequest
		if err := api.Body(r, &req); err != nil {
			return nil, err
		}
		options := CreateOrderRequest{
			Tenant:       tenant,
			Kind:         req.Kind,
			Organization: org,
			Description:  req.Description,
			Items:        req.Items,
		}
		return EstimateOrderFromSkuOptions(ctx, s.Orders, s.SKUs, options)
	})
}

func (s *API) GetTenantOrganizationOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		id := api.Path(r, "id", "")
		return s.Orders.GetOrder(ctx, id, GetOrderOptions{Tenant: tenant, Organization: org})
	})
}

func (s *API) DeleteTenantOrganizationOrder(w http.ResponseWriter, r *http.Request) {
	s.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, org string) (any, error) {
		id := api.Path(r, "id", "")
		if err := s.Orders.DeleteOrder(ctx, id, DeleteOrderOptions{Tenant: tenant, Organization: org}); err != nil {
			return nil, err
		}
		return api.Empty, nil
	})
}

func (s *API) tenantOrganizationOrderGroup() api.Group {
	return api.NewGroup("").
		SubGroup(
			base.NewTenantOrganizationGroup("").
				SubGroup(s.MetadataGroup()),
			base.
				NewTenantOrganizationGroup("orders").
				Route(
					api.GET("").
						To(s.ListTenantOrganizationOrders).
						Param(
							api.QueryParam("state", "").In(OrderStatusPhaseNotPaid, OrderStatusPhasePaid, OrderStatusPhaseFinished).Optional(),
							api.QueryParam("creator", "").Optional(),
							api.QueryParam("from", "").Format("date-time").Optional(),
							api.QueryParam("to", "").Format("date-time").Optional(),
							api.QueryParam("kind", "").Multiple(),
							api.QueryParam("skuName", "").Optional(),
							api.QueryParam("skuProduct", "").Optional(),
							api.QueryParam("skuCategory", "").Optional(),
						).
						Param(api.PageParams...).
						Response(store.List[Order]{}),

					api.POST("").
						To(s.CreateTenantOrganizationOrder).
						Param(api.BodyParam("order", CreateOrderRequest{})),

					api.POST(":estimate").
						To(s.EstimateTenantOrganizationOrder).
						Param(api.BodyParam("order", CreateOrderRequest{})).
						Response(EstimateOrder{}),

					api.GET("/{id}").
						To(s.GetTenantOrganizationOrder).
						Response(Order{}),

					api.DELETE("/{id}").
						To(s.DeleteTenantOrganizationOrder),

					api.POST("/{id}:pay").
						To(s.PayOrder).
						Param(api.BodyParam("pay", PayOrderOptions{})),
				),
		)
}

func (s *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Order").
		SubGroup(
			s.orderGroup(),
			s.tenantOrderGroup(),
			s.tenantOrganizationOrderGroup(),
		)
}

func (a *API) GetResourceMetadata(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrTenantOrganizationScopes(w, r, func(ctx context.Context, scopes []store.Scope) (any, error) {
		ref := store.ResourcedObjectReference{
			Resource: api.Query(r, "resource", ""),
			Name:     api.Query(r, "name", ""),
			Scopes:   scopes,
		}
		metadata, err := a.ResourceMetadataService.GetMetadata(ctx, ref)
		if err != nil {
			return nil, err
		}
		sku, err := a.SKUs.GetSKUPrice(ctx, metadata.SKU.Category, metadata.SKU.Product, metadata.SKU.Name, time.Time{})
		if err != nil {
			// when sku not found, return a disabled sku
			if errors.IsNotFound(err) {
				return product.SKUData{Name: metadata.SKU.Name, Category: metadata.SKU.Category, Product: metadata.SKU.Product, Enabled: false}, nil
			}
			return nil, err
		}
		return sku, nil
	})
}

func (a *API) MetadataGroup() api.Group {
	return api.NewGroup("").
		Route(
			api.GET("/resource-sku").
				Operation("query resource sku").
				Param(
					api.QueryParam("resource", "resource name"),
					api.QueryParam("name", "sku name"),
				).
				To(a.GetResourceMetadata).
				Response(product.SKUData{}),
		)
}
