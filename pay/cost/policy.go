package cost

import (
	"context"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/pay/billing"
	"xiaoshiai.cn/core/pay/product"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&PricingPolicy{}, mongo.ObjectDefination{
		Uniques: []mongo.UnionFields{
			{"name"},
		},
		Indexes: []mongo.UnionFields{
			{"period"},
			{"clusterCategory"},
		},
	})
}

// PricingPolicy
type PricingPolicy struct {
	store.ObjectMeta   `json:",inline"`
	MeasurementName    string                  `json:"measurementName,omitempty"`
	ClusterCategory    cluster.ClusterCategory `json:"clusterCategory,omitempty"`
	MatchLabels        map[string]string       `json:"matchLabels,omitempty"`
	MatchClusterLabels map[string]string       `json:"matchClusterLabels,omitempty"`
	Period             ResourcePeriod          `json:"period,omitempty"`
	Unit               ResourceUnit            `json:"unit,omitempty"`
	UnitPrice          base.Price              `json:"unitPrice,omitempty"`
	// Priority is used to determine which policy to use when multiple policies match.
	Priority int  `json:"priority,omitempty"`
	Disabled bool `json:"disabled,omitempty"`
}

func InitPricingPolicies(ctx context.Context, mongo store.Store, sku product.SKUService) error {
	resources := []PricingPolicy{
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "cpu",
				Description: "Generic CPU",
			},
			MeasurementName: MeasurementContainerCPU,
			ClusterCategory: cluster.ClusterCategoryContainer,
			Unit:            ResourceUnitCorePerHour,
			UnitPrice:       base.NewPrice(0.1),
			Period:          ResourcePeriodHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "memory",
				Description: "Generic Memory",
			},
			MeasurementName: MeasurementContainerMemory,
			ClusterCategory: cluster.ClusterCategoryContainer,
			Unit:            ResourceUnitGBPerHour,
			Period:          ResourcePeriodHour,
			UnitPrice:       base.NewPrice(0.1),
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "standard-storage",
				Description: "Generic Storage",
			},
			MeasurementName: MeasurementContainerStorage,
			ClusterCategory: cluster.ClusterCategoryContainer,
			Unit:            ResourceUnitGBPerHour,
			UnitPrice:       base.NewPrice(0.1),
			Period:          ResourcePeriodHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "block-storage",
				Description: "Block Storage",
			},
			MeasurementName: MeasurementContainerStorage,
			ClusterCategory: cluster.ClusterCategoryContainer,
			Unit:            ResourceUnitGBPerHour,
			UnitPrice:       base.NewPrice(0.3),
			Period:          ResourcePeriodHour,
			MatchLabels: map[string]string{
				"storageclass": ".*rbd.*",
			},
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "ingress_rx",
				Description: "Ingress receive traffic",
			},
			MeasurementName: MeasurementIngressRx,
			ClusterCategory: cluster.ClusterCategoryContainer,
			Unit:            ResourceUnitGB,
			UnitPrice:       base.NewPrice(0.1),
			Period:          ResourcePeriodHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "ingress_tx",
				Description: "Ingress send traffic",
			},
			MeasurementName: MeasurementIngressTx,
			ClusterCategory: cluster.ClusterCategoryContainer,
			Unit:            ResourceUnitGB,
			UnitPrice:       base.NewPrice(0.1),
			Period:          ResourcePeriodHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "vcpu",
				Description: "Virtual Machine CPU",
			},
			MeasurementName: MeasurementVirtualMachineCPU,
			ClusterCategory: cluster.ClusterCategoryVirtualMachine,
			UnitPrice:       base.NewPrice(0.1),
			Unit:            ResourceUnitCorePerHour,
			Period:          ResourcePeriodHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "vmemory",
				Description: "Virtual Machine Memory",
			},
			MeasurementName: MeasurementVirtualMachineMemory,
			ClusterCategory: cluster.ClusterCategoryVirtualMachine,
			Unit:            ResourceUnitGBPerHour,
			UnitPrice:       base.NewPrice(0.1),
			Period:          ResourcePeriodHour,
		},
		{
			ObjectMeta: store.ObjectMeta{
				Name:        "disk",
				Description: "Virtual Machine Disk",
			},
			MeasurementName: MeasurementVirtualMachineStorage,
			ClusterCategory: cluster.ClusterCategoryVirtualMachine,
			Unit:            ResourceUnitGBPerHour,
			UnitPrice:       base.NewPrice(0.1),
			Period:          ResourcePeriodHour,
		},
	}
	var errs []error
	for _, resource := range resources {
		exists := &PricingPolicy{}
		if err := mongo.Get(ctx, resource.Name, exists); err != nil {
			if !errors.IsNotFound(err) {
				errs = append(errs, err)
				continue
			}
			if err := createPolicy(ctx, mongo, sku, &resource); err != nil {
				errs = append(errs, err)
			}
		}
	}
	return errors.NewAggregate(errs)
}

var _ CostService = &DefaultCostService{}

func NewDefaultCostService(mongo store.Store, billingService billing.BillingService) *DefaultCostService {
	return &DefaultCostService{
		Mongo:          mongo,
		BillingService: billingService,
	}
}

type DefaultCostService struct {
	Mongo          store.Store
	BillingService billing.BillingService
}

// EstimateResourceCost implements CostService.
func (d *DefaultCostService) EstimateResourceCost(ctx context.Context, options EstimateCostOptions) (*EstimateCostResult, error) {
	policylist := &store.List[PricingPolicy]{}
	if err := d.Mongo.List(ctx, policylist); err != nil {
		return nil, err
	}
	account := options.Tenant
	if account == "" {
		return nil, errors.NewBadRequest("tenant is required")
	}
	resolution := ResourcePeriodToDuration(options.Period)
	now := time.Now().Truncate(resolution)
	from, to := now, now.Add(resolution)

	estimateBillingOptions := billing.CreateBillingOptions{
		Tenant:       options.Tenant,
		Organization: options.Organization,
		From:         from,
		To:           to,
		Round:        ResoucesRound,
		Items:        make([]billing.CreateBillingItem, 0, len(options.Items)),
	}
	for _, item := range options.Items {
		policy := findMatchPolicy(policylist.Items, item.MeasurementName, UsageItem{Labels: item.Labels, Value: item.Quantity})
		if policy == nil {
			continue
		}
		it := billing.CreateBillingItem{
			SKU:      SystemResourceSKU(item.MeasurementName, policy.Name),
			Quantity: item.Quantity,
		}
		estimateBillingOptions.Items = append(estimateBillingOptions.Items, it)
	}
	ret, err := d.BillingService.EstimateBilling(ctx, account, estimateBillingOptions)
	if err != nil {
		return nil, err
	}
	result := &EstimateCostResult{
		OriginalAmount: ret.OriginalAmount,
		PayableAmount:  ret.PayableAmount,
	}
	for _, item := range ret.Items {
		result.Items = append(result.Items, EstimateCostResultItem{
			Period:         options.Period,
			SKU:            item.SKU,
			UnitPrice:      item.UnitPrice,
			Unit:           item.Unit,
			Quantity:       item.Quantity,
			OriginalAmount: item.OriginalAmount,
			PayableAmount:  item.PayableAmount,
		})
	}
	return result, nil
}
