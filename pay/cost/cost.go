package cost

import (
	"context"
	"time"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/product"
)

func Init(ctx context.Context, mongo store.Store, sku product.SKUService) error {
	if err := InitMeasurementPolicies(ctx, mongo); err != nil {
		return err
	}
	if err := InitPricingPolicies(ctx, mongo, sku); err != nil {
		return err
	}
	return nil
}

type AllocationSummary struct {
	Summary  AllocationDetails   `json:"summary,omitempty"`
	Clusters []AllocationDetails `json:"clusters,omitempty"`
}

type GetSummaryOptions struct {
	Window     Window        `json:"window,omitempty"`
	Resolution time.Duration `json:"resolution,omitempty"`
}

func (o GetSummaryOptions) Validate() error {
	return o.Window.Validate()
}

type EstimateCostOptions struct {
	Tenant       string                    `json:"tenant,omitempty"`
	Organization string                    `json:"organization,omitempty"`
	Period       ResourcePeriod            `json:"period,omitempty"`
	Cluster      store.ObjectReference     `json:"cluster,omitempty"`
	Items        []EstimateCostOptionsItem `json:"items,omitempty"`
}

type EstimateCostOptionsItem struct {
	Labels          map[string]string `json:"labels,omitempty"`
	MeasurementName string            `json:"measurementName,omitempty"`
	Quantity        float64           `json:"quantity,omitempty"`
}

type EstimateCostResult struct {
	OriginalAmount base.Price               `json:"originalAmount,omitempty"`
	PayableAmount  base.Price               `json:"payableAmount,omitempty"`
	Items          []EstimateCostResultItem `json:"items,omitempty"`
}

type EstimateCostResultItem struct {
	SKU            product.SKUReference `json:"sku,omitempty"`
	Period         ResourcePeriod       `json:"period,omitempty"`
	UnitPrice      base.Price           `json:"unitPrice,omitempty"`
	Unit           string               `json:"unit,omitempty"`
	Quantity       float64              `json:"quantity,omitempty"`
	OriginalAmount base.Price           `json:"originalAmount,omitempty"`
	PayableAmount  base.Price           `json:"payableAmount,omitempty"`
}

type CostService interface {
	EstimateResourceCost(ctx context.Context, options EstimateCostOptions) (*EstimateCostResult, error)
}
