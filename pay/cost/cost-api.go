package cost

import (
	"context"
	"maps"
	"net/http"
	"slices"
	"sync"
	"time"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster"
	"xiaoshiai.cn/core/observerability/prometheus"
	"xiaoshiai.cn/core/organization"
	"xiaoshiai.cn/core/pay/product"
)

func NewAPI(base base.API, mongo store.Store, sku product.SKUService, clodinfo cluster.CloudInfoGetter) *API {
	return &API{
		API:        base,
		Cloudinfo:  clodinfo,
		PolicyAPI:  NewPolicyAPI(mongo, sku),
		DetailsAPI: NewDetailsAPI(mongo),
	}
}

type API struct {
	base.API
	Cloudinfo  cluster.CloudInfoGetter
	PolicyAPI  *PolicyAPI
	DetailsAPI *DetailsAPI
}

func (a *API) OrganizationUsage(w http.ResponseWriter, r *http.Request) {
	a.OnTenantOrganization(w, r, func(ctx context.Context, storage store.Store, tenant, organization string) (any, error) {
		now := time.Now()
		options := GetSummaryOptions{
			Window: Window{
				Start: store.Time{Time: api.Query(r, "start", now.Add(-time.Hour))},
				End:   store.Time{Time: api.Query(r, "end", now)},
			},
			Resolution: api.Query(r, "resolution", time.Hour),
		}
		if err := options.Validate(); err != nil {
			return nil, err
		}
		getopt := GetAllocationsOptions{
			Window:     options.Window,
			Resolution: options.Resolution,
			Namespaces: []string{base.GetNamespaceFromTenantOrganization(tenant, organization)},
		}
		return a.SummaryClusterAllocations(ctx, getopt)
	})
}

func (a *API) TenantUsage(w http.ResponseWriter, r *http.Request) {
	a.OnTenant(w, r, func(ctx context.Context, storage store.Store, tenant string) (any, error) {
		now := time.Now()
		options := GetSummaryOptions{
			Window: Window{
				Start: store.Time{Time: api.Query(r, "start", now.Add(-time.Hour))},
				End:   store.Time{Time: api.Query(r, "end", now)},
			},
			Resolution: api.Query(r, "resolution", time.Hour),
		}
		if err := options.Validate(); err != nil {
			return nil, err
		}
		orgs := &store.List[organization.Organization]{}
		if err := storage.List(ctx, orgs); err != nil {
			return nil, err
		}
		orgnamespaces := []string{}
		for _, org := range orgs.Items {
			orgnamespaces = append(orgnamespaces, base.GetNamespaceFromTenantOrganization(tenant, org.Name))
		}
		getopt := GetAllocationsOptions{
			Window:     options.Window,
			Resolution: options.Resolution,
			Namespaces: orgnamespaces,
		}
		return a.SummaryClusterAllocations(ctx, getopt)
	})
}

func (a *API) SummaryClusterAllocations(ctx context.Context, options GetAllocationsOptions) (*AllocationSummary, error) {
	clusters := &store.List[cluster.Cluster]{}
	if err := a.Store.List(ctx, clusters); err != nil {
		return nil, err
	}
	allocationmap := map[string]AllocationDetails{}
	allocationmapmu := sync.Mutex{}
	eg, ctx := errgroup.WithContext(ctx)
	for _, cluster := range clusters.Items {
		clusterref := store.ObjectReferenceFrom(&cluster)
		eg.Go(func() error {
			allocation, err := a.GetClusterAllocation(ctx, clusterref, options)
			if err != nil {
				log.FromContext(ctx).Error(err, "failed to get cluster allocation", "cluster", clusterref.String())
				return nil
			}
			allocationmapmu.Lock()
			allocationmap[clusterref.String()] = *allocation
			allocationmapmu.Unlock()
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	allocations := slices.Collect(maps.Values(allocationmap))
	sum := SumAllocationDetails(allocations)
	sum.Labels = map[string]string{}
	ret := &AllocationSummary{
		Summary:  sum,
		Clusters: allocations,
	}
	return ret, nil
}

func (a *API) GetClusterAllocation(ctx context.Context, clusterref store.ObjectReference, options GetAllocationsOptions) (*AllocationDetails, error) {
	info, err := a.Cloudinfo.Get(ctx, clusterref)
	if err != nil {
		return nil, err
	}
	op, err := cluster.NewContainerOperation(info)
	if err != nil {
		return nil, err
	}
	prometheus := prometheus.ContainerOperationPrometheus{Operation: op}
	allication, err := AllocationCalculator{Prometheus: prometheus}.GetAllocations(ctx, options)
	if err != nil {
		return nil, err
	}
	allication.Labels = map[string]string{
		"cluster": base.ReferenceToName(clusterref),
	}
	return allication, nil
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Cost").
		SubGroup(
			base.
				NewTenantWorkspaceGroup("costs").
				Tag("Cost").
				Route(
					api.GET("/summary").
						Operation("get resource usage").
						To(a.OrganizationUsage).
						Param(
							api.QueryParam("start", "start time(rfc3339) of the window"),
							api.QueryParam("end", "end time(rfc3339) of the window"),
							api.QueryParam("resolution", "resolution of the window"),
						).
						Response(AllocationSummary{}),
				),
			base.
				NewTenantGroup("costs").
				Tag("Cost").
				Route(
					api.GET("/summary").
						Operation("get resource usage").
						To(a.TenantUsage).
						Param(
							api.QueryParam("start", "start time(rfc3339) of the window"),
							api.QueryParam("end", "end time(rfc3339) of the window"),
							api.QueryParam("resolution", "resolution of the window"),
						).
						Response(AllocationSummary{}),
				),
			a.PolicyAPI.Group(),
			a.DetailsAPI.Group(),
		)
}

func (a *API) PublicGroup() api.Group {
	return api.
		NewGroup("").
		Tag("Cost").
		SubGroup(
			a.PolicyAPI.PublicGroup(),
		)
}
