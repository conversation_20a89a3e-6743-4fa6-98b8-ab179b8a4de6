package cost

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"
	corev1 "k8s.io/api/core/v1"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/observerability/prometheus"
)

type AllocationCalculator struct {
	Prometheus prometheus.Prometheus
}

type GetAllocationsOptions struct {
	Window              Window
	Namespaces          []string
	AdditionalCondition string
	Resolution          time.Duration
}

type Window struct {
	Start store.Time `json:"start,omitempty"`
	End   store.Time `json:"end,omitempty"`
}

func (w *Window) Duration() time.Duration {
	return w.End.Sub(w.Start.Time).Truncate(time.Second)
}

func (w *Window) Empty() bool {
	return w.Start.IsZero() || w.End.IsZero()
}

func (w *Window) Validate() error {
	if w.Empty() {
		return fmt.Errorf("window start and end time must be specified")
	}
	if w.Start.After(w.End.Time) {
		return fmt.Errorf("window start time must be before end time")
	}
	return nil
}

// Usage represents a quantity of a resource used over a period of time.
type Usage struct {
	Value float64 `json:"value"`
	Unit  string  `json:"unit,omitempty"`
}

func (u *Usage) Add(other Usage) {
	u.Value += other.Value
}

type AllocationDetails struct {
	Window                Window             `json:"window,omitempty"`
	Labels                map[string]string  `json:"labels,omitempty"`
	CPU                   Usages             `json:"cpu,omitempty"`
	Memory                Usages             `json:"memory,omitempty"`
	ContainerNetwork      NetworkUsage       `json:"containerNetwork,omitempty"`
	PersistentVolumeClaim Usages             `json:"persistentVolumeClaim,omitempty"`
	GPU                   TypedResourceUsage `json:"gpu,omitempty"`
	Ingress               NetworkUsage       `json:"ingress,omitempty"`
	Loadbalancer          NetworkUsage       `json:"loadbalancer,omitempty"`
}

type TypedResourceUsage map[corev1.ResourceName]Usages

func (t TypedResourceUsage) Add(other TypedResourceUsage) {
	if other == nil || t == nil {
		return
	}
	for name, usage := range other {
		if val, ok := t[name]; !ok {
			t[name] = usage
		} else {
			val.Add(usage)
			t[name] = val
		}
	}
}

type Usages struct {
	// Requested is the requested usage of the resource.
	Requested Usage `json:"requested,omitempty"`
	// Used is the actual usage of the resource.
	Used Usage `json:"used,omitempty"`
}

func (r *Usages) Add(other Usages) {
	r.Requested.Add(other.Requested)
	r.Used.Add(other.Used)
}

type NetworkUsage struct {
	Rx Usage `json:"rx,omitempty"`
	Tx Usage `json:"tx,omitempty"`
}

func (n *NetworkUsage) Add(other *NetworkUsage) {
	if other == nil {
		return
	}
	n.Rx.Add(other.Rx)
	n.Tx.Add(other.Tx)
}

func (c AllocationCalculator) GetAllocations(ctx context.Context, options GetAllocationsOptions) (*AllocationDetails, error) {
	if err := options.Window.Validate(); err != nil {
		return nil, err
	}
	return c.getAllocations(ctx, options)
}

const MaxPrometheusQueryDuration = 24 * time.Hour

func (c *AllocationCalculator) rangeAllocations(ctx context.Context, options GetAllocationsOptions) ([]AllocationDetails, error) {
	window := options.Window
	if err := window.Validate(); err != nil {
		return nil, err
	}
	start, end := window.Start.Time, window.End.Time
	rangeduration := window.Duration()
	resolution := options.Resolution
	if resolution == 0 {
		resolution = min(rangeduration, MaxPrometheusQueryDuration)
	}
	pos := start
	alldetails := []AllocationDetails{}
	for pos.Before(end) {
		next := pos.Add(resolution)
		if next.After(end) {
			next = end
		}
		options.Window = Window{Start: store.Time{Time: pos}, End: store.Time{Time: next}}
		details, err := c.getAllocations(ctx, options)
		if err != nil {
			return nil, err
		}
		alldetails = append(alldetails, *details)
		pos = next
	}
	return alldetails, nil
}

func (c *AllocationCalculator) getAllocations(ctx context.Context, options GetAllocationsOptions) (*AllocationDetails, error) {
	window := options.Window
	resolution := options.Resolution

	details := &AllocationDetails{
		Window: window,
	}
	condition, by := "", ""
	if len(options.Namespaces) != 0 {
		// exmaple: http_requests_total{environment=~"staging|testing|development"}
		condition += fmt.Sprintf(`namespace=~"%s",`, strings.Join(options.Namespaces, "|"))
	}
	if options.AdditionalCondition != "" {
		condition += options.AdditionalCondition
	}
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		query := fmt.Sprintf(`avg(avg_over_time(kube_pod_container_resource_limits{resource="cpu", unit="core", %s}[%s])) by (pod, namespace, %s)`, condition, resolution, by)
		details.CPU.Requested.Unit = "core/h"
		return c.queryRange(ctx, query, window, resolution, &details.CPU.Requested.Value)
	})
	eg.Go(func() error {
		query := fmt.Sprintf(`avg(avg_over_time(kube_pod_container_resource_limits{resource="memory", unit="byte", %s}[%s])) by (pod, namespace, %s)`, condition, resolution, by)
		details.Memory.Requested.Unit = "byte/h"
		return c.queryRange(ctx, query, window, resolution, &details.Memory.Requested.Value)
	})
	eg.Go(func() error {
		query := fmt.Sprintf(`avg(avg_over_time(kube_persistentvolumeclaim_resource_requests_storage_bytes{%s}[%s])) by (persistentvolumeclaim, namespace, %s)`, condition, resolution, by)
		details.PersistentVolumeClaim.Requested.Unit = "byte/h"
		return c.queryRange(ctx, query, window, resolution, &details.PersistentVolumeClaim.Requested.Value)
	})
	eg.Go(func() error {
		query := fmt.Sprintf(`round(sum(increase(container_network_receive_bytes_total{pod!="", %s}[%s])) by (pod, namespace, %s))`, condition, resolution, by)
		details.ContainerNetwork.Rx.Unit = "byte"
		return c.queryRange(ctx, query, window, resolution, &details.ContainerNetwork.Rx.Value)
	})
	eg.Go(func() error {
		query := fmt.Sprintf(`round(sum(increase(container_network_transmit_bytes_total{pod!="", %s}[%s])) by (pod, namespace, %s))`, condition, resolution, by)
		details.ContainerNetwork.Tx.Unit = "byte"
		return c.queryRange(ctx, query, window, resolution, &details.ContainerNetwork.Tx.Value)
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	return details, nil
}

func (c *AllocationCalculator) queryRange(ctx context.Context, query string, window Window, step time.Duration, into *float64) error {
	matrixs, err := prometheus.QueryMatrix(ctx, c.Prometheus, query, window.Start.Time, window.End.Time, step)
	if err != nil {
		return err
	}
	val := float64(0)
	for _, matrix := range matrixs.Result {
		if len(matrix.Values) > 0 {
			// remove the first value, because it is the previous value of the window
			matrix.Values = matrix.Values[1:]
		}
		for _, v := range matrix.Values {
			f, err := strconv.ParseFloat(v.Value, 64)
			if err != nil {
				return err
			}
			val += f
		}
	}
	*into = val
	return nil
}

func SumAllocationDetails(details []AllocationDetails) AllocationDetails {
	if len(details) == 0 {
		return AllocationDetails{}
	}
	sum := details[0]
	for _, detail := range details[1:] {
		if detail.Window.Start.Before(&sum.Window.Start) {
			sum.Window.Start = detail.Window.Start
		}
		if detail.Window.End.After(sum.Window.End.Time) {
			sum.Window.End = detail.Window.End
		}
		sum.CPU.Add(detail.CPU)
		sum.Memory.Add(detail.Memory)
		sum.PersistentVolumeClaim.Add(detail.PersistentVolumeClaim)
		sum.ContainerNetwork.Add(&detail.ContainerNetwork)
		sum.GPU.Add(detail.GPU)
	}
	return sum
}
