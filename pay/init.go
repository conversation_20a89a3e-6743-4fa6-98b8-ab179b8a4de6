package pay

import (
	"context"
	"fmt"

	"golang.org/x/sync/errgroup"
	"xiaoshiai.cn/common/controller"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/artifact"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/cluster/privatenode"
	"xiaoshiai.cn/core/message/events"
	"xiaoshiai.cn/core/pay/analytics"
	"xiaoshiai.cn/core/pay/autorenew"
	"xiaoshiai.cn/core/pay/billing"
	"xiaoshiai.cn/core/pay/cost"
	"xiaoshiai.cn/core/pay/order"
	"xiaoshiai.cn/core/pay/payment"
	paymentwallet "xiaoshiai.cn/core/pay/payment/wallet"
	"xiaoshiai.cn/core/pay/paymentcallback"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/pay/promotion"
	"xiaoshiai.cn/core/pay/resourcemetadata"
	"xiaoshiai.cn/core/pay/shipment"
	"xiaoshiai.cn/core/wallet"
)

type Options struct {
	Order *order.StoreOrderOptions
}

func NewDefaultOptions() *Options {
	return &Options{
		Order: order.NewLocalOrderOptions(),
	}
}

func New(ctx context.Context, store store.Store, mongostore *mongo.MongoStorage, recorder events.Recorder, artifact *artifact.ArtifactService, options *Options) (*PaySystem, error) {
	// promotion system
	promotiondiscount := promotion.NewDefaultDiscountService(mongostore)

	// product system
	skuservice := product.NewLocalStoreSKUService(mongostore)

	// resource metadata
	resourceMetadata := &DefaultResourceMetadata{Store: store}

	// wallet payment
	paymentQueue := base.NewQueue(mongostore, "payment-events-queue")

	walletEventQueue := base.NewQueue(mongostore, "wallet-events-queue")
	walletService := wallet.NewWalletService(mongostore, walletEventQueue, recorder)
	walletpayment := paymentwallet.NewWalletPaymentServiceQueueCallback(walletService, mongostore, paymentQueue)

	// payments
	paymentman := payment.NewPaymentChannelManager(mongostore)
	paymentman.AddPayment(ctx, payment.ChannelWallet, walletpayment)

	// order system
	orderEventsQueue := base.NewQueue(mongostore, "order-events-queue")
	orderservice := order.NewLocalOrderService(store, mongostore, paymentman, promotiondiscount, walletService, skuservice, orderEventsQueue, resourceMetadata, options.Order)

	// billing system
	billingEventsQueue := base.NewQueue(mongostore, "billing-events-queue")
	billingservice := billing.NewDefaultBillingService(mongostore, billingEventsQueue, promotiondiscount, skuservice)

	// shipment system
	ordereventconsumer := shipment.NewOrderShipment(store, mongostore, orderEventsQueue, orderservice, skuservice, artifact)
	billingeventconsumer := shipment.NewBillingShipment(mongostore, billingEventsQueue, billingservice)
	walleteventconsumer := shipment.NewWalletEventConsumer(store, walletService, walletEventQueue)

	// analytics system
	aggrator := analytics.NewOrderReportAggrator(mongostore, orderservice, billingservice)

	// callback
	paymentconsumer := paymentcallback.NewPaymentEventsConsumer(mongostore, paymentQueue, orderservice, billingservice)

	costsvc := cost.NewDefaultCostService(mongostore, billingservice)

	ps := &PaySystem{
		PaymentManager:       paymentman,
		OrderService:         orderservice,
		OrderStore:           mongostore,
		SKUService:           skuservice,
		BillingService:       billingservice,
		PaymentEventConsumer: paymentconsumer,
		DiscountService:      promotiondiscount,
		Wallet:               walletService,
		WalletPayment:        walletpayment,
		OrderShipment:        ordereventconsumer,
		BillingShipment:      billingeventconsumer,
		WalletEventConsumer:  walleteventconsumer,
		OrderReportAggrator:  aggrator,
		CostService:          costsvc,
	}
	return ps, nil
}

type PaySystem struct {
	PaymentManager       *payment.PaymentChannelManager
	OrderStore           store.TransactionStore
	OrderService         *order.LocalOrderService
	SKUService           product.SKUService
	BillingService       *billing.DefaultBillingService
	PaymentEventConsumer *paymentcallback.PaymentEventConsumer
	DiscountService      promotion.DiscountService
	Wallet               wallet.WalletService
	WalletPayment        *paymentwallet.WalletPaymentService
	OrderShipment        *shipment.OrderEventConsumer
	BillingShipment      *shipment.BillingEventConsumer
	OrderReportAggrator  *analytics.Aggrator
	WalletEventConsumer  *shipment.WalletEventConsumer
	CostService          cost.CostService
}

func NewPaySystemController(pay *PaySystem) *PaySystemController {
	return &PaySystemController{
		Pay:                     pay,
		PaymentChannelContrller: payment.NewPaymentChannelContrller(pay.PaymentManager),
		OrderProcess:            order.NewLocalOrderProcess(pay.OrderStore, pay.PaymentManager, pay.OrderService),
		WalletPaymentProcess:    paymentwallet.NewPaymentProcess(pay.WalletPayment, pay.OrderStore),
		WalletRefundProcess:     paymentwallet.NewRefundProcess(pay.WalletPayment, pay.OrderStore),
		BillingProcess:          billing.NewBillingLocalProcess(pay.OrderStore, pay.BillingService, pay.WalletPayment),
		AutoRenew:               autorenew.NewOrderRenewProcess(pay.OrderStore, pay.OrderService),
	}
}

type PaySystemController struct {
	Pay                     *PaySystem
	PaymentChannelContrller *payment.PaymentChannelContrller
	OrderProcess            controller.Runable
	BillingProcess          controller.Runable
	WalletPaymentProcess    controller.Runable
	WalletRefundProcess     controller.Runable
	AutoRenew               *autorenew.ResourceRenewProcess
}

func (p *PaySystemController) Name() string {
	return "pay-system-daemon"
}

func (p *PaySystemController) Initialize(ctx context.Context) error {
	if err := p.PaymentChannelContrller.Initialize(ctx); err != nil {
		return err
	}
	return nil
}

func (p *PaySystemController) Run(ctx context.Context) error {
	eg, ctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		return p.Pay.PaymentEventConsumer.Run(ctx)
	})
	eg.Go(func() error {
		return p.PaymentChannelContrller.Run(ctx)
	})
	eg.Go(func() error {
		return p.WalletPaymentProcess.Run(ctx)
	})
	eg.Go(func() error {
		return p.WalletRefundProcess.Run(ctx)
	})
	eg.Go(func() error {
		return p.OrderProcess.Run(ctx)
	})
	eg.Go(func() error {
		return p.BillingProcess.Run(ctx)
	})
	eg.Go(func() error {
		return p.Pay.OrderShipment.Run(ctx)
	})
	eg.Go(func() error {
		return p.Pay.BillingShipment.Run(ctx)
	})
	eg.Go(func() error {
		return p.Pay.WalletEventConsumer.Run(ctx)
	})
	eg.Go(func() error {
		return p.Pay.OrderReportAggrator.Run(ctx)
	})
	eg.Go(func() error {
		return p.AutoRenew.Run(ctx)
	})
	return eg.Wait()
}

type DefaultResourceMetadata struct {
	Store store.Store
}

func (d *DefaultResourceMetadata) GetMetadata(ctx context.Context, ref store.ResourcedObjectReference) (*resourcemetadata.ResourceMetadata, error) {
	switch ref.Resource {
	case "privatenodeclaims":
		privateNodeClaim := &privatenode.PrivateNodeClaim{}
		if err := d.Store.Scope(ref.Scopes...).Get(ctx, ref.Name, privateNodeClaim); err != nil {
			return nil, err
		}
		ret := &resourcemetadata.ResourceMetadata{
			SKU: product.SKUReference{
				Category: product.CategoryPrivateNode,
				Product:  privateNodeClaim.Cluster.Name,
				Name:     privateNodeClaim.Model,
			},
			Expires: privateNodeClaim.Expire.Time,
		}
		return ret, nil
	}
	return nil, fmt.Errorf("cannot find sku reference for %s", ref.Resource)
}
