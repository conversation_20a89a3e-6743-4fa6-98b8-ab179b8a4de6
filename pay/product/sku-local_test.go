package product

import (
	"reflect"
	"testing"
	"time"

	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func Test_getSKUPriceAtTime(t *testing.T) {
	type args struct {
		sku  *SKU
		date time.Time
	}
	tests := []struct {
		name string
		args args
		want base.Price
	}{
		{
			name: "test1",
			args: args{
				sku: &SKU{
					Price: base.NewPrice(1),
					Histories: []SKUHistory{
						{
							StartTimestamp: store.Time{Time: time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC)},
							EndTimestamp:   store.Time{Time: time.Date(2023, 10, 2, 0, 0, 0, 0, time.UTC)},
							Price:          base.NewPrice(3),
						},
						{
							StartTimestamp: store.Time{Time: time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC)},
							EndTimestamp:   store.Time{Time: time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC)},
							Price:          base.NewPrice(2),
						},
					},
				},
				date: time.Date(2023, 10, 1, 11, 0, 0, 0, time.UTC),
			},
			want: base.NewPrice(2),
		},
		{
			name: "test2",
			args: args{
				sku: &SKU{
					Price: base.NewPrice(1),
					Histories: []SKUHistory{
						{
							EndTimestamp:   store.Time{Time: time.Date(2023, 10, 2, 0, 0, 0, 0, time.UTC)},
							StartTimestamp: store.Time{Time: time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC)},
							Price:          base.NewPrice(3),
						},
						{
							EndTimestamp:   store.Time{Time: time.Date(2023, 10, 1, 12, 0, 0, 0, time.UTC)},
							StartTimestamp: store.Time{Time: time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC)},
							Price:          base.NewPrice(2),
						},
					},
				},
				date: time.Date(2023, 10, 0, 11, 0, 0, 0, time.UTC),
			},
			want: base.NewPrice(1),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getSKUPriceAtTime(tt.args.sku, tt.args.date); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSKUPriceAtTime() = %v, want %v", got, tt.want)
			}
		})
	}
}
