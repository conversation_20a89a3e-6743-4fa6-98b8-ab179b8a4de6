package billing

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"xiaoshiai.cn/common/errors"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/queue"
	"xiaoshiai.cn/common/rand"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/common/store/mongo"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/payment"
	"xiaoshiai.cn/core/pay/product"
	"xiaoshiai.cn/core/pay/promotion"
)

func init() {
	mongo.GlobalObjectsScheme.Register(&BillingRecord{},
		mongo.ObjectDefination{
			Uniques: []mongo.UnionFields{
				{"name"},
			},
			Indexes: []mongo.UnionFields{
				{"creationTimestamp"},
				{"tenant"},
				{"creator"},
				{"organization"},
			},
		})
}

const BillingIDPrefix = "bill-"

func NewBillingID() string {
	return BillingIDPrefix + time.Now().Format("20060102150405") + rand.RandomNumeric(6)
}

type QueueBillingServiceCallback struct {
	Queue queue.Queue
}

func (q *QueueBillingServiceCallback) Callback(ctx context.Context, event BillingEvent) error {
	data, err := json.Marshal(event)
	if err != nil {
		return err
	}
	return q.Queue.Enqueue(ctx, "", data, queue.EnqueueOptions{})
}

func NewDefaultBillingService(mongo store.TransactionStore, events queue.Queue, discount promotion.DiscountService, skus product.SKUService) *DefaultBillingService {
	return &DefaultBillingService{
		mongo:    mongo,
		discount: discount,
		skus:     skus,
		callback: &QueueBillingServiceCallback{Queue: events},
	}
}

var _ BillingService = &DefaultBillingService{}

type DefaultBillingService struct {
	mongo    store.TransactionStore
	discount promotion.DiscountService
	skus     product.SKUService
	callback BillingServiceCallback
}

// OnPaymentEvent implements BillingService.
func (d *DefaultBillingService) OnPaymentEvent(ctx context.Context, status payment.PaymentEvent) error {
	obj, err := d.GetBilling(ctx, status.OrderID, GetBillingOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}
	switch status.State {
	case payment.PaymentStatePaid:
		if obj.State == BillingRecordStatePaid {
			return nil
		}
		patch := store.MapMergePatch{
			"state":    BillingRecordStatePaid,
			"paidTime": status.PaiedTime,
		}
		if err := d.mongo.Patch(ctx, obj, patch); err != nil {
			return err
		}
		d.event(ctx, BillingEvent{ID: obj.Name, State: BillingRecordStatePaid})
		return nil
	case payment.PaymentStateClosed:
		if obj.State == BillingRecordStateDue {
			return nil
		}
		patch := store.MapMergePatch{
			"state": BillingRecordStateDue,
		}
		if err := d.mongo.Patch(ctx, obj, patch); err != nil {
			return err
		}
		d.event(ctx, BillingEvent{ID: obj.Name, State: BillingRecordStateDue})
		return nil
	default:
		if obj.State == BillingRecordStateUnpaid {
			return nil
		}
		patch := store.MapMergePatch{
			"state": BillingRecordStateUnpaid,
		}
		if err := d.mongo.Patch(ctx, obj, patch); err != nil {
			return err
		}
		d.event(ctx, BillingEvent{ID: obj.Name, State: BillingRecordStateUnpaid})
		return nil
	}
}

func (d *DefaultBillingService) event(ctx context.Context, e BillingEvent) {
	if d.callback == nil {
		return
	}
	if err := d.callback.Callback(ctx, e); err != nil {
		log.FromContext(ctx).Error(err, "callback billing event")
	}
}

// GetBilling implements BillingService.
func (d *DefaultBillingService) GetBilling(ctx context.Context, name string, options GetBillingOptions) (*BillingRecord, error) {
	opts := []store.GetOption{}
	if options.Account != "" {
		opts = append(opts, store.WithGetFieldRequirements(store.RequirementEqual("account", options.Account)))
	}
	if options.Tenant != "" {
		opts = append(opts, store.WithGetFieldRequirements(store.RequirementEqual("tenant", options.Tenant)))
	}
	if options.Organization != "" {
		opts = append(opts, store.WithGetFieldRequirements(store.RequirementEqual("organization", options.Organization)))
	}
	billing := &BillingRecord{}
	if err := d.mongo.Get(ctx, name, billing, opts...); err != nil {
		return nil, err
	}
	return billing, nil
}

// ListBilling implements BillingService.
func (d *DefaultBillingService) ListBilling(ctx context.Context, options ListBillingOptions) (store.List[BillingRecord], error) {
	fields := store.Requirements{}
	fields = append(fields, store.NewCreationRangeRequirement(options.From, options.To)...)
	if options.Account != "" {
		fields = append(fields, store.RequirementEqual("account", options.Account))
	}
	if options.Tenant != "" {
		fields = append(fields, store.RequirementEqual("tenant", options.Tenant))
	}
	if options.Organization != "" {
		fields = append(fields, store.RequirementEqual("organization", options.Organization))
	}
	if options.State != "" {
		fields = append(fields, store.RequirementEqual("state", options.State))
	}
	if options.ReferenceName != "" {
		fields = append(fields, store.RequirementEqual("reference.name", options.ReferenceName))
	}
	opts := base.ListOptionsToStoreListOptions(options.ListOptions)
	opts = append(opts, store.WithFieldRequirements(fields...))
	list := store.List[BillingRecord]{}
	if err := d.mongo.List(ctx, &list, opts...); err != nil {
		return list, err
	}
	return list, nil
}

// CreateBilling implements BillingService.
func (d *DefaultBillingService) CreateBilling(ctx context.Context, account string, options CreateBillingOptions) (*BillingRecord, error) {
	result, err := d.calcItemsAmount(ctx, account, options)
	if err != nil {
		return nil, err
	}
	record := &BillingRecord{
		ObjectMeta: store.ObjectMeta{
			Name:        NewBillingID(),
			Description: options.Description,
		},
		Tenant:         result.Tenant,
		Organization:   result.Organization,
		Account:        account,
		From:           result.From,
		To:             result.To,
		OriginalAmount: result.OriginalAmount,
		PayableAmount:  result.PayableAmount,
		Items:          result.Items,
		Operator:       options.Operator,
		Reference:      options.Reference,
		NotAfter:       time.Now().Add(options.PaymentTimeout).Truncate(time.Second),
		State:          BillingRecordStateUnpaid,
	}
	if err := d.mongo.Create(ctx, record); err != nil {
		return nil, err
	}
	d.event(ctx, BillingEvent{ID: record.Name, State: BillingRecordStateUnpaid})
	return record, nil
}

// EstimateBilling implements BillingService.
func (d *DefaultBillingService) EstimateBilling(ctx context.Context, account string, options CreateBillingOptions) (*EstimateBillingRecord, error) {
	ret, err := d.calcItemsAmount(ctx, account, options)
	if err != nil {
		return nil, err
	}
	return &EstimateBillingRecord{
		Account:        ret.Account,
		From:           ret.From,
		To:             ret.To,
		Tenant:         ret.Tenant,
		Organization:   ret.Organization,
		Operator:       options.Operator,
		Reference:      options.Reference,
		OriginalAmount: ret.OriginalAmount,
		PayableAmount:  ret.PayableAmount,
		Items:          ret.Items,
	}, nil
}

type itemsWithAmount struct {
	Account        string        `json:"account,omitempty"`
	From           time.Time     `json:"from,omitempty"`
	To             time.Time     `json:"to,omitempty"`
	Tenant         string        `json:"tenant,omitempty"`
	Organization   string        `json:"organization,omitempty"`
	OriginalAmount base.Price    `json:"originalAmount,omitempty"`
	PayableAmount  base.Price    `json:"payableAmount,omitempty"`
	Items          []BillingItem `json:"items,omitempty"`
}

func (d *DefaultBillingService) calcItemsAmount(ctx context.Context, account string, options CreateBillingOptions) (*itemsWithAmount, error) {
	items := make([]BillingItem, 0, len(options.Items))
	for _, item := range options.Items {
		it := BillingItem{
			SKU:          item.SKU,
			Quantity:     item.Quantity,
			Namespace:    item.Namespace,
			Instance:     item.Instance,
			InstanceName: item.InstanceName,
			Cluster:      item.Cluster,
			Description:  item.Description,
		}
		sku, err := d.skus.GetSKUPrice(ctx, item.SKU.Category, item.SKU.Product, item.SKU.Name, options.From)
		if err != nil {
			return nil, fmt.Errorf("get sku: %s", err.Error())
		}
		it.UnitPrice = sku.Price
		if item.Description == "" {
			it.Description = sku.Description
		}
		it.Unit = sku.Unit
		items = append(items, it)
	}
	totaloriginal, totalpayable := base.NewPrice(0), base.NewPrice(0)
	if d.discount != nil {
		req := promotion.CalculateDiscountOptions{
			Tenant:       options.Tenant,
			Organization: options.Organization,
			Kind:         string(BillingKindComputeResource),
			Items:        make([]promotion.CalculateDiscountItem, 0, len(items)),
			Round:        options.Round,
		}
		for _, item := range items {
			req.Items = append(req.Items, promotion.CalculateDiscountItem{
				SKU:       item.SKU,
				Quantity:  item.Quantity,
				UnitPrice: item.UnitPrice,
			})
		}
		result, err := d.discount.EstimateDiscount(ctx, account, req)
		if err != nil {
			return nil, err
		}
		for i := range result.Items {
			items[i].PayableAmount = result.Items[i].PayableAmount
			items[i].OriginalAmount = result.Items[i].OriginalAmount
			items[i].Discount = result.Items[i].Discount
		}
		totaloriginal = result.OriginalAmount
		totalpayable = result.PayableAmount
	} else {
		for i := range items {
			items[i].PayableAmount = items[i].UnitPrice.Mul(base.NewPrice(items[i].Quantity)).Round(options.Round)
			items[i].OriginalAmount = items[i].PayableAmount
			items[i].Discount = 1.0

			totaloriginal = totaloriginal.Add(items[i].OriginalAmount)
			totalpayable = totalpayable.Add(items[i].PayableAmount)
		}
	}
	record := &itemsWithAmount{
		Tenant:         options.Tenant,
		Organization:   options.Organization,
		Account:        account,
		From:           options.From,
		To:             options.To,
		OriginalAmount: totaloriginal,
		PayableAmount:  totalpayable,
		Items:          items,
	}
	return record, nil
}
