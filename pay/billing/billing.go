package billing

import (
	"context"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
	"xiaoshiai.cn/core/pay/payment"
	"xiaoshiai.cn/core/pay/product"
)

type BillingKind string

const (
	BillingKindComputeResource BillingKind = "ComputeResource"
)

type BillingRecordState string

const (
	BillingRecordStateUnpaid    BillingRecordState = "Unpaid"
	BillingRecordStateDue       BillingRecordState = "Due"
	BillingRecordStatePaid      BillingRecordState = "Paid"
	BillingRecordStateCancelled BillingRecordState = "Cancelled"
)

type BillingRecord struct {
	store.ObjectMeta `json:",inline"`
	Account          string     `json:"account,omitempty"`
	From             time.Time  `json:"from,omitempty"`
	To               time.Time  `json:"to,omitempty"`
	Tenant           string     `json:"tenant,omitempty"`
	Organization     string     `json:"organization,omitempty"`
	Operator         string     `json:"operator,omitempty"`
	OriginalAmount   base.Price `json:"originalAmount,omitempty"`
	PayableAmount    base.Price `json:"payableAmount,omitempty"`
	// Reference is the reference to the object that is being billed.
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	Items     []BillingItem                  `json:"items,omitempty"`
	NotAfter  time.Time                      `json:"notAfter,omitempty"`

	State     BillingRecordState `json:"state,omitempty"`
	PaymentID string             `json:"paymentID,omitempty"`
	PaidTime  *time.Time         `json:"paidTime,omitempty"`
}

type EstimateBillingRecord struct {
	Account        string     `json:"account,omitempty"`
	From           time.Time  `json:"from,omitempty"`
	To             time.Time  `json:"to,omitempty"`
	Tenant         string     `json:"tenant,omitempty"`
	Organization   string     `json:"organization,omitempty"`
	Operator       string     `json:"operator,omitempty"`
	OriginalAmount base.Price `json:"originalAmount,omitempty"`
	PayableAmount  base.Price `json:"payableAmount,omitempty"`
	// Reference is the reference to the object that is being billed.
	Reference store.ResourcedObjectReference `json:"reference,omitempty"`
	Items     []BillingItem                  `json:"items,omitempty"`
}

type BillingItem struct {
	Description string               `json:"description,omitempty"`
	SKU         product.SKUReference `json:"sku,omitempty"`
	Unit        string               `json:"unit,omitempty"` // sku unit
	Quantity    float64              `json:"quantity,omitempty"`
	UnitPrice   base.Price           `json:"unitPrice,omitempty"`

	OriginalAmount base.Price `json:"originalAmount,omitempty"`
	PayableAmount  base.Price `json:"payableAmount,omitempty"`
	Discount       float64    `json:"discount,omitempty"` // discount ratio

	StartTime   *time.Time        `json:"startTime,omitempty"`
	EndTime     *time.Time        `json:"endTime,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`

	Cluster      store.ObjectReference `json:"cluster,omitempty"`
	Instance     string                `json:"instance,omitempty"`
	InstanceName string                `json:"instanceName,omitempty"`
	Namespace    string                `json:"namespace,omitempty"`
}

type CreateBillingOptions struct {
	Tenant       string
	Organization string
	Operator     string
	From         time.Time
	To           time.Time
	Round        int32
	Description  string
	Reference    store.ResourcedObjectReference
	Items        []CreateBillingItem
	// PaymentTimeout is the timeout for payment.
	// If the payment is not paid within the timeout, the billing will be marked as due.
	PaymentTimeout time.Duration
}

// CreateBillingItem
// if OriginalAmount is zero, the price will be calculated from the SKU service
type CreateBillingItem struct {
	SKU         product.SKUReference
	Quantity    float64
	Description string

	Cluster      store.ObjectReference
	Instance     string // instance id
	InstanceName string // instance name
	Namespace    string
}

type ListBillingOptions struct {
	ListOptions   api.ListOptions
	Account       string
	Tenant        string
	Organization  string
	From          time.Time
	To            time.Time
	State         BillingRecordState
	ReferenceName string
}

type GetBillingOptions struct {
	Account      string
	Tenant       string
	Organization string
}

type BillingEvent struct {
	ID    string             `json:"id"`
	State BillingRecordState `json:"state"`
}

type BillingServiceCallback interface {
	Callback(ctx context.Context, event BillingEvent) error
}

type BillingService interface {
	OnPaymentEvent(ctx context.Context, event payment.PaymentEvent) error

	EstimateBilling(ctx context.Context, account string, options CreateBillingOptions) (*EstimateBillingRecord, error)
	CreateBilling(ctx context.Context, account string, options CreateBillingOptions) (*BillingRecord, error)
	ListBilling(ctx context.Context, options ListBillingOptions) (store.List[BillingRecord], error)
	GetBilling(ctx context.Context, name string, options GetBillingOptions) (*BillingRecord, error)
}
