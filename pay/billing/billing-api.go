package billing

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/base"
)

func NewAPI(svc BillingService) *API {
	return &API{Service: svc}
}

type API struct {
	Service BillingService
}

func (a *API) ListBilling(w http.ResponseWriter, r *http.Request) {
	base.OnSystemOrTenantOrTenantOrganization(w, r, func(ctx context.Context, tenant, org string) (any, error) {
		from, to := api.Query(r, "from", time.Time{}), api.Query(r, "to", time.Time{})

		// allow query parameters if path parameters not set
		if tenant == "" {
			tenant = api.Query(r, "tenant", "")
		}
		if org == "" {
			org = api.Query(r, "organization", "")
		}

		reqOptions := ListBillingOptions{
			ListOptions:  api.GetListOptions(r),
			Tenant:       tenant,
			Organization: org,
			From:         from,
			To:           to,
		}
		if application := api.Path(r, "application", ""); application != "" {
			reqOptions.ReferenceName = application
		}
		if reqOptions.ReferenceName == "" {
			reqOptions.ReferenceName = api.Query(r, "referenceName", "")
		}
		return a.Service.ListBilling(ctx, reqOptions)
	})
}

func (a *API) GetBilling(w http.ResponseWriter, r *http.Request) {
	base.OnSystemOrTenantOrTenantOrganization(w, r, func(ctx context.Context, tenant, org string) (any, error) {
		bill := api.Path(r, "bill", "")
		return a.Service.GetBilling(ctx, bill, GetBillingOptions{Tenant: tenant, Organization: org})
	})
}

func (a *API) billingRoutes() []api.Route {
	return []api.Route{
		api.GET("").
			To(a.ListBilling).
			Param(api.PageParams...).
			Param(api.QueryParam("from", "Start time").In("query", "from")).
			Param(api.QueryParam("to", "End time").In("query", "to")).
			Param(api.QueryParam("referenceName", "Reference name").Optional()).
			Param(api.PathParam("tenant", "Tenant name").Optional()).
			Param(api.PathParam("organization", "Organization name").Optional()).
			Response(store.List[BillingRecord]{}),

		api.GET("/{bill}").
			To(a.GetBilling).
			Response(BillingRecord{}),
	}
}

func (a *API) Group() api.Group {
	return api.
		NewGroup("").
		Tag("Billing").
		SubGroup(
			api.NewGroup("bills").Route(a.billingRoutes()...),
			base.NewTenantGroup("bills").Route(a.billingRoutes()...),
			base.NewTenantOrganizationGroup("bills").Route(a.billingRoutes()...),
			base.NewApplicationGroup("bills").Route(a.billingRoutes()...),
		)
}
