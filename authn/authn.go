package authn

import (
	"context"
	"net/http"
	"time"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/common/rest/api"
	"xiaoshiai.cn/common/store"
	"xiaoshiai.cn/core/auth"
)

type AuthenticationOptions struct {
	Anonymous        bool             `json:"anonymous,omitempty"`
	TokenExpiration  time.Duration    `json:"tokenExpiration,omitempty"`
	RememberDuration time.Duration    `json:"rememberDuration,omitempty"`
	RefreshThreshold time.Duration    `json:"refreshThreshold,omitempty"`
	OIDC             *api.OIDCOptions `json:"oidc,omitempty"`
	CacheDuration    time.Duration    `json:"cacheDuration,omitempty" description:"cache duration for an valid token"`
	CacheSize        int              `json:"cacheSize,omitempty" description:"cache size for an valid token"`
}

func DefaultAuthenticationOptions() *AuthenticationOptions {
	return &AuthenticationOptions{
		Anonymous:       false,
		OIDC:            api.NewDefaultOIDCOptions(),
		TokenExpiration: 7 * 24 * time.Hour,
	}
}

func BuildAuthn(ctx context.Context, storage store.Store,
	options *AuthenticationOptions,
	tokenautn []api.TokenAuthenticator,
	basic []api.BasicAuthenticator,
	authn []api.Authenticator,
	// otherauthn ...api.TokenAuthenticator,
) (api.Authenticator, error) {
	tokenAuthnChain := api.TokenAuthenticatorChain{}
	tokenAuthnChain = append(tokenAuthnChain, tokenautn...)
	if options.OIDC.Issuer != "" {
		// https://kubernetes.io/docs/reference/access-authn-authz/authentication/#openid-connect-tokens
		// same with k8s, use id_token(not access_token) as bearer token
		// we only need to know who the user is, not what the user can do
		oidcauthenticator, err := api.NewOIDCAuthenticator(ctx, options.OIDC)
		if err != nil {
			return nil, err
		}
		tokenAuthnChain = append(tokenAuthnChain, oidcauthenticator)
	}
	tokenauthn := api.TokenAuthenticator(tokenAuthnChain)
	// if cahce enabled, cache token
	if options.CacheDuration > 0 {
		cachesize := options.CacheSize
		if cachesize <= 0 {
			cachesize = 64
		}
		tokenauthn = api.NewCacheTokenAuthenticator(tokenauthn, cachesize, options.CacheDuration)
	}
	basicauthn := api.BasicAuthenticatorChain(basic)
	authnlist := []api.Authenticator{
		api.BasicAuthenticatorWrap(basicauthn),
		api.BearerTokenAuthenticatorWrap(tokenauthn),
		UnsetCookieOnUnauthrizedAuthenticator{
			Authenticator: api.SessionAuthenticatorWrap(tokenauthn, auth.SessionCookieKey),
		},
	}
	if options.Anonymous {
		log.FromContext(ctx).Info("anonymous authentication enabled")
		// anonymous cames at last in the chain, so that it users use token can be authenticated
		authnlist = append(authnlist, api.NewAnonymousAuthenticator())
	}
	return api.DelegateAuthenticator(authnlist), nil
}

type UnsetCookieOnUnauthrizedAuthenticator struct {
	Authenticator api.Authenticator
}

// Authenticate implements api.Authenticator.
func (u UnsetCookieOnUnauthrizedAuthenticator) Authenticate(w http.ResponseWriter, r *http.Request) (*api.AuthenticateInfo, error) {
	info, err := u.Authenticator.Authenticate(w, r)
	if err != nil {
		// unset session cookie if authentication failed
		if cookie, _ := r.Cookie(auth.SessionCookieKey); cookie != nil {
			cookie.MaxAge = -1 // delete the cookie
			cookie.Path = "/"  // ensure the cookie is deleted
			http.SetCookie(w, cookie)
		}
		return nil, err
	}
	return info, nil
}

var _ api.Authenticator = &UnsetCookieOnUnauthrizedAuthenticator{}
