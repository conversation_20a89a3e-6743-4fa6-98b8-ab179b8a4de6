package tunnel

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"time"

	"github.com/quic-go/quic-go"
)

var EdgeRootAddr = EdgeAddr{Addr: "00000000-0000-0000-0000-000000000000"}

const NetworkEdge = "edge"

type ListInterfaceOptions struct {
	IsUpstream bool
}

type ListInterfaceOption func(o *ListInterfaceOptions)

func WithUpstream() ListInterfaceOption {
	return func(o *ListInterfaceOptions) {
		o.IsUpstream = true
	}
}

type Manager interface {
	// edge
	Connect(ctx context.Context, dev Device, options ConnectOptions) error
	Interfaces(options ...ListInterfaceOption) []RouteableInterface
	LocalAddr() net.Addr

	// edge network
	DialContext(ctx context.Context, network, address string) (net.Conn, error)
	Listen(ctx context.Context, network, address string) (net.Listener, error)
}

type ConnectOptions struct {
	Token                  string
	LocalAddressOverride   string
	AuthenticatorOverride  Authenticator
	IgnoreRouteAdvertise   bool
	RouteAdvertiseInterval time.Duration // 0 means no advertise
	// IsUpstream is eq to default out, if no out dev selected  traffic will output via upsteam dev
	IsUpstream        bool
	ConnectedCallback func(iface Interface)
}

type Options struct {
	Eventer       Eventer
	Authenticator Authenticator
}

func NewManager(ctx context.Context, devid string, options *Options) (*DefaultManager, error) {
	if devid == "" {
		return nil, fmt.Errorf("device id is required")
	}
	m := &DefaultManager{
		ifaces:  NewDict[string, *mdevices](),
		laddr:   EdgeAddr{Addr: devid},
		Eventer: options.Eventer,
		Authn:   options.Authenticator,
		pktpool: NewPacketPool(),
	}
	m.mainPacketConn = &PacketConn{
		rx:     make(chan *Packet),
		local:  m,
		laddr:  EdgeAddr{Addr: devid},
		closed: ctx.Done(),
	}
	m.quickTransport = &quic.Transport{Conn: m.mainPacketConn}

	m.quickDailer = &Dialer{
		ctx:            ctx,
		quickTransport: m.quickTransport,
		connections:    make(map[string]quic.Connection),
	}
	return m, nil
}

var _ Manager = (*DefaultManager)(nil)

type DefaultManager struct {
	Authn          Authenticator
	Eventer        Eventer
	ifaces         *Dict[string, *mdevices]
	laddr          net.Addr
	mainPacketConn *PacketConn
	http3Transport http.RoundTripper
	httpTransport  http.RoundTripper
	quickTransport *quic.Transport
	quickDailer    *Dialer
	pktpool        *PacketPool
}

func (c *DefaultManager) LocalAddr() net.Addr {
	return c.laddr
}

func (d *DefaultManager) Interfaces(opts ...ListInterfaceOption) []RouteableInterface {
	opt := &ListInterfaceOptions{}
	for _, o := range opts {
		o(opt)
	}

	items := make([]RouteableInterface, 0, d.ifaces.Len())
	d.ifaces.Range(func(key string, value *mdevices) bool {
		if opt.IsUpstream {
			if value.IsDefault() {
				items = append(items, value)
			}
		} else {
			items = append(items, value)
		}
		return true
	})
	return items
}

func (d *DefaultManager) Connect(ctx context.Context, dev Device, options ConnectOptions) error {
	defer dev.Close()
	authn := d.Authn
	if options.AuthenticatorOverride != nil {
		authn = options.AuthenticatorOverride
	}
	laddr := d.laddr.String()
	if options.LocalAddressOverride != "" {
		laddr = options.LocalAddressOverride
	}
	iface, err := AuthDevice(ctx, authn, laddr, dev, options)
	if err != nil {
		return err
	}
	return d.serve(ctx, iface, options)
}
