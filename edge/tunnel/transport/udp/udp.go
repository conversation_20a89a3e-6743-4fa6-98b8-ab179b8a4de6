package udp

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"sync"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/edge/tunnel"
)

type UDPDevice struct {
	conn   net.PacketConn
	remote net.Addr
}

func (d *UDPDevice) Close() error {
	return d.conn.Close()
}

func (d *UDPDevice) Recv(pkt *tunnel.Packet) error {
	buf := pool.Get()
	defer pool.Put(buf)

	_, addr, err := d.conn.ReadFrom(buf)
	if err != nil {
		return err
	}
	if addr != d.remote {
		return fmt.Errorf("unexpected remote addr %s", addr)
	}
	return tunnel.DecodePacket(bytes.NewReader(buf), pkt)
}

func (d *UDPDevice) Send(pkt *tunnel.Packet) error {
	buf := pool.Get()
	defer pool.Put(buf)

	if err := tunnel.EncodePacket(bytes.NewBuffer(buf), pkt); err != nil {
		return err
	}
	_, err := d.conn.WriteTo(buf, d.remote)
	return err
}

func Open(ctx context.Context, addr string) (tunnel.Device, error) {
	conn, err := net.Dial("udp", addr)
	if err != nil {
		return nil, err
	}
	remoteaddr := conn.RemoteAddr()
	log.Info("open udp", "remoteaddr", remoteaddr)
	udpconn, ok := conn.(*net.UDPConn)
	if !ok {
		return nil, fmt.Errorf("not a udp connection")
	}
	return &UDPDevice{conn: udpconn, remote: remoteaddr}, nil
}

func Listen(ctx context.Context, addr string, tlsconfig *tls.Config, onaccept func(context.Context, tunnel.Device) error) error {
	log.Info("listening udp", "addr", addr)
	ln, err := net.Listen("udp", addr)
	if err != nil {
		return err
	}
	if tlsconfig != nil {
		ln = tls.NewListener(ln, tlsconfig)
	}

	for {
		defer ln.Close()
		conn, err := ln.Accept()
		if err != nil {
			return err
		}

		remoteaddr := conn.RemoteAddr()
		log.Info("accept udp", "remoteaddr", remoteaddr)

		udpconn, ok := conn.(*net.UDPConn)
		if !ok {
			return fmt.Errorf("not a udp connection")
		}
		go func() {
			defer conn.Close()
			if err := onaccept(ctx, &UDPDevice{conn: udpconn, remote: remoteaddr}); err != nil {
				log.Error(err, "on device accept")
				return
			}
		}()
	}
}

var pool = NewBufferPool()

type BufferPool struct {
	pool sync.Pool
}

func NewBufferPool() *BufferPool {
	return &BufferPool{
		pool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 1500)
			},
		},
	}
}

func (p *BufferPool) Get() []byte {
	return p.pool.Get().([]byte)
}

func (p *BufferPool) Put(b []byte) {
	p.pool.Put(b)
}
