package websocket

import (
	"bytes"
	"context"
	"crypto/tls"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"xiaoshiai.cn/common/errors"
	libio "xiaoshiai.cn/common/io"
	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/edge/tunnel"
)

type WebSocketDevice struct {
	ctx        context.Context
	conn       *websocket.Conn
	bufferPool *libio.BufferPool
	recvpkts   chan *bytes.Buffer
	mu         sync.Mutex
}

func (d *WebSocketDevice) Close() error {
	return d.conn.Close()
}

func (d *WebSocketDevice) Recv(pkt *tunnel.Packet) error {
	for {
		typ, reader, err := d.conn.NextReader()
		if err != nil {
			return err
		}
		switch typ {
		case websocket.TextMessage, websocket.BinaryMessage:
			return tunnel.DecodePacket(reader, pkt)
		case websocket.PingMessage:
			if err := d.conn.WriteMessage(websocket.PongMessage, nil); err != nil {
				return err
			}
		}
	}
}

func (d *WebSocketDevice) Send(pkt *tunnel.Packet) error {
	d.mu.Lock()
	defer d.mu.Unlock()

	wc, err := d.conn.NextWriter(websocket.BinaryMessage)
	if err != nil {
		return err
	}
	defer wc.Close()
	return tunnel.EncodePacket(wc, pkt)
}

func Open(ctx context.Context, addr string, tlsconfig *tls.Config) (tunnel.Device, error) {
	dailer := websocket.Dialer{
		TLSClientConfig:  tlsconfig,
		Proxy:            http.ProxyFromEnvironment,
		HandshakeTimeout: 45 * time.Second,
	}
	conn, resp, err := dailer.DialContext(ctx, addr, nil)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusSwitchingProtocols {
		return nil, errors.NewBadRequest("failed to upgrade to websocket")
	}
	dev := NewDevice(ctx, conn)

	return dev, nil
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func HandlerConnect(w http.ResponseWriter, r *http.Request, ondev func(ctx context.Context, dev tunnel.Device) error) {
	ctx := r.Context()
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	defer conn.Close()

	dev := NewDevice(ctx, conn)
	if err := ondev(ctx, dev); err != nil {
		conn.WriteMessage(websocket.CloseMessage, websocket.FormatCloseMessage(websocket.CloseNormalClosure, err.Error()))
		return
	}
}

func NewDevice(ctx context.Context, conn *websocket.Conn) *WebSocketDevice {
	dev := &WebSocketDevice{
		conn:       conn,
		ctx:        ctx,
		recvpkts:   make(chan *bytes.Buffer, 16),
		bufferPool: libio.NewBufferPool(128),
	}
	go KeepAlive(ctx, dev)
	return dev
}

func KeepAlive(ctx context.Context, dev *WebSocketDevice) {
	defer dev.Close()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			dev.mu.Lock()
			err := dev.conn.WriteMessage(websocket.PingMessage, nil)
			dev.mu.Unlock()
			if err != nil {
				log.FromContext(ctx).Error(err, "failed to send ping")
				return
			}
		}
	}
}
