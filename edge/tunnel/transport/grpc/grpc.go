package grpc

import (
	context "context"
	"crypto/tls"
	"net"
	sync "sync"
	"time"

	grpc "google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"k8s.io/klog/v2"
	"xiaoshiai.cn/core/edge/tunnel"
)

// Open implements tunnel.Transport.
func Open(ctx context.Context, addr string, tlsconfig *tls.Config) (tunnel.Device, error) {
	dialoptions := []grpc.DialOption{}
	if tlsconfig != nil {
		dialoptions = append(dialoptions,
			grpc.WithTransportCredentials(credentials.NewTLS(tlsconfig)))
	} else {
		dialoptions = append(dialoptions,
			grpc.WithTransportCredentials(insecure.NewCredentials()))
	}
	c, err := grpc.DialContext(ctx, addr, dialoptions...)
	if err != nil {
		return nil, err
	}
	go func() {
		<-ctx.Done()
		c.Close()
	}()

	stream, err := NewPeerClient(c).Tunnel(ctx)
	if err != nil {
		return nil, err
	}
	dev := &Device[Peer_TunnelClient]{
		stream: stream,
		close:  stream.CloseSend,
	}
	return dev, nil
}

func NewHandler(onAccept func(d tunnel.Device), opt ...grpc.ServerOption) *grpc.Server {
	grpcServer := grpc.NewServer(opt...)
	RegisterPeerServer(grpcServer, &Server{Accept: onAccept})
	return grpcServer
}

// Listen implements tunnel.Transport.
func Listen(ctx context.Context, addr string, tls *tls.Config, accept func(tunnel.Device)) error {
	serveroptions := []grpc.ServerOption{
		grpc.KeepaliveParams(keepalive.ServerParameters{Time: time.Hour}),
	}
	if tls != nil {
		serveroptions = append(serveroptions, grpc.Creds(credentials.NewTLS(tls)))
	}
	grpcServer := NewHandler(accept, serveroptions...)
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		return err
	}
	defer lis.Close()

	go func() {
		<-ctx.Done()
		grpcServer.Stop()
	}()
	klog.Infof("starting grpc server: %s", addr)
	return grpcServer.Serve(lis)
}

type Server struct {
	UnimplementedPeerServer
	Accept func(tunnel.Device)
}

func (g *Server) Tunnel(server Peer_TunnelServer) error {
	dev := &Device[Peer_TunnelServer]{
		stream: server,
		close:  func() error { return nil },
	}
	g.Accept(dev)
	return nil
}

type grpcstream interface {
	Send(*Packet) error
	Recv() (*Packet, error)
}

type Device[T grpcstream] struct {
	stream T
	lock   sync.Mutex
	close  func() error
}

func (d *Device[T]) Send(pkt *tunnel.Packet) error {
	d.lock.Lock()
	defer d.lock.Unlock()
	return d.stream.Send(&Packet{
		Kind: int64(pkt.Kind),
		Src:  pkt.Src,
		Dest: pkt.Dest,
		Data: pkt.Data,
	})
}

func (d *Device[T]) Recv(pkt *tunnel.Packet) error {
	item, err := d.stream.Recv()
	if err != nil {
		return err
	}
	pkt.Kind = tunnel.PacketKind(item.Kind)
	pkt.Src = item.Src
	pkt.Dest = item.Dest
	pkt.Data = item.Data
	return nil
}

func (d *Device[T]) Close() error {
	return d.close()
}
