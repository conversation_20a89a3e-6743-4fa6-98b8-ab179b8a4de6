/*
Copyright The Kubegems Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

syntax = "proto3";
package tunnel;
option go_package = "xiaoshiai.cn/edge/pkg/tunnel/transport/grpc";

service Peer {
  rpc Tunnel(stream Packet) returns (stream Packet) {}
}

message Packet {
  int64 kind = 1;
  string src = 2;
  string dest = 3;
  bytes data = 4;
}
