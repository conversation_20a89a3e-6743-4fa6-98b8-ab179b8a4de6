package tcp

import (
	"context"
	"crypto/tls"
	"net"
	"sync"

	"xiaoshiai.cn/common/log"
	"xiaoshiai.cn/core/edge/tunnel"
)

type TCPDevice struct {
	mu   sync.Mutex
	conn net.Conn
}

func (d *TCPDevice) Close() error {
	return d.conn.Close()
}

func (d *TCPDevice) Recv(pkt *tunnel.Packet) error {
	return tunnel.DecodePacket(d.conn, pkt)
}

func (d *TCPDevice) Send(pkt *tunnel.Packet) error {
	d.mu.Lock()
	defer d.mu.Unlock()
	return tunnel.EncodePacket(d.conn, pkt)
}

func Open(ctx context.Context, addr string, tlsconfig *tls.Config) (tunnel.Device, error) {
	conn, err := net.Dial("tcp", addr)
	if err != nil {
		return nil, err
	}
	if tlsconfig != nil {
		conn = tls.Client(conn, &tls.Config{InsecureSkipVerify: true})
	}
	return &TCPDevice{conn: conn}, nil
}

func Listen(ctx context.Context, addr string, tlsconfig *tls.Config, onaccept func(context.Context, tunnel.Device) error) error {
	log.Info("listening tcp", "addr", addr)
	ln, err := net.Listen("tcp", addr)
	if err != nil {
		return err
	}
	if tlsconfig != nil {
		ln = tls.NewListener(ln, tlsconfig)
	}
	go func() {
		<-ctx.Done()
		ln.Close()
	}()
	for {
		defer ln.Close()
		conn, err := ln.Accept()
		if err != nil {
			return err
		}
		go func() {
			defer conn.Close()
			if err := onaccept(ctx, &TCPDevice{conn: conn}); err != nil {
				log.Error(err, "on device accept")
				return
			}
		}()
	}
}
