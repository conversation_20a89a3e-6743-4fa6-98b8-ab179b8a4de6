package tunnel

import (
	"context"
	"crypto/tls"
	"net/http"
	"strings"

	"github.com/quic-go/quic-go"
	"github.com/quic-go/quic-go/http3"
)

func (c *DefaultManager) ServeHttp3(ctx context.Context, listen string, tlsConfig *tls.Config, handler http.Handler) error {
	if tlsConfig == nil {
		selfSeignTLSconfig, err := NewSelfSignedTLSConfig()
		if err != nil {
			return err
		}
		tlsConfig = selfSeignTLSconfig
	}
	tlsConfig = http3.ConfigureTLSConfig(tlsConfig)
	s := http3.Server{
		TLSConfig: tlsConfig,
		<PERSON><PERSON>:   handler,
		Addr:      listen,
	}
	config := &quic.Config{}
	ln, err := c.QuicTransport().ListenEarly(tlsConfig, config)
	if err != nil {
		return err
	}
	return s.ServeListener(ln)
}

func (c *DefaultManager) Http3Transport() http.RoundTripper {
	if c.http3Transport == nil {
		c.http3Transport = &http3.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
			Dial: func(ctx context.Context, addr string, tlsCfg *tls.Config, cfg *quic.Config) (quic.EarlyConnection, error) {
				if i := strings.LastIndex(addr, ":"); i > 0 {
					addr = addr[:i]
				}
				return c.QuicTransport().DialEarly(ctx, EdgeAddr{Addr: addr}, tlsCfg, cfg)
			},
		}
	}
	return c.http3Transport
}
