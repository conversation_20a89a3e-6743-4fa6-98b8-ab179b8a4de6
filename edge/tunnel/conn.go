package tunnel

import (
	"io"
	"net"
	"time"

	"k8s.io/apimachinery/pkg/types"
)

var _ net.PacketConn = (*PacketConn)(nil)

type PacketConn struct {
	uid    types.UID
	local  *DefaultManager
	rx     chan *Packet
	laddr  net.Addr
	closed <-chan struct{}
}

// Close implements net.PacketConn.
func (c *PacketConn) Close() error {
	return nil
}

// LocalAddr implements net.PacketConn.
func (c *PacketConn) LocalAddr() net.Addr {
	return c.laddr
}

func (c *PacketConn) recv(pkt *Packet) error {
	select {
	case c.rx <- pkt:
		return nil
	case <-c.closed:
		return io.ErrClosedPipe
	}
}

// ReadFrom implements net.PacketConn.
func (c *PacketConn) ReadFrom(p []byte) (n int, addr net.Addr, err error) {
	select {
	case <-c.closed:
		return 0, nil, io.ErrClosedPipe
	case pkt, ok := <-c.rx:
		if !ok {
			return 0, nil, io.ErrClosedPipe
		}

		// put back to pool
		defer c.local.pktpool.Put(pkt)

		return copy(p, pkt.Data), &EdgeAddr{Addr: pkt.Src}, nil
	}
}

// SetDeadline implements net.PacketConn.
func (c *PacketConn) SetDeadline(t time.Time) error {
	return nil
}

// SetReadDeadline implements net.PacketConn.
func (c *PacketConn) SetReadDeadline(t time.Time) error {
	return nil
}

// SetWriteDeadline implements net.PacketConn.
func (c *PacketConn) SetWriteDeadline(t time.Time) error {
	return nil
}

// WriteTo implements net.PacketConn.
func (c *PacketConn) WriteTo(p []byte, addr net.Addr) (n int, err error) {
	select {
	case <-c.closed:
		return 0, io.ErrClosedPipe
	default:
		err := c.local.output(&Packet{Kind: PacketKindData, Src: c.laddr.String(), Dest: addr.String(), Data: p})
		if err != nil {
			return 0, err
		}
		return len(p), nil
	}
}

type EdgeAddr struct {
	Addr string
}

func (a EdgeAddr) Network() string {
	return NetworkEdge
}

func (a EdgeAddr) String() string {
	return a.Addr
}
