package tunnel

import (
	"context"
	"crypto/tls"
	"net"
	"net/http"
	"time"
)

func ServeHTTP(ctx context.Context, manager Manager, handler http.Handler) error {
	ln, err := manager.Listen(ctx, NetworkEdge, manager.LocalAddr().String())
	if err != nil {
		return err
	}
	srv := http.Server{
		Handler: handler,
		// disable http2
		TLSNextProto: map[string]func(*http.Server, *tls.Conn, http.Handler){},
	}
	return srv.Serve(ln)
}

func HttpTransport(manager Manager) http.RoundTripper {
	return &http.Transport{
		MaxIdleConns:    100,
		IdleConnTimeout: 90 * time.Second,
		// disable http2
		TLSNextProto: map[string]func(authority string, c *tls.Conn) http.RoundTripper{},
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			return manager.DialContext(ctx, network, addr)
		},
	}
}
