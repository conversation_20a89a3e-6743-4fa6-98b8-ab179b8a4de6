package tunnel

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/google/uuid"
	"golang.org/x/exp/maps"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
)

var debugtunnel bool

func init() {
	debugtunnel, _ = strconv.ParseBool(os.Getenv("DEBUG_TUNNEL"))
}

type RouteDataKind uint8

const (
	RouteDataKindInvalid RouteDataKind = iota
	RouteDataKindReferesh
	RouteDataKindAdd
	RouteDataKindRemove
)

type RouteData struct {
	Kind  RouteDataKind
	Peers []string
}

const (
	ControlDataReasonNone                = 0
	ControlDataReasonDestinationNotExist = 1
)

type ControlData struct {
	Reason uint8
}

func (r *DefaultManager) updateRouter(from Interface, pkt *Packet) {
	src := pkt.Src
	iface, ok := r.ifaces.Get(src)
	if !ok {
		return
	}
	data := &RouteData{}
	if err := DecodeRoutePacket(pkt, data); err != nil {
		klog.Errorf("decode route packet error: %v", err)
		return
	}
	klog.V(3).Infof("route update from %s: %v", src, data)
	switch data.Kind {
	case RouteDataKindReferesh:
		iface.RestPeers(data.Peers)
	case RouteDataKindAdd:
		for _, peer := range data.Peers {
			iface.AddPeer(peer)
			r.sendevent(Event{
				Addr: peer,
				Via:  []string{from.Addr()},
				Kind: EventKindConnected,
			})
		}
	case RouteDataKindRemove:
		for _, peer := range data.Peers {
			iface.DelPeer(peer)
			r.sendevent(Event{
				Addr: peer,
				Via:  []string{from.Addr()},
				Kind: EventKindDisconnected,
			})
		}
	}
}

func (r *DefaultManager) sendevent(e Event) {
	if r.Eventer != nil {
		r.Eventer.Send(e)
	}
}

func (r *DefaultManager) serve(ctx context.Context, iface Interface, options ConnectOptions) error {
	ifaceid := iface.Addr()

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	miface, ok := r.ifaces.Get(ifaceid)
	if !ok {
		miface = NewMultiInterfaces()
		r.ifaces.Set(ifaceid, miface)
		r.onDevChange(ifaceid, true)

		if options.IsUpstream && options.RouteAdvertiseInterval > 0 {
			go r.runAdvertiseTo(ctx, iface, options.RouteAdvertiseInterval)
		}
	}

	newiface := newPeers(iface, options.IsUpstream, options.IgnoreRouteAdvertise)
	thisuid := uuid.NewString()

	miface.AddDevice(thisuid, newiface)
	defer func() {
		miface.RemoveDevice(thisuid)
		if len(miface.Devices()) == 0 {
			r.ifaces.Remove(ifaceid)
			r.onDevChange(ifaceid, false)
		}
	}()

	if options.ConnectedCallback != nil {
		options.ConnectedCallback(iface)
	}

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			pkt := r.pktpool.Get()
			if err := iface.Recv(pkt); err != nil {
				r.pktpool.Put(pkt)
				return err
			}
			r.input(iface, pkt)
		}
	}
}

func (r *DefaultManager) runAdvertiseTo(ctx context.Context, iface Interface, interval time.Duration) error {
	pkt := &Packet{}
	return wait.PollUntilContextCancel(ctx, interval, true, func(ctx context.Context) (done bool, err error) {
		routedata := RouteData{
			Kind:  RouteDataKindReferesh,
			Peers: r.peers(iface),
		}
		pkt = NewRoutePacket("", iface.Addr(), routedata)
		klog.V(3).Infof("route advertise to %s: %v", pkt.Dest, routedata)

		if err := r.output(pkt); err != nil {
			return false, err
		}
		return true, nil
	})
}

func (r *DefaultManager) peers(exclude Interface) []string {
	allpeers := map[string]struct{}{}
	r.ifaces.Range(func(key string, value *mdevices) bool {
		if exclude != nil && value.Addr() == exclude.Addr() {
			return true
		}
		allpeers[value.Addr()] = struct{}{}
		for _, peer := range value.Peers() {
			allpeers[peer] = struct{}{}
		}
		return true
	})
	return maps.Keys(allpeers)
}

func (r *DefaultManager) onDevChange(addr string, online bool) {
	r.routeChanged(addr, online)
	if online {
		r.sendevent(Event{
			Addr: addr,
			Kind: EventKindConnected,
		})
	} else {
		r.sendevent(Event{
			Addr: addr,
			Kind: EventKindDisconnected,
		})
	}
}

func (r *DefaultManager) routeChanged(addr string, online bool) {
	r.ifaces.Range(func(_ string, value *mdevices) bool {
		if value.Addr() == addr || !value.IsDefault() {
			return true
		}
		pkt := NewRoutePacket(value.LocalAddr(), value.Addr(), RouteData{
			Kind: func() RouteDataKind {
				if online {
					return RouteDataKindAdd
				} else {
					return RouteDataKindRemove
				}
			}(),
			Peers: []string{addr},
		})
		value.Send(pkt)
		return true
	})
}

func (r *DefaultManager) input(from Interface, pkt *Packet) {
	if pkt.Kind == PacketKindUnknown {
		return
	}
	if pkt.Dest == "" || pkt.Dest == r.laddr.String() || pkt.Dest == from.LocalAddr() {
		if debugtunnel {
			klog.V(5).Infof("local input, src=%s dest=%s kind=%d", pkt.Src, pkt.Dest, pkt.Kind)
		}
		r.localInput(from, pkt)
		return
	}
	r.forward(from, pkt)
}

func (r *DefaultManager) forward(from Interface, pkt *Packet) {
	via := r.find(pkt.Dest)
	if via == nil {
		r.drop(from, pkt)
		return
	}
	via.Send(pkt)
	r.pktpool.Put(pkt)
}

func (r *DefaultManager) localInput(from Interface, pkt *Packet) {
	switch pkt.Kind {
	case PacketKindRoute:
		r.updateRouter(from, pkt)
		r.pktpool.Put(pkt)
	case PacketKindCtrl:
		// ignore
	case PacketKindData:
		r.recvPacket(from, pkt)
	}
}

func (r *DefaultManager) recvPacket(_ Interface, pkt *Packet) {
	r.mainPacketConn.recv(pkt)
}

func (r *DefaultManager) find(dest string) Interface {
	ifaces := r.ifaces.Values()
	if dest != "" {
		for _, iface := range ifaces {
			if iface.Addr() == dest {
				return iface
			}
			// peer match
			if iface.HasPeer(dest) {
				return iface
			}
		}
	}
	// default route
	for _, iface := range ifaces {
		if iface.IsDefault() {
			return iface
		}
	}
	return nil
}

func (r *DefaultManager) drop(from Interface, pkt *Packet) {
	if debugtunnel {
		klog.V(5).Infof("drop packet, src=%s, dest=%s", pkt.Src, pkt.Dest)
	}
	from.Send(NewControlPacket(from.Addr(), pkt.Src, ControlData{Reason: ControlDataReasonDestinationNotExist}))
}

func (r *DefaultManager) output(pkt *Packet) error {
	via := r.find(pkt.Dest)
	if via == nil {
		return fmt.Errorf("localout: destination: %s not found", pkt.Dest)
	}
	pkt.Src = via.LocalAddr()
	if debugtunnel {
		klog.V(5).Infof("local out packet, src=%s, dest=%s", pkt.Src, pkt.Dest)
	}
	return via.Send(pkt)
}
