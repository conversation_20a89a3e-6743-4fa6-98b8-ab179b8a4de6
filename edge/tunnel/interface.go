package tunnel

import (
	"net"
	"sync"
	"time"

	"golang.org/x/exp/maps"
	"xiaoshiai.cn/common/errors"
)

type RouteableInterface interface {
	Interface
	IsDefault() bool
	Peers() []string
	HasPeer(peer string) bool
	AddPeer(peer string)
	DelPeer(peer string)
	RestPeers(peers []string)
}

type ConnectedInterface struct {
	UID         string
	Interface   RouteableInterface
	ConnectTime time.Time
}

func NewMultiInterfaces() *mdevices {
	return &mdevices{}
}

type mdevices struct {
	devices []ConnectedInterface
	mu      sync.RWMutex
}

// AddPeer implements RouteableInterface.
func (m *mdevices) AddPeer(peer string) {
	dev := m.GetDevice()
	if dev == nil {
		return
	}
	dev.AddPeer(peer)
}

// Addr implements RouteableInterface.
func (m *mdevices) Addr() string {
	dev := m.GetDevice()
	if dev == nil {
		return ""
	}
	return dev.Addr()
}

// Close implements RouteableInterface.
func (m *mdevices) Close() error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	var errs []error
	for _, dev := range m.devices {
		if err := dev.Interface.Close(); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.NewAggregate(errs)
}

// DelPeer implements RouteableInterface.
func (m *mdevices) DelPeer(peer string) {
	dev := m.GetDevice()
	if dev == nil {
		return
	}
	dev.DelPeer(peer)
}

// HasPeer implements RouteableInterface.
func (m *mdevices) HasPeer(peer string) bool {
	dev := m.GetDevice()
	if dev == nil {
		return false
	}
	return dev.HasPeer(peer)
}

// IsDefault implements RouteableInterface.
func (m *mdevices) IsDefault() bool {
	dev := m.GetDevice()
	if dev == nil {
		return false
	}
	return dev.IsDefault()
}

// LocalAddr implements RouteableInterface.
func (m *mdevices) LocalAddr() string {
	dev := m.GetDevice()
	if dev == nil {
		return ""
	}
	return dev.LocalAddr()
}

// Peers implements RouteableInterface.
func (m *mdevices) Peers() []string {
	dev := m.GetDevice()
	if dev == nil {
		return nil
	}
	return dev.Peers()
}

// Recv implements RouteableInterface.
func (m *mdevices) Recv(pkt *Packet) error {
	dev := m.GetDevice()
	if dev == nil {
		return net.ErrClosed
	}
	return dev.Recv(pkt)
}

// RestPeers implements RouteableInterface.
func (m *mdevices) RestPeers(peers []string) {
	dev := m.GetDevice()
	if dev == nil {
		return
	}
	dev.RestPeers(peers)
}

// Send implements RouteableInterface.
func (m *mdevices) Send(pkt *Packet) error {
	dev := m.GetDevice()
	if dev == nil {
		return net.ErrClosed
	}
	return dev.Send(pkt)
}

// GetDevice implements MultipleDevices.
func (m *mdevices) GetDevice() RouteableInterface {
	m.mu.RLock()
	defer m.mu.RUnlock()
	if len(m.devices) == 0 {
		return nil
	}
	return m.devices[len(m.devices)-1].Interface
}

// Devices implements MultipleDevicesInterface.
func (m *mdevices) Devices() []ConnectedInterface {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.devices
}

func (m *mdevices) AddDevice(uid string, dev RouteableInterface) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.devices = append(m.devices, ConnectedInterface{UID: uid, Interface: dev, ConnectTime: time.Now()})
}

func (m *mdevices) RemoveDevice(uid string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	for i, dev := range m.devices {
		if dev.UID == uid {
			m.devices = append(m.devices[:i], m.devices[i+1:]...)
			return
		}
	}
}

var _ RouteableInterface = (*mdevices)(nil)

func newPeers(iface Interface, isDefault bool, ignoreAdvertisment bool) *peers {
	return &peers{
		Interface:          iface,
		isdeault:           isDefault,
		ignoreAdvertisment: ignoreAdvertisment,
		peers:              make(map[string]struct{}),
	}
}

type peers struct {
	Interface
	isdeault           bool
	ignoreAdvertisment bool
	lock               sync.RWMutex
	peers              map[string]struct{}
}

// AddPeer implements RouteableInterface.
func (d *peers) AddPeer(peer string) {
	if d.ignoreAdvertisment {
		return
	}
	d.lock.Lock()
	defer d.lock.Unlock()
	d.peers[peer] = struct{}{}
}

// DelPeer implements RouteableInterface.
func (d *peers) DelPeer(peer string) {
	d.lock.Lock()
	defer d.lock.Unlock()
	delete(d.peers, peer)
}

// HasPeer implements RouteableInterface.
func (d *peers) HasPeer(peer string) bool {
	d.lock.RLock()
	defer d.lock.RUnlock()
	_, ok := d.peers[peer]
	return ok
}

// RestPeers implements RouteableInterface.
func (d *peers) RestPeers(peers []string) {
	if d.ignoreAdvertisment {
		return
	}
	d.lock.Lock()
	defer d.lock.Unlock()
	newpeers := map[string]struct{}{}
	for _, peer := range peers {
		newpeers[peer] = struct{}{}
	}
	d.peers = newpeers
}

// IsDefault implements RouteableInterface.
func (d *peers) IsDefault() bool {
	return d.isdeault
}

// Peer implements RouteableInterface.
func (d *peers) Peers() []string {
	d.lock.RLock()
	defer d.lock.RUnlock()
	return maps.Keys(d.peers)
}
