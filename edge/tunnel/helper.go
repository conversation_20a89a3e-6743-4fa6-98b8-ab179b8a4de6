package tunnel

import (
	"context"
	"crypto/rand"
	"crypto/tls"
	"crypto/x509"
	"encoding/hex"
	"math"
	"sync"
	"time"

	"golang.org/x/exp/maps"
	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"xiaoshiai.cn/common/log"
)

func RandHex(n int) string {
	b := make([]byte, n)
	rand.Read(b)
	return hex.EncodeToString(b)
}

type TLSOptions struct {
	CertFile           string `json:"certFile,omitempty"`
	KeyFile            string `json:"keyFile,omitempty"`
	InsecureSkipVerify bool   `json:"insecureSkipVerify,omitempty"`
}

func NewDefaultTLS() *TLSOptions {
	return &TLSOptions{}
}

func (o *TLSOptions) ToTLSConfig() *tls.Config {
	if o == nil || o.CertFile == "" || o.KeyFile == "" {
		return nil
	}
	// certs
	certificate, err := tls.LoadX509KeyPair(o.CertFile, o.KeyFile)
	if err != nil {
		panic(err)
	}
	config := &tls.Config{
		Certificates: []tls.Certificate{certificate},
	}
	// ca
	cas := x509.NewCertPool()
	config.ClientCAs = cas
	config.RootCAs = cas
	if o.InsecureSkipVerify {
		config.InsecureSkipVerify = true
	}
	return config
}

func NewDict[K comparable, V any]() *Dict[K, V] {
	return &Dict[K, V]{
		items: make(map[K]V),
	}
}

type Dict[K comparable, V any] struct {
	items map[K]V
	lock  sync.RWMutex
}

func (p *Dict[K, V]) Values() []V {
	p.lock.RLock()
	defer p.lock.RUnlock()
	return maps.Values(p.items)
}

func (p *Dict[K, V]) Exists(key K) bool {
	p.lock.RLock()
	defer p.lock.RUnlock()
	_, ok := p.items[key]
	return ok
}

func (p *Dict[K, V]) Len() int {
	return len(p.items)
}

func (p *Dict[K, V]) Get(key K) (V, bool) {
	p.lock.RLock()
	defer p.lock.RUnlock()
	value, ok := p.items[key]
	return value, ok
}

func (p *Dict[K, V]) Set(key K, value V) {
	p.lock.Lock()
	defer p.lock.Unlock()
	p.items[key] = value
}

func (p *Dict[K, V]) LoadOrStore(key K, value V) (V, bool) {
	p.lock.Lock()
	defer p.lock.Unlock()
	if v, ok := p.items[key]; ok {
		return v, true
	}
	p.items[key] = value
	return value, false
}

func (p *Dict[K, V]) Remove(key K) {
	p.lock.Lock()
	defer p.lock.Unlock()
	delete(p.items, key)
}

func (p *Dict[K, V]) Range(fn func(key K, value V) bool) {
	p.lock.RLock()
	defer p.lock.RUnlock()
	for key, value := range p.items {
		if !fn(key, value) {
			return
		}
	}
}

// Retry retries the function use exponential backoff if always fails,it reset backoff after success
func Retry(ctx context.Context, fn func(ctx context.Context) error) error {
	initDuration := 5 * time.Second
	successGateDuration := 10 * time.Second

	backoff := wait.Backoff{
		Duration: initDuration,
		Cap:      5 * time.Minute,
		Factor:   1.5,
		Jitter:   0.2,
		Steps:    math.MaxInt,
	}

	tim := time.NewTimer(0)
	defer tim.Stop()
	<-tim.C // to make sure the first occur consumed

	var preverr error
	for {
		start := time.Now()
		func() {
			defer runtime.HandleCrash()
			preverr = fn(ctx)
		}()
		// when the function takes more than 10 seconds,
		// we consider it run successfully, so we reset the backoff
		if time.Since(start) > successGateDuration {
			backoff.Duration = initDuration
		}
		if preverr == nil {
			return nil
		}
		nextbackoff := backoff.Step()
		log.Error(preverr, "retry", "after", nextbackoff)
		tim.Reset(nextbackoff)
		select {
		case <-ctx.Done():
			return preverr
		case <-tim.C:
		}
	}
}

type BitMap struct {
	lock sync.RWMutex
	bits []uint64
}

func NewBitMap(n int) *BitMap {
	return &BitMap{
		bits: make([]uint64, (n+63)>>6),
	}
}

func (b *BitMap) Set(n int) {
	b.lock.Lock()
	defer b.lock.Unlock()
	b.bits[n>>6] |= 1 << (n & 63)
}

func (b *BitMap) Get(n int) bool {
	b.lock.RLock()
	defer b.lock.RUnlock()
	return b.bits[n>>6]&(1<<(n&63)) != 0
}

func (b *BitMap) Clear(n int) {
	b.lock.Lock()
	defer b.lock.Unlock()
	b.bits[n>>6] &^= 1 << (n & 63)
}
