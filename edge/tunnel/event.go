package tunnel

import (
	"k8s.io/klog/v2"
)

type EventKind string

const (
	EventKindUnknown      EventKind = "Unknown"
	EventKindConnected    EventKind = "Connected"
	EventKindDisconnected EventKind = "Disconnected"
)

type Event struct {
	Kind EventKind
	Addr string
	Via  []string
}

type Eventer interface {
	Send(Event)
}

type EventerFunc func(Event)

func (f EventerFunc) Send(e Event) {
	f(e)
}

type LoggerEventer struct{}

func (l LoggerEventer) Send(e Event) {
	klog.Infof("tunnel event: device %s %s via %v", e.Addr, e.Kind, e.Via)
}
